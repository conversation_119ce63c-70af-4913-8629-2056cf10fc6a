:root
{
	--card-width: 200px;
	--card-height: 118px;
	--card-gap-horizontal: 12.2px;
	--card-gap-vertical: 8px;
	--background-color-select: #e3e5e9;
	--background-color-cards: #eef6f1;
	--text-selects-colors: #335d67;
	--text-inside-car-colors: #2c455c;
	--filled-progress-bar-color: #bfc7cc;
	--background-color-card-none: #e3e5e9;
	--buttonvalide-background-color: #e7f5e5;
	--buttonvalide-text-color: green;
	--buttonvalide-border-type: none;
	--btn-border-color: transparent; /* Default no border in light mode */
	--pagination-text-color: #333;
	--pagination-border-color: #ddd;
	--pagination-hover-bg-color: #ddd;
	--pagination-active-bg-color: #47c2ae;
	--pagination-active-text-color: #fff;
}

[data-theme="dark"] {
	--card-width: 200px;
	--card-height: 115px;
	--card-gap-horizontal: 12.2px;
	--card-gap-vertical: 12px;
	--background-color-select: #1e1f21;
	--background-color-cards: #1e1f21;
	--text-selects-colors: #7d7e81;
	--text-inside-car-colors: #888888;
	--filled-progress-bar-color: #3f3e40;
	--background-color-card-none: #272a31;
	--buttonvalide-background-color: transparent;
	--buttonvalide-text-color: #6a6a6e;
	--buttonvalide-border-type: 1px;
	--btn-border-color: #888888; /* Border color same as text color in dark mode */
	--pagination-text-color: #ccc;
	--pagination-border-color: #444;
	--pagination-hover-bg-color: #555;
	--pagination-active-bg-color: #47c2ae;
	--pagination-active-text-color: #000;
}

#rsuro .container {
	display: flex;
	flex-wrap: wrap;
	padding: 1px;
	gap: var(--card-gap-vertical) var(--card-gap-horizontal);

}

#rsuro .container > div {
	flex: 0 0 auto; /* Prevent flex items from growing or shrinking */
	margin: 0; /* Reset margin if needed */
}

#rsuro .card-custom {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-cards);
	padding: 5px; /* Reduced padding by 10% (from 5px to 4.5px) */
	text-align: center;
	flex-direction: column;
}

#rsuro .card-custom-none {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-card-none);
	padding: 6px; /* Reduced padding by 10% (from 5px to 4.5px) */
	text-align: center;
	flex-direction: column;
}

#red {
	border-left: 5px solid #ff4c4c; /* Left border in red */
}
#green {
	border-left: 5px solid #21b39f; /* Left border in red */
}
#orange {
	border-left: 5px solid #ea9765; /* Left border in red */
}

#yellow {
	border-left: 5px solid #f4af00; /* Left border in red */
}

#gray {
	border-left: 5px solid var(--filled-progress-bar-color); /* Left border in red */
}

#rsuro .card-header {
	font-size: 1.2rem;
	font-weight: 600;
	background-color: transparent;
	border: none;
	margin-bottom: 2px; /* Reduced the margin between header and number */
}


#headertextolor {
	color: var(--text-inside-car-colors);
}

#nonetext {
	color: var(--filled-progress-bar-color);
}

#rsuro .col-auto {
	margin-left: -1.4%;
}
#rsuro .card-number {

	font-size: 2.4rem; /* Reduced text size by 10% */
	font-weight: bold;
	margin-left: 29.5%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
}

#rsuro .card-number-none {

	font-size: 2.4rem; /* Reduced text size by 10% */
	font-weight: bold;
	margin-left: 44%;
	color: var(--filled-progress-bar-color);
	background-color: transparent;
}


#rsuro .card-subtext {
	font-size: 0.8rem; /* Slightly smaller for "KO 852" */
	font-weight: 500;
	color: var(--text-inside-car-colors);
	background-color: transparent;
	text-align: left; /* Align text to the left */
}

#rsuro .card-footer {
	font-size: 0.68rem; /* Reduced text size by 10% */
	color: var(--text-inside-car-colors);
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: transparent;

	border: none;
}

.card-header {
	padding: 0;
}
.card-footer {
	padding: 0;
}
.card-footer-none {
	padding: 0;
}
#rsuro .card-footer-none {
	font-size: 0.68rem; /* Reduced text size by 10% */
	color: var(--filled-progress-bar-color);
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
}

#rsuro .progress-bar-custom {
	height: 4px;
	background-color: #d3d4d5;
	border-radius: 2px;
	width: 90%;
	margin-left: 10px;

}

#rsuro .progress-bar-fill {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 48%;

}

#rsuro .progress-bar-none {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 0;
}
#rsuro .navbar {
	background-color: transparent;
	padding: 15px;
}

#rsuro .navbar .form-select {
	height: 90%; /* Reduced by 10% */
	margin-right: 9px; /* Reduced by 10% */
	font-size: 90%; /* Reduced text size by 10% */
}

#rsuro .navbar .form-select.date-select {
	background-color: var(--background-color-select);
	border: none;
	width: 126px; /* Reduced by 10% */
	color: var(--text-selects-colors);
	font-size: 90%; /* Reduced text size by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .navbar .form-select.week-select {
	width: 153px; /* Reduced by 10% */
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%; /* Reduced text size by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .navbar .form-select.manager-select {
	width: 160px; /* Reduced by 10% */
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%; /* Reduced text size by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .navbar .btn {
	width: 90px; /* Reduced by 10% */
	height: 81%; /* Reduced by 10% */
}

#rsuro .btnicon {
	color: var(--text-selects-colors);
	background-color: var(--background-color-select);
	border: none;
	height: 90%; /* Reduced by 10% */
	margin-right: 9px; /* Reduced by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}


#rsuro .btn {
	color: var(--buttonvalide-text-color);
	background-color: var(--buttonvalide-background-color);
	border: var(--buttonvalide-border-type) solid var(--btn-border-color); /* Apply the border color */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .select-wrapper {
	display: flex;
	align-items: center;
	background-color: var(--background-color-select);
	border-radius: 9px;
	padding: 0 2px;
	margin-right: 9px; /* Reduced by 10% */
}

#rsuro .select-wrapper .bi {
	color: var(--text-selects-colors);
	margin-left: 1.5%;
}
#rsuro .select-wrapper .form-select {
	border: none;
	margin: 0;
	background: none; /* Remove individual backgrounds */
	box-shadow: none;
	border-radius: 0;
	width: auto;
}

#rsuro .start-date {
	border-top-left-radius: 9px; /* Round only the left corner */
	border-bottom-left-radius: 9px;
}

#rsuro .end-date {
	border-top-right-radius: 9px;
  border-bottom-right-radius: 9px;
}

#rsuro .cardscont {
	margin-left: -1.5%;
}


.pagination-container {
	display: flex;
	align-items: center;
	font-family: Arial, sans-serif;
	color: #666;
	font-size: 14px;
	margin-top: 20px;
	justify-content: center;
}

.pagination-container .pagination-label {
	margin-right: 10px;
}

.pagination {
	display: flex;
	gap: 4px;
}

.pagination a {
	color: #666;
	padding: 4px 10px;
	text-decoration: none;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	transition: background-color 0.3s, color 0.3s;
}

.pagination a.disabled {
	color: #ccc;
	border-color: #f0f0f0;
	cursor: not-allowed;
}

.pagination a:hover:not(.disabled) {
	background-color: #f0f0f0;
}


.pagination-info {
	margin-left: 10px;
	color: #666;
}
.containers {
	font-family: 'CONSOLAS' !important;
	color: #d3e4e6;
	border-radius: 8px;
	margin: 0;
}

.top-bar {

	height: 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;

}

.top-bar-left {
	color: #a8b4b8;
	font-weight: bold;
}

.top-bar-icons {
	display: flex;
	gap: 10px;
}

.top-bar-icons img {
	width: 20px;
	height: 20px;
	cursor: pointer;
}

.containeres {
	background-color: #06262b;
	padding: 10px 10px 10px 30px;
	border-radius: 8px;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);

}

h1 {
	text-align: center;
	color: #e3f2fd;
}

p {
	margin: 10px 0;
}

.calculation {
	font-weight: bold;
	color: #76c7c0;
}

.input-icon {
	position: relative;
	display: flex;
	align-items: center;
}

.input-icon input {
	padding-right: 35px; /* pour laisser de la place pour l'icône */
	height: 30px;
	font-size: 16px;
	flex: 1;
	border-radius: 5px;
}

.input-icon .icon {
	position: absolute;
	right: 10px; /* placer l'icône à droite de l'input */
	width: 20px;
	height: 20px;
}
.suite {
	color: #fff;  
}
highlight-text {
	font-weight: bold;
	color: #ce00c4;
}

autocomplete-field {
	position: relative;
	display: flex;
	color: #fff;
}

autocomplete-field input {
	padding-right: 35px;
	height: 30px;
	font-size: 16px;
	flex: 1;
	color: #fff;
}

autocomplete-field .autocomplete-icon {
	position: absolute;
	right: 10px;
	width: 20px;
	color: #fff;
}


.input-line {
	display: flex;
	align-items: center;
}

.cmd-prompt {
	margin-right: 5px;
}

#cmd-input {
	background: none;
	border: none;
	color: white;
	outline: none;
	width: 100%;
}

.command-line {
	font-size: 14px;
	width: 100%;

	overflow-x: hidden;
	overflow-y: scroll
}

.command-line.light {
	background-color: #fff
}

.command-line.light .command-row {
	position: relative;
	margin-bottom: 5px
}

.command-line.light .command-row.active {
	background: #f5f5f5
}

.command-line .command-row {
	position: relative;
	margin-bottom: 5px
}


.command-line .command-row .command-time,
.command-line .command-row .command-user {
	color: #e7e7e7;
	display: inline-block;
	padding-right: 5px
}

.command-line .command-row .command-user {
	font-weight: 700
}

.command-line .command-row .command-entry {
	padding-right: 5px;
	color: #fff;
	display: inline;
	overflow-wrap: break-word;
	word-wrap: break-word;
	-ms-word-break: break-all;
	word-break: break-all;
	-ms-hyphens: auto;
	-webkit-hyphens: auto;
	hyphens: auto
}

.command-line .command-row .command-entry.command-entry-protected:empty {
	display: none
}

.command-line .command-row .command-entry.block {
	display: block
}

.command-line .command-row .command-entry:focus {
	outline: none
}

.command-line .command-row .secret {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	opacity: 0
}

.command-line .command-row.error .command-entry {
	font-weight: 700;
	color: red
}

.command-line .command-row.success .command-entry {
	font-weight: 700;
	color: #00c300
}

.command-line .command-row.info .command-entry {
	font-weight: 700;
	color: #00a9ff
}

.command-line .command-row.warning .command-entry {
	font-weight: 700;
	color: orange
}