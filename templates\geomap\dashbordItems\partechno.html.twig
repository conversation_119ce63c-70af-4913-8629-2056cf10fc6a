<style>
.bonustechno-header {
    font-size: 0.8em;
    margin: 0;
}




.bonustechno-legend {
    list-style: none;
    padding: 0;
    margin: 0;
}

.bonustechno-legend li {
    display: flex;
    align-items: center;
     color: var( --4text-colors);
    font-size: 0.8em;
    margin-bottom: 5px;
}

.bonustechno-legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 30%;
}
</style>
<div class="bonustechno-container">
    <h5 class="text-header4s">Par Techno</h5>
    <div class="ChartsByType bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="bonustechnoPieChart"></canvas>
        </div>
        <ul class="bonustechno-legend">
            <!-- The legend will be populated dynamically with JavaScript -->
        </ul>
    </div>
</div>

<script>
var bonustechnoChart; // Global reference to the chart instance
function ParTechno(data) {
    console.log('ParTechno', data);

    let chartData = Array.isArray(data.Tech) ? data.Tech : [];

    function updateChart() {
        console.log(chartData);
        
        let canvas = document.getElementById('bonustechnoPieChart');
        if (!canvas) {
            console.error("Canvas element not found.");
            return;
        }
        
        let bonustechnoCtx = canvas.getContext('2d');

        // Destroy existing chart if it exists
        if (window.bonustechnoChart) {
            window.bonustechnoChart.destroy();
        }

        // Create predefined colors
        const colors = ['#9a4eca',  '#145277','#62eade']; 
        let backgroundColors = [];

        let labels = chartData.map(item => item.tech);
        let dataPoints = chartData.map(item => item.totalVentes);

        // Generate gradients dynamically
        chartData.forEach((item, index) => {
            let gradient = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
            gradient.addColorStop(0, colors[index % colors.length]);
            backgroundColors.push(gradient);
        });

        // Update the legend
        createLegend(chartData, colors);

        // Create the pie chart
        window.bonustechnoChart = new Chart(bonustechnoCtx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: dataPoints,
                    backgroundColor: colors, // Gradients need special handling, using solid colors for now
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false }
                },
                cutout: '80%'
            }
        });
    }

    function createLegend(chartData, colors) {
        let legendContainer = document.querySelector('.bonustechno-legend');
        if (!legendContainer) {
            console.error("Legend container not found.");
            return;
        }

        legendContainer.innerHTML = ''; // Clear previous legend

        chartData.forEach((item, index) => {
            let legendItem = document.createElement('li');
            legendItem.classList.add('legend-item');

            let legendColor = document.createElement('span');
            legendColor.classList.add('bonustechno-legend-color');
            legendColor.style.background = colors[index % colors.length];

            legendItem.appendChild(legendColor);
            legendItem.appendChild(document.createTextNode(` ${item.tech}`));
            legendContainer.appendChild(legendItem);
        });
    }

    updateChart();
}


 



</script>
