let recognition;
let finalTranscript = '';
const keywords = {
    "zoom sur": 8,
    "vas à": 8,
    "montre-moi": 8,
    "lance": 8,
    "affiche le cluster de": 8,
    "montre moi le cluster de": 8
}
function extractCityName(text) {
    let patterns = [
        /zoom sur (.+)/,
        /va sur la ville de (.+)/,
        /vas à (.+)/,
        /montre-moi (.+)/,
        /lance (.+)/,
        /affiche le cluster de (.+)/,
        /montre moi le cluster de (.+)/,
        /^(.+)$/
    ];

    for (let pattern of patterns) {
        let match = text.match(pattern);
        if (match) {
            return match[1].trim();
        }
    }

    return null;
}
function startListening() {
    recognition.start();
}
function stopListening() {
    recognition.stop();
}
function updateTextArea(message) {
    let output = document.getElementById("microCard");
    output.innerHTML = message + "\n"; 
    output.scrollTop = output.scrollHeight;
}


function detectCity(city) {
    console.log(`🔎 Recherche de la ville : ${city}`);
    updateTextArea(`🌍 Ville détectée : ${city}`);

    fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${city},France`)
        .then(response => response.json())
        .then(data => {
            if (data.length > 0) {
                const { lon, lat } = data[0];
                console.log(`📍 Coordonnées trouvées: ${lat}, ${lon}`);
                updateTextArea(`📍 Coordonnées trouvées: ${lat}, ${lon}`);

                map.flyTo({
                    center: [lon, lat],
                    zoom: 12,
                    essential: true
                });

                new mapboxgl.Marker()
                    .setLngLat([lon, lat])
                    .addTo(map);
            } else {
                console.log(`❌ Ville non trouvée : ${city}`);
                updateTextArea(`❌ Ville non trouvée : ${city}`);
            }
        })
        .catch(error => {
            console.error('Erreur lors de la recherche de la ville:', error);
            updateTextArea(`⚠ Erreur lors de la recherche de la ville.`);
        });
}

function detectContract(text) {
    let words = text.split(" ");
    let contractNumber = words[words.length - 1]; 
    updateTextArea(`📜 Contrat détecté : ${contractNumber}`);
    searchContract(contractNumber);
    //alert(`🔎 Recherche de contrat pour : ${contractNumber}`);
}

function sendEmailAlert() {
    updateTextArea("📧 Envoi d'un email : Résultat de votre demande envoyé.");
    //alert("📧 Je viens d'envoyer le résultat de votre demande par email.");
}

function searchContract(queryWord) {
    const searchInput = document.getElementById("SearchInputTopBar");
    if (searchInput) {
        searchInput.value = queryWord;
        searchInput.dispatchEvent(new Event('input'));
    }
}



if ('webkitSpeechRecognition' in window) {
    recognition = new webkitSpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = false;
    recognition.lang = 'fr-FR';

    recognition.onresult = (event) => {
        let transcript = event.results[event.resultIndex][0].transcript.trim().toLowerCase();
        console.log(`🎤 Texte détecté: "${transcript}"`);
        updateTextArea(`🎤 Détection: "${transcript}"`);

        if (transcript.includes("mail") || transcript.includes("e-mail")) {
            sendEmailAlert();
            return;
        }

        if (transcript.includes("contrat")) {
    
            detectContract(transcript);
            return;
        }

        let cleanedCity = extractCityName(transcript);
        if (cleanedCity) {
            detectCity(cleanedCity);
        }
    };

    recognition.onerror = (event) => {
        console.error("Erreur de reconnaissance vocale:", event.error);
    };
} else {
    alert("Votre navigateur ne supporte pas la reconnaissance vocale.");
}

document.addEventListener("DOMContentLoaded", function () {
    const card = document.getElementById("microCard");
    const micIcon = document.getElementById("micIcon");
    const terminalIcon = document.getElementById("terminalicon");
    const hiddenDiv = document.getElementById("hiddendiv");

    // Afficher/cacher la carte microCard
    micIcon.addEventListener("click", function () {  
        card.style.display = (card.style.display === "none" || card.style.display === "") ? "block" : "none";
        if (card.style.display === "none"|| card.style.display === ""){
            startListening();   
        }else {
            stopListening();
        }
        

    });

    // Afficher/cacher le div caché quand on clique sur terminalicon
    terminalIcon.addEventListener("click", function() {
        hiddenDiv.style.display = (hiddenDiv.style.display === "none" || hiddenDiv.style.display === "") ? "block" : "none";
    });

    // Rendre la carte microCard déplaçable
    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifie que c'est bien un clic gauche
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
        document.body.style.userSelect = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
        document.body.style.userSelect = "auto";
    });
});
