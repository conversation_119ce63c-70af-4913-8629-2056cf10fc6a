<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('styles/MainBloc.css') }}">

<div class="table-container">
    <nav class="navbar table-navbar navbar-expand-lg">
        <ul class="navbar-nav me-auto mb-1 mb-lg-0">
            <li class="nav-item" id="tablenav">
                <a class="nav-link active" href="#">All</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">Unfulfilled</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">Unpaid</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">Open</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">Closed</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">Local Delivery</a>
            </li>
        </ul>
        <div class="navbar-nav">
            <a class="nav-link" id="iconnav" href="#">
                <i class="bi bi-search"></i>
            </a>
            <a class="nav-link" id="iconnav" href="#">
                <i class="bi bi-sliders"></i>
            </a>
            <a class="nav-link" id="iconnav" href="#">
                <i class="bi bi-filter"></i>
            </a>
            <a class="nav-link" id="iconnav" href="#">
                <i class="bi bi-arrow-down-up"></i>
            </a>
            <a class="nav-link" id="iconnav" href="#">
                <i class="bi bi-three-dots"></i>
            </a>
        </div>
    </nav>

    <div class="container mt-2">
        <div class="row mb-3 align-items-center">
            <div class="col-10 col-md-2">
                <div class="custom-select-container">
                    <i class="bi bi-star icon"></i>
                    <select class="form-select" id="roleSelect">
                        <option value="">Role</option>
                        {% set displayedRoles = [] %}
                        {% for product in roleapps %}
                            {% if product.roleApp.name not in displayedRoles %}
                                <option value="{{ product.roleApp.name }}">{{ product.roleApp.name }}</option>
                                {% set displayedRoles = displayedRoles | merge([product.roleApp.name]) %}
                            {% endif %}
                        {% else %}
                            <option disabled>No roles available</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div id="addfilter" class="col-12 col-md-2 d-flex align-items-center justify-content-center">
                <span>+ Add filter</span>
            </div>
        </div>
    </div>

    <table class="table table-responsive">
        <thead>
            <tr>
                <th>
                    <label class="custom-checkbox">
                        <input type="checkbox" class="form-check-input" id="customCheck">
                    </label>
                </th>
                <th style="width:3%;">
                    <i class="bi bi-hash"></i> ID
                </th>
                <th style="width:20%;">
                    <i class="bi bi-person"></i> Email
                </th>
                <th style="width:10%;">
                    <i class="bi bi-star"></i> Role
                </th>
                <th>
                    <i class="bi bi-wallet2"></i> Code
                </th>
            </tr>
        </thead>
        <tbody>
            {% for users in roleapps %}
                <tr data-role="{{ users.roleApp.name }}">
                    <th style="width:3%;">
                        <label class="custom-checkbox">
                            <input type="checkbox" class="form-check-input" id="customCheck">
                        </label>
                    </th>
                    <td><strong>{{ users.id }}</strong></td>
                    <td><strong>{{ users.email }}</strong></td>
                    <td>{{ users.roleApp.name }}</td>
                    <td>{{ users.roleApp.code }}</td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="4">No role applications available.</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.getElementById('roleSelect').addEventListener('change', function() {
        var selectedRole = this.value;
        var rows = document.querySelectorAll('tbody tr');

        rows.forEach(function(row) {
            if (row.getAttribute('data-role') === selectedRole || selectedRole === "") {
                row.style.display = ''; // Show row
            } else {
                row.style.display = 'none'; // Hide row
            }
        });
    });
</script>
