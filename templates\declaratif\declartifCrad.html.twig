<style>


:root
{
	--card-width: 200px;
	--card-height: 135px;
	--card-gap-horizontal: 12.2px;
	--card-gap-vertical: 8px;
	--background-color-select: #ecf4ef;
	--background-color-cards: #e7f3fa;
	--text-selects-colors: #335d67;
	--text-inside-car-colors: #2c455c;
	--filled-progress-bar-color: #bfc7cc;
	--background-color-card-none: #e3e5e9;
	--buttonvalide-background-color: #e7f5e5;
	--buttonvalide-text-color: green;
	--buttonvalide-border-type: none;
	--btn-border-color: transparent;
	--small-ko-button-colorbg: #fef7fe;
	--scrollbar-track: #eee;
}

[data-theme="dark"] {
	--card-width: 200px;
	--card-height: 135px;
	--card-gap-horizontal: 12.2px;
	--card-gap-vertical: 12px;
	--background-color-select: #1e1f21;
	--background-color-cards: #1e1f21;
	--text-selects-colors: #7d7e81;
	--text-inside-car-colors: #888888;
	--filled-progress-bar-color: #3f3e40;
	--background-color-card-none: #272a31;
	--buttonvalide-background-color: transparent;
	--buttonvalide-text-color: #6a6a6e;
	--buttonvalide-border-type: 1px;
	--btn-border-color: #888888;
	--small-ko-button-colorbg: #242c2d;
	--scrollbar-thumb: #2B2D31;

}

.row {
    --bs-gutter-x: 0rem;}
#rsuro {
	display: flex;
	flex-direction: column;
	scrollbar-width: thin;
	scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
	overflow-y: auto; /* Enable scrolling when content overflows */
	max-height: 80vh; /* Limit the height, adjust as needed */
}

#rsuro .container {
	display: flex;
	flex-wrap: wrap;
	padding: 1px;
	gap: var(--card-gap-vertical) var(--card-gap-horizontal);
	max-width: 100%;

}

#rsuro .container > div {

	margin: 0;
}

#rsuro .card-custom {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-cards);
	padding: 5px;
	text-align: center;
	flex-direction: column;
}

#rsuro .card-custom-none {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-card-none);
	padding: 6px;
	text-align: center;
	flex-direction: column;
}

#red {
	border-left: 5px solid #ff4c4c;
}
#green {
	border-left: 5px solid #21b39f;
}
#orange {
	border-left: 5px solid #ea9765;
}

#yellow {
	border-left: 5px solid #f4af00;
}

#gray {
	border-left: 5px solid var(--filled-progress-bar-color);
}

#rsuro .card-header {
	font-size: 1.2rem;
	font-weight: 600;
	background-color: transparent;
	border: none;
	margin-bottom: 2px;
}

#headertextolor {
	color: var(--text-inside-car-colors);
}

#nonetext {
	color: var(--filled-progress-bar-color);
}

#rsuro .col-auto {
	margin-left: -1.5% !;
	margin-bottom: 10px;
}
#rsuro .card-number {
	margin-top: -15%;
	font-size: 2.2rem;
	font-weight: bold;
	margin-left: 30.5%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
}

#rsuro .card-number-none {
	margin-top: -15%;
	font-size: 2.3rem;
	font-weight: bold;
	margin-left: 44%;
	color: var(--filled-progress-bar-color);
	background-color: transparent;
}

#rsuro .card-subtext {
	font-size: 0.7rem;
	font-weight: 500;
	margin-left: 1%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
	text-align: left;
}

#rsuro .ko-button {
	background-color: var(--small-ko-button-colorbg);
	color: white;
	border-radius: 30%;
	padding: 3px 8px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	font-size: 0.65rem;
	border: none;
}

#rsuro .ko-text {
	color: red;
	margin-right: 10%;
}

#rsuro .number {
	color: var(--text-inside-car-colors);
}

#rsuro .card-footer {
	font-size: 0.68rem;
	color: var(--text-inside-car-colors);
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: transparent;

	border: none;
}

#rsuro .card-footer-none {
	font-size: 0.68rem;
	color: var(--filled-progress-bar-color);
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
}

#rsuro .progress-bar-custom {
	height: 4px;
	background-color: #515254;
	border-radius: 2px;
	width: 100%;
}

#rsuro .progress-bar-fill {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 48%;
}

#rsuro .progress-bar-none {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 0;
}
#rsuro .navbar {
	background-color: transparent;
	padding: 15px;
}

#rsuro .navbar .form-select {
	height: 90%;
	margin-right: 9px;
	font-size: 90%;
}

#rsuro .navbar .form-select.date-select {
	background-color: var(--background-color-select);
	border: none;
	width: 126px;
	color: var(--text-selects-colors);
	font-size: 90%;
	border-radius: 9px;
}

#rsuro .navbar .form-select.week-select {
	width: 153px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%;
	border-radius: 9px;
}

#rsuro .navbar .form-select.manager-select {
	width: 160px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%;
	border-radius: 9px;
}

#rsuro .navbar .btn {
	width: 90px;
	height: 75%;
}

#rsuro .btnicon {
	color: var(--text-selects-colors);
	background-color: var(--background-color-select);
	border: none;
	height: 90%;
	margin-right: 9px;
	border-radius: 9px;
}

#rsuro .btn {
	color: var(--buttonvalide-text-color);
	background-color: var(--buttonvalide-background-color);
	border: var(--buttonvalide-border-type) solid var(--btn-border-color);
	border-radius: 9px;
}

#rsuro .btncqt {
	color: #ffff;
	background-color: #33b4e8;
	border: none;
	height: 35px;
	width: 90px;
	border-radius: 10.8px;
}

#rsuro .btnmig {
	color: #3d7fa8;
	background-color: transparent;
	border: 1px solid #3d7fa8;
	height: 35px;
	width: 90px;
	border-radius: 10.8px;
}


#rsuro .select-wrapper {
	display: flex;
	align-items: center;
	background-color: var(--background-color-cards);
	border-radius: 9px;
	padding: 0 2px;
	margin-right: 9px;
}

#rsuro .select-wrapper .bi {
	color: var(--text-selects-colors);
	margin-left: 1.5%;
}
#rsuro .select-wrapper .form-select {
	border: none;
	margin: 0;
	background: none;
	box-shadow: none;
	border-radius: 0;
	width: auto;
}

#rsuro .start-date {
	border-top-left-radius: 9px;
	border-bottom-left-radius: 9px;
}

#rsuro .end-date {
	border-top-right-radius: 9px;
	/ border-bottom-right-radius: 9px;
}

#rsuro .cardscont {
	margin-left: -1.5%;
}
.rightnav {
    background-color: #000; /* Dark background */
    padding: 5px;
    border-radius: 8px;
    display: inline-flex;
    gap: 10px;

}


.btns:hover {
    background-color: #333; /* Hover state */
}

.bi {
    font-size: 16px; /* Icon size */
}
@media screen and(min-width: 1920px) and(min-height: 1080px){:root {
	--card-width: 320px;
	--card-height: 180px;
	--card-gap-horizontal: 10.2px;
	--card-gap-vertical: 12px;
	--background-color-select: #1e1f21;
	--background-color-cards: #1e1f21;
	--text-selects-colors: #7d7e81;
	--text-inside-car-colors: #888888;
	--filled-progress-bar-color: #3f3e40;
	--background-color-card-none: #272a31;
	--buttonvalide-background-color: transparent;
	--buttonvalide-text-color: #6a6a6e;
	--buttonvalide-border-type: 1px;

	--btn-border-color: #888888;
	--small-ko-button-colorbg: #242c2d;
}

[data-theme="dark"] {
	--card-width: 320px;
	--card-height: 180px;
	--card-gap-horizontal: 10.2px;
	--card-gap-vertical: 12px;
	--background-color-select: #1e1f21;
	--background-color-cards: #1e1f21;
	--text-selects-colors: #7d7e81;
	--text-inside-car-colors: #888888;
	--filled-progress-bar-color: #3f3e40;
	--background-color-card-none: #272a31;
	--buttonvalide-background-color: transparent;
	--buttonvalide-text-color: #6a6a6e;
	--buttonvalidemig-text-color: #40a9dd;
	--buttonvalidemig-text-color: #40a9dd;
	--buttonvalide-border-type: 1px;
	--btn-border-color: #888888;
	--btnmig-border-color: #40a9dd;
	--small-ko-button-colorbg: #242c2d;

}

#rsuro .col-auto {
	margin-left: 10%;
	margin-left: 1.2% !;
}

#rsuro .container {
	display: flex;
	flex-wrap: wrap;
	padding: 1px;
	gap: var(--card-gap-vertical) var(--card-gap-horizontal);
	max-width: 100%;
}

#rsuro .container > div {

	margin: 0;
}

#rsuro .card-custom {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-cards);
	padding: 30px;
	text-align: center;
	flex-direction: column;
}

#rsuro .card-custom-none {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-card-none);
	padding: 30px;
	text-align: center;
	flex-direction: column;
}

#red {
	border-left: 5px solid #ff4c4c;
}
#green {
	border-left: 5px solid #21b39f;
}
#orange {
	border-left: 5px solid #ea9765;
}

#yellow {
	border-left: 5px solid #f4af00;
}

#gray {
	border-left: 5px solid var(--filled-progress-bar-color);
}

#rsuro .card-header {
	font-size: 2rem;
	font-weight: 600;
	background-color: transparent;
	border: none;
	margin-right: 4%;
	margin-top: -10%;
	margin-bottom: 2px;
}

#headertextolor {
	color: var(--text-inside-car-colors);
}

#nonetext {
	color: var(--filled-progress-bar-color);
}

#rsuro .card-number {
	font-size: 3rem;
	font-weight: bold;
	margin-top: -13%;
	margin-left: 30.5%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
}

#rsuro .card-number-none {
	margin-top: -13%;
	font-size: 3rem;
	font-weight: bold;
	margin-left: 43.5%;
	color: var(--filled-progress-bar-color);
	background-color: transparent;
}

#rsuro .card-subtext {
	font-size: 0.7rem;
	font-weight: 500;
	margin-left: 1%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
	text-align: left;
}

#rsuro .ko-button {
	background-color: var(--small-ko-button-colorbg);
	color: white;
	border-radius: 30%;
	padding: 3px 8px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	font-size: 1.2rem;
	border: none;
}

#rsuro .ko-text {
	color: red;
	margin-right: 10%;
}

#rsuro .number {
	color: var(--text-inside-car-colors);
}

#rsuro .card-footer {
	font-size: 1rem;
	color: var(--text-inside-car-colors);
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: transparent;

	border: none;
}

#rsuro .card-footer-none {
	font-size: 1rem;
	color: var(--filled-progress-bar-color);
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
}

#rsuro .progress-bar-custom {
	height: 4px;
	background-color: #515254;
	border-radius: 2px;
	width: 100%;
}

#rsuro .progress-bar-fill {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 48%;
}

#rsuro .progress-bar-none {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 0;
}
#rsuro .navbar {
	background-color: transparent;
	padding: 18px;
}

#rsuro .navbar .form-select {
	height: 108%;
	margin-right: 10.8px;
	font-size: 108%;
}

#rsuro .navbar .form-select.date-select {
	background-color: var(--background-color-select);
	border: none;
	width: 151.2px;
	color: var(--text-selects-colors);
	font-size: 108%;
	border-radius: 10.8px;
}

#rsuro .navbar .form-select.week-select {
	width: 183.6px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 108%;
	border-radius: 10.8px;
}

#rsuro .navbar .form-select.manager-select {
	width: 192px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 108%;
	border-radius: 10.8px;
}

#rsuro .navbar .btn {
	width: 108px;
	height: 97.2%;
}

#rsuro .btnicon {
	color: var(--text-selects-colors);
	background-color: var(--background-color-select);
	border: none;
	height: 108%;
	margin-right: 10.8px;
	border-radius: 10.8px;
}

#rsuro .btns {
	color: var(--buttonvalide-text-color);
	background-color: var(--buttonvalide-background-color);
	border: var(--buttonvalide-border-type) solid var(--btn-border-color);
	border-radius: 10.8px;
}


#rsuro .btncqt {
	color: #ffff;
	background-color: #33b4e8;
	border: none;
	height: 40px;
	width: 100px;
	border-radius: 10.8px;
}

#rsuro .btnmig {
	color: #3d7fa8;
	background-color: transparent;
	border: 1px solid #3d7fa8;
	height: 40px;
	width: 100px;
	border-radius: 10.8px;
}


#rsuro .select-wrapper {
	display: flex;
	align-items: center;
	background-color: var(--background-color-cards);
	border-radius: 10.8px;
	padding: 0 2.4px;
	margin-right: 10.8px;
}

#rsuro .select-wrapper .bi {
	color: var(--text-selects-colors);
	margin-left: 1.8%;
}

#rsuro .start-date {
	border-top-left-radius: 10.8px;
	border-bottom-left-radius: 10.8px;
}

#rsuro .end-date {
	border-top-right-radius: 10.8px;
	border-bottom-right-radius: 10.8px;
}

#rsuro .cardscont {
	margin-left: 0.5%;
}
.rightnav {
    background-color: #000; /* Dark background */
    padding: 5px;
    border-radius: 8px;
    display: inline-flex;
    gap: 10px;
}

.btns {
    color: white;
    font-size: 14px;
    font-weight: 500;
    background-color: transparent; /* Button background */
    border: none;
    padding: 2px 15px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 5px; /* Space between icon and text */
    transition: background-color 0.2s;
}
.active{
  background-color:#333  
}
.btns:hover {
    background-color: #333; /* Hover state */
}

.bi {
    font-size: 16px; /* Icon size */
}
}


.pagination-container {
	display: flex;
	align-items: center;
	font-family: Arial, sans-serif;
	color: #666;
	font-size: 14px;
	padding: 10px;
	justify-content: center;
}

.pagination-container .pagination-label {
	margin-right: 10px;
}

.pagination {
	display: flex;
	gap: 4px;
}

.pagination a {
	color: #666;
	padding: 4px 10px;
	text-decoration: none;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	transition: background-color 0.3s, color 0.3s;
}

.pagination a.disabled {
	color: #ccc;
	border-color: #f0f0f0;
	cursor: not-allowed;
}

.pagination a:hover:not(.disabled) {
	background-color: #f0f0f0;
}

.pagination .active {}
{
	# background-color: #47c2ae;
	color: white;
	border: 1px solid #47c2ae; #
}
.pagination-info {
	margin-left: 10px;
	color: #666;
}
</style>

<style>
.containers {
	font-family: 'CONSOLAS' !important;
	color: #d3e4e6;
	border-radius: 8px;
	margin: 0;
}

.top-bar {

	height: 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;

}

.top-bar-left {
	color: #a8b4b8;
	font-weight: bold;
}

.top-bar-icons {
	display: flex;
	gap: 10px;
}

.top-bar-icons img {
	width: 20px;
	height: 20px;
	cursor: pointer;
}

.containeres {
	background-color: #06262b;
	padding: 10px 10px 10px 30px;
	border-radius: 8px;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);

}

h1 {
	text-align: center;
	color: #e3f2fd;
}

p {
	margin: 10px 0;
}

.calculation {
	font-weight: bold;
	color: #76c7c0;
}

.input-icon {
	position: relative;
	display: flex;
	align-items: center;
}

.input-icon input {
	padding-right: 35px; /* pour laisser de la place pour l'icône */
	height: 30px;
	font-size: 16px;
	flex: 1;
	border-radius: 5px;
}

.input-icon .icon {
	position: absolute;
	right: 10px; /* placer l'icône à droite de l'input */
	width: 20px;
	height: 20px;
}
.suite {
	color: #fff;  !important
}
highlight-text {
	font-weight: bold;
	color: #ce00c4;
}

autocomplete-field {
	position: relative;
	display: flex;
	color: #fff: align-items: center;
}

autocomplete-field input {
	padding-right: 35px;
	height: 30px;
	font-size: 16px;
	flex: 1;
	color: #fff: border-radius: 5px;
}

autocomplete-field .autocomplete-icon {
	position: absolute;
	right: 10px;
	width: 20px;
	color: #fff: height: 20px;
}


.input-line {
	display: flex;
	align-items: center;
}

.cmd-prompt {
	margin-right: 5px;
}

#cmd-input {
	background: none;
	border: none;
	color: white;
	outline: none;
	width: 100%;
}

.command-line {
	font-size: 14px;
	width: 100%;

	overflow-x: hidden;
	overflow-y: scroll
}

.command-line.light {
	background-color: #fff
}

.command-line.light .command-row {
	position: relative;
	margin-bottom: 5px
}

.command-line.light .command-row.active {
	background: #f5f5f5
}

.command-line .command-row {
	position: relative;
	margin-bottom: 5px
}


.command-line .command-row .command-time,
.command-line .command-row .command-user {
	color: #e7e7e7;
	display: inline-block;
	padding-right: 5px
}

.command-line .command-row .command-user {
	font-weight: 700
}

.command-line .command-row .command-entry {
	padding-right: 5px;
	color: #fff;
	display: inline;
	overflow-wrap: break-word;
	word-wrap: break-word;
	-ms-word-break: break-all;
	word-break: break-all;
	-ms-hyphens: auto;
	-webkit-hyphens: auto;
	hyphens: auto
}

.command-line .command-row .command-entry.command-entry-protected:empty {
	display: none
}

.command-line .command-row .command-entry.block {
	display: block
}

.command-line .command-row .command-entry:focus {
	outline: none
}

.command-line .command-row .secret {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	opacity: 0
}

.command-line .command-row.error .command-entry {
	font-weight: 700;
	color: red
}

.command-line .command-row.success .command-entry {
	font-weight: 700;
	color: #00c300
}

.command-line .command-row.info .command-entry {
	font-weight: 700;
	color: #00a9ff
}

.command-line .command-row.warning .command-entry {
	font-weight: 700;
	color: orange
}</style>

<div id="rsuro">
  <nav class="navbar">
    <div class="container">
      <div class="d-flex align-items-center">
        <button class="btnicon btn-primary ms-auto">
          <i class="bi bi-geo-alt-fill" style="font-size: 1.3rem;"></i>
        </button>
        <div class="select-wrapper">
          <i class="bi bi-calendar-week" style="font-size: 1.2rem;"></i>
          <select class="form-select date-select start-date">
            <option selected>01/10/2024</option>
          </select>
          <span class="date-separator">-</span>
          <select class="form-select date-select end-date">
            <option selected>31/10/2024</option>
          </select>
        </div>

        <select class="form-select week-select me-2">
          <option selected>Toutes semaines</option>
        </select>
        <select class="form-select manager-select me-2">
          <option selected>Tous gestionnaires</option>
        </select>
        <button class="btn btn-primary ms-auto">Valider</button>
      </div>

      {# <div class="rightnav">
        <button class="btns btn-cqt ms-auto me-2 active">
          <i class="bi bi-arrow-clockwise"></i>
          CQT
        </button>
        <button class="btns btn-mig ms-auto">
          <i class="bi bi-arrow-clockwise"></i>
          MIG
        </button>
      </div> #}
    </div>
  </nav>

  <div class="container cardscont">
    <div class="test" style="padding-left: 30px;">
	<div class="row custom-row justify-content-center">
		<div class="container cardscont">
			<div style="margin-left: 50px;" class="row custom-row justify-content-center">
				{% if declaratifData['hydra:member'] is defined and declaratifData['hydra:member'] is not empty %}
					{% for declaratif in declaratifData['hydra:member'] %}
						{% if declaratif is not empty and declaratif.id <= 5 %}
							<div class="col-auto">
								<div class="card-custom" id="red">
									<div class="card-header" id="headertextolor">{{ declaratif.seller.nom }}
										{{ declaratif.seller.prenom }}</div>
									<div class="card-footer">
										<div class="card-number">{{ declaratif.seller.id }}</div>
									</div>
									<div class="progress-bar-custom">
										<div class="progress-bar-fill"></div>
									</div>
									<div class="card-footer">
										<span>4.84%</span>
										<span>3</span>
									</div>
								</div>
							</div>
						{% endif %}
					{% endfor %}
				{% else %}
					<p style="margin-left: 50px;">No declaratif data available.</p>
				{% endif %}
			</div>

			
			
		</div>
	</div>
    </div>

    <script>
      // Consolidated card data (duplicates removed)
      const cardsData = [
        { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
		 { id: "red", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },
        { id: "green", title: "Arcachon", number: 413, ko: 4, progress: 4.84, value: 3 },

        { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		   { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		      { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		   { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		 { id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
		{ id: "gray", title: "Blaye", number: 0, progress: 0, ko: 0, value: 0 },
      ];

  // Function to generate cards dynamically
    function generateCards(data, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = data
            .map(card => {
                const isNoneType = card.number === 0;
                const commonHTML = `
                    <div class="col-auto">
                        <div class="${isNoneType ? "card-custom-none" : "card-custom"}" id="${card.id}">
                            <div class="card-header">${card.title}</div>
                            <div class="card-footer">
                                <div class="${isNoneType ? "card-number-none" : "card-number"}">${card.number}</div>
                                ${!isNoneType ? `
                                <button class="ko-button">
                                    <span class="ko-text">KO</span>
                                    <span class="number">${card.ko}</span>
                                </button>` : ""}
                            </div>
                            <div class="progress-bar-custom">
                                <div class="${isNoneType ? "progress-bar-none" : "progress-bar-fill"}" style="width: ${card.progress}%"></div>
                            </div>
                            <div class="${isNoneType ? "card-footer-none" : "card-footer"}">
                                <span>${card.progress}%</span>
                                ${!isNoneType ? `<span>${card.value}</span>` : ""}
                            </div>
                        </div>
                    </div>`;
                return commonHTML;
            })
            .join('');
    }

    // Generate cards for both types (with number > 0 and number === 0)
    generateCards(cardsData, 'cards-container');
    </script>
  </div>
</div>


<div style="position: absolute;bottom: 0; width: 100%;">
<div class="pagination-container">
	<span class="pagination-label">Pages:</span>
	<div class="pagination">
		<a href="#" class="disabled">First</a>
		<a href="#" class="disabled">Prev</a>
		<a href="#" class="active">1</a>
		<a href="#">2</a>
		<a href="#">3</a>
		<a href="#">4</a>
		<a href="#">5</a>
		<a href="#">Next</a>
		<a href="#">Last</a>
	</div>
	<span class="pagination-info">1 of 5</span>
</div>

	<div class="containers">
			<div class="containeres">
				<div class="top-bar">
					<div class="top-bar-left calculation">DESKTOP-ON5C84Q  ></div>
					<div class="top-bar-icons">
						<div class="input-icon">
							<img src="https://img.icons8.com/ios-glyphs/30/4b484b/filter.png" alt="Filter" class="icon">
							<input type="text" style="background-color: #06262b;" placeholder="">
						</div>
						<i class="bi bi-download fs-5"></i>
						{# <img src="https://img.icons8.com/ios-glyphs/30/ffffff/download.png" alt="Download"> #}
						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/add-file.png" alt="Add File">

						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/settings.png" alt="Settings">
						<i class="bi bi-chevron-up"></i>

						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/microphone.png" alt="Microphone">

						<img src="https://img.icons8.com/?size=100&id=45&format=png&color=FFFFFF" alt="close">

					</div>

				</div>

				<div>
					<div id="command-line" class="command-line"></div>
				</div>
			</br>
		</div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ asset('cmd/bundle.min.js') }}"></script><script src="{{ asset('cmd/app.min.js') }}"></script><script src="{{ asset('cmd/script.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
<script>
const buttons = document.querySelectorAll('.btns');
buttons.forEach(button => {
    button.addEventListener('click', () => {
      
        buttons.forEach(btn => btn.classList.remove('active'));
     
        button.classList.add('active');
    });
});

</script>