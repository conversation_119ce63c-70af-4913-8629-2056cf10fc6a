<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpFoundation\Request;

class DashboardController extends AbstractController
{
    #[Route('/dashboard', name: 'dashboard')]
    public function index(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        // Get JWT token from the session
        $jwt = $session->get('jwt');

        // MODE DÉVELOPPEMENT : Contourner l'authentification
        if (!$jwt) {
            $jwt = 'dev-token-' . time();
            $session->set('jwt', $jwt);
        }

        // Initialize the $productions variable to ensure it's defined
        $productions = [];

        try {
            // Fetch user data
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/parent/all?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
           

            // Fetch product data
            $response2 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/products', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $productData = json_decode($response2->getContent(), true);
           

            // Check if product data is received
            if (!isset($productData['hydra:member'])) {
                $this->addFlash('error', 'No products found.');
                return $this->redirectToRoute('error_route');
            }

            $products = $productData['hydra:member'];

            // Fetch production data
            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/productions-by-point-sales?etat=2&id_point_of_sale=2&page=10', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            $productionData = json_decode($response3->getContent(), true);
         
            if (isset($productionData['hydra:member'])) {
                $productions = $productionData['hydra:member']; 
            } 

        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage()); 
        }

        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'DashboardController',
            'user' => $userData,
            'produits' => $products, 
            'productions' => $productions ,
            'showMainBloc' => true,
            'showClusters' => false,  // This variable must be set
            'showVentes' => false,   
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=>false,
            'showprisesArretcutest'=>false,
            'prisesArretcuCluster'=>false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => false,
        ]);
    }

    #[Route('/clusters/dashboard', name: 'clusters_dashboard')]
    public function clustersDashboard(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login'); 
        }
    
        $userData = [];
        try {
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Clusters Dashboard',
            'user' => $userData,
            'showMainBloc' => false,
            'showClusters' => true, 
            'showVentes' => false,  
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> false,

        ]);
    }
    
    #[Route('/ventes/dashboard', name: 'ventes_dashboard')]
    public function ventesDashboard(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        try {
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $userData = json_decode($response->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching user data: ' . $e->getMessage());
        }
    
        $optionGroups = ['Tech', 'Produit', 'Categorie', 'PreCommande'];
        $productionsData = [];
    
        // Function to make API request and decode response
        $getData = function($optionGroup) use ($httpClient, $jwt) {
            $url = sprintf(
                'https://api.nomadcloud.fr/api/productions-kpi-by-option-group/3/B/%s?debut=01-10-2024&fin=31-10-2024&page=1',
                $optionGroup
            );
    
            try {
                $response = $httpClient->request('GET', $url, [
                    'headers' => ['Authorization' => 'Bearer ' . $jwt],
                ]);
                return json_decode($response->getContent(), true);
            } catch (\Exception $e) {
                $this->addFlash('error', "Error fetching data for $optionGroup: " . $e->getMessage());
                return [];
            }
        };
    
        // Fetch data for each option group
        foreach ($optionGroups as $optionGroup) {
            $productionsData[$optionGroup] = $getData($optionGroup);
        }

        // Fetch intervention places
        try {
            $response2 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/intervention_places?page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $InterventionPlacesData = json_decode($response2->getContent(), true);
            $IntPlaces = $InterventionPlacesData['hydra:member'] ?? [];
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching intervention places: ' . $e->getMessage());
            $IntPlaces = [];
        }
    
        // Fetch other data (productionsMotif)
        try {
            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/productions-by-lib-motif-instance/3?debut=01-10-2024&fin=31-10-2024&page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $productionsMotif = json_decode($response3->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching motif productionsMotif: ' . $e->getMessage());
            $productionsMotif = [];
        }
        //fetch data productions-by-annulation-types
        try {
            $response4 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/productions-by-annulation-types/3?debut=01-10-2024&fin=31-10-2024&page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $productionsAnnulation = json_decode($response4->getContent(), true);
          
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching motif productionsAnnulation: ' . $e->getMessage());
            $productionsAnnulation = [];
        }
        try {
            $response5 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/productions-by-intervention-status/3?debut=01-10-2024&fin=31-10-2024&page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $productionsIntervention = json_decode($response5->getContent(), true);
     
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching motif productionsIntervention: ' . $e->getMessage());
            $productionsIntervention = [];
        }
        try {
            $response6 = $httpClient->request('GET', 'http://api.nomadcloud.fr/api/productions-by-category/3?month=11&year=2024&page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $productionscategory= json_decode($response6->getContent(), true);
  
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching motif productionscategory: ' . $e->getMessage());
            $productionscategory = [];
        }
    
        // Process the productionscategory data to calculate the totals for each category
        $categoryTotals = [];
        foreach ($productionscategory as $month => $categories) {
            $monthTotal = 0;
            foreach ($categories as $status => $data) {
                foreach ($data as $key => $value) {
                    $monthTotal += $value;  // Add the value for each category
                }
            }
            $categoryTotals[$month] = $monthTotal;  // Store the total for the month
        }

    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'IntPlaces' => $IntPlaces,
            'productionsMotif' => $productionsMotif,
            'productionsAnnulation'=>$productionsAnnulation,
            'productionsIntervention'=>$productionsIntervention,
            'productionsGroupData' => $productionsData, 
            "productionscategory"=>$productionscategory,
            'categoryTotals' => $categoryTotals,  
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => true,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => false,
        ]);
    }
    


    #[Route('/prises/livraison', name: 'prises_livraison')]
    public function prisesLivraison(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            // Récupération des données utilisateur
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Récupération des détails utilisateur
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            // Récupération des clusters totaux
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $response2 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/clusters-totals/{$cpv}?page=1", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch cluster totals.');
                return $this->redirectToRoute('app_dashboard');
            }
            
    
            $InterventionClustersTotal = json_decode($response2->getContent(), true);
         
        
            // Initialiser les états des boutons à partir des paramètres de la requête
            $params = [
                'adsl' => $request->query->get('adsl', 'no'),
                'thd' => $request->query->get('thd', 'no'),
                'mobMono' => $request->query->get('mobMono', 'no'),
                'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
                'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
                'page' => $request->query->get('page', 1)
            ];
    
            // Correction de l'URL avec l'interpolation correcte
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                $cpv,
                $params['adsl'],
                $params['thd'],
                $params['mobMono'],
                $params['mobMultiThd'],
                $params['mobMultiAdsl'],
                $params['page']
            );
    
            $InterventionClustersTotals = [];
    
            $response4 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            
            
            if ($response4->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response4->getContent(), true);
                
             
            } else {
                $this->addFlash('error', 'Failed to fetch intervention clusters.');
            }
           
    
            // Correction de l'URL avec l'interpolation correcte
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi-arret-cuivre/%s?moisArretCuivre=1&anneeArretCuivre=2025&page=1',
                $cpv,
            
            
            );
    
            $interventionsplaceskpi = [];
    
            $response6 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            
            if ($response6->getStatusCode() === 200) {
                $interventionsplaceskpi = json_decode($response6->getContent(), true);
            
            } else {
                $this->addFlash('error', 'Failed to fetch intervention clusters.');
            }
            

        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'Clusterstotal' => $InterventionClustersTotal,
            'Clusterstotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'interventionsplaceskpi'=>$interventionsplaceskpi ?? [],
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => true,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => false,
        ]);
    }
    


    #[Route('/clusters/details/{code_cluster}', name: 'cluster_details')]
    public function clusterDetails(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $clusterDetails = [];
        $userData = [];
    
        try {
            // Fetch user-connected data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $dataUser = json_decode($response1->getContent(), true);
            $userId = $dataUser['user_id'] ?? null;
    
            if (!$userId) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard'); 
            }
    
            // Fetch user details
            $response2 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userId}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response2->getContent(), true);
            $cpv = $dataUser['cpv'] ?? null;
    
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard'); 
            }
    
            // Fetch cluster details
            $response3 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/clusters-details/{$cpv}/{$code_cluster}?page=1", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response3->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch cluster details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $clusterDetails = json_decode($response3->getContent(), true);
            
            // Initialize the button states from request parameters
            $params = [
                'adsl' => $request->query->get('adsl', 'no'),
                'thd' => $request->query->get('thd', 'no'),
                'mobMono' => $request->query->get('mobMono', 'no'),
                'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
                'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
                'page' => $request->query->get('page', 1)
            ];
    
            // Correct the string interpolation for URL
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?codeCluster=%s&adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                $cpv,
                $code_cluster,
                $params['adsl'],
                $params['thd'],
                $params['mobMono'],
                $params['mobMultiThd'],
                $params['mobMultiAdsl'],
                $params['page']
            );
    
            $InterventionClustersTotals = [];
            
            // Fetch intervention cluster totals
            $response4 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $InterventionClustersTotals = json_decode($response4->getContent(), true);
    
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'clusterDetails' => $clusterDetails,
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'showMainBloc' => false,
            'showClusters' => false,
            'code_cluster' => $code_cluster,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => true,
            'showClusterDetailsRue' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> false,
            'Clusterstotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'showVenteSet' => false,
        ]);
    }
    
    
    #[Route('/clusters/details/rue/{code_cluster}/{detailkpiinseeCode}', name: 'cluster_detailsrue')]
public function clusterDetailsRue(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster, string $detailkpiinseeCode, Request $request): Response
{
    $jwt = $session->get('jwt');
    if (!$jwt) {
        return $this->redirectToRoute('app_login');
    }

    $clusterDetailsRue = [];
    $userData = [];
    $InterventionClustersTotals = [];

    try {
        // Fetch user-connected data
        $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response1->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user data.');
            return $this->redirectToRoute('app_dashboard');
        }

        $dataUser = json_decode($response1->getContent(), true);
        $userId = $dataUser['user_id'] ?? null;

        if (!$userId) {
            $this->addFlash('error', 'User ID not found in the response.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Fetch user details
        $response2 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userId}", [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response2->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user details.');
            return $this->redirectToRoute('app_dashboard');
        }

        $userData = json_decode($response2->getContent(), true);
        $cpv = $dataUser['cpv'] ?? null;

        if (!$cpv) {
            $this->addFlash('error', 'CPV not found in the user data.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Fetch cluster details by Rue
        $response3 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/interventions-places/totals/{$cpv}/{$code_cluster}/{$detailkpiinseeCode}?page=1", [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response3->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch cluster details by Rue.');
            return $this->redirectToRoute('app_dashboard');
        }

        $clusterDetailsRue = json_decode($response3->getContent(), true);

        // Initialize button states from query parameters
        $params = [
            'adsl' => $request->query->get('adsl', 'no'),
            'thd' => $request->query->get('thd', 'no'),
            'mobMono' => $request->query->get('mobMono', 'no'),
            'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
            'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
            'page' => $request->query->get('page', 1),
        ];

        // Fix URL generation using double quotes for interpolation
        $url = sprintf(
            'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?codeCluster=%s&codeInsee=%s&adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
            $cpv,
            $code_cluster,
            $detailkpiinseeCode,
            $params['adsl'],
            $params['thd'],
            $params['mobMono'],
            $params['mobMultiThd'],
            $params['mobMultiAdsl'],
            $params['page']
        );

        // Fetch InterventionClustersTotals
        $response4 = $httpClient->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response4->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch intervention cluster totals.');
        } else {
            $InterventionClustersTotals = json_decode($response4->getContent(), true);
        }

    } catch (\Exception $e) {
        $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
    }

    return $this->render('dashboard/layout.html.twig', [
        'clusterDetailsRue' => $clusterDetailsRue,
        'detailkpiinseeCode' => $detailkpiinseeCode,
        'controller_name' => 'Ventes Dashboard',

        'user' => $userData,
        'showMainBloc' => false,
        'showClusters' => false,
        'code_cluster' => $code_cluster,
        'showVentes' => false,
        'showPrisesLivraison' => false,
        'showVentesRsuro' => false,
        'showClusterDetailsRueVoie' => false,
        'showClusterDetails' => false,
        'showClusterDetailsRue' => true,
        'showClusterDetailsDataliste' => false,
        'showprisesArretcudetailleRue'=>false,
        'showprisesArretcutest'=>false,
        'prisesArretcuCluster'=>false,
        'priseslivraisonmap'=> false,
        'Clusterstotals' => $InterventionClustersTotals ?? [],
        'params' => $params,
        'showVenteSet' => false,
    ]);
}

    
    

#[Route('/clusters/details/rue/{code_cluster}/{detailkpiinseeCode}/{nom_voie}', name: 'clusterdetailsruevoie')]
public function clusterDetailsRueVoie(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster, string $detailkpiinseeCode, string $nom_voie, Request $request): Response
{
    $jwt = $session->get('jwt');
    if (!$jwt) {
        return $this->redirectToRoute('app_login');
    }

    $userData = [];
    $dataReceived = [];

    try {
        // Fetch user-connected data
        $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ],
        ]);

        if ($response1->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user data.');
            return $this->redirectToRoute('app_dashboard');
        }

        $dataUser = json_decode($response1->getContent(), true);
        $userId = $dataUser['user_id'] ?? null;

        if (!$userId) {
            $this->addFlash('error', 'User ID not found in the response.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Fetch user details
        $response2 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userId}", [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ],
        ]);

        if ($response2->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user details.');
            return $this->redirectToRoute('app_dashboard');
        }

        $userData = json_decode($response2->getContent(), true);
        $cpv = $dataUser['cpv'] ?? null;

        if (!$cpv) {
            $this->addFlash('error', 'CPV not found in the user data.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Fetch cluster details for the specified street
        $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/interventions-places/streets/{$cpv}/{$detailkpiinseeCode}/{$nom_voie}?page=1", [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ],
        ]);
       
        if ($response->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch cluster details.');
            return $this->redirectToRoute('app_dashboard');
        }

        $dataReceived = json_decode($response->getContent(), true);
      
        // Initialize button states from query parameters
        $params = [
            'adsl' => $request->query->get('adsl', 'no'),
            'thd' => $request->query->get('thd', 'no'),
            'mobMono' => $request->query->get('mobMono', 'no'),
            'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
            'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
            'page' => $request->query->get('page', 1)
        ];

        // Fix URL string formatting (correcting string interpolation)
        $url = sprintf(
            'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?codeCluster=%s&codeInsee=%s&adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
            $cpv,
            $code_cluster,
            $detailkpiinseeCode,
            $params['adsl'],
            $params['thd'],
            $params['mobMono'],
            $params['mobMultiThd'],
            $params['mobMultiAdsl'],
            $params['page']
        );

        $InterventionClustersTotals = [];

        // Fetch total intervention clusters data
        $response2 = $httpClient->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response2->getStatusCode() === 200) {
            $InterventionClustersTotals = json_decode($response2->getContent(), true);
            // Log the received data
          
        }

    } catch (\Exception $e) {
        $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
    }

    // Render the response with the required variables
    return $this->render('dashboard/layout.html.twig', [
        'clusterDetailsRueNomVoie' => $dataReceived,
        'detailkpiinseeCode' => $detailkpiinseeCode,
        'controller_name' => 'Ventes Dashboard',

        'user' => $userData,
        'showMainBloc' => false,
        'showClusters' => false,
        'code_cluster' => $code_cluster,
        'showVentes' => false,
        'showPrisesLivraison' => false,
        'showVentesRsuro' => false,
        'showClusterDetails' => false,
        'showClusterDetailsRue' => false,
        'showClusterDetailsDataliste' => false,
        'showClusterDetailsRueVoie' => true,
        'showprisesArretcudetailleRue'=>false,
        'showprisesArretcutest'=>false,
        'prisesArretcuCluster'=>false,
        'priseslivraisonmap'=> false,
        'Clusterstotals' => $InterventionClustersTotals ?? [],
        'params' => $params,
        'showVenteSet' => false,
    ]);
}

    
    
    #[Route('/clusters/detailstable/{code_cluster}', name: 'detail_cluster_datalist')]
    public function clusterDetailsTable(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

         $userData = [];
         try {
             $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                 'headers' => [
                     'Authorization' => 'Bearer ' . $jwt,
                 ]
             ]);
             $userData = json_decode($response->getContent(), true);
         } catch (\Exception $e) {
             $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
         }

        $clusterDetails = [];
        try {
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/clusters-details/1058766/' . $code_cluster . '?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $clusterDetails = json_decode($response->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster details: ' . $e->getMessage());
        }

        return $this->render('dashboard/layout.html.twig', [
            'clusterDetails' => $clusterDetails,
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'code_cluster' => $code_cluster,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => true,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => false,
        ]);
    }

    #[Route('/ventes/rsuro', name: 'rsuro_livraison')]
    public function ventesrsuro(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $productionskpi = [];
        $categories = [];
    
        try {
            // Fetch user-connected data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $dataUser = json_decode($response1->getContent(), true);
            $userId = $dataUser['user_id'] ?? null;
    
            if (!$userId) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response2 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userId}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            if ($response2->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response2->getContent(), true);
            $point_of_sale_id = $dataUser['point_of_sale_id'] ?? null;
    
            $startDate = $request->query->get('startDate', '01-10-2024');
            $endDate = $request->query->get('endDate', '31-10-2024');
            $clusterId = $request->query->get('clusterId', null);
            $categoryId = $request->query->get('categoryId', '1');
    
            // Fetch production KPI data
            $url = sprintf(
                'http://api.nomadcloud.fr/api/productions-kpi/%s?debut=%s&fin=%s&page=1',
                urlencode($point_of_sale_id),
                urlencode($startDate),
                urlencode($endDate)
            );
    
            if ($clusterId) {
                $url .= '&clusterId=' . urlencode($clusterId);
            }
            $url .= '&categoryId=' . urlencode($categoryId);
    
            $response = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            $productionskpi = json_decode($response->getContent(), true);
            
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching production data: ' . $e->getMessage());
        }
    
        try {
            // Fetch categories
            $url = 'https://api.nomadcloud.fr/api/categories_products?page=1';
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            $categories = json_decode($response2->getContent(), true);
    
            if (!isset($categories['hydra:member'])) {
                $this->addFlash('error', 'No categories found.');
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching categories data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',
           
            'user' => $userData,
            'productionskpi' => $productionskpi,
            'categories' => $categories['hydra:member'] ?? [],
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => true,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => false,
            'clusterId' => $clusterId,
        ]);
    }
    

 #[Route('/ventes/sat', name: 'ventesrsurosat')]
    public function ventesrsuroprises(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $productionskpi = [];
        $categories = [];
    
        try {
            // Fetch user-connected data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $dataUser = json_decode($response1->getContent(), true);
            $userId = $dataUser['user_id'] ?? null;
    
            if (!$userId) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response2 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userId}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            if ($response2->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response2->getContent(), true);
            $point_of_sale_id = $dataUser['point_of_sale_id'] ?? null;
    
            $startDate = $request->query->get('startDate', '01-10-2024');
            $endDate = $request->query->get('endDate', '31-10-2024');
            $clusterId = $request->query->get('clusterId', null);
            $categoryId = $request->query->get('categoryId', '1');
    
            // Fetch production KPI data
            $url = sprintf(
                'https://api.nomadcloud.fr/api/satisfaction-clients-averages/3?page=1',
                // urlencode($point_of_sale_id),
                // urlencode($startDate),
                // urlencode($endDate)
            );
    
            if ($clusterId) {
                $url .= '&clusterId=' . urlencode($clusterId);
            }
            $url .= '&categoryId=' . urlencode($categoryId);

            $response = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            $productionskpis = json_decode($response->getContent(), true);
          
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching production data: ' . $e->getMessage());
        }
    
        try {
            // Fetch categories
            $url = 'https://api.nomadcloud.fr/api/categories_products?page=1';
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            $categories = json_decode($response2->getContent(), true);
    
            if (!isset($categories['hydra:member'])) {
                $this->addFlash('error', 'No categories found.');
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching categories data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'productionskpis' => $productionskpis,
            'categories' => $categories['hydra:member'] ?? [],
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => true,
        ]);
    }
    

    #[Route('/prises/arretcu', name: 'prisesArretcu')]
    public function prisesArretcu(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            // Récupération des données utilisateur
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Récupération des détails utilisateur
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            // Récupération des clusters totaux
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
          // Récupération des détails utilisateur
          $response8 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/interventions-places-arrets-cuivre/{$cpv}", [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response8->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user details.');
            return $this->redirectToRoute('app_dashboard');
        }
        $priseData = json_decode($response8->getContent(), true);
        if (!is_array($priseData)) {
            $this->addFlash('error', 'Invalid data format.');
            return $this->redirectToRoute('app_dashboard');
        }
        
        // Créer des tableaux pour les mois et années uniques
        $months = [];
        $years = [];
        
        foreach ($priseData as $date) {
            // Vérifiez que la date est bien une chaîne au format "DD-MM-YYYY"
            $dateParts = explode('-', $date); // On suppose que la date est au format "DD-MM-YYYY"
            
            if (count($dateParts) === 3) {
                $month = (int)$dateParts[1]; // Mois
                $year = (int)$dateParts[2]; // Année
                
                // Ajouter le mois et l'année dans les tableaux, en évitant les doublons
                if (!in_array($month, $months)) {
                    $months[] = $month;
                }
                if (!in_array($year, $years)) {
                    $years[] = $year;
                }
            }
        }
        
        // Trier les mois et les années pour un affichage ordonné
        sort($months);
        sort($years);
  
            // Initialiser les états des boutons à partir des paramètres de la requête
            $params = [
                'adsl' => $request->query->get('adsl', 'no'),
                'thd' => $request->query->get('thd', 'no'),
                'mobMono' => $request->query->get('mobMono', 'no'),
                'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
                'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
                'page' => $request->query->get('page', 1)
            ];
    
            // Correction de l'URL avec l'interpolation correcte
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                $cpv,
                $params['adsl'],
                $params['thd'],
                $params['mobMono'],
                $params['mobMultiThd'],
                $params['mobMultiAdsl'],
                $params['page']
            );
    
            $InterventionClustersTotals = [];
    
            $response4 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            
            
            if ($response4->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response4->getContent(), true);
                
             
            } else {
                $this->addFlash('error', 'Failed to fetch intervention clusters.');
            }
           
    
            // Récupérer les valeurs de mois et d'année depuis la requête GET
            $moisArretCuivre = $request->query->get('moisArretCuivre', 1);  // Mois par défaut : 1 (Janvier)
            $anneeArretCuivre = $request->query->get('anneeArretCuivre', 2025);  // Année par défaut : 2025
        // Valider que les valeurs sont présentes
        if (!$moisArretCuivre || !$anneeArretCuivre) {
            $this->addFlash('error', 'Le mois et l\'année doivent être sélectionnés.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Vous pouvez maintenant utiliser ces valeurs dans vos appels API ou autres logiques
        $url = sprintf(
            'https://api.nomadcloud.fr/api/interventions-places-kpi-arret-cuivre/%s?moisArretCuivre=%s&anneeArretCuivre=%s&page=1',
            $cpv,
            $moisArretCuivre,
            $anneeArretCuivre
        );

        $response = $httpClient->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response->getStatusCode() === 200) {
            $interventionsplaceskpi = json_decode($response->getContent(), true);
    
        } else {
            $this->addFlash('error', 'Failed to fetch intervention clusters.');
        }
        // $response4 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/city-geolocs-polygons/01", [
        //     'headers' => [
        //         'Authorization' => 'Bearer ' . $jwt,
        //     ]
        // ]);

        // if ($response4->getStatusCode() !== 200) {
        //     $this->addFlash('error', 'Failed to fetch user details.');
        //     return $this->redirectToRoute('app_dashboard');
        // }

        // $polygons = json_decode($response4->getContent(), true);  

        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',
            
            'user' => $userData,
            
            'months' => $months,
            'years' => $years,
            'Clusterstotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'interventionsplaceskpi'=>$interventionsplaceskpi ?? [],
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcutest'=> true,
            'prisesArretcuCluster'=> false,
            'showprisesArretcudetailleRue'=> false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => false,    

        ]);
    }

    #[Route('/prises/arretcu/detaille/{code_cluster}', name: 'prisesArretcuCluster')]
    public function prisesArretcuCluster(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            // Récupération des données utilisateur
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Récupération des détails utilisateur
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            // Récupération des clusters totaux
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard');
            }
              // Récupération des détails utilisateur
              $response8 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/interventions-places-arrets-cuivre/{$cpv}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response8->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
            $priseData = json_decode($response8->getContent(), true);
            if (!is_array($priseData)) {
                $this->addFlash('error', 'Invalid data format.');
                return $this->redirectToRoute('app_dashboard');
            }
            
            // Créer des tableaux pour les mois et années uniques
            $months = [];
            $years = [];
            
            foreach ($priseData as $date) {
                // Vérifiez que la date est bien une chaîne au format "DD-MM-YYYY"
                $dateParts = explode('-', $date); // On suppose que la date est au format "DD-MM-YYYY"
                
                if (count($dateParts) === 3) {
                    $month = (int)$dateParts[1]; // Mois
                    $year = (int)$dateParts[2]; // Année
                    
                    // Ajouter le mois et l'année dans les tableaux, en évitant les doublons
                    if (!in_array($month, $months)) {
                        $months[] = $month;
                    }
                    if (!in_array($year, $years)) {
                        $years[] = $year;
                    }
                }
            }
            
            // Trier les mois et les années pour un affichage ordonné
            sort($months);
            sort($years);
      
            
        
            // Initialiser les états des boutons à partir des paramètres de la requête
            $params = [
                'adsl' => $request->query->get('adsl', 'no'),
                'thd' => $request->query->get('thd', 'no'),
                'mobMono' => $request->query->get('mobMono', 'no'),
                'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
                'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
                'page' => $request->query->get('page', 1)
            ];
    
            // Correction de l'URL avec l'interpolation correcte
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                $cpv,
                $params['adsl'],
                $params['thd'],
                $params['mobMono'],
                $params['mobMultiThd'],
                $params['mobMultiAdsl'],
                $params['page']
            );
    
            $InterventionClustersTotals = [];
    
            $response4 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            
            
            if ($response4->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response4->getContent(), true);
                
             
            } else {
                $this->addFlash('error', 'Failed to fetch intervention clusters.');
            }
           
    
            // Récupérer les valeurs de mois et d'année depuis la requête GET
            $moisArretCuivre = $request->query->get('moisArretCuivre',1);  // Mois par défaut : 1 (Janvier)
            $anneeArretCuivre = $request->query->get('anneeArretCuivre',2025);  // Année par défaut : 2025
        // Valider que les valeurs sont présentes
        if (!$moisArretCuivre || !$anneeArretCuivre) {
            $this->addFlash('error', 'Le mois et l\'année doivent être sélectionnés.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Vous pouvez maintenant utiliser ces valeurs dans vos appels API ou autres logiques
        $url = sprintf(
            'https://api.nomadcloud.fr/api/interventions-places-kpi-arret-cuivre/%s?codeCluster=%s&moisArretCuivre=%s&anneeArretCuivre=%s&page=1',
            $cpv,
            $code_cluster,
            $moisArretCuivre,
            $anneeArretCuivre

        );

        $response = $httpClient->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response->getStatusCode() === 200) {
            $interventionsplaceskpi = json_decode($response->getContent(), true);
           
        } else {
            $this->addFlash('error', 'Failed to fetch intervention clusters.');
        }
            
        $response4 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/city-geolocs-polygons/01", [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response4->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user details.');
            return $this->redirectToRoute('app_dashboard');
        }

        $polygons = json_decode($response4->getContent(), true);  
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'months' => $months,
            'years' => $years,
            'polygons' => $polygons,
            'Clusterstotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'interventionsplaceskpi'=>$interventionsplaceskpi ?? [],
            'code_cluster' => $code_cluster,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'prisesArretcuCluster'=> true,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'priseslivraisonmap'=> false,
                'showVenteSet' => false,
        ]);
    }
    #[Route('/prises/arretcu/detaille/rue/{code_cluster}/{detailkpiinseeCode}', name: 'prisesArretcuClusterRue')]
    public function prisesArretcuClusterRue(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster,string $detailkpiinseeCode, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            // Récupération des données utilisateur
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Récupération des détails utilisateur
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            // Récupération des clusters totaux
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
                // Récupération des détails utilisateur
                $response8 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/interventions-places-arrets-cuivre/{$cpv}", [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $jwt,
                    ]
                ]);
        
                if ($response8->getStatusCode() !== 200) {
                    $this->addFlash('error', 'Failed to fetch user details.');
                    return $this->redirectToRoute('app_dashboard');
                }
                $priseData = json_decode($response8->getContent(), true);
                if (!is_array($priseData)) {
                    $this->addFlash('error', 'Invalid data format.');
                    return $this->redirectToRoute('app_dashboard');
                }
                
                // Créer des tableaux pour les mois et années uniques
                $months = [];
                $years = [];
                
                foreach ($priseData as $date) {
                    // Vérifiez que la date est bien une chaîne au format "DD-MM-YYYY"
                    $dateParts = explode('-', $date); // On suppose que la date est au format "DD-MM-YYYY"
                    
                    if (count($dateParts) === 3) {
                        $month = (int)$dateParts[1]; // Mois
                        $year = (int)$dateParts[2]; // Année
                        
                        // Ajouter le mois et l'année dans les tableaux, en évitant les doublons
                        if (!in_array($month, $months)) {
                            $months[] = $month;
                        }
                        if (!in_array($year, $years)) {
                            $years[] = $year;
                        }
                    }
                }
                
                // Trier les mois et les années pour un affichage ordonné
                sort($months);
                sort($years);
            
        
            // Initialiser les états des boutons à partir des paramètres de la requête
            $params = [
                'adsl' => $request->query->get('adsl', 'no'),
                'thd' => $request->query->get('thd', 'no'),
                'mobMono' => $request->query->get('mobMono', 'no'),
                'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
                'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
                'page' => $request->query->get('page', 1)
            ];
    
            // Correction de l'URL avec l'interpolation correcte
            // Correction de l'URL avec l'interpolation correcte
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                $cpv,
                $params['adsl'],
                $params['thd'],
                $params['mobMono'],
                $params['mobMultiThd'],
                $params['mobMultiAdsl'],
                $params['page']
            );
            $InterventionClustersTotals = [];
    
            $response4 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            
            
            if ($response4->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response4->getContent(), true);
                
             
            } else {
                $this->addFlash('error', 'Failed to fetch intervention clusters.');
            }
           
    
            // Récupérer les valeurs de mois et d'année depuis la requête GET
            $moisArretCuivre = $request->query->get('moisArretCuivre',1);  // Mois par défaut : 1 (Janvier)
            $anneeArretCuivre = $request->query->get('anneeArretCuivre',2025);  // Année par défaut : 2025
        // Valider que les valeurs sont présentes
        if (!$moisArretCuivre || !$anneeArretCuivre) {
            $this->addFlash('error', 'Le mois et l\'année doivent être sélectionnés.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Vous pouvez maintenant utiliser ces valeurs dans vos appels API ou autres logiques
        $url = sprintf(
            'https://api.nomadcloud.fr/api/interventions-places-kpi-arret-cuivre/%s?codeCluster=%s&codeInsee=%s&moisArretCuivre=%s&anneeArretCuivre=%s&page=1',
            $cpv,
            $code_cluster,
            $detailkpiinseeCode,
            $moisArretCuivre,
            $anneeArretCuivre

        );

        $response = $httpClient->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response->getStatusCode() === 200) {
            $interventionsplaceskpi = json_decode($response->getContent(), true);
     
        } else {
            $this->addFlash('error', 'Failed to fetch intervention clusters.');
        }
            

        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
        }
    
        return $this->render('dashboard/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'months' => $months,
            'years' => $years,
            'detailkpiinseeCode' => $detailkpiinseeCode,
            'Clusterstotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'interventionsplaceskpi'=>$interventionsplaceskpi ?? [],
            'code_cluster' => $code_cluster,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> true,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> false,
            'showVenteSet' => false,
        ]);
    }

    #[Route('/prises/livraison/map', name: 'priseslivraisonmap')]
    public function priseslivraisonmap(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
        try {
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
         
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard');
            }

            $response4 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/city-geolocs-polygons/01", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response4->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $polygons = json_decode($response4->getContent(), true);
      // Default values for parameters
      $codeCluster = $request->query->get('codeCluster', '07-01');
            
      // Validate codeCluster format
      if (!preg_match('/^\d{2}-\d{2}$/', $codeCluster)) {
          $this->addFlash('error', 'Invalid code cluster.');
          return $this->redirectToRoute('neuves');
      }

      // Fetch clusters data
      $response3 = $httpClient->request('GET', "http://api.nomadcloud.fr/api/clusters/{$cpv}?page=1", [
          'headers' => ['Authorization' => 'Bearer ' . $jwt]
      ]);

      if ($response3->getStatusCode() === 200) {
          $clusters = json_decode($response3->getContent(), true);
      } else {
          $this->addFlash('error', 'Failed to fetch clusters data.');
      }

      // Fetch INSEE data
      $url = sprintf('http://api.nomadcloud.fr/api/clusters/%s?codeCluster=%s', $cpv, $codeCluster);
      $response4 = $httpClient->request('GET', $url, [
          'headers' => ['Authorization' => 'Bearer ' . $jwt]
      ]);
      
      if ($response4->getStatusCode() === 200) {
          $insee = json_decode($response4->getContent(), true);
      }

    
      if (!empty($insee['codes_insee'])) {
          $selectedInseeId = $request->query->get('codeInsee', $insee['codes_insee'][0]['code_insee']);
      } else {
          // Handle case where 'codes_insee' is empty or not available
          $this->addFlash('error', 'No INSEE codes found.');
          $selectedInseeId = '07011'; // Fallback value or redirect as needed
      }

      // Pagination and fetching coordinates
      $page = max(1, (int) $request->query->get('page', 1)); // Always at least page 1
      $url = sprintf(
          'http://api.nomadcloud.fr/api/interventions-places/coordinates/%s?codeInsee=%s&page=%d',
          $codeCluster,
          $selectedInseeId,
          $page
      );
      $response2 = $httpClient->request('GET', $url, [
          'headers' => ['Authorization' => 'Bearer ' . $jwt]
      ]);

      if ($response2->getStatusCode() === 200) {
          $coordinates = json_decode($response2->getContent(), true);
      } else {
          $this->addFlash('error', 'Failed to fetch coordinates data.');
      }
      
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster data: ' . $e->getMessage());
        }
        try {
            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/interventions-places-hierarchy/1058766?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
        
            // Check for successful status code
            if ($response3->getStatusCode() === 200) {
                $HierarchyData = $response3->getContent();
                $HierarchyData = json_encode($HierarchyData);
                // Use $data as needed
                //dd($HierarchyData);
            } else {
                $this->addFlash('error', 'Failed to fetch data. Status: ' . $response3->getStatusCode());
                // Log more details for debugging
                error_log('Response: ' . $response3->getContent(false));
            }
        } catch (\Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface $e) {
            $this->addFlash('error', 'Network error: ' . $e->getMessage());
            // Log full error
            error_log('Network error: ' . $e->getMessage());
        } catch (\Throwable $e) {
            // Catch all other possible errors
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }
        
    
        return $this->render('dashMap/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',

            'user' => $userData,
            'polygons' => $polygons,
            'coordinates' => json_encode($coordinates['results'] ?? []),
            'clusters' => $clusters ?? [],
            'selectedClusterId' => $codeCluster,
            'HierarchyData'=>$HierarchyData,
            'selectedInseeId' => $selectedInseeId,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'showVentesRsuro' => false,
            'showClusterDetails' => false,
            'showClusterDetailsDataliste' => false,
            'showClusterDetailsRue' => false,
            'showClusterDetailsRueVoie' => false,
            'showprisesArretcudetailleRue'=> false,
            'showprisesArretcutest'=> false,
            'prisesArretcuCluster'=> false,
            'priseslivraisonmap'=> true,
            'showVenteSet' => false,
        ]);
    }
}
