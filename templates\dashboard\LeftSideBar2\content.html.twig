<style>

:root {
    --activebutton-color-text: #fff ;
    --bgcolor-active-button : #bfbfbf; 
}

[data-theme="dark"] {
  
    --bgcolor-active-button : #0f181f; 
    --activebutton-color-text: #4b96c1 ;
}

    .channel-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    .channel-item a {
        text-decoration: none;
        color: gray;
    }

    .channel-item a:hover,
    .channel-item a.active {
        color: darkgray;
    }

    .sidebar-link {
        font-size: 32px;
        width: 250px;
        display: block;
        padding: 8px; 
        padding-left: 24px;
        border-radius: 10px;
        transition: background-color 0.3s;
        color: #fff;
        text-decoration: none;
    }

    .sidebar-link:hover {
        background-color: var(--bgcolor-active-button); 
    }

    .sidebar-link.active {
        background-color: var(--bgcolor-active-button); 
        color: var(--activebutton-color-text);
    }

    .sectionDivider {
        color: #222;
    }

    .sidebar-header {
        text-align: center;
        margin-bottom: 10px;
    }

    .toggle-section-sideb2 {
        margin: 0; /* Remove or reduce margin to reduce space between items */
    }

    .toggle-section-sideb2 a {
        text-decoration: none;
    }

    .sidebar-section-titleactive {
        color: var(--activebutton-color-text);
        font-size: 14.2px;
        text-transform: uppercase;
        margin: 2px 0; 
    }
    .sidebar-section-title{
          color: var(--section-title-color);
        font-size: 14.2px;
        text-transform: uppercase;
        margin: 1px 0; 
    }
</style>


<div class="sidebar-header" id="eventTrigger">
    <i class="bi bi-calendar-event" style="color: transparent"></i>
</div>

<div class="sectionDivider"></div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="#" class="sidebar-link active">
            <p class="sidebar-section-titleactive">Ventes</p>
        </a>
    </div>
</div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="#" class="sidebar-link">
            <p class="sidebar-section-title">Déclaratif</p>
        </a>
    </div>
</div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="#" class="sidebar-link">
            <p class="sidebar-section-title">Suivi R/O</p>
        </a>
    </div>
</div>

<script>
    const modal = document.getElementById("eventModal");
    const trigger = document.getElementById("eventTrigger");
    const closeBtn = document.querySelector(".close-btn");

    trigger.addEventListener("click", function () {
        modal.style.display = "block";
    });

    closeBtn.addEventListener("click", function () {
        modal.style.display = "none";
    });

    window.addEventListener("click", function (event) {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    });
</script>
