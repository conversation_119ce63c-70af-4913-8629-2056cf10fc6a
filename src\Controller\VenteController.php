<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class VenteController extends AbstractController
{
    #[Route('/vente/vente', name: 'vente')]

    public function index(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        // Get JWT token from the session
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }


        try {
            // Fetch user data
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
     



        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('vente/layout.html.twig', [
            'controller_name' => 'VenteController',
            'user' => $userData,
            'showMainBloc' => true,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'declaratif' => false,
            'declaratifcard' => false,
            'ventemap'=>false,
        ]);
    }
    #[Route('/ventes/declaratif', name: 'declaratif')]
    public function declaratif(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        // Retrieve JWT token from the session
        $jwt = $session->get('jwt');

        // If JWT token is not present, redirect to login page
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        $userData = [];
        $declaratifData = [];

        try {
            // Fetch user data using the API and the JWT token
            $userResponse = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            // Check if user response is successful (HTTP status code 200)
            if ($userResponse->getStatusCode() === 200) {
                $userData = json_decode($userResponse->getContent(), true);
            } else {
                $this->addFlash('error', 'Error fetching user data');
            }

            // Fetch declaratif data
            $declaratifResponse = $httpClient->request('GET', 'http://api.nomadcloud.fr/api/declaratifs/tree/all?etat=3&page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            // Check if declaratif response is successful (HTTP status code 200)
            if ($declaratifResponse->getStatusCode() === 200) {
                $declaratifData = json_decode($declaratifResponse->getContent(), true);
            } else {
                $this->addFlash('error', 'Error fetching declaratif data');
            }
        } catch (\Exception $e) {

            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }

        return $this->render('vente/layout.html.twig', [
            'controller_name' => 'VenteController',
            'user' => $userData,
            'declaratifData' => $declaratifData,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'declaratif' => true,
            'declaratifcard' => false,
            'ventemap'=>false,
        ]);
    }
    #[Route('/ventes/declaratif/card', name: 'declaratifCard', methods: ['GET'])]
    public function declaratifcard(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        $userData = [];
        $declaratifData = [];
        $declaratifDatas = [];
        try {
            // Fetch user data
            $userResponse = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            if ($userResponse->getStatusCode() === 200) {
                $userData = $userResponse->toArray();
            } else {
                $this->addFlash('error', 'User data could not be retrieved.');
            }

            // Fetch declaratif data
            $url = 'http://api.nomadcloud.fr/api/declaratifs/users/5/tree/all?etat=2&page=1';
            $declaratifResponse = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            if ($declaratifResponse->getStatusCode() === 200) {
                $declaratifData = $declaratifResponse->toArray();
                foreach ($declaratifData['hydra:member'] as $declaratif) {
            
                }
            } else {
                $this->addFlash('error', 'Erreur lors de la récupération des données declaratifs : ' . $declaratifResponse->getStatusCode());
            }

            $url = 'http://api.nomadcloud.fr/api/declaratifs/users/30/tree/first?etat=3&page=2';
            $declaratifResponses = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            
            if ($declaratifResponses->getStatusCode() === 200) {
                $declaratifDatas = $declaratifResponses->toArray();
            
                  // Log the data to the PHP error log using error_log()

            } else {
                $this->addFlash('error', 'Erreur lors de la récupération des données declaratifs : ' . $declaratifResponses->getStatusCode());
            }
            
        } catch (\Exception $e) {
            $this->addFlash('error', 'Erreur lors de la récupération des données : ' . $e->getMessage());
        }

        return $this->render('vente/layout.html.twig', [
            'controller_name' => 'VenteController',
            'user' => $userData,
            'declaratifData' => $declaratifData,
            'declaratifDatas' => $declaratifDatas,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'declaratif' => false,
            'declaratifcard' => true,
            'ventemap'=>false,
        ]);
    }








    #[Route('/ventes/vente/map', name: 'ventemap')]
    public function map(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
        $userData = [];
        $productions = [];

        try {
            $userResponse = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            if ($userResponse->getStatusCode() === 200) {
                $userData = $userResponse->toArray();
            } else {
                $this->addFlash('error', 'User data could not be retrieved.');
            }
            $response3 = $httpClient->request('GET', 'http://api.nomadcloud.fr/api/productions/coordinates/05-02?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $productionData = json_decode($response3->getContent(), true);
     
            if (isset($productionData)) {
                $productions = $productionData;
            }
        } catch (\Exception $e) {

            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }

        return $this->render('vente/layout.html.twig', [
            'controller_name' => 'VenteController',
            'user' => $userData,
            'productions' => json_encode($productions),
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showPrisesLivraison' => false,
            'declaratif' => false,
            'declaratifcard' => false,
            'ventemap'=>true,
        ]);
    }
}
