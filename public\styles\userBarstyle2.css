.user-profile {
    display: flex;
    align-items: center;
    width: 80px;
    height: 39px;
    cursor: pointer;
  }
  
  .user-profile:hover {
    background-color: var(--hover-bg);
    border-radius: 3px;
  }
  
  .avatar-wrapper {
    position: relative;
    display: inline-block;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }
  
  .status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background-color: #43b581;
    border-radius: 50%;
    border: 2px solid var(--background-secondary);
  }
  
  .user-details {
    display: flex;
    flex-direction: column;
    margin-left: 8px;
  }
  
  .user-name {
    font-size: 14px;
    color: var(--text-normal);
    font-weight: 500;
  }
  
  .user-status {
    font-size: 12px;
    color: var(--text-muted);
  }
  
  .controls-section {
    display: flex;
    align-items: center;
    margin-right: 20%
  }
  
  .icon-wrapper {
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    cursor: pointer;
  }
  
  .icon-wrapper:hover {
    background-color: var(--background-modifier-hover);
    border-radius: 50%;
  }