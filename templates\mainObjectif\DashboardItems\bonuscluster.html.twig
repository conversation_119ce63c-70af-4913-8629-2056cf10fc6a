<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Bonus by Cluster</title>
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
		<style>
			.bonus-cluster {
				border: 1px solid #e0e0e0;
				border-radius: 15px;
				background-color: #e7f0ef;
				display: flex;
				width: 48%;
				position: relative;
			}
			.expand-icon {
				position: absolute;
				top: 8px;
				right: 8px;
				font-size: 0.1rem;
				color: #888;
				cursor: pointer;
			}
			.container {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
			}
			.chart-container {
				width: 40%;
				height: 200px;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.chart-containers {
				width: 100%;
				height: 100px;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.tables-container {
				width: 49%;
				margin-left: 10px;
				display: flex;
				flex-direction: column;
				gap: 8px;
			}
			.sales-table,
			.sales-table th,
			.sales-table td {
				border: none !important;
				background-color: transparent;
			}
			.sales-table th {
				color: #000;
				background-color: transparent;
				font-size: 0.8em;
				padding: 1px 11px;
				text-align: left;
			}
			.sales-table td {
				color: #72ac9d;
				padding: 1px 11px;
				font-size: 0.8em;
				text-align: left;
			}
			.sales-table thead {
				font-weight: bold;
				border-bottom: 2px solid transparent;
			}
			.sales-table tbody tr:nth-child(even) {
				background-color: transparent;
			}
			.sales-table .negative-sales {
				color: red;
			}
			.sales-table-bad {
				background-color: #ceece7;
				color: black;
				padding: 15px;
				border-radius: 15px;
				border-collapse: collapse;
				margin-top: 10px;
				text-align: left;
				border: none;
			}
			.sales-table-bad td {
				padding: 1px 11px;
				font-size: 0.8em;
				color: red;
				text-align: left;
				border: none;
			}
			.text-overlay {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				text-align: center;
				color: #000;
				z-index: 10;
			}
			#global-text {
				font-size: 1.1em;
				color: #333;
				text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
				margin: 0;
				line-height: 1.2;
			}
			#total-sales {
				font-size: 1.5em;
				font-weight: bold;
				color: #333;
				margin: 0;
				line-height: 1.2;
			}
			#sales-info {
				font-size: 0.6em;
				color: #87a094;
				margin: 0;
				font-weight: bold;
				line-height: 1.2;
			}
			#sales-bad {
				font-size: 0.6em;
				color: red;
				margin: 0;
				font-weight: bold;
				line-height: 1.2;
			}
			.sales-table th,
			.sales-table td {
				text-align: left;
			}


			.cards1 {
				border: 1px solid #ccc; /* Adjust the border color as needed */
				border-radius: 15px; /* Set the border radius */
				padding: 8px; /* Add some padding for better spacing */
				background-color: #47c2ae; /* Set the background color */
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Optional: add shadow for depth */
				width: 130px;
				height: 75px;
				line-height: 0.5;

			}
			.cards2 {
				border: 2px solid #ccc; /* Adjust the border color as needed */
				border-radius: 15px; /* Set the border radius */
				padding: 13px; /* Add some padding for better spacing */
				background-color: #e7f0ef; /* Set the background color */
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Optional: add shadow for depth */
				width: 130px;
				height: 60px;
				line-height: 0.8;
				border-color: #72ac9d;

			}

			.card-titles {
				/* Optional: make the title bold */
				margin-bottom: 10px; /* Add space below the title */
				margin-left: 26px;
			}

			.bonus-amounts {
				font-weight: bold;
				font-size: 2.0em; /* Adjust the size as needed */
				margin-bottom: 10px; /* Space below the amount */
				margin-left: 10px;
			}

			.card-dates {
				/* Adjust the color of the date as needed */
				margin-bottom: 20px; /* Space below the date */
				margin-left: 26px;
			}

			.cardes {
				border-top: 1px solid #ccc; /* Optional: add a top border to the inner card */
				padding-top: 10px; /* Space above the inner card content */
			}

			.bonus-amountss {
				font-weight: bold;
				font-size: 1.5em; /* Adjust the font size */
				/* Space below the amount */
				margin-left: 15px;
			}
      

			.card-datess {
				color: red;
				margin-left: 30px;
			}

			.expand-icon {
				position: absolute;
				top: 8px;
				right: 8px;
				font-size: 0.1rem;
				color: #888;
				cursor: pointer;
			}
		</style>
	</head>
	<body>

		<h3>Bonus par Cluster</h3>

		<div class="container">
			<div class="col-4 ">
				<div class="cards1">
					<div class="card-titles">BONUS</div>
					<div class="bonus-amounts" style="color: #fff;">7600€</div>
					<p class="card-dates">Octobre</p>


				</div>
				<div class="cards2 mt-5">
					<div class="bonus-amountss">2497.8</div>
					<div class="card-datess">a venir</div>
				</div>
			</div>
			<div
				class="tables-container">

				<!-- Canvas for Chart.js -->
				<div class="chart-containers">
					<canvas id="globalSalesCharts" style="width: 100%; height: 100px;!important"></canvas>
				</div>


<script>
    // Replace Feather icons (if used)
    feather.replace();

    // Get the canvas context
    var ctx = document.getElementById('globalSalesCharts').getContext('2d');

    // Create the bar chart
    var globalSalesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: [
                ' ',
                ' ',
                ' ',
                ' ',
                ' ',
                ' ',
            ], // Monthly labels
            datasets: [
                {
                    label: 'Sales', // Optional label for dataset
                    data: [
                        12,
                        19,
                        8,
                        15,
                        10,
                        20,
                    ], // Example data
                    backgroundColor: '#47c2ae', // Set bar color
                    borderWidth: 0,
                }
            ]
        },
        options: {
            responsive: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        display: false // Set to true to display y-axis ticks
                    },
                    grid: {
                        display: false // Hide grid lines
                    }
                },
                x: {
                    ticks: {
                        display: true // Set to true to display x-axis ticks
                    },
                    grid: {
                        display: false // Hide grid lines
                    }
                }
            },
            plugins: {
                legend: {
                    display: false // Hide legend
                },
                // Ensure smilePlugin is properly defined if used
                smilePlugin: {
                   display: false
                }
            }
        }
    });
</script>

				<table class="table sales-table" style="background-color: #fff;border-radius: 20px;">
					<thead>
						<tr>
							<th>Ville</th>
							<th>Prises</th>
							<th>Ventes</th>
							<th>Bonus</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>Lille</td>
							<td>13</td>
							<td>13</td>
							<td>32%</td>
						</tr>
						<tr>
							<td>Amiens</td>
							<td>12</td>
							<td>12</td>
							<td>9.79%</td>
						</tr>
						<tr>
							<td>Arras</td>
							<td>14</td>
							<td>14</td>
							<td>522€</td>
						</tr>
						<tr>
							<td>Toulouse</td>
							<td>9</td>
							<td>9</td>
							<td>752€</td>
						</tr>
						<tr>
							<td>Rouen</td>
							<td>2</td>
							<td>2</td>
							<td>45€</td>
						</tr>
					</tbody>
				</table>


			</div>
		</div>
		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>
	</body>
</html>
