<style>
:root {
    --link-color: gray;
    --link-hover-color: darkgray;
    --sidebar-link-color: #fff;
    --sidebar-background-color: #cbd1c7;
    --active-link-color: #fff;
    --divider-color: #222;
    --section-title-color: #333;
}

[data-theme="dark"] {
    --link-color: #f0f4f3;
    --link-hover-color: #d0d0d0;
    --sidebar-link-color: #fff;
    --sidebar-background-color: #1E1F22;
    --active-link-color: #4b96c1;
    --divider-color: #ddd;
    --section-title-color: lightgray;
}

.channel-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.channel-item a {
    text-decoration: none;
    color: var(--link-color);
}

.channel-item a:hover,
.channel-item a.active {
    color: var(--link-hover-color);
}

.sidebar-link {
    font-size: 32px;
    width: 250px;
    display: block;
    padding: 8px; 
    padding-left: 24px;
    border-radius: 10px;
    transition: background-color 0.3s;
    color: var(--sidebar-link-color);
    text-decoration: none;
}

.sidebar-link:hover {
    background-color: var(--sidebar-background-color);
    color: var(--sidebar-color);
}

.sidebar-link.active {
    background-color: var(--sidebar-background-color);
    color: var(--active-link-color); 
}

.sectionDivider {
    color: var(--divider-color);
}

.sidebar-header {
    text-align: center;
    margin-bottom: 10px;
    text-decoration: none;
}

.toggle-section-sideb2 {
    margin: 0; /* Remove or reduce margin to reduce space between items */
    text-decoration: none;
}

.toggle-section-sideb2 a {
    text-decoration: none;
}

.sidebar-section-titleactive {
    color: var(--section-title-color); 
    font-size: 14.2px;
    margin: 2px 0; 
    text-decoration: none;
}

.sidebar-section-title {
    color: var(--section-title-color);
    font-size: 14.2px;
    text-decoration: none;
    margin: 1px 0; 
}
</style>



<div class="sidebar-header" id="eventTrigger">
    <i class="bi bi-calendar-event" style="color: transparent"></i>
</div>

<div class="sectionDivider"></div>



<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="{{ path('app_objectifs') }}" class="sidebar-link ">
            <p class="sidebar-section-titleactive">objectif</p>
        </a>
    </div>
</div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="{{ path('penetration') }}" class="sidebar-link active" >
            <p class="sidebar-section-titleactive">taux de penetration</p>
        </a>
    </div>
</div>

<script>
    const modal = document.getElementById("eventModal");
    const trigger = document.getElementById("eventTrigger");
    const closeBtn = document.querySelector(".close-btn");

    trigger.addEventListener("click", function () {
        modal.style.display = "block";
    });

    closeBtn.addEventListener("click", function () {
        modal.style.display = "none";
    });

    window.addEventListener("click", function (event) {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    });
</script>
