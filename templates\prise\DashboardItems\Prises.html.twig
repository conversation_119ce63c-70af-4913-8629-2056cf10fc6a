<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

<style>
	body {
		font-family: 'Ubuntu', }



	.leaflet-container {
		background-color: #f0f4f3;
	}

	/* Container for the background image */
	.background-container {
		position: relative;
		background-image: url('{{ asset('image/image-prises.png') }}');
		background-size: cover;
		background-position: center;
		width: auto; /* Adjust as needed */
		height: 280px; /* Adjust as needed */
		position: relative;

		border-radius: 10px;
		z-index: 1000;
	}

	.dot-graph {
		display: flex;
		align-items: center;
		padding: 10px;
		background: linear-gradient(to right, #6e67ff, #83B8FF);
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		justify-content: space-between;
		border-radius: 0 0 15px 15px;
	}

	.floating-title {
		position: absolute;
		padding: 10px;
		font-size: 1.5em;
		color: #333;
		margin: 0;
		pointer-events: none;
		z-index: 1000;
	}

	.circle {
		width: 90px;
		height: 65px;
		background: linear-gradient(135deg, #fff, #A885F9);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin-left: 2px;
	}

	.circle::before {
		content: '';
		width: 50px;
		height: 50px;
		background-color: white;
		border-radius: 50%;
		position: absolute;
	}

	.circle-text,
	.text-overlay {
		color: #333;
		position: relative;
		font-weight: bold;
		z-index: 1;
		font-size: 12px;
		text-align: center;
     line-height: 1.2;
	}

	.text-overlay {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 10;
	}

	#total-prise,
	#sales-info {
		font-size: 14px;
		font-weight: bold;
		color: #333;
		line-height: 1.2;
	}

	#sales-info {
		font-size: 0.9em;
		color: #BF40BF;
	}

	.dots-container {
		display: flex;
		margin-top: 18px;
		border-radius: 15px;
		margin-left: 15px;


	}

	.dts-dot-container {
		display: flex;
		align-items: flex-end;
		font-size: 11px;
	}

	.dts-dot {
		width: 10.6px;
		height: 10.6px;
		border-radius: 50%;
		margin: 0 10.6px;
	}

	.dts-dot1 {
		background-color: #9f1b08;
	}
	.dts-dot2 {
		background-color: #f6a942;
	}
	.dts-dot3 {
		background-color: #058300;
	}

	.dts-text-top,
	.dts-text-bottom {
		position: absolute;
		font-size: 12.15px;
		font-weight: bold;
		text-align: center;
		width: 100%;
	}

	.dts-text-top {
		top: -21.08px;
	}
	.dts-text-bottom {
		bottom: -21.08px;
	}

	.dts-line {
		position: relative;
		border-top: 1px dashed black;
		width: 53.13px;
		margin-top: -3.52px;
		transition: border-width 0.3s;
	}

body.dark-mode #total-prise{
		color: #f0f4f3;
}
body.dark-mode .circle::before  {
	background-color: #222;

	
}
body.dark-mode .test{
	color: #333;
}

</style>

<h3 class="floating-title">Prises</h3>

<!-- Background Image Container -->
<div class="background-container"></div>

<div class="dot-graph">
	<div class="circle">
		<div class="text-overlay">
			<div id="total-prise">21890</div>
			<div id="sales-info">ce mois</div>
		</div>
	</div>
	<div class="dots-container">
		<div class="dts-dot-container">
			<div class="row">
				<span style="font-size:18px; line-height: 1; color: #fff; font-weight: bold;">6918</span>
				<span class="test">semaine</span>
			</div>
			<div class="row">
				<span style="font-size:18px; line-height: 1;color: #fff; font-weight: bold;">1382</span>
				<span  class="test">Aujourd'hui</span>
			</div>
		</div>
	</div>
</div>

<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<script>
    document.getElementById('chk').addEventListener('change', function() {
        if (this.checked) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
    });
</script>