{# content.html.twig #}
<style>

.checkbox {
    opacity: 0;
    position: absolute;
}

.label {
    background-color: #111;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    position: relative;
    height: 15px;
    width: 35px;
    transform: scale(1.5);
}

.label .ball {
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: -10px;
    height: 12px;
    width: 12px;
    transform: translateX(10px);
    transition: transform 0.2s linear;
}

.checkbox:checked + .label .ball {
    transform: translateX(30px);
}

.fa-moon {
    color: #f1c40f;
}

        .collapsed {
            width: 0 !important;
            overflow: hidden;
            transition: width 0.3s;
        }

  .nav-buttons {
        
        padding: 5px 10px;
        background-color: transparent;
        color: #000;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        margin-left: 30px;
    }
    .nav-button {
        
        padding: 4px 8px;
        background-color: #ad78e1;
        color: #FFF;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        font-weight: bold;
        width: 100px;
        margin-left: 20px;
        
    }
 .dark-mode .nav-buttons{
    color: darkgrey;
 }
  .dark-mode .nav-button{
    color: #000;
 }
</style>
<div class="left-side-topbar">

</div>

<div class="right-side-topbar">



    <div class="switch-container">
        <input type="checkbox" class="checkbox" id="chk" />
        <label class="label" for="chk">
            <div class="ball"></div>
        </label>
    </div>

 
</div>
