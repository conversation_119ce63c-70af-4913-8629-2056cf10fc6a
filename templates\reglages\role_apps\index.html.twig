{% extends 'base.html.twig' %}

{% block body %}
	<style>


		#right-sidebar {
			padding-right: 17%;
		}
	</style>
	<body data-theme="light">
		<div class="container-fluid d-flex" style="margin: 0; padding: 0; min-height: 100vh;">
			<div class="sidebar">
				{% include 'objectif/LeftSideBar/content.html.twig' %}
			</div>
			<div id="leftSidebar" class="left-block col-auto col-md-3 col-xl-2 px-sm-2 px-0">
				<div class="content">
					{% include 'objectif/LeftSideBar2/content.html.twig' %}
				</div>
				<div class="bottombar footer">
					{% include 'objectif/bottombar/content.html.twig' %}
				</div>
			</div>
			<div class="main-content col">
				<div class="right-block col-24">
					<div class="topbar topbar-right-blcok">
						{% include 'objectif/Navbar/content.html.twig' %}
					</div>
					<div class="chat-areablock" id="right-sidebar">
						<div class="table-area">
							{% include 'reglages/role_apps/MainBloc/content.html.twig' %}
						</div>
                     
						<div class="chat-area row">
							<div id="right-sidebar-forms" class="right-sidebar-forms col-auto col-md-3 col-xl-2 px-sm-2 px-0 d-flex flex-column align-items-end" style="height: 100%; position: absolute; right: 0;"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	<script>
		const checkbox = document.getElementById('chk');

function applyTheme(theme) {
if (theme === 'dark') {
document.body.setAttribute('data-theme', 'dark');
localStorage.setItem('theme', 'dark');
} else {
document.body.setAttribute('data-theme', 'light');
localStorage.setItem('theme', 'light');
}
}

checkbox.addEventListener('change', () => {
applyTheme(checkbox.checked ? 'dark' : 'light');
});

const currentTheme = localStorage.getItem('theme') || 'light';
checkbox.checked = currentTheme === 'dark';
applyTheme(currentTheme);
	</script>
{% endblock %}
