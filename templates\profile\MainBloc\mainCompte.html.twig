<!-- Add necessary Bootstrap and icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">


<style>

:root {
    --sidebar-left-right-color: #2B2D31 !important; /* Replace #your_new_color with your desired color value */
    --rightblock-color:#313338!important;
}

body {
    margin: 0;
    font-family: var(--bs-body-font-family);
    font-size: var(--bs-body-font-size);
    font-weight: var(--bs-body-font-weight);
    line-height: var(--bs-body-line-height);
    color: var(--bs-body-color);
    text-align: var(--bs-body-text-align);
    background-color: var(--bs-body-bg);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

	.mains {
		padding: 20px;
		margin-top: 30px;
		max-width: 700px;
		color: #fff;
	}
	.profile-card {
		background: linear-gradient(to top, #000, #1d2a40);
		padding: 20px;
		border-radius: 10px;
	}
	.profile-card h3 {
		font-weight: bold;
		margin-bottom: 20px;
		color: #fff;
	}
	.profile-info {
		text-align: left;
	}
	.profile-info .info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20px;
	}
	.profile-info .info-item button {
		width: 80px;
		font-size: 12px;
		border-radius: 10px;
	}
	.cardpro {
		background: #2B2D31;
		padding: 10px;
		border-radius: 10px;
	}
	.profile-info .info-item span {
		color: gray;
		font-size: 14px;
	}
	.donne {
		color: #00bfff;
		font-size: 14px;
	}
	.bottom-section {
		margin-top: 30px;
	}
	.avatar {
		width: 60px;
		height: 60px;
		background-color: #00bfff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24px;
	}
	.avatar-name {
		display: flex;
		align-items: center;
		margin-top: 10px;
	}
	.bi-person-circle {
		font-size: 24px;
		color: white;
	}
    .styled-input {
    width: 100%;
   
    
    border-radius: 5px;
   background-color: #3d3f46;
    color: #00bfff;
}

</style>
<div class="mains">
    <div style="display: flex; justify-content: space-between;">
        <h5>Mon compte</h5>
        <i class="bi bi-x-circle"></i>
    </div>

    <div class="profile-card">
        <div class="avatar-name">
            <div class="avatar">
                <i class="bi bi-person-circle"></i>
            </div>
            <p style="font-weight: bold; color: #fff; margin-left: 20px;">{{ user.prenom }}</p>
        </div>

        <div class="d-flex justify-content-end mb-2">
            <button style="background-color: #00bfff;" class="btn btn-primary btn-sm">Modifier mon Profil</button>
        </div>

        <div class="profile-info cardpro">
            <form method="post">
                <div class="info-item">
                    <span>
                        <strong>NOM</strong><br>
                        <div class="donne" id="prenomDisplay">{{ user.prenom }}</div>
                        <input type="text" id="prenom" name="prenom" class="styled-input"  value="{{ user.prenom }}" style="display: none;" required>
                    </span>
                    <div>
                        <button class="btn btn-secondary btn-sm mb-2" onclick="toggleEdit(event, 'prenom')">Modifier</button>
                        <button class="btn btn-secondary btn-sm mb-2" id="savePrenomBtn" style="display: none;" onclick="saveChanges(event, 'prenom')">Save</button>
                        <button class="btn btn-secondary btn-sm mb-2" id="cancelPrenomBtn" style="display: none;" onclick="cancelEdit(event, 'prenom')">Annuler</button>
                    </div>
                </div>

                <div class="info-item">
                    <span>
                        <strong>IDENTIFIANT</strong><br>
                        <div class="donne" id="nomDisplay">{{ user.nom }}</div>
                        <input type="text" id="nom" name="nom" value="{{ user.nom }}" class="styled-input"  style="display: none;" required>
                    </span>
                  
                      <div>
                        <button class="btn btn-secondary btn-sm mb-2" onclick="toggleEdit(event, 'nom')">Modifier</button>
                        <button class="btn btn-secondary btn-sm mb-2" id="saveNomBtn" style="display: none;" onclick="saveChanges(event, 'nom')">Save</button>
                        <button class="btn btn-secondary btn-sm mb-2" id="cancelNomBtn" style="display: none;" onclick="cancelEdit(event, 'nom')">Annuler</button>
                    </div>
                </div>

                <div class="info-item">
                    <span>
                        <strong>E-MAIL</strong><br>
                        <div class="donne" id="emailDisplay">{{ user.email }}</div>
                        <input type="email" id="email" name="email"class="styled-input"  value="{{ user.email }}" style="display: none;" required>
                    </span>
                    <div>
                        <button class="btn btn-secondary btn-sm mb-2" onclick="toggleEdit(event, 'email')">Modifier</button>
                        <button class="btn btn-secondary btn-sm mb-2" id="saveEmailBtn" style="display: none;" onclick="saveChanges(event, 'email')">Save</button>
                        <button class="btn btn-secondary btn-sm mb-2" id="cancelEmailBtn" style="display: none;" onclick="cancelEdit(event, 'email')">Annuler</button>
                    </div>
                </div>

                <div class="info-item">
                    <span>
                        <strong>TÉLÉPHONE</strong><br>
                        <div class="donne">Pas encore numéro de téléphone</div>
                    </span>
                    <button class="btn btn-secondary btn-sm mb-2">Ajouter</button>
                </div>
            </form>
        </div>
    </div>

    <div class="bottom-section">
        <div>mot de passe et authentification</div>
        <button style="background-color: #00bfff;" class="btn btn-primary btn-sm">Changer mon mot de passe</button>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    function toggleEdit(event, field) {
        event.preventDefault();
        const displayField = document.getElementById(field + 'Display');
        const inputField = document.getElementById(field);
        const saveBtn = document.getElementById('save' + field.charAt(0).toUpperCase() + field.slice(1) + 'Btn');
        const cancelBtn = document.getElementById('cancel' + field.charAt(0).toUpperCase() + field.slice(1) + 'Btn');

        const isEditing = inputField.style.display === 'block';
        
        displayField.style.display = isEditing ? 'block' : 'none';
        inputField.style.display = isEditing ? 'none' : 'block';
        saveBtn.style.display = isEditing ? 'none' : 'inline-block';
        cancelBtn.style.display = isEditing ? 'none' : 'inline-block';
    }

    function cancelEdit(event, field) {
        event.preventDefault();
        const displayField = document.getElementById(field + 'Display');
        const inputField = document.getElementById(field);
        const saveBtn = document.getElementById('save' + field.charAt(0).toUpperCase() + field.slice(1) + 'Btn');
        const cancelBtn = document.getElementById('cancel' + field.charAt(0).toUpperCase() + field.slice(1) + 'Btn');

        inputField.value = displayField.innerText; // Reset to original value
        displayField.style.display = 'block';
        inputField.style.display = 'none';
        saveBtn.style.display = 'none';
        cancelBtn.style.display = 'none';
    }
</script>