<!DOCTYPE html>
<html lang="en">
	<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-tagsinput/dist/bootstrap-tagsinput.css" rel="stylesheet">
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">
    <title>JbsCout</title>
    <link rel="stylesheet" href="{{asset('geomap/AllColorsThemes.css')}}">
    <link rel="stylesheet" href="{{asset('Clubs/style.css')}}">
    <link rel="stylesheet" href="{{asset('css/competition.css')}}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.Default.css" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">

        <style>
        .tree-view .nested {
  display: none;
  margin-left: 1rem;
}

.tree-view .nested.active {
  display: block;
}
            .autocomplete-list {
                border: 1px solid #ccc;
                max-height: 150px;
                overflow-y: auto;
                background-color: var(--sidebar-left-right-color);
                z-index: 99999;
                position: absolute;
                top: 36px;
                width: 100%;
            }
            .autocomplete-item {
                padding: 5px;
                cursor: pointer;
            }
            .autocomplete-item:hover {
                background-color: #eee;
            }
            .playerSearchResults {
                display: flex            ;
                margin-top: 1rem;
                justify-items: stretch;
                flex-wrap: wrap;
                justify-content: space-evenly;
                align-items: center;
            }
            .playersCard{
                    border: 1px solid #ccc;
                    padding: 10px;
                    margin-bottom: 8px;
                    border-radius: 5px;
                    color: var(--bootom-colortext);
                    /* min-width: 80px; */
                    width: 350px;
                           height: 120px;
                    gap: 5px;
                    display: flex;
                    flex-wrap: wrap;
                    flex-direction: column;
            }
            .profile-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 6px 15px rgba(0,0,0,0.1);
  font-family: 'Segoe UI', sans-serif;
  max-width: 800px;
  margin: auto;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.profile-image {
  width: 140px;
  height: 140px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #ddd;
}

.profile-info h2 {
  margin: 0;
  font-size: 24px;
}

.profile-body {
  margin-top: 20px;
}

.profile-body p {
  margin: 6px 0;
}

.market-value {
  color: #2e8b57;
  font-weight: bold;
}
        </style>

        <style>
@import url('https://fonts.googleapis.com/css?family=Abel');

html, body {
  background: #FCEEB5;
  font-family: Abel, Arial, Verdana, sans-serif;
}

.center {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
}

.card {
  width: 450px;
  height: 250px;
  background-color: #fff;
  background: linear-gradient(#f8f8f8, #fff);
  box-shadow: 0 8px 16px -8px rgba(0,0,0,0.4);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  margin: 1.5rem;
}

.card h1 {
  text-align: center;
}

.card .additional {
  position: absolute;
  width: 150px;
  height: 100%;
  background: linear-gradient(#dE685E, #EE786E);
  transition: width 0.4s;
  overflow: hidden;
  z-index: 2;
}

.card.green .additional {
  background: linear-gradient(#92bCa6, #A2CCB6);
}


.card:hover .additional {
  width: 100%;
  border-radius: 0 5px 5px 0;
}

.card .additional .user-card {
  width: 150px;
  height: 100%;
  position: relative;
  float: left;
}

.card .additional .user-card::after {
  content: "";
  display: block;
  position: absolute;
  top: 10%;
  right: -2px;
  height: 80%;
  border-left: 2px solid rgba(0,0,0,0.025);*/
}

.card .additional .user-card .level,
.card .additional .user-card .points {
  top: 15%;
  color: #fff;
  text-transform: uppercase;
  font-size: 0.75em;
  font-weight: bold;
  background: rgba(0,0,0,0.15);
  padding: 0.125rem 0.75rem;
  border-radius: 100px;
  white-space: nowrap;
}

.card .additional .user-card .points {
  top: 85%;
}

.card .additional .user-card svg {
  top: 50%;
}

.card .additional .more-info {
  width: 300px;
  float: left;
  position: absolute;
  left: 150px;
  height: 100%;
}

.card .additional .more-info h1 {
  color: #fff;
  margin-bottom: 0;
}

.card.green .additional .more-info h1 {
  color: #224C36;
}

.card .additional .coords {
  margin: 0 1rem;
  color: #fff;
  font-size: 1rem;
}

.card.green .additional .coords {
  color: #325C46;
}

.card .additional .coords span + span {
  float: right;
}

.card .additional .stats {
  font-size: 2rem;
  display: flex;
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  top: auto;
  color: #fff;
}

.card.green .additional .stats {
  color: #325C46;
}

.card .additional .stats > div {
  flex: 1;
  text-align: center;
}

.card .additional .stats i {
  display: block;
}

.card .additional .stats div.title {
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
}

.card .additional .stats div.value {
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1.5rem;
}

.card .additional .stats div.value.infinity {
  font-size: 2.5rem;
}

.card .general {
  width: 300px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  box-sizing: border-box;
  padding: 1rem;
  padding-top: 0;
}

.card .general .more {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 0.9em;
}


</style>
</head>
<body>
<div class="container-fluid d-flex" style="margin:0 !important; padding:0 !important; height:100vh;">
    <div class="main-content">
        <div class="left-block">
            <div class="navbar topbar topbar-left-block" style="top:0;align-content: center;">
                <span style="" id="topbarText">
                <i class="bi bi-tag-fill" style="color: var(--coloredTextPrimary); margin-left: 10px; margin-right: 4px;"></i> BlankPage</span>
                <i class="bi bi-chevron-down dropdown-icon icon-dropdown" style="cursor: pointer;margin-left: -53px;"></i>
                <div class="dropdown-content dropdown-menu  content-dropdown " style="display: none;">
                    <a href="#" class="hover-link">
                        <div style="display: flex; justify-content: space-between;">
                            <span >test1</span>
                            <i class="bi bi-tag-fill" style=" margin-right: 8px;"></i>
                        </div>
                    </a>
                    <a href="#" class="hover-link">
                        <div style="display: flex; justify-content: space-between;">
                            <span >test2</span>
                            <i class="bi bi-plugin" style=" margin-right: 8px;"></i>
                        </div>
                    </a>
                    <a href="#" class="hover-link">
                        <div style="display: flex; justify-content: space-between;">
                            <span >test3</span>
                            <i class="bi bi-plugin" style=" margin-right: 8px;"></i>
                        </div>
                    </a>
                </div>
                <div class="MonthSelectorContainer dropdown">
                        <button class="MonthSelector btn btn-secondary dropdown-toggle"type="button" id="MonthSelector" aria-haspopup="true" aria-expanded="false">
                            <i class="bi bi-calendar4-week"></i>
                            <span class="CurrentlyMonthApplyed"></span>
                            <i class="bi bi-chevron-down dropdown-icon toggleMonthSelector" style="cursor: pointer;margin-top: 0px;padding:0;"></i>
                        </button>
                        <div class="MonthSelectorDropdown dropdown-menu" aria-labelledby="dropdownMenuButton" style="display: none;">
                            <div class="MonthToSelect" onclick="updateMonth('01','Janvier')">Janvier</div>
                            <div class="MonthToSelect" onclick="updateMonth('02','Février')">Février</div>
                            <div class="MonthToSelect" onclick="updateMonth('03','Mars')">Mars</div>
                            <div class="MonthToSelect" onclick="updateMonth('04','Avril')">Avril</div>
                            <div class="MonthToSelect" onclick="updateMonth('05','Mai')">Mai</div>
                            <div class="MonthToSelect" onclick="updateMonth('06','Juin')">Juin</div>
                            <div class="MonthToSelect" onclick="updateMonth('07','Juillet')">Juillet</div>
                            <div class="MonthToSelect" onclick="updateMonth('08','Août')">Août</div>
                            <div class="MonthToSelect" onclick="updateMonth('09','Septembre')">Septembre</div>
                            <div class="MonthToSelect" onclick="updateMonth('10','Octobre')">Octobre</div>
                            <div class="MonthToSelect" onclick="updateMonth('11','Novembre')">Novembre</div>
                            <div class="MonthToSelect" onclick="updateMonth('12','Décembre')">Décembre</div>
                        </div>
                    </div>
            </div>
            <div class="content" style="">
                <!-- Section Formulaires -->
                <div class="Tree-Formulaires">
                    <div class="svg-topbar">
                        <div class="search-container" >
                            <svg xmlns:xlink="http://www.w3.org/1999/xlink" class="icon_effbe2 visible_effbe2" aria-hidden="true" role="img" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewbox="0 0 24 24">
                                <path fill="var(--icon-topbar)" fill-rule="evenodd" d="M15.62 17.03a9 9 0 1 1 1.41-1.41l4.68 4.67a1 1 0 0 1-1.42 1.42l-4.67-4.68ZM17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" clip-rule="evenodd" class=""></path>
                            </svg>
                            <input class="InputSearshTreeView"type="text"id="TreeSearch" placeholder="Rechercher">
                            <i  class="bi bi-mic-fill microIcon" id="micIcon"></i>
                        </div>
                    </div>
                    <div class="headerTreeSection" style="margin-top:2px;" >
                    <div  class="dropdown"style="display: flex; align-items: center;width: 100%;" id="headerTreeSection">
                        <i class="bi bi-tag-fill" style="color: var(--coloredTextPrimary); margin-left: 10px; margin-right: 4px; margin-left: 6px;"></i>
                        <p class="sidebar-title">BlankPage<span class="itemChoosed" categoryFilter="" style="margin-left: 15px;"></span></p>
                    </div>
                    <div class=" ClusterFilter dropdown-content dropdown-menu " aria-labelledby="dropdownMenuButton" id="initialDropdown" style="">
                        <span class="close-btnInitialDropdown">&times;</span>
                        <a class="dropdown-item TousCluster">
                            <span >Tous</span>
                        </a>
                        <a class="dropdown-item CONQUETE">
                            <span >test1</span>
                        </a>
                        <a class="dropdown-item MIGRATION">
                            <span >test2</span>
                        </a>
                        <a class="dropdown-item MOBILES">
                            <span>test3</span>
                        </a>
                    </div>
                    <i class="bi bi-sort-up icon-color" id="FilterByEtatCluster" onclick="FindClustersByEtat();"style="font-size: 25px;color: #c6d2ff;"></i>
                    <div id="modalByEtatCluster" class="modalByEtatCluster" style="display: none;flex-direction: column;gap: 8px;">
                        <span class="FilterClusterByEtat" data="CategoryAll">Tout</span>
                        <span class="FilterClusterByEtat" data="Category100" style="border-right:3px solid green;">Objectif Atteint</span>
                        <span class="FilterClusterByEtat" data="Category80" style="border-right:3px solid yellow;">Objectif > 80%</span>
                        <span class="FilterClusterByEtat" data="Category51" style="border-right:3px solid orange;">Objectif > 50%</span>
                        <span class="FilterClusterByEtat" data="Category49" style="border-right:3px solid red;">Objectif < 50%</span>
                        <span class="FilterClusterByEtat" data="CategoryHorsZone">Hors zone</span>
                    </div>
                    </div>

                    <div class="sectionDivider skeleton" style="margin-top: 5px; margin-bottom: 0px;"></div>
                    <div class="TreeFormsCluster Tree-Froms scrollbar-custom skeleton">
                        <div class="tree-view skeleton">
                            <ul id="tree-root">

                            </ul>
                        </div>
                    </div>
                </div>
                <div class="  resizer2" style="display:block;"><spans>. . .</spans></div>
                <!-- Section Roles -->
                <div class="Tree-Roles">
                    <div class="ActualMonthProductions"style="margin-bottom: 110px;">
                        <div class="AllDaysName">
                            <div class="day-name">Lu</div>
                            <div class="day-name">Ma</div>
                            <div class="day-name">Me</div>
                            <div class="day-name">Je</div>
                            <div class="day-name">Ve</div>
                            <div class="day-name">Sa</div>
                            <div class="day-name">Di</div>
                        </div>
                        <div class="ActualMonthProductionsDays"style="margin-left: 0px;display: flex;padding: 2px;gap:2px;flex-wrap: wrap;">
                            <!-- Days will be dynamically generated here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="bottombar">
                <div class="user-section" onclick="showUserModal.call(this, 'Mansour', 80, -200, 'owner')">
                    <div class="avatar-container">
                        <img src="{{asset('discord/avatardiscord.png')}}" alt="User Icon" class="user-icon">
                        <div class="status-indicator"></div>
                    </div>
                    <div class="user-info">
                        <span class="username">User Test</span>
                        <span class="user-tag">en ligne</span>
                        <span class="user-tag2">300570</span>
                    </div>
                </div>
                <div class="controls-section">
                    <div class="svg-section">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewbox="0 0 24 24" width="24" height="24" preserveaspectratio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
                            <defs>
                                <clipPath id="__lottie_element_5">
                                    <rect width="24" height="24" x="0" y="0"></rect>
                                </clipPath>
                                <clipPath id="__lottie_element_7">
                                    <path d="M0,0 L600,0 L600,600 L0,600z"></path>
                                </clipPath>
                                <clipPath id="__lottie_element_11">
                                    <path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
                                </clipPath>
                                <clipPath id="__lottie_element_18">
                                    <path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
                                </clipPath>
                                <clipPath id="__lottie_element_28">
                                    <path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
                                </clipPath>
                                <mask id="__lottie_element_29">
                                    <rect fill="var(--icon-topbar)" width="600" height="600" transform="matrix(1,0,0,1,200,200)"></rect>
                                    <path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M681.219970703125,212.79600524902344 C681.219970703125,212.79600524902344 215.69700622558594,681.75 215.69700622558594,681.75 C215.69700622558594,681.75 151.75999450683594,749.2319946289062 201.13699340820312,798.8619995117188 C255,853 319.67999267578125,785.0560302734375 319.67999267578125,785.0560302734375 C319.67999267578125,785.0560302734375 785.2030029296875,316.10198974609375 785.2030029296875,316.10198974609375 C785.2030029296875,316.10198974609375 868,234 816.2150268554688,184.4459991455078 C764.781005859375,135.22799682617188 681.219970703125,212.79600524902344 681.219970703125,212.79600524902344" fill-opacity="1"></path>
                                    <path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M698,405 C698,405 642,405 642,405 C642,405 642,479 642,479 C642,479 698,479 698,479 C698,479 698,405 698,405" fill-opacity="1"></path>
                                </mask>
                            </defs>
                            <g clip-path="url(#__lottie_element_5)">
                                <g clip-path="url(#__lottie_element_7)" transform="matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)" opacity="1" style="display: block;">
                                    <g clip-path="url(#__lottie_element_28)" transform="matrix(1,0,0,1,-200,-200)" opacity="1" style="display: block;">
                                        <g mask="url(#__lottie_element_29)">
                                            <g style="display: none;" transform="matrix(-25,0,0,25,800,173)" opacity="1">
                                                <g opacity="1" transform="matrix(1,0,0,1,12,8.5)">
                                                    <path fill="#F23F42" fill-opacity="1" d=" M-4,-1.3799999952316284 C-4,-3.5889999866485596 -2.2090001106262207,-5.380000114440918 0,-5.380000114440918 C2.2090001106262207,-5.380000114440918 4,-3.5889999866485596 4,-1.3799999952316284 C4,-1.3799999952316284 4,2.509999990463257 4,2.509999990463257 C4,4.718999862670898 2.2090001106262207,6.5 0,6.5 C-2.2090001106262207,6.5 -4,4.718999862670898 -4,2.509999990463257 C-4,2.509999990463257 -4,-1.3799999952316284 -4,-1.3799999952316284z"></path>
                                                </g>
                                                <g opacity="1" transform="matrix(1,0,0,1,12,14)">
                                                    <path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-7,-2.990000009536743 C-7,0.8759999871253967 -3.865999937057495,4.010000228881836 0,4.010000228881836 C3.865999937057495,4.010000228881836 7,0.8759999871253967 7,-2.990000009536743"></path>
                                                </g>
                                                <g opacity="1" transform="matrix(1,0,0,1,12,20)">
                                                    <path fill="#F23F42" fill-opacity="1" d=" M-1,-2 C-1,-2.2760000228881836 -0.7760000228881836,-2.5 -0.5,-2.5 C-0.5,-2.5 0.5,-2.5 0.5,-2.5 C0.7760000228881836,-2.5 1,-2.2760000228881836 1,-2 C1,-2 1,2 1,2 C1,2.2760000228881836 0.7760000228881836,2.5 0.5,2.5 C0.5,2.5 -0.5,2.5 -0.5,2.5 C-0.7760000228881836,2.5 -1,2.2760000228881836 -1,2 C-1,2 -1,-2 -1,-2z"></path>
                                                </g>
                                                <g opacity="1" transform="matrix(1,0,0,1,12,22)">
                                                    <path fill="#F23F42" fill-opacity="1" d=" M3,-1 C3.552000045776367,-1 4,-0.5519999861717224 4,0 C4,0.5519999861717224 3.552000045776367,1 3,1 C3,1 -3,1 -3,1 C-3.552000045776367,1 -4,0.5519999861717224 -4,0 C-4,-0.5519999861717224 -3.552000045776367,-1 -3,-1 C-3,-1 3,-1 3,-1z"></path>
                                                </g>
                                            </g>
                                            <g transform="matrix(-25,0,0,25,800,173)" opacity="1" style="display: block;">
                                                <g opacity="1" transform="matrix(1,0,0,1,12,8.5)">
                                                    <path fill="#F23F42" fill-opacity="1" d=" M-4,-1.4600000381469727 C-4,-3.6689999103546143 -2.2090001106262207,-5.460000038146973 0,-5.460000038146973 C2.2090001106262207,-5.460000038146973 4,-3.6689999103546143 4,-1.4600000381469727 C4,-1.4600000381469727 4,2.5 4,2.5 C4,4.709000110626221 2.2090001106262207,6.5 0,6.5 C-2.2090001106262207,6.5 -4,4.709000110626221 -4,2.5 C-4,2.5 -4,-1.4600000381469727 -4,-1.4600000381469727z"></path>
                                                </g>
                                                <g opacity="1" transform="matrix(1,0,0,1,12,14)">
                                                    <path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-7,-3 C-7,0.8659999966621399 -3.865999937057495,4 0,4 C3.865999937057495,4 7,0.8659999966621399 7,-3"></path>
                                                </g>
                                                <g opacity="1" transform="matrix(1,0,0,1,12,20)">
                                                    <path fill="#F23F42" fill-opacity="1" d=" M-1,-2 C-1,-2.2760000228881836 -0.7760000228881836,-2.5 -0.5,-2.5 C-0.5,-2.5 0.5,-2.5 0.5,-2.5 C0.7760000228881836,-2.5 1,-2.2760000228881836 1,-2 C1,-2 1,2 1,2 C1,2.2760000228881836 0.7760000228881836,2.5 0.5,2.5 C0.5,2.5 -0.5,2.5 -0.5,2.5 C-0.7760000228881836,2.5 -1,2.2760000228881836 -1,2 C-1,2 -1,-2 -1,-2z"></path>
                                                </g>
                                                <g opacity="1" transform="matrix(1,0,0,1,12,22)">
                                                    <path fill="#F23F42" fill-opacity="1" d=" M3,-1 C3.552000045776367,-1 4,-0.5519999861717224 4,0 C4,0.5519999861717224 3.552000045776367,1 3,1 C3,1 -3,1 -3,1 C-3.552000045776367,1 -4,0.5519999861717224 -4,0 C-4,-0.5519999861717224 -3.552000045776367,-1 -3,-1 C-3,-1 3,-1 3,-1z"></path>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                    <g clip-path="url(#__lottie_element_18)" transform="matrix(1,0,0,1,-200,-200)" opacity="1" style="display: none;">
                                        <g transform="matrix(25,0,0,25,200,200)" opacity="1" style="display: none;">
                                            <g opacity="1" transform="matrix(1,0,0,1,12,12)">
                                                <path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-10,10 C-10,10 10,-10 10,-10"></path>
                                            </g>
                                        </g>
                                        <g style="display: none;">
                                            <g>
                                                <path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"></path>
                                            </g>
                                        </g>
                                    </g>
                                    <g clip-path="url(#__lottie_element_11)" style="display: block;" transform="matrix(1,0,0,1,-200,-200)" opacity="1">
                                        <g style="display: block;" transform="matrix(25,0,0,25,200,200)" opacity="1">
                                            <g opacity="1" transform="matrix(1,0,0,1,12,12)">
                                                <path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-10,10 C-10,10 10,-10 10,-10"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <div class="svg-section">
                            <div class="theme-switcher">
                            <div class="current-theme" tabindex="0">
                                <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark Theme" class="theme-icon">
                            </div>
                            <ul class="theme-options">
                                 <li data-theme="Darkgreen">
                                            <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark light Theme" class="theme-icon">
                                            <span>Dark Green</span>
                                </li>
                                <li data-theme="darklight">
                                    <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark light Theme" class="theme-icon">
                                    <span>Dark Light</span>
                                </li>
                                <li data-theme="lightsand">
                                    <img src="{{asset('discord/light-mode.svg')}}" alt="Light sand Theme" class="theme-icon">
                                    <span>Light Sand</span>
                                </li>
                                <li data-theme="darkpurple">
                                    <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark purple Theme" class="theme-icon">
                                    <span>Dark Purple</span>
                                </li>
                                <li data-theme="darkblue">
                                    <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark Blue Theme" class="theme-icon">
                                    <span>Dark Blue</span>
                                </li>
                                <li data-theme="light">
                                    <img src="{{asset('discord/light-mode.svg')}}" alt="Light Theme" class="theme-icon">
                                    <span>Light</span>
                                </li>
                                <li data-theme="dark" class="active">
                                    <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark Theme" class="theme-icon">
                                    <span>Dark</span>
                                </li>
                                <li data-theme="auto">
                                    <img src="{{asset('discord/auto-mode.svg')}}" alt="Auto Theme" class="theme-icon">
                                    <span>Auto</span>
                                </li>
                            </ul>

                        </div>
                    </div>
                    <div class="svg-section settings" id="settingsButton">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewbox="0 0 24 24" width="24" height="24" preserveaspectratio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
                            <defs>
                                <clipPath id="__lottie_element_97">
                                    <rect width="24" height="24" x="0" y="0"></rect>
                                </clipPath>
                                <clipPath id="__lottie_element_99">
                                    <path d="M0,0 L600,0 L600,600 L0,600z"></path>
                                </clipPath>
                            </defs>
                            <g clip-path="url(#__lottie_element_97)">
                                <g clip-path="url(#__lottie_element_99)" transform="matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)" opacity="1" style="display: block;">
                                    <g transform="matrix(25,0,0,25,300,300)" opacity="1" style="display: block;">
                                        <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                            <path fill="oklab(0.786807 -0.0025776 -0.0110238)" fill-opacity="1" d=" M-1.4420000314712524,-10.906000137329102 C-1.8949999809265137,-10.847000122070312 -2.1470000743865967,-10.375 -2.078000068664551,-9.92300033569336 C-1.899999976158142,-8.756999969482422 -2.265000104904175,-7.7210001945495605 -3.061000108718872,-7.390999794006348 C-3.8570001125335693,-7.060999870300293 -4.8480000495910645,-7.534999847412109 -5.546000003814697,-8.484999656677246 C-5.816999912261963,-8.852999687194824 -6.329999923706055,-9.008999824523926 -6.691999912261963,-8.730999946594238 C-7.458000183105469,-8.142999649047852 -8.142999649047852,-7.458000183105469 -8.730999946594238,-6.691999912261963 C-9.008999824523926,-6.329999923706055 -8.852999687194824,-5.816999912261963 -8.484999656677246,-5.546000003814697 C-7.534999847412109,-4.8480000495910645 -7.060999870300293,-3.8570001125335693 -7.390999794006348,-3.061000108718872 C-7.7210001945495605,-2.265000104904175 -8.756999969482422,-1.899999976158142 -9.92300033569336,-2.078000068664551 C-10.375,-2.1470000743865967 -10.847000122070312,-1.8949999809265137 -10.906000137329102,-1.4420000314712524 C-10.968000411987305,-0.9700000286102295 -11,-0.48899999260902405 -11,0 C-11,0.48899999260902405 -10.968000411987305,0.9700000286102295 -10.906000137329102,1.4420000314712524 C-10.847000122070312,1.8949999809265137 -10.375,2.1470000743865967 -9.92300033569336,2.078000068664551 C-8.756999969482422,1.899999976158142 -7.7210001945495605,2.265000104904175 -7.390999794006348,3.061000108718872 C-7.060999870300293,3.8570001125335693 -7.534999847412109,4.8470001220703125 -8.484999656677246,5.546000003814697 C-8.852999687194824,5.816999912261963 -9.008999824523926,6.328999996185303 -8.730999946594238,6.691999912261963 C-8.142999649047852,7.458000183105469 -7.458000183105469,8.142999649047852 -6.691999912261963,8.730999946594238 C-6.329999923706055,9.008999824523926 -5.816999912261963,8.852999687194824 -5.546000003814697,8.484999656677246 C-4.8480000495910645,7.534999847412109 -3.8570001125335693,7.060999870300293 -3.061000108718872,7.390999794006348 C-2.265000104904175,7.7210001945495605 -1.899999976158142,8.756999969482422 -2.078000068664551,9.92300033569336 C-2.1470000743865967,10.375 -1.8949999809265137,10.847000122070312 -1.4420000314712524,10.906000137329102 C-0.9700000286102295,10.968000411987305 -0.48899999260902405,11 0,11 C0.48899999260902405,11 0.9700000286102295,10.968000411987305 1.4420000314712524,10.906000137329102 C1.8949999809265137,10.847000122070312 2.1470000743865967,10.375 2.078000068664551,9.92300033569336 C1.899999976158142,8.756999969482422 2.2660000324249268,7.7210001945495605 3.062000036239624,7.390999794006348 C3.8580000400543213,7.060999870300293 4.8480000495910645,7.534999847412109 5.546000003814697,8.484999656677246 C5.816999912261963,8.852999687194824 6.328999996185303,9.008999824523926 6.691999912261963,8.730999946594238 C7.458000183105469,8.142999649047852 8.142999649047852,7.458000183105469 8.730999946594238,6.691999912261963 C9.008999824523926,6.328999996185303 8.852999687194824,5.816999912261963 8.484999656677246,5.546000003814697 C7.534999847412109,4.8480000495910645 7.060999870300293,3.8570001125335693 7.390999794006348,3.061000108718872 C7.7210001945495605,2.265000104904175 8.756999969482422,1.899999976158142 9.92300033569336,2.078000068664551 C10.375,2.1470000743865967 10.847000122070312,1.8949999809265137 10.906000137329102,1.4420000314712524 C10.968000411987305,0.9700000286102295 11,0.48899999260902405 11,0 C11,-0.48899999260902405 10.968000411987305,-0.9700000286102295 10.906000137329102,-1.4420000314712524 C10.847000122070312,-1.8949999809265137 10.375,-2.1470000743865967 9.92300033569336,-2.078000068664551 C8.756999969482422,-1.899999976158142 7.7210001945495605,-2.265000104904175 7.390999794006348,-3.061000108718872 C7.060999870300293,-3.8570001125335693 7.534999847412109,-4.8480000495910645 8.484999656677246,-5.546000003814697 C8.852999687194824,-5.816999912261963 9.008999824523926,-6.329999923706055 8.730999946594238,-6.691999912261963 C8.142999649047852,-7.458000183105469 7.458000183105469,-8.142999649047852 6.691999912261963,-8.730999946594238 C6.328999996185303,-9.008999824523926 5.817999839782715,-8.852999687194824 5.546999931335449,-8.484999656677246 C4.848999977111816,-7.534999847412109 3.8580000400543213,-7.060999870300293 3.062000036239624,-7.390999794006348 C2.2660000324249268,-7.7210001945495605 1.9010000228881836,-8.756999969482422 2.0789999961853027,-9.92300033569336 C2.1480000019073486,-10.375 1.8949999809265137,-10.847000122070312 1.4420000314712524,-10.906000137329102 C0.9700000286102295,-10.968000411987305 0.48899999260902405,-11 0,-11 C-0.48899999260902405,-11 -0.9700000286102295,-10.968000411987305 -1.4420000314712524,-10.906000137329102z M4,0 C4,2.2090001106262207 2.2090001106262207,4 0,4 C-2.2090001106262207,4 -4,2.2090001106262207 -4,0 C-4,-2.2090001106262207 -2.2090001106262207,-4 0,-4 C2.2090001106262207,-4 4,-2.2090001106262207 4,0z"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
            </div>
            <div id="settings-modal" class="settings-modal" style="display:none">
                <div class="settings-modal-content">
                    <span class="close-settings">&times;</span>
                    <h2>Settings</h2>
                    <p>Adjust your settings here.</p>
                </div>
            </div>
        </div>
        <div class="right-block" style="background: var(--sidebar-left-right-color);">
            <div class="topbar topbar-right-blcok" style="background-color: var(--sidebar-left-right-color);">
                <div class="left-side-topbar">
                    <div class="TypeOptionSelector" dataType="B" onclick="updateTypeOption(this)">
                        <i class="bi bi-check-circle">
                        <span>test </i></span>
                    </div>
                    <div class="TypeOptionSelector" dataType="V" onclick="updateTypeOption(this)">
                        <i class="bi bi-check-circle"></i>
                        <span>test </span>
                    </div>
                    <div class="TypeOptionSelector" dataType="R" onclick="updateTypeOption(this)">
                        <i class="bi bi-check-circle"></i>
                        <span>test</span>
                    </div>
                </div>

                <div class="right-side-topbar">
                    <div class="svg-topbar">
                        <div class="container searchContainerTopbar mt-4">
                            <div class="input-group" style="position: relative">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input list="suggestions" autocomplete="off" type="text" class="form-control searchContainerTopbarInput" id="SearchInputTopBar" placeholder="Rechercher..." oninput="">
                                <div id="autocomplete-container" class="autocomplete-list" style="display:none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="simple-resolution" style="height: 100vh;display: flex;flex-direction: column;">
                <div style="bottom: 0;height: 96vh; width: 100%; overflow: hidden;">
                    <div class="map-container" style="height: 100%; width: 100%; position: relative;z-index: 0;">
                        <div id="playerSearchResults" class="playerSearchResults" style="margin-top: 1rem;"></div>
                        <div id="PlayerProfile" class="PlayerProfile" style="    height: 97%;margin-top: 1rem;display:none;">
                            <div class="profile-card">
                                <div class="profile-header">
                                <img src="__imageUrl__" alt="__name__" class="profile-image"/>
                                <div class="profile-info">
                                    <h2>__name__ <small>(__nameInHomeCountry__)</small></h2>
                                    <p class="position">__position__</p>
                                    <p class="market-value">__marketValue__</p>
                                </div>
                                </div>

                                <div class="profile-body">
                                <p><strong>Age:</strong> __age__</p>
                                <p><strong>Nationality:</strong> __nationality__</p>
                                <p><strong>Club:</strong> __clubName__ <small>(since __joined__)</small></p>
                                <p><strong>Shirt Number:</strong> __shirtNumber__</p>
                                <p><strong>Foot:</strong> __foot__</p>
                                <p><strong>Birthplace:</strong> __city__, __country__</p>
                                <p><strong>Outfitter:</strong> __outfitter__</p>
                                <p><strong>Relatives:</strong> __relatives__</p>
                                <p><strong>Social Media:</strong><br>__socialLinks__</p>
                                <p><a href="__profileUrl__" target="_blank">View full profile on Transfermarkt</a></p>
                                </div>
                                <button onclick="backToSearch()" class="btn btn-secondary">← Back to search results</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="productionPannel" id="displayproductionPanel" style="bottom: -100%;" >
                    <div class="DetailsCAlenderContainer">
                        <div class="DetailsCAlenderdata scrollbar-custom ">
                        </div>
                        <div id="detailsPopup" class="popup">
                            <div class="popup-content">
                                <span class="close-btn" onclick="closePopup()">&times;</span>
                                <div id="popupContent"></div>
                            </div>
                        </div>
                    </div>
                    <div class="PanelContrats" style="right:-360px">
                    </div>
                    <div id="closePanel" class="ClosingBigPanel">
                        <div class="bottom-panel-toggle">
                            <i class="bi bi-caret-up-fill " style="transform: rotate(180deg);"></i>
                        </div>
                    </div>
                    <div class="SearchContainerINPanel">
                        <div class="container mt-4"style="display: flex;justify-content: center;">
                            <div class="input-group" >
                                <span class="input-group-text textPanel"style="display:none">
                                    <i class="fas fa-search"></i> <!-- Icône FontAwesome -->
                                </span>
                                <input type="text" class="form-control forminputPanel" style="display:none" placeholder="Rechercher...">
                            </div>
                        </div>
                    </div>
                    <div class="PanelIncidentTicket"id="PanelIncidentTicket" style="    right: -100%;"></div>
                </div>
                <div class="col-12 bottomPanel" id="displayChartPanel" style="">
                    <div style="display: flex;">
                        <div class="NameClickedPlace" id="openPanel" style="display:none" >
                            <span style="font-size: 14px;padding: 6px;color:var(--coloredTextPrimary);"></span>
                        </div>
                            <div class="NameClickedPlaceConsole" id="openPanelConsole" >
                            <span style="font-size: 14px;padding: 6px;color:var(--coloredTextPrimary);"></span>
                        </div>
                        <div class="bottom-panel-toggle" style="margin-right: 80%;" id="toggle-Chart">
                            <i class="bi bi-caret-up-fill IcontoggleChart"></i>
                        </div>
                    </div>
                    <div class="ChartPannel" id="statPlayer" style="    color: #fff !important;">
                        <div style="display: inline-block;width: 100%;float: left">

                            <div class="PanelChartUpDown   statDashcard" style="height: 200px;float: left">
                                <div class="chart-container-time">
                                    <canvas id="graph_goals" style="display: block; box-sizing: border-box; height: 95px; width: 198px;" width="198" height="95"></canvas>
                                </div>
                            </div>
                            <div class="PanelChartUpDown   statDashcard" style="height: 200px;float: left">
                                <div class="chart-container-time">
                                    <canvas id="graph_playedMinutes" style="display: block; box-sizing: border-box; height: 95px; width: 198px;" width="198" height="95"></canvas>
                                </div>
                            </div>
                            <div class="PanelChartUpDown salesdashboard2-row3" style="height: 200px; align-items: center;float: left;width: calc(100% - 736px);">
                                <div class="containermotifechec" style="height: 100%;">
                                    <div id="stats_list" style="color: var(--coloredTextPrimary);height: 100%;overflow-y: auto" class="list-container scrollbar-custom skeleton">

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div id="right-sidebar-concepteur" class="right-sidebar-concepteur col-auto col-md-3 col-xl-2 px-0 d-flex flex-column align-items-end">
                <div class="rightpanl" style="    color: #fff !important;">
                    <div class="RightPanelToggle" id="HandleRightPanel">
                        <i class="bi bi-caret-up-fill IconRightPanelToggle"></i>
                    </div>
                    <div class="panel">
                    </div>
                    <div class="interface">
                        <div id="clubInfo" style="display: none">
                            <h3 id="clubName"></h3>
                            <div class="row">
                                <div class="col-md-4">
                                    <img src="" id="pictureClub" style="width: 150px" />
                                </div>
                                <div class="col-8">
                                    <div>
                                        <span>Official Name : </span>
                                        <span id="officialNameClub"></span>
                                    </div>
                                    <div>
                                        <span>Adresse : </span>
                                        <span id="addressClub"></span>
                                    </div>
                                    <div>
                                        <span>Telephone : </span>
                                        <span id="phoneClub"></span>
                                    </div>
                                    <div>
                                        <span>Fax : </span>
                                        <span id="faxClub"></span>
                                    </div>
                                    <div>
                                        <span>Website : </span>
                                        <span id="websiteClub"></span>
                                    </div>
                                    <div>
                                        <span>Founded On : </span>
                                        <span id="foundedOnClub"></span>
                                    </div>
                                    <div>
                                        <span>Members : </span>
                                        <span id="members"></span>
                                    </div>
                                    <div>
                                        <span>colors : </span>
                                        <span id="colors"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="playerInfo" style="display: none">
                            <h3 id="playerName"></h3>
                            <div class="row">
                                <div class="col-md-4">
                                    <img src="" id="picturePlayer" style="width: 100px" />
                                </div>
                                <div class="col-8">
                                    <div>
                                        <span>Name : </span>
                                        <span id="namePlayer"></span>
                                    </div>
                                    <div>
                                        <span>Position : </span>
                                        <span id="positionPlayer"></span>
                                    </div>
                                    <div>
                                        <span>Club : </span>
                                        <span id="clubPlayer"></span>
                                    </div>
                                    <div>
                                        <span>Age : </span>
                                        <span id="agePlayer"></span>
                                    </div>
                                    <div>
                                        <span>Nationality : </span>
                                        <span id="nationalityPlayer"></span>
                                    </div>
                                    <div>
                                        <span>market Value : </span>
                                        <span id="marketValuePlayer"></span>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="statusErrorsModal" tabindex="-1" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false"> 
    <div class="modal-dialog modal-dialog-centered modal-sm" role="document"> 
        <div class="modal-content"> 
            <div class="modal-body text-center p-lg-4"> 
                <svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 130.2 130.2">
                    <circle class="path circle" fill="none" stroke="#db3646" stroke-width="6" stroke-miterlimit="10" cx="65.1" cy="65.1" r="62.1" /> 
                    <line class="path line" fill="none" stroke="#db3646" stroke-width="6" stroke-linecap="round" stroke-miterlimit="10" x1="34.4" y1="37.9" x2="95.8" y2="92.3" />
                    <line class="path line" fill="none" stroke="#db3646" stroke-width="6" stroke-linecap="round" stroke-miterlimit="10" x1="95.8" y1="38" X2="34.4" y2="92.2" /> 
                </svg> 
                <h4 class="text-danger mt-3">Invalid Inputs!</h4> 
                <p class="mt-3">Your request Incorrected, Try Again.</p>
                <button type="button" class="btn btn-sm mt-3 btn-danger" data-bs-dismiss="modal">Ok</button> 
            </div> 
        </div> 
    </div> 
</div>
<div class="modal fade" id="statusSuccessModal" tabindex="-1" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false"> 
    <div class="modal-dialog modal-dialog-centered modal-sm" role="document"> 
        <div class="modal-content"> 
            <div class="modal-body text-center p-lg-4"> 
                <svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 130.2 130.2">
                    <circle class="path circle" fill="none" stroke="#198754" stroke-width="6" stroke-miterlimit="10" cx="65.1" cy="65.1" r="62.1" />
                    <polyline class="path check" fill="none" stroke="#198754" stroke-width="6" stroke-linecap="round" stroke-miterlimit="10" points="100.2,40.2 51.5,88.8 29.8,67.5 " /> 
                </svg> 
                <h4 class="text-success mt-3">Oh Yeah!</h4> 
                <p class="mt-3">Your request successfully done.</p>
                <button type="button" class="btn btn-sm mt-3 btn-success" data-bs-dismiss="modal">Ok</button> 
            </div> 
        </div> 
    </div> 
</div>

<ul id="dropdownTypes" class="ULInputMessage" style="display:none;">
    <li class="ListInputMessage" id="uploadFileBtn"><span>Text</span></li>
	<li class="ListInputMessage"><span>Long Texte</span></li>
	<li class="ListInputMessage"><span>Email</span></li>
	<li class="ListInputMessage"><span>Date</span></li>
	<li class="ListInputMessage"><span>Date RDV</span></li>
	<li class="ListInputMessage"><span>Addresse</span></li>
</ul>


<ul id="dropdownRoles" class="ULInputMessage" style="display: none;bottom: auto;">
</ul>



<div id="createInputIcons" headlist="{{asset('discord/headlist.svg')}}" crayon="{{asset('discord/crayon.svg')}}" threepoint="{{asset('discord/threepointInfor.svg')}}" delete="{{asset('discord/supprimer.svg')}}" listInput="{{asset('discord/listInput.svg')}}"  ajouter="{{asset('discord/ajouter.svg')}}"></div>
<div id="TreeViewIcon" folder="{{asset('discord/treeview/dossier.svg')}}" fleche="{{asset('discord/treeview/Fleche.svg')}}" flecheFields="{{asset('discord/treeview/Fleche-contour.svg')}}" databaseIcon="{{asset('discord/treeview/databaseIcon.svg')}}"></div>
<script>
    var jwtToken = "{{ jwt }}";
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.9.6/lottie.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.12.2/lottie.min.js"></script>
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
<script src="https://www.gstatic.com/charts/loader.js"></script>
<script src="{{ asset('Data/CompetitionData.js') }}"></script>
{# <script src="{{ asset('Clubs/index.js') }}" defer></script> #}
<script src="{{ asset('Data/competition.js') }}" defer></script>
{# <script src="{{ asset('BlankPage/data.js') }}" defer></script> #}
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js" defer></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.min.js"></script>
<script src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet.markercluster/dist/leaflet.markercluster.js"></script>
<script src="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.9.1/underscore-min.js"></script>
<script src="https://code.jquery.com/jquery-1.12.4.min.js" integrity="sha384-nvAa0+6Qg9clwYCGGPpDQLVpLNn0fRaROjHqs13t4Ggj3Ez50XnGQqc/r8MhnRDZ" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script type="text/template" id="playerProfileTemplate">
  <div class="card card-football-player" data-state="#about" style="width: 98%;    height: 75%;">
  <div class="card-header">
    <div class="card-cover" style="background-image: url('https://images.unsplash.com/photo-1549068106-b024baf5062d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=934&q=80')"></div>
    <img class="card-avatar" src="https://images.unsplash.com/photo-1549068106-b024baf5062d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=934&q=80" alt="avatar" />
    <h1 class="card-fullname">William Rocheald</h1>
    <h2 class="card-jobtitle">UI Developer</h2>
  </div>
  <div class="card-main">
    <div class="card-section is-active" id="about">
      <div class="card-content">
        <div class="card-subtitle">ABOUT</div>
        <p class="card-desc">Whatever tattooed stumptown art party sriracha gentrify hashtag intelligentsia readymade schlitz brooklyn disrupt.
        </p>
      </div>
      <div class="card-social">
        <a href="#"><svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M15.997 3.985h2.191V.169C17.81.117 16.51 0 14.996 0c-3.159 0-5.323 1.987-5.323 5.639V9H6.187v4.266h3.486V24h4.274V13.267h3.345l.531-4.266h-3.877V6.062c.001-1.233.333-2.077 2.051-2.077z" />
          </svg></a>
        <a href="#"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
            <path d="M512 97.248c-19.04 8.352-39.328 13.888-60.48 16.576 21.76-12.992 38.368-33.408 46.176-58.016-20.288 12.096-42.688 20.64-66.56 25.408C411.872 60.704 384.416 48 354.464 48c-58.112 0-104.896 47.168-104.896 104.992 0 8.32.704 16.32 2.432 23.936-87.264-4.256-164.48-46.08-216.352-109.792-9.056 15.712-14.368 33.696-14.368 53.056 0 36.352 18.72 68.576 46.624 87.232-16.864-.32-33.408-5.216-47.424-12.928v1.152c0 51.008 36.384 93.376 84.096 103.136-8.544 2.336-17.856 3.456-27.52 3.456-6.72 0-13.504-.384-19.872-1.792 13.6 41.568 52.192 72.128 98.08 73.12-35.712 27.936-81.056 44.768-130.144 44.768-8.608 0-16.864-.384-25.12-1.44C46.496 446.88 101.6 464 161.024 464c193.152 0 298.752-160 298.752-298.688 0-4.64-.16-9.12-.384-13.568 20.832-14.784 38.336-33.248 52.608-54.496z" />
          </svg></a>
        <a href="#"><svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
            <path d="M301 256c0 24.852-20.148 45-45 45s-45-20.148-45-45 20.148-45 45-45 45 20.148 45 45zm0 0" />
            <path d="M332 120H180c-33.086 0-60 26.914-60 60v152c0 33.086 26.914 60 60 60h152c33.086 0 60-26.914 60-60V180c0-33.086-26.914-60-60-60zm-76 211c-41.355 0-75-33.645-75-75s33.645-75 75-75 75 33.645 75 75-33.645 75-75 75zm86-146c-8.285 0-15-6.715-15-15s6.715-15 15-15 15 6.715 15 15-6.715 15-15 15zm0 0" />
            <path d="M377 0H135C60.562 0 0 60.563 0 135v242c0 74.438 60.563 135 135 135h242c74.438 0 135-60.563 135-135V135C512 60.562 451.437 0 377 0zm45 332c0 49.625-40.375 90-90 90H180c-49.625 0-90-40.375-90-90V180c0-49.625 40.375-90 90-90h152c49.625 0 90 40.375 90 90zm0 0" />
          </svg></a>
        <a href="#"><svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M23.994 24v-.001H24v-8.802c0-4.306-.927-7.623-5.961-7.623-2.42 0-4.044 1.328-4.707 2.587h-.07V7.976H8.489v16.023h4.97v-7.934c0-2.089.396-4.109 2.983-4.109 2.549 0 2.587 2.384 2.587 4.243V24zM.396 7.977h4.976V24H.396zM2.882 0C1.291 0 0 1.291 0 2.882s1.291 2.909 2.882 2.909 2.882-1.318 2.882-2.909A2.884 2.884 0 002.882 0z" />
          </svg></a>
      </div>
    </div>
    <div class="card-section" id="experience">
      <div class="card-content">
        <div class="card-subtitle">WORK EXPERIENCE</div>
        <div class="card-timeline">
          <div class="card-item" data-year="2014">
            <div class="card-item-title">Front-end Developer at <span>JotForm</span></div>
            <div class="card-item-desc">Disrupt stumptown retro everyday carry unicorn.</div>
          </div>
          <div class="card-item" data-year="2016">
            <div class="card-item-title">UI Developer at <span>GitHub</span></div>
            <div class="card-item-desc">Developed new conversion funnels and disrupt.</div>
          </div>
          <div class="card-item" data-year="2018">
            <div class="card-item-title">Illustrator at <span>Google</span></div>
            <div class="card-item-desc">Onboarding illustrations for App.</div>
          </div>
          <div class="card-item" data-year="2020">
            <div class="card-item-title">Full-Stack Developer at <span>CodePen</span></div>
            <div class="card-item-desc">Responsible for the encomposing brand expreience.</div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-section" id="contact">
      <div class="card-content">
        <div class="card-subtitle">CONTACT</div>
        <div class="card-contact-wrapper">
          <div class="card-contact">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
            Algonquin Rd, Three Oaks Vintage, MI, 49128
          </div>
          <div class="card-contact">
            <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z" />
            </svg>(269) 756-9809
          </div>
          <div class="card-contact">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
              <path d="M22 6l-10 7L2 6" />
            </svg>
            <EMAIL>
          </div>
          <button class="contact-me">WORK TOGETHER</button>
        </div>
      </div>
    </div>
    <div class="card-buttons">
      <button data-section="#about" class="is-active">ABOUT</button>
      <button data-section="#experience">EXPERIENCE</button>
      <button data-section="#contact">CONTACT</button>
    </div>
  </div>
</div>
</script>

</body>
</html>
