
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/all.css" />
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/duotone.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

   <style>

 :root {
            --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 320px;
            --prisescardcard-height: 180px;
            --navbackground-color: #edecfc;
            --card-background-color: #edecfd;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #fcfbff;
            --icons-colors-i: #949fa9;
            --smaller-text-color-numbers: #c1c9ce;
            --bigtext-color: #394d5f;
            		--skeleton-bg:#e0e0e0
            
        }

        [data-theme="dark"] {
              --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 300px;
            --prisescardcard-height: 165px;
            --navbackground-color: #2b3942;
            --card-background-color:#2b3942;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #1f2c34;
            --icons-colors-i: #6e6f71;
            --smaller-text-color-numbers: #6e6f71;
            --bigtext-color: #6e6f71;
            --skeleton-bg:#e0e0e0;
        }

     
@media screen and (min-width: 1400px) {
    #prisescard {
        display: grid;
       grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 20px;
    }
       .prisesdashcard-container {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            scrollbar-width: none;
            scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
            overflow-y: auto; 
            max-height: 98vh; 
    
        }
 .dataselect{
        padding-left:700px;
  
    }
}

@media screen and (max-width: 1366px) and (min-width: 400px) {
    #prisescard {
        display: grid;
        grid-template-columns: repeat(3, minmax(300px, 340px)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 10px;
    }
       .prisesdashcard-container {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            scrollbar-width: none;
            scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
            overflow-y: auto; 
            max-height: 98vh; 
      
        }
    .dataselect{
        padding-left:210px;
    }
}


        #prisescard .custom-card {
            background-color: var(--card-background-color);
            border-radius: 12px;
            padding: 10px;
            color: white;
            width: var(--prisescardcard-width);
            height: var(--prisescardcard-height);
            opacity: 0; /* Initial state for lazy loading */
            transition: opacity 0.3s ease-out;
             z-index: 1;
        }

        #prisescard .card-header {
            background-color: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            border: none;
            box-shadow: none;
        }

        #prisescard .card-header i {
            color: #bb86fc;
            margin-right: 5px;
        }

        #prisescard .card-title {
            font-weight: bold;
            color: var(--bigtext-color);
            font-size: 0.8em;
        }

        #prisescard .number {
            color: var(--bigtext-color);
            font-weight: bold;
            font-size: 0.8em;
            margin-top: -3%;
        }

        #prisescard .data-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 0.85em;
        }

        #prisescard .data-row {
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }

        #prisescard .data-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            background-color: var(--small-cards-bg-colors);
            border-radius: 6px;
            width: 160px;
            font-size: 1.1em;
        }

        #prisescard .data-box i {
            color: var(--icons-colors-i);
        }

        #prisescard .data-box .texticon {
            color: var(--icons-colors-i);
            font-weight: bold;
        }

        #prisescard .data-box .number {
            color: var(--smaller-text-color-numbers);
            font-size: 1em;
        }

        /* Navbar Styles */
        #smallprisesnav .navbar {
            display: flex;
            gap: 8px;
            padding: 20px;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
        }

        #smallprisesnav .nav-button {
            display: flex;
            align-items: center;
            gap: 7px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 1rem;
            font-weight: bold;
            color: var(--bigtext-color);
            background-color: transparent;
            border: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        #smallprisesnav .nav-button.active {
            background-color: var(--navbackground-color);
            color: var(--bigtext-color);
        }

        #smallprisesnav .nav-button .icon {
            width: 24px;
            height: 24px;
        }


</style>
 <style>
 :root {
    /* Default Variables */

    --skeleton-bg: #e0e0e0;
    --skeleton-highlight: #f0f0f0;
    --prisescardcard-width: 300px;
    --prisescardcard-height: 165px;
}

/* Styles for dark mode */
[data-theme="dark"] {
 --prisescardcard-width: 300px;
    --prisescardcard-height: 165px;
    --skeleton-bg: #1f2c34;
    --skeleton-highlight: #2b3942;
}

.skeleton-card {
    background-color: var(--card-background-color);
    border-radius: 12px;
    padding: 10px;
    width: var(--prisescardcard-width);
    height: var(--prisescardcard-height);
    display: flex;
    flex-direction: column;
    gap: 10px;
    animation: shimmer 1.5s infinite;
}

/* Header Skeleton */
.skeleton-card-header {
    height: 20px;
    width: 80%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
    margin-bottom: 8px;
}

/* Data Container Skeleton */
.skeleton-data-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.skeleton-data-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.skeleton-data-row div {
    height: 30px;
    width: 45%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
}

/* Animation for Shimmer Effect */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: 200px 0;
    }
}

.skeleton-card-header,
.skeleton-data-row div {
    background: linear-gradient(
        90deg,
        var(--skeleton-bg) 25%,
        var(--skeleton-highlight) 50%,
        var(--skeleton-bg) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Hide actual cards during loading */
.custom-card.loading {
    display: none;
}

.custom-card.loaded {
    opacity: 1;
    display: block;
}

/* Responsive Design */
@media (max-width: 600px) {
    :root {
        --prisescardcard-width: 100%;
        --prisescardcard-height: auto;
    }
    
    .skeleton-card {
        padding: 8px;
    }
    
    .skeleton-data-row div {
        height: 20px;
    }
}
[data-theme="dark"] #btn-adsl img {
    content: url("{{ asset('image/icon/adsl-gris.svg') }}");
}
[data-theme="dark"] #btn-fibre img {
    content: url("{{ asset('image/icon/Fibre-gris.svg') }}");
}
[data-theme="dark"] #btn-mob img {
    content: url("{{ asset('image/icon/Mobile-gris.svg') }}");
}
[data-theme="dark"] #btn-mob-fibre img {
    content: url("{{ asset('image/icon/mobile-fibre-gris.svg') }}");
}
[data-theme="dark"] #btn-mob-adsl img {
    content: url("{{ asset('image/icon/mobile-adsl-gris.svg') }}");
}
</style>

<style>

.selectmonth{
   
    background-color: var(--card-background-color);
    border: none;
    color: var(--text-selects-colors);
    font-size: 90%;
    border-radius: 9px;
    padding: 5px;
}
/* Style the toggle switch container */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 43px;
    height: 22px;
}

/* Hide the default checkbox */
.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* Style the slider */
.toggle-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color:var(--card-background-color);
    transition: 0.4s;
    border-radius: 34px;
}

/* Create the circle inside the slider */
.toggle-switch .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    border-radius: 50%;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
}

/* When the checkbox is checked */
.toggle-switch input:checked + .slider {
    background-color:#bb86fc;
}

/* Move the circle when checked */
.toggle-switch input:checked + .slider:before {
    transform: translateX(18px);
}
     #prisescard {
        position: relative;
        width: 100%;
        height: 98vh; /* Ajustez la hauteur en fonction de vos besoins */
    }
        .mafrenchsoilp {
        height: 100vh; /* Full viewport height */
        width: 100%;
        margin: 0;
        padding: 0;
        position: absolute;
        z-index: 1; /* Arrière-plan */
    }
</style>
    <div class="prisesdashcard-container">
     {% set total_nb_fyr_adsl = 0 %}
        {% set total_nb_fyr_mob_mono = 0 %}
        {% set total_nb_fyr_mob_multi_thd = 0 %}
        {% set total_nb_fyr_mob_multi_adsl = 0 %}
        {% set total_nb_fyr_thd = 0 %}
              {% if Clusterstotals is not empty %}
                {% for cluster in Clusterstotals.code_cluster %}
            {% for date, values in cluster %}
                {% for cluster_key, cluster_data in cluster %}

                    {# Accumulate the sum of each value #}
                    {% set total_nb_fyr_adsl = total_nb_fyr_adsl + values.data.nb_fyr_adsl %}
                    {% set total_nb_fyr_mob_mono = total_nb_fyr_mob_mono + values.data.nb_fyr_mob_mono %}
                    {% set total_nb_fyr_mob_multi_thd = total_nb_fyr_mob_multi_thd + values.data.nb_fyr_mob_multi_thd %}
                    {% set total_nb_fyr_mob_multi_adsl = total_nb_fyr_mob_multi_adsl + values.data.nb_fyr_mob_multi_adsl %}
                    {% set total_nb_fyr_thd = total_nb_fyr_thd + values.data.nb_fyr_thd %}
                           {% endfor %}
            {% endfor %}
        {% endfor %}   
            {% else %}
      
    {% endif %} 
<div id="smallprisesnav">

<div class="navbar">

    <button class="nav-button" id="btn-adsl" data-param="adsl" 
            data-src-light="{{ asset('image/icon/adsl-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/adsl-gris.svg') }}">
        <img src="{{ asset('image/icon/adsl-violet.svg') }}" alt="Router Icon" class="icon" />
        <span>{{ total_nb_fyr_adsl }}</span>
    </button>
    <button class="nav-button" id="btn-fibre" data-param="thd" 
            data-src-light="{{ asset('image/icon/Fibre-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/Fibre-gris.svg') }}">
        <img src="{{ asset('image/icon/Fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_thd }}</span>
    </button>
    <button class="nav-button" id="btn-mob" data-param="mobMono" 
            data-src-light="{{ asset('image/icon/Mobile-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/Mobile-gris.svg') }}">
        <img src="{{ asset('image/icon/Mobile-violet.svg') }}" alt="Mobile Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_mono }}</span>
    </button>
    <button class="nav-button" id="btn-mob-fibre" data-param="mobMultiThd" 
            data-src-light="{{ asset('image/icon/mobile-fibre-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/mobile-fibre-gris.svg') }}">
        <img src="{{ asset('image/icon/mobile-fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_multi_thd }}</span>
    </button>
    <button class="nav-button" id="btn-mob-adsl" data-param="mobMultiAdsl" 
            data-src-light="{{ asset('image/icon/mobile-adsl-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/mobile-adsl-gris.svg') }}">
        <img src="{{ asset('image/icon/mobile-adsl-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_multi_adsl }}</span>
    </button>
    <div class="dataselect">

{# Page 2 #}
{% include 'Arretcu/formprisesarretcu.html.twig' with {
  'action': path('prisesArretcuCluster', { 'code_cluster': code_cluster, 'moisArretCuivre': app.request.get('moisArretCuivre'), 'anneeArretCuivre': app.request.get('anneeArretCuivre') }),
  'months': months, 'years': years
} %}



        
    </div>

</div>






</div>


<div id="prisescard">
<div class="mafrenchsoilp" id="mapprisearretuc" data-coords="[46.603354, 1.888334]" data-zoom="6"></div>
<!-- Loop for Skeleton Cards -->
{% for inseeCode, inseeData in interventionsplaceskpi.codesInsee %}
    {% if inseeData.data is defined and inseeData.data is iterable %}
        {% for location in inseeData.data %}
            <div class="custom-card skeleton-card">
                <div class="skeleton-card-header"></div>
                <div class="skeleton-data-container">
                    <div class="skeleton-data-row">
                        <div></div>
                        <div></div>
                    </div>
                    <div class="skeleton-data-row">
                        <div></div>
                        <div></div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% endif %}
{% endfor %}

<!-- Loop for Actual Cards -->
{% for inseeCode, inseeData in interventionsplaceskpi.codesInsee %}
    {% if inseeData.data is defined and inseeData.data is iterable %}
        {% for location in inseeData.data %}
            <div class="custom-card real-card loading" data-lazy-load="true" data-cluster-code="{{ inseeCode }}">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-map-marker-alt"></i> {{ location.ville }}
                    </div>
                    <div class="number">
                        <i class="fa-solid fa-plug"></i> {{ location.totalPrises }}
                    </div>
                </div>
                <div class="data-container">
                    <div class="data-row">
                        <div class="data-box">
                            <span class="texticon">FTTB</span>
                            <span class="number">{{ location.totalPrisesFTTB }}</span>
                        </div>
                        <div class="data-box">
                            <span class="texticon">FTTH</span>
                            <span class="number">{{ location.totalPrisesFTTH }}</span>
                        </div>
                    </div>
                    <div class="data-row">
                        <div class="data-box">
                            <i class="fas fa-home"></i>
                            <span class="number">{{ location.totalImmeubles }}</span>
                        </div>
                        <div class="data-box">
                            <i class="fas fa-building"></i>
                            <span class="number">{{ location.totalPavillons }}</span>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% endif %}
{% endfor %}
</div>


</div>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>


<script>
document.addEventListener('DOMContentLoaded', function () {
    const buttons = document.querySelectorAll('.nav-button');
    const params = {
        adsl: localStorage.getItem('adsl') || 'no',
        thd: localStorage.getItem('thd') || 'no',
        mobMono: localStorage.getItem('mobMono') || 'no',
        mobMultiThd: localStorage.getItem('mobMultiThd') || 'no',
        mobMultiAdsl: localStorage.getItem('mobMultiAdsl') || 'no',
    };

    // Initialize button states
    buttons.forEach(button => {
        const param = button.dataset.param;
        if (params[param] === 'yes') button.classList.add('active');

        button.addEventListener('click', function () {
            const isActive = button.classList.toggle('active');
            params[param] = isActive ? 'yes' : 'no';
            localStorage.setItem(param, params[param]);

            const queryString = new URLSearchParams(params).toString();
            const apiUrl = `${window.location.pathname}?${queryString}&page=1`;
            window.location.href = apiUrl;
        });
    });

    // Update icon based on theme
    const updateIcons = theme => {
        buttons.forEach(button => {
            const img = button.querySelector('img');
            const src = theme === 'dark' ? button.dataset.srcDark : button.dataset.srcLight;
            img.src = src;
        });
    };

    // Detect theme and update icons
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    updateIcons(currentTheme);

    const observer = new MutationObserver(() => {
        const newTheme = document.documentElement.getAttribute('data-theme');
        updateIcons(newTheme);
    });

    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['data-theme'] });
});
</script>

<script>
document.addEventListener("DOMContentLoaded", () => {
    const skeletons = document.querySelectorAll('.skeleton-card');
    const realCards = document.querySelectorAll('.real-card');

    // Show skeletons first
    skeletons.forEach(skeleton => {
        skeleton.style.display = 'block';
    });

    realCards.forEach(card => {
        card.style.display = 'none'; // Initially hide real cards
    });

    // Simulate 2-second loading
    setTimeout(() => {
        // Hide skeletons
        skeletons.forEach(skeleton => {
            skeleton.style.display = 'none';
        });

        // Show actual cards
        realCards.forEach(card => {
            card.style.display = 'block';
            card.classList.add('loaded'); // Smooth transition
        });
    }, 2000); // 2 seconds delay
});

</script>
    <script>
    document.addEventListener("DOMContentLoaded", () => {
    const skeletons = document.querySelectorAll('.skeleton-card');
    const realCards = document.querySelectorAll('.custom-card');

    // Show skeletons first
    skeletons.forEach(skeleton => {
        skeleton.style.display = 'block';
    });

    realCards.forEach(card => {
        card.classList.add('loading'); // Initially hide real cards
    });

    // Simulate 2-second loading
    setTimeout(() => {
        // Hide skeletons
        skeletons.forEach(skeleton => {
            skeleton.style.display = 'none';
        });

        // Show actual cards
        realCards.forEach(card => {
            card.classList.remove('loading');
            card.classList.add('loaded');
        });
    }, 2000); // 2 seconds
});

    </script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const cards = document.querySelectorAll('.custom-card');
        const monthSelect = document.getElementById('month-year-select'); // Corrected ID

        cards.forEach(card => {
            card.addEventListener('click', () => {
                const detailkpiinseeCode = card.getAttribute('data-cluster-code');
                const code_cluster = "{{ code_cluster }}"; // Ensure code_cluster is defined in Twig

                // Check if detailkpiinseeCode and code_cluster are valid
                if (!detailkpiinseeCode || !code_cluster) {
                    console.error('detailkpiinseeCode or code_cluster is missing.');
                    return;
                }

                // Retrieve the selected month and year value
                const monthYearValue = monthSelect.value;

                // Check if a value is selected
            

                // Split the month and year
                const parts = monthYearValue.split('-');
                const moisArretCuivre = parts[0];
                const anneeArretCuivre = parts[1];

                // Construct the URL with additional parameters
                const url = new URL(`/prises/arretcu/detaille/rue/${code_cluster}/${detailkpiinseeCode}`, window.location.origin);

                // Add parameters only if they are not empty
                if (moisArretCuivre) {
                    url.searchParams.set('moisArretCuivre', moisArretCuivre);
                }
                if (anneeArretCuivre) {
                    url.searchParams.set('anneeArretCuivre', anneeArretCuivre);
                }

                // Redirect to the modified URL
                window.location.href = url.toString();
            });
        });
    });
</script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // IntersectionObserver to handle lazy loading
            const options = {
                root: null, // use the viewport
                rootMargin: '0px',
                threshold: 0.1 // trigger when 10% of the card is visible
            };

            const loadCard = (entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = 1; // Fade-in effect
                        observer.unobserve(entry.target); // Stop observing
                    }
                });
            };

            const observer = new IntersectionObserver(loadCard, options);
            const cards = document.querySelectorAll('.custom-card');
            cards.forEach(card => {
                observer.observe(card); // Start observing each card
            });
        });
    </script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
    const polygonsData = {{ interventionsplaceskpi.codesInsee|json_encode|raw }};

    if (polygonsData && Object.keys(polygonsData).length > 0) {
        const mapElement = document.getElementById("mapprisearretuc");
        const coords = JSON.parse(mapElement.dataset.coords);
        const zoomLevel = parseInt(mapElement.dataset.zoom);

        const map = L.map(mapElement.id, {
            center: coords,
            zoom: zoomLevel,
            zoomControl: false,
            attributionControl: false,
            maxBounds: L.latLngBounds(L.latLng(41.0, -5.0), L.latLng(51.5, 9.0)),
            maxBoundsViscosity: 1.0,
            minZoom: zoomLevel,
            maxZoom: 16,
        });

        const lightTileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            maxZoom: 18,
        }).addTo(map);

        const darkTileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
            maxZoom: 18,
        });

        const geoJsonStyle = {
            color: "#ea7362",
            weight: 1,
            opacity: 1,
            fillColor: "#ea7362",
            fillOpacity: 0.3,
        };

        Object.values(polygonsData).forEach((data) => {
            const polygonData = data.polygon;
            const geoJsonFeature = {
                type: "Feature",
                geometry: {
                    type: "Polygon",
                    coordinates: polygonData.coordinates,
                },
                properties: {
                    name: data.nomCommune || "Polygon",
                },
            };

            L.geoJSON(geoJsonFeature, { style: geoJsonStyle }).addTo(map);
        });

        const updateMapTheme = () => {
            const currentTheme = document.documentElement.getAttribute("data-theme");
            if (currentTheme === "dark") {
                map.removeLayer(lightTileLayer);
                darkTileLayer.addTo(map);
                map.getContainer().style.backgroundColor = "#0f181f";
            } else {
                map.removeLayer(darkTileLayer);
                lightTileLayer.addTo(map);
                map.getContainer().style.backgroundColor = "#fff";
            }
        };

        const observer = new MutationObserver(updateMapTheme);
        observer.observe(document.documentElement, { attributes: true, attributeFilter: ["data-theme"] });

        updateMapTheme();
    } else {
        console.error("No polygon data available.");
    }
});

</script>
