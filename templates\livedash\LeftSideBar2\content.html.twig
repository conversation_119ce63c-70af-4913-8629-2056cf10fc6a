<style>
:root {
    --link-color: gray;
    --link-hover-color: darkgray;
    --sidebar-link-color: #fff;
    --sidebar-background-color: #cbd1c7;
    --active-link-color: #fff;
    --divider-color: #222;
    --section-title-color: #333;
    --title-color:#222;
}

[data-theme="dark"] {
    --link-color: #f0f4f3;
    --link-hover-color: #d0d0d0;
    --sidebar-link-color: #fff;
    --sidebar-background-color: #6c757d;
    --active-link-color: #4b96c1;
    --divider-color: #ddd;
    --section-title-color: lightgray;
    --title-color:white;
}

.channel-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.channel-item a {
    text-decoration: none;
    color: var(--link-color);
}

.channel-item a:hover,
.channel-item a.active {
    color: var(--link-hover-color);
}

.sidebar-link {
    font-size: 32px;
    width: 220px;
    display: block;
    padding: 8px; 
    padding-left: 24px;
    border-radius: 10px;
    transition: background-color 0.3s;
    color: var(--sidebar-link-color);
    text-decoration: none;
}

.sidebar-link:hover {
    background-color: var(--sidebar-background-color);
    color: var(--sidebar-color);
}

.sidebar-link.active {
    background-color: var(--sidebar-background-color);
    color: var(--active-link-color); 
}

.sectionDivider {
    color: var(--divider-color);
}

.sidebar-header {
    text-align: center;
    margin-bottom: 10px;
    text-decoration: none;
}

.toggle-section-sideb2 {
    margin: 0; /* Remove or reduce margin to reduce space between items */
    text-decoration: none;
}

.toggle-section-sideb2 a {
    text-decoration: none;
}

.sidebar-section-titleactive {
    color: var(--section-title-color); 
    font-size: 16.2px;
    margin: 2px 0; 
    text-decoration: none;
    font-weight: bold;
     gap: 5px;
}

.sidebar-section-title {
    color: var(--section-title-color);
    font-size: 14.2px;
    text-decoration: none;
    margin: 1px 0; 
   
}
  .sidebar-section-titleactive i {
        margin-right: 8px; /* Ajustez la valeur selon vos besoins */
        font-size: 20px;
    }
    h4{
            color: var(--title-color);
    }
</style>



<div class="sidebar-header" id="eventTrigger">
<div style="margin-top: 15px;"><h4>Decouvrir</h4></div>
   

</div>

<div class="sectionDivider"></div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="#" class="sidebar-link active">
   
            <p class="sidebar-section-titleactive">      <i  class="bi bi-house"></i>   Serveurs</p>
        </a>
    </div>
</div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="" class="sidebar-link ">
            <p class="sidebar-section-titleactive"> <i class="bi bi-gear"></i> Quetes</p>
        </a>
    </div>
</div>


<script>
    const modal = document.getElementById("eventModal");
    const trigger = document.getElementById("eventTrigger");
    const closeBtn = document.querySelector(".close-btn");

    trigger.addEventListener("click", function () {
        modal.style.display = "block";
    });

    closeBtn.addEventListener("click", function () {
        modal.style.display = "none";
    });

    window.addEventListener("click", function (event) {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    });
</script>
