 <link rel="stylesheet" href="{{asset('styles/priseslivraisonmap/priseslivraisonmap.css')}}">
<div class="left-side-topbar">
     {% if showClusters %}
       <div class="nav-buttons-group">
        <div class="nav-button active">Ventes Brutes</div>
        <div class="nav-button">Ventes Validés</div>
        <div class="nav-button">Ventes Raccordé</div>
    </div>
    {% elseif showVentes %}
        <div class="nav-buttons-group">
        <div class="nav-button active">Ventes Brutes</div>
        <div class="nav-button">Ventes Validés</div>
        <div class="nav-button">Ventes Raccordé</div>
    </div>
      {% elseif showPrisesLivraison %}
         <div class="nav-buttons-group">
             <a href="{{ path('prises_livraison') }}" class="nav-button " > <i class="fas fa-home"></i></a>
               </div>
      {% elseif showClusterDetails %}
        <div class="nav-buttons-group">
             <a href="{{ path('prises_livraison') }}" class="nav-button " > <i class="fas fa-home"></i></a>
             <div class="nav-button active" > {{clusterDetails.details[0].kpi.libelle_cluster }}</div>
        </div>
        {% elseif showClusterDetailsRue %}
        <div class="nav-buttons-group">
        <a href="{{ path('prises_livraison') }}"  class="nav-button " > <i class="fas fa-home"></i></a>
         <a href="/clusters/details/{{ code_cluster }}" class="nav-button">
            {{ clusterDetailsRue[1].libelle_cluster }}
        </a>
        <div class="nav-button active" id="vill">
            {{ clusterDetailsRue[1].vill }}
        </div>
         </div>

        {% elseif showClusterDetailsRueVoie %}
        <div class="nav-buttons-group">
        <a href="{{ path('prises_livraison') }}"  class="nav-button " > <i class="fas fa-home"></i></a>
       <a href="/clusters/details/{{ code_cluster }}"id="libelleCluster" class="nav-button"> libelle</a>
        <a href="/clusters/details/rue/{{ code_cluster }}/{{ detailkpiinseeCode }}" class="nav-button" id="vill">vill</a>
        <div class="nav-button active" >{{ clusterDetailsRueNomVoie[0].nom_voie }}</div>
        </div>


        {% elseif showprisesArretcutest %}
            <div class="nav-buttons-group">
                  <div href="/prises/arretcu" class="nav-button active"> <i class="fas fa-home"></i>      </div>
            </div>
        {% elseif prisesArretcuCluster %}
            <div class="nav-buttons-group">
                {% if interventionsplaceskpi is not empty %}
                           <a href="{{ path('prisesArretcu') }}" class="nav-button"> 
                        <i class="fas fa-home"></i> 
                        </a>
                 <div href="" class="nav-button active">
                 {{ interventionsplaceskpi.libelle_cluster }} 
                    </div>
                {% else %}
                    <!-- Handle case when interventionsplaceskpi is empty -->
                    <div class="nav-button">No data available</div>
                {% endif %}
            </div>

        {% elseif showprisesArretcudetailleRue %}
            <div class="nav-buttons-group">
                {% if interventionsplaceskpi is not empty %}
                       <a href="{{ path('prisesArretcu') }}" class="nav-button"> 
                        <i class="fas fa-home"></i> 
                        </a>

           <a href="/prises/arretcu/detaille/{{ code_cluster }}" class="nav-button">
                             {{ interventionsplaceskpi.libelle_cluster }} 
                    </a>
                    <div href="" class="nav-button active">
                       {{ interventionsplaceskpi.ville }} 
                    </div>
                {% else %}
                    <div class="nav-button">No data available</div>
        {% endif %}
    </div>
      {% elseif showVentesRsuro %}
  <div class="nav-buttons-group">
    <a href="{{ path('rsuro_livraison') }}" class="nav-button"><i class="fas fa-home"></i></a>
    {% if clusterId  %}
    <a id="selectedNomClusterRue" class="nav-button active">noncluster</a>
     {% endif %}
</div>

      {% elseif showClusterDetailsDataliste %}
    {% elseif priseslivraisonmap %}
        {% else %}
            <div class="nav-buttons-group">
            <div class="nav-button active" >Ventes Brutes</div>
            <div class="nav-button">Ventes Validés</div>
            <div class="nav-button">Ventes Raccordé</div>
        </div>
        {% endif %}
    </div>

<div class="right-side-topbar">
    <div class="svg-topbar" id="sidebarToggleBtn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-chevron-left" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
        </svg>
    </div>

    <div class="svg-topbar" id="collapse-right-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-chevron-right" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
        </svg>
    </div>

    <div class="search-container">
        <input type="text" placeholder="Rechercher Ventes" class="search-input">
        <span class="search-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-search" viewBox="0 0 16 16">
                <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.*************.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.099zm-5.442 1.36a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11z"/>
            </svg>
        </span>
    </div>
</div>

<script>

    const libelle = localStorage.getItem("libelleCluster");
    const vill = localStorage.getItem("vill");
    


    document.getElementById("libelleCluster").innerText = libelle;
    document.getElementById("vill").innerText = vill;


</script>
<script>
    document.addEventListener("DOMContentLoaded", () => {
        const element = document.getElementById("selectedNomClusterRue");
        if (element) {
            const noncluster = localStorage.getItem("selectedNomClusterRue") || "noncluster";
            element.innerText = noncluster;
        }
    });
</script>