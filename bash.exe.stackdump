Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB36380000 ntdll.dll
7FFB35780000 KERNEL32.DLL
7FFB33C70000 KERNELBASE.dll
7FFB35320000 USER32.dll
000210040000 msys-2.0.dll
7FFB33B90000 win32u.dll
7FFB35B30000 GDI32.dll
7FFB33750000 gdi32full.dll
7FFB33BC0000 msvcp_win.dll
7FFB33570000 ucrtbase.dll
7FFB360E0000 advapi32.dll
7FFB34790000 msvcrt.dll
7FFB36000000 sechost.dll
7FFB34F90000 RPCRT4.dll
7FFB32AB0000 CRYPTBASE.DLL
7FFB334D0000 bcryptPrimitives.dll
7FFB36280000 IMM32.DLL
