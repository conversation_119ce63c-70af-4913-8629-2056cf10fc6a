<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class LoginController extends AbstractController
{
    private $httpClient;

    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    #[Route('/', name: 'app_login', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $session = $request->getSession();
        $jwt = $session->get('jwt');
    
        if ($jwt && $this->isTokenValid($jwt)) {
            return $this->redirectToRoute('geomap');
        }
    
        if ($request->isMethod('POST')) {
            $email = $request->request->get('emailAddress');
            $password = $request->request->get('password');
    
            try {
                $response = $this->httpClient->request('POST', 'https://api.nomadcloud.fr/api/auth', [
                    'headers' => [
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'email' => $email,
                        'password' => $password,
                    ],
                ]);
    
                $statusCode = $response->getStatusCode();
                $content = $response->toArray();
    
                if ($statusCode === 200) {
                    $jwt = $content['token']; 
                    $session->set('jwt', $jwt);
                    
                    return $this->redirectToRoute('geomap'); 
                } else {
                    $this->addFlash('api_response', 'Authentication failed. Please check your credentials.');
                }
            } catch (\Symfony\Component\HttpClient\Exception\ClientException $e) {
                $this->addFlash('api_response', 'Error: ' . $e->getMessage());
            } catch (\Symfony\Component\HttpClient\Exception\ServerException $e) {
                $this->addFlash('api_response', 'Server Error: ' . $e->getMessage());
            } catch (\Exception $e) {
                $this->addFlash('api_response', 'An error occurred: ' . $e->getMessage());
            }
        }
    
        return $this->render('login/index.html.twig');
    }
    
    /**
     * Validate the JWT token by checking its expiration date.
     */
    private function isTokenValid(string $jwt): bool
    {
        try {
            $parts = explode('.', $jwt);
            if (count($parts) !== 3) {
                return false;
            }
    
            $payload = json_decode(base64_decode($parts[1]), true);
            if (!isset($payload['exp'])) {
                return false;
            }
    
            return $payload['exp'] > time();
        } catch (\Exception $e) {
            return false;
        }
    }
    

}
