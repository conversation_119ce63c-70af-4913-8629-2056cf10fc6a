<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

<style>
* {
    font-family: 'Ubuntu', sans-serif;
}

  .bonus-cluster {
    border: 0.5px solid #e0e0e0;
    border-radius: 15px;
    padding: 15px;
    background: linear-gradient(to bottom, #b9ded6,#edf3ef); /* Gradient from top to bottom */
    display: flex;
    width: 50%;
    position: relative;
  }

  .bonus-expand-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 0.9rem;
    color: #777;
    cursor: pointer;
  }

  .bonus-container {
    display: flex;
    width: 100%;
  }

  .bonus-left-container {
    width: 40%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }

  .bonus-info-box {
    background-color: #68b3a3;
    color: white;
    border-radius: 10px;
    padding: 10px;
    width: 80%;
    text-align: center;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  }

  .bonus-info-box .amount {
    font-size: 1.6em;
    font-weight: bold;
  }

  .bonus-info-box .label {
    font-size: 0.9em;
    margin-top: 6px;
  }

  .bonus-info-box.border-box {
   background-color: transparent;
    border: 1px solid #c0e1e0;
    color: #555;
    margin-top: 10%; /* Adjust this value as needed */
    
  }

  .bonus-info-box.border-box .amount {
    color: #72ac9d;
  }

  .bonus-info-box.border-box .label {
    color: #888;
    font-weight: 500;
  }

  .main-bonus-label {
    font-size: 0.85em;
    color: black;
    text-transform: uppercase;
    font-weight: 600;
  }

  .main-bonus-label-month {
    font-size: 0.7em;
    color: black;
    text-transform: uppercase;
    font-weight: 600;
  }

  .main-bonus-label-avenir {
    font-size: 0.7em;
    color: red;
    text-transform: uppercase;
    font-weight: 600;
  }

  .bonus-chart-and-table {
    width: 60%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 12px;
  }

.chart-labels {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin-top: 8px; 
}

.chart-label {
    font-size: 0.8em; 
    color: #555; 
}

.chart-border {
    width: 100%;
}
.bonus-chart-container {
    height: 120px; 
    width: 100%;
    position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
}

  .bonus-sales-table-container {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 12px;
    width: 100%;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.08);
  }

  .bonus-sales-table {
    width: 100%;
    border-collapse: collapse;
  }

  .bonus-sales-table th, .bonus-sales-table td {
    text-align: left;
        padding: 1px 11px;
    font-size: 0.8em;
  }

  .bonus-sales-table th {
    font-weight: 600;
    color: #333;
  }

  .bonus-sales-table td {
    color: #72ac9d;
  }

  .bonus-sales-table tbody tr:nth-child(even) {
    background-color: #f4f7f7;
  }
</style>
    <i class="bonus-expand-icon bi bi-fullscreen"></i> 

<h4>Bonus par Cluster</h4>
<div class="bonus-container">
  <div class="bonus-left-container">
    <div class="bonus-info-box">
      <div class="main-bonus-label">Bonus</div>
      <div class="amount">7600€</div>
      <div class="main-bonus-label-month">October</div>
    </div>

    <div class="bonus-info-box border-box">
      <div class="amount">2497.8€</div>
      <div class="main-bonus-label-avenir">à venir</div>
    </div>
  </div>

  <div class="bonus-chart-and-table">
              <div class="bonus-chart-container">
            <canvas id="bonusChart"></canvas>
        </div>
    <div class="bonus-sales-table-container">
      <table class="bonus-sales-table">
        <thead>
          <tr>
            <th>Ville</th>
            <th>Obj.</th>
            <th>Ventes</th>
            <th>Bonus</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Lille</td>
            <td>13</td>
            <td>12</td>
            <td>2044€</td>
          </tr>
           <tr>
            <td>Lille</td>
            <td>13</td>
            <td>12</td>
            <td>2044€</td>
          </tr>
           <tr>
            <td>Lille</td>
            <td>13</td>
            <td>12</td>
            <td>2044€</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const startColor = '#5fadbc';
        const endColor = '#74b490';

        function getGradientColor(index, total) {
            const ratio = index / total;
            const r1 = parseInt(startColor.substring(1, 3), 16);
            const g1 = parseInt(startColor.substring(3, 5), 16);
            const b1 = parseInt(startColor.substring(5, 7), 16);
            const r2 = parseInt(endColor.substring(1, 3), 16);
            const g2 = parseInt(endColor.substring(3, 5), 16);
            const b2 = parseInt(endColor.substring(5, 7), 16);

            const r = Math.round(r1 + (r2 - r1) * ratio);
            const g = Math.round(g1 + (g2 - g1) * ratio);
            const b = Math.round(b1 + (b2 - b1) * ratio);

            return `rgb(${r}, ${g}, ${b})`;
        }

        const smilePlugin = {
            id: 'smilePlugin',
            afterDraw: (chart) => {
                const ctx = chart.ctx;
                const data = chart.data.datasets[0].data;
                const labels = chart.data.labels;
                const xScale = chart.scales.x;
                const yScale = chart.scales.y;

                ctx.font = '12px sans-serif';
                ctx.fillStyle = 'green'; 
                ctx.textAlign = 'center';

                data.forEach((value, index) => {
                    const x = xScale.getPixelForValue(index);
                    const y = yScale.getPixelForValue(value) + 15; 
                    ctx.fillText(':)', x, y);
                });
            }
        };

        document.addEventListener('DOMContentLoaded', () => {
            const ctx = document.getElementById('bonusChart').getContext('2d');
            const dataValues = [4, 8, 10, 12, 14, 14]; 
            const numBars = dataValues.length;

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['', '', '', '', '', ''],
                    datasets: [{
                        label: 'Ventes',
                        data: dataValues,
                        backgroundColor: dataValues.map((_, i) => getGradientColor(i, numBars)),
                        borderColor: 'rgba(0,0,0,0)', 
                        borderWidth: 0,
                        borderRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                display: false
                            },
                            grid: {
                                display: false
                            }
                        },
                        x: {
                            ticks: {
                                display: false
                            },
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        smilePlugin
                    }
                }
            });
        });
    </script>