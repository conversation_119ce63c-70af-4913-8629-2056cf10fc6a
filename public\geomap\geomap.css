

:root {
    --activeTab:#bdbdbd;
    --TopbarRightBlock:#eeeeee;
    --sectionDefault:#bdbdbd;
    --bg-tabs:#fff;
    --border-color-tabs:#cfcfcf;
    --color-sectionDefault:#333;
    --bg-table-section:#f0f0f0;
    --coleur-table-section:#161616;
    --color-FomsContent: #f9f9f9;  
    --dropdown-container-color: #cfcfcf;
    --content-concepteur-color:#eeeeee;
    --tree-view-bg:#f9f9f9;
    --tree-view-color:#1f2c34;
    --nested-bg:#ffffff ;
    --fieldSectionDetails-bg:#dbdbdb;
    --fieldSectionDetails-border:#dbdbdb;
    --fieldSectionDetails-color:#333;
    --groups-section-details-bg:#cfcfcf;
    --groups-section-details-color:#323232;
    --checkedFieldLine-span:#cfcfcf;
    --nav-link-active-bg:#cfcfcf;
    --Tree-Froms-scrolbar-bg:#fff;
    --Tree-Froms-scrolbar-bg-font:#dbdbdb;
    --headerTreeSection-bg:#dbdbdb;
    --PopUpBackground:#fff;
    --border-tree-view-caret:#161616;
    --border-fieldSpan:#ececec;
    --header-tabs-bg:#cfcfcf;
    --Sections-flex-column:#f0f0f0;
    --active-field-bg: #dbdbdb;
    --active-field-color: #333;
    --colorgroupspan:#cfcfcf;
    --TitreFormulaire-color:#464646;
    --TbodyTr-border:#dbdbdb;
    --skeleton-bg: #e0e0e0;
    --skeleton-shimmer: #cccccc;
    --resizeContent-bg:#eee9e5;
    --coloredTextPrimary:#145177;
    
}
[data-theme="dark"] {
    --activeTab:#373737;
    --TopbarRightBlock:#161616;
    --sectionDefault:#373737;
    --bg-tabs:#161616;
    --color-sectionDefault:#ccc;
    --border-color-tabs:8d8d8d;
    --bg-table-section:#161616;
    --coleur-table-section:#ccc;
    --color-FomsContent: #161616;   
    --dropdown-container-color: #262626;
    --content-concepteur-color:#323232;
    --tree-view-bg:#262626;
    --tree-view-color:#b0b0b0;
    --nested-bg:#323232;
    --fieldSectionDetails-bg:#373737;
    --fieldSectionDetails-border:#323232;
    --fieldSectionDetails-color:#ccc;
    --groups-section-details-bg:#161616;
    --groups-section-details-color:#cfcfcf;
    --checkedFieldLine-span:313131;
    --nav-link-active-bg:#232222;
    --Tree-Froms-scrolbar-bg:#161616;
    --Tree-Froms-scrolbar-bg-font:#323232;
    --headerTreeSection-bg:transparent;
    --PopUpBackground:transparent;
    --border-tree-view-caret:#161616;
    --border-fieldSpan:#323232;
    --header-tabs-bg:#323232;
    --backoftable-bg:#323232;
    --Sections-flex-column:#323232;
    --active-field-bg: #04395e;
    --active-field-color: #333;
    --colorgroupspan:#323232;
    --TitreFormulaire-color:#ccc;
    --TbodyTr-border:#323232;
    --skeleton-bg: #2e2e2e;
    --skeleton-shimmer: #444444;
    --tree-view-bg: #3b3b3b;
    --resizeContent-bg:#333;
    --coloredTextPrimary:#54C5d0;
}
[data-theme="darkblue"] {
    --activeTab:#333c4d;
    --TopbarRightBlock:#1d2636;
    --sectionDefault:#333c4d;
    --bg-tabs:#1d2636;
    --color-sectionDefault:#e9e9e9;
    --border-color-tabs:#333c4d;
    --bg-table-section:#1d2636;
    --coleur-table-section:#ccc;
    --color-FomsContent: #1d2636;   
    --dropdown-container-color: #292f39;
    --content-concepteur-color:#333c4d;
    --tree-view-bg:#292f39;
    --tree-view-color:#b0b0b0;
    --nested-bg:#1d2636;
    --fieldSectionDetails-bg:#333c4d;
    --fieldSectionDetails-border:#333c4d;
    --fieldSectionDetails-color:#e9e9e9;
    --groups-section-details-bg:#1d2636;
    --groups-section-details-color:#e9e9e9;
    --checkedFieldLine-span:#333c4d;
    --nav-link-active-bg:#292f39;
    --Tree-Froms-scrolbar-bg:#1d2636;
    --Tree-Froms-scrolbar-bg-font:#333c4d;
    --headerTreeSection-bg:#1d2636;
    --PopUpBackground:#1d2636;
    --border-tree-view-caret:#fff;
    --border-fieldSpan:#292f39;
    --header-tabs-bg:#333c4d;
    --backoftable-bg:#333c4d;
    --Sections-flex-column:#333c4d;
    --active-field-bg: #04395e;
    --active-field-color: #fff;
    --colorgroupspan:#04395e;
    --TitreFormulaire-color:#ccc;
    --TbodyTr-border:#333c4d;
    --skeleton-bg: #1d2636;
    --skeleton-shimmer: #333c4d;
    --tree-view-bg: #333c4d;
    --TitreFormulaire-bg:#292f39;
    --resizeContent-bg:#292f39;
    --coloredTextPrimary:#aac7ff;
    --drawLine-bg:#1d2636;
}
[data-theme="darkpurple"] {
    --activeTab:#342c3d;
    --TopbarRightBlock:#11091b;
    --sectionDefault:#342c3d;
    --bg-tabs:#11091b;
    --color-sectionDefault:#e9e9e9;
    --border-color-tabs:#342c3d;
    --bg-table-section:#11091b;
    --coleur-table-section:#ccc;
    --color-FomsContent: #11091b;   
    --dropdown-container-color: #251e2e;
    --content-concepteur-color:#342c3d;
    --tree-view-bg:#251e2e;
    --tree-view-color:#b0b0b0;
    --nested-bg:#11091b;
    --fieldSectionDetails-bg:#342c3d;
    --fieldSectionDetails-border:#342c3d;
    --fieldSectionDetails-color:#e9e9e9;
    --groups-section-details-bg:#11091b;
    --groups-section-details-color:#e9e9e9;
    --checkedFieldLine-span:#342c3d;
    --nav-link-active-bg:#251e2e;
    --Tree-Froms-scrolbar-bg:#11091b;
    --Tree-Froms-scrolbar-bg-font:#342c3d;
    --headerTreeSection-bg:#11091b;
    --PopUpBackground:#11091b;
    --border-tree-view-caret:#fff;
    --border-fieldSpan:#251e2e;
    --header-tabs-bg:#342c3d;
    --backoftable-bg:#342c3d;
    --Sections-flex-column:#342c3d;
    --active-field-bg: #04395e;
    --active-field-color: #fff;
    --colorgroupspan:#04395e;
    --TitreFormulaire-color:#ccc;
    --TbodyTr-border:#342c3d;

    
    --skeleton-bg: #3b2d4f;
    --skeleton-shimmer: #11091b;
    --tree-view-bg: #342c3d;
    --TitreFormulaire-bg:#251e2e;
    --resizeContent-bg:#251e2e;
    --coloredTextPrimary:#54C5d0;
}
[data-theme="lightsand"] {
    --activeTab:#d5c9bd;
    --TopbarRightBlock:#fbfaf9;
    --sectionDefault:#d5c9bd;
    --bg-tabs:#fbfaf9;
    --border-color-tabs:#e9e9e9;
    --color-sectionDefault:#464646;
    --bg-table-section:#f9f9f9;
    --coleur-table-section:#161616;
    --color-FomsContent: #fbfaf9;  
    --dropdown-container-color: #e9e9e9;
    --content-concepteur-color:#eeeeee;
    --tree-view-bg:#f9f9f9;
    --tree-view-color:#1f2c34;
    --nested-bg:#eee9e5;
    --fieldSectionDetails-bg:#d5c9bd;
    --fieldSectionDetails-border:#d5c9bd;
    --fieldSectionDetails-color:#464646;
    --groups-section-details-bg:#fbfaf9;
    --groups-section-details-color:#464646;
    --checkedFieldLine-span:#eee9e5;
    --nav-link-active-bg:#eee9e5;
    --Tree-Froms-scrolbar-bg:#fff;
    --Tree-Froms-scrolbar-bg-font:#d5c9bd;
    --headerTreeSection-bg:#fbfaf9;
    --PopUpBackground:#fbfaf9;
    --border-tree-view-caret:#161616;
    --border-fieldSpan:#eee9e5;
    --header-tabs-bg:#e9e9e9;
    --Sections-flex-column:#e9e9e9;
    --active-field-bg: #eee9e5;
    --active-field-color: #333;
    --colorgroupspan:#e9e9e9;
    --TitreFormulaire-color:#464646;
    --TbodyTr-border:#eee9e5;

    --skeleton-bg: #eee9e5;
    --skeleton-shimmer: #d5c9bd;
    --tree-view-bg: #d5c9bd;
    --TitreFormulaire-bg:#eee9e5;
    --resizeContent-bg:#eee9e5;
    --coloredTextPrimary:#54C5d0;
}
[data-theme="darklight"] {
    --activeTab:#bdbdbd;
    --TopbarRightBlock:#f0f0f0;
    --sectionDefault:#bdbdbd;
    --bg-tabs:#f0f0f0;
    --border-color-tabs:#e9e9e9;
    --color-sectionDefault:#464646;
    --bg-table-section:#f9f9f9;
    --coleur-table-section:#161616;
    --color-FomsContent: #f9f9f9;  
    --dropdown-container-color: #e9e9e9;
    --content-concepteur-color:#eeeeee;
    --tree-view-bg:#f9f9f9;
    --tree-view-color:#1f2c34;
    --nested-bg:#dbdbdb;
    --fieldSectionDetails-bg:#bdbdbd;
    --fieldSectionDetails-border:#bdbdbd;
    --fieldSectionDetails-color:#464646;
    --groups-section-details-bg:#f0f0f0;
    --groups-section-details-color:#464646;
    --checkedFieldLine-span:#dbdbdb;
    --nav-link-active-bg:#dbdbdb;
    --Tree-Froms-scrolbar-bg:#fff;
    --Tree-Froms-scrolbar-bg-font:#bdbdbd;
    --headerTreeSection-bg:#f0f0f0;
    --PopUpBackground:#f0f0f0;
    --border-tree-view-caret:#161616;
    --border-fieldSpan:#bdbdbd;
    --header-tabs-bg:#f9f9f9;
    --Sections-flex-column:#f9f9f9;
    --active-field-bg: #dbdbdb;
    --active-field-color: #333;
    --colorgroupspan:#f9f9f9;
    --TitreFormulaire-color:#464646;
    --TbodyTr-border:#dbdbdb;
 
    --skeleton-bg: #bdbdbd;
    --skeleton-shimmer: #cccccc;
    --tree-view-bg: #dbdbdb;
    --TitreFormulaire-bg:#dbdbdb;
    --resizeContent-bg:#dbdbdb;
    --coloredTextPrimary:#54C5d0;

}
[data-theme="Darkgreen"] {
    --activeTab:#273125;
    --TopbarRightBlock:#232721;
    --sectionDefault:#273125;
    --bg-tabs:#232721;
    --color-sectionDefault:#e9e9e9;
    --border-color-tabs:#273125;
    --bg-table-section:#030303;
    --coleur-table-section:#ccc;
    --color-FomsContent: #232721;   
    --dropdown-container-color: #3a3e39;
    --content-concepteur-color:#273125;
    --tree-view-bg:#3a3e39;
    --tree-view-color:#b0b0b0;
    --nested-bg:#232721;
    --fieldSectionDetails-bg:#273125;
    --fieldSectionDetails-border:#273125;
    --fieldSectionDetails-color:#e9e9e9;
    --groups-section-details-bg:#232721;
    --groups-section-details-color:#e9e9e9;
    --checkedFieldLine-span:#273125;
    --nav-link-active-bg:#3a3e39;
    --Tree-Froms-scrolbar-bg:#232721;
    --Tree-Froms-scrolbar-bg-font:#273125;
    --headerTreeSection-bg:#232721;
    --PopUpBackground:#232721;
    --border-tree-view-caret:#fff;
    --border-fieldSpan:#3a3e39;
    --header-tabs-bg:#273125;
    --backoftable-bg:#273125;
    --Sections-flex-column:#273125;
    --active-field-bg: #24501b;
    --active-field-color: #fff;
    --colorgroupspan:#04395e;
    --TitreFormulaire-color:#ccc;
    --TbodyTr-border:#273125;
    --skeleton-bg: #232721;
    --skeleton-shimmer: #273125;
    --tree-view-bg: #273125;
    --TitreFormulaire-bg:#3a3e39;
    --resizeContent-bg:#3a3e39;
    --coloredTextPrimary:#97cd8f;
    --drawLine-bg:#232721;
}
.mb-3 {
    margin-bottom: 0 !important;
}

.form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 0;
    margin-bottom: .125rem;
}


.modal-options {
    position: fixed;
    top: 70%;
    left: 68.5%;
    transform: translate(-50%, -50%);
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 2000;
    display: none;
    background-color: var(--modal-content);
    border-radius: 10px;
    padding: 15px;
}

.modal-content-options {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* width: 200px; */
    height: auto;
    max-height: 160px;
    padding:11px;
}

.modal-header-options {
    display: flex;
    
    align-items: center;
    width: 100%;
    border-bottom: 1px solid #5d5858;
    
    gap: 15px;
}

.option-item{    color: #A9ACAF;}
.btn-option-add{    display: inline-block;
    width: 25px;
    height: 25px;
    padding: 0px;
    margin-top: 5px;
}

#option-input {
    background-color: var(--form-control-inputs);
    transition: background-color 0.3s ease; /* Optional for smooth effect */
}

#option-input:focus {
    background-color: var(--form-control-inputs); /* Ensure consistency when focused */
}





.options-content::-webkit-scrollbar {
  width: 4px !important;
  
}

.options-content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  background-color:#ccc;
}
.options-content{
margin-top: 20px;width: 100%; overflow: auto;
}

.options-items {
    margin-right: 5px;
    background-color: #5d5b5b;
    padding: 7px;
    border-radius: 10px;
}
.options-items-icon:hover{
    background-color: #424141;
}
.options-items-icon{
    padding: 5px;
    border-radius: 6px;
}


.bootstrap-tagsinput .tag {
    margin-right: 2px;
    color: white;
    background-color: #575656;
    padding: 5px;
    border-radius: 7px;

}

.bootstrap-tagsinput{background-color: var(--sidebar-color);border:none;    width: 58%;
    height: auto;    padding: 7px;
    }

    .bootstrap-tagsinput input {
    border: none;
    box-shadow: none;
    outline: none;
    background-color: transparent;
    padding: 0;
    margin: 2px;
    width: 66px;;
    max-width: inherit;
    color: var(--text-placholder);
}
.nav-tabs{
    --bs-nav-tabs-border-radius:none;
    border-radius:8px;
   
}

.option-btn-group-item:hover{background: var(--sidebar-color);border-radius:8px;}

/* right panel for create model form */
#slidingPanelForms {
    position: absolute;
    top: 31%;
    left: 50%;
    width: 50%;
    height: 60%;
    background-color: var(--modal-content);
    z-index: 2;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    display: none;
    transition: left 0.3s ease-in-out;
    border-radius: 8px;
        border: 1px solid #464343ad;
}

/* When open */
#slidingPanelForms.open {
    left: 100%; /* Slide into view */
}

.sliding-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: transparent;
    color: black;
}

.sliding-panel-content {
    padding: 15px;
    overflow-y: auto;
    height:84%;color: #b9b9b9;
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #1b1b1b #2d2d2d;
}

/* For WebKit-based browsers (Chrome, Edge, Safari) */
.sliding-panel-content::-webkit-scrollbar {
    width: 8px; /* Width of the scrollbar */
}

.sliding-panel-content::-webkit-scrollbar-thumb {
    border-radius: 10px; /* Round edges of the thumb */
}


.sliding-panel-content::-webkit-scrollbar-track {
    border-radius: 10px; /* Round edges of the track */
}


.close-panel-btn {
    background: none;
    border: none;
    color: black;
    font-size: 30px;
    cursor: pointer;
}

.accordion-header {
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.accordion-header:hover {
    background-color: #e9ecef;
}

.accordion-body {
    border-top: 1px solid #ddd;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
/* .accordion-body li {
    padding: 5px 10px;
    border-bottom: 1px solid #343434
    transition: background-color 0.3s;
} */

.formSpan{
    position:relative;
}

.active-field::before {
    content:"";
    background-color: var(--active-field-bg);
    border: 1px solid var(--coloredTextPrimary);
    color: var(--active-field-color);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 2px;
    left: 0;
}


.accordion-body li:hover {
    background-color: #f8f9fa;color: black;
}
.first-sidebar-section:hover {background-color:#262626;}
 .tree-view ul {
    list-style-type: none;
    margin-bottom: 60px;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    font-size: 14px;
    font-family: 'Ubuntu';
    border-bottom:1px solid var(--border-fieldSpan) !important;
    margin-bottom: 74px;
    }
    .tree-view li {
       cursor: pointer;
    /* border-bottom:1px solid var(--border-fieldSpan); */
    position: relative;
    display: flex;
    flex-direction: column;
    }
    .tree-view .caret {
        user-select: none;
        border-bottom: 1px solid var(--border-fieldSpan);
        /* border-right: 1px solid var(--border-fieldSpan); */
    }
    .flecheimg{
        width:13px;margin-left: 3px;margin-right: 5px;
    }
    .tree-view .caret::before {
        /* content: "\25B6"; */
        display: inline-block;
        margin-right: 6px;
        margin-top: -2px;
    }
    .tree-view .caret-down .flecheimg{
        transform: rotate(90deg);
    }
    .caret .flecheimg {
        transform: rotate(0deg);
        transition: transform 0.3s ease;
    }
    .microIcon{
        color: var(--coloredTextPrimary);
    position: absolute;
    right: 1px;
    top: 50%;
    transform: translateY(-50%);
    }
    /* CSS for rotated caret */
    .caret-down .flecheimg {
        transform: rotate(0deg);
    }
    .tree-view .nested {
        display: none;
       
    }
    .tree-view .nested{
        border: 1px solid var(--border-fieldSpan); 
        background-color: var(--headerTreeSection-bg);
       
    }
    .tree-view .active {
        display: block;
        margin: 0;
        
    }
    .data{content:"\F8BE";}
    .nested{
    background-color:var(--nested-bg);
    /* margin-top: 9px; */
    }
    .tree-view{
    background-color: var(--tree-view-bg);
    border-radius: 5px;
    color: var( --tree-view-color);
        display: flex;
    }
.Tree-Froms,.Tree-data-content ,.Tree-Roles-content{
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    background: var(--tree-view-bg);
}
 .Tree-Formulaires{
    height: 40%;
    flex-grow: 1;
    overflow: hidden;
}
.Tree-Formulairees{
    height: 55%;
    flex-grow: 1;
    overflow: hidden;
}
.Tree-Roles { 
    height: 28%;
    flex-grow: 1;
    overflow: hidden;
}
.Tree-Rolees { 
    height: 40%;
    flex-grow: 1;
    overflow: hidden;
}
.content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-right: 1px solid var(--sidebar-color);
    height: 89vh;
    flex-grow: unset;
}
.resizer,.resizer2,.resizer3 {
        display: flex;
        height: 10px;
        background: var(--nav-link-active-bg);
        cursor: row-resize;
        align-items: flex-end;
        justify-content: space-around;
        font-size: 20px;
    }

.Tree-data-container,
.Tree-Formulaires-container,
.Tree-Roles-container {
    position: absolute;
    width: 100%;
    transition: height 0.2s;
}


.scrollbar-custom::-webkit-scrollbar {
    width: 9px;
    background-color: var(--Tree-Froms-scrolbar-bg);
  }
  
  .scrollbar-custom::-webkit-scrollbar-thumb {
    background-color: var(--Tree-Froms-scrolbar-bg-font);
    border-radius: 4px;
  }
  
  .scrollbar-custom::-webkit-scrollbar-corner {
    background-color: var(--Tree-Froms-scrolbar-bg);
  }
  
  .scrollbar-custom::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px var(--Tree-Froms-scrolbar-bg) !important;
    border-radius: 10px;
  }
  

.table-responsive{overflow: auto;height: 75vh;background-color: var(--color-FomsContent);}
/* .ViewListForm{overflow: auto;    width: 99%;} */
.FomsContent{overflow: auto;width: 99%;}
.table-bordered{font-size: 14px;font-family: 'Ubuntu';overflow:auto;background-color: var(--color-FomsContent);}
.fieldsDisplayDetailsContainer{
    padding: 1px;
    /* overflow: auto; */
    height: 87vh;
    background: var(--chat-area-background);
}
.groupSpan{  padding-left: 26px;    height: 26px;display: flex;
        align-items: center;
        gap: 4px;}
 .colorgroupspan {
    width:25px; 
    background-color:var(--colorgroupspan);
 }      
.formSpan {
    display: flex;
    /* margin-left: 8px; */
    padding: 4px;
    border-bottom: 1px solid var(--border-fieldSpan);
    width: 100%;
}
.fieldLiSpan {
    padding: 4px 0px 1px 33px;
    border-bottom: 1px solid var(--border-fieldSpan);
    /* border-right: 1px solid var(--border-fieldSpan); */
    height: 26px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 295px;
}

/*
#search-input{
    height: 30px;
    margin-bottom: 3px;
    width: 100%;
    padding: 8px;
    background-color: #000000;
    border-radius: 8px;
    border: none;
    padding-top: 5px;
    margin-top: 2px;
    color: white;
    margin-top: 5px;
}*/

.bi-table{cursor: pointer;
    margin-left: 11px;
    }


.field-row span {
    display: inline-block;
    width: 150px; /* Adjust width as needed */
    margin-right: 10px;
}

.sectionDefault {
    display: block;
    background-color: var(--sectionDefault);
    border-bottom:none;
    border-top-right-radius:5px;
    padding: 6px;
    color: var(--color-sectionDefault);
    border-top: #373737;
        padding-left: 32px;
}

.groups-section-details{
    display: flex;position: relative;
    background-color: var(--groups-section-details-bg);
    color: var(--groups-section-details-color);}
.groups-details{    display: flex;
    flex-direction: column;width: 100%}

.groupDetailsSpan{
    width: 100%;
    border-bottom: 1px solid var(--border-fieldSpan);
    padding: 3px;    padding-left: 10px;height: 26px;
}
.fieldSpan{
    border: 1px solid var(--border-fieldSpan);
    padding: 3px;    padding-left: 10px;
    background-color: var(--bg-table-section);
}
.fieldSectionDetails {
   background-color: var(--fieldSectionDetails-bg);
    padding: 6px;
    border-bottom: 1px solid var(--fieldSectionDetails-border);
    display: flex;
    color: var(--fieldSectionDetails-color);
    }
.fieldSpanwithouBottom{
    border-bottom: none;
}
.colorFieldDetails{color: #5e5e5e;width: 200px;overflow: hidden;
    text-overflow: ellipsis;}
.Tab-details-fields{
    background-color: var(--fieldSectionDetails-bg);
    border-bottom: 1px solid var(--fieldSectionDetails-border);
    border-radius: 0;
}
.nav-link.active{
    border-radius: 5px;
    background-color: #323232;
    padding-left: 20px;
}
#formulaireContainer{
    background-color: #323232;
}
.fieldSpan:last-child {
    flex-grow: 1; /* Allows this span to grow and fill remaining space */
    overflow: hidden;
    text-overflow: ellipsis;
}
.material-icons {
    font-size: 14px;
    color: var(--active-field-color);
}

/* .nav-link.active{background-color: #373737 !important;} */

.nav-link {width:135px;padding: 0;height:30px;}
.fieldTabs{
    width: 135px;
    padding: 0;
    height: 30px;
    padding: 7px;
    margin: 0 !important;
    border-radius: 0 !important;
}
.TitreFormulaire { height: 50px;
    padding: 12px;
    color: var(--TitreFormulaire-color);
    font-size: 22px;
    background-color: var(--TitreFormulaire-bg);
}
.tabs{
    background-color: var(--TitreFormulaire-bg);
}
#nav-tab1-pane,#nav-tab3-pane,#nav-tab2-pane{padding:0;}

.tabs {
    display: flex; /* Aligns tabs horizontally */
  }
  
  .tab {
    display: inline-block;
    padding: 6px 11px;
    color: white;
    background-color: var(--bg-tabs);
    font-family: Arial, sans-serif;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
    border: none;
    margin-right: -10px;
    position: relative;
  }
  
  
  
  .first-tab::before,
  .other-tab::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    clip-path: inherit;
    z-index: -1;
  }
  
  
  /* .other-tab {
    clip-path: polygon(0% 0, 82% 0, 100% 100%, 15% 100%);
  
  } */
  .other-tab, .first-tab {
    /* transform: skew(30deg, 0deg); */
    border-bottom: 1px solid var(--border-color-tabs);
    border-left: none;
    color: #5e5e5e;
    font-size: 14px;
    width: 162px;
   height:30px;
  }
  
  /* .other-tab-withoutSkew {
    transform: skew(-30deg, 0deg);
  } */
  /* .tab:hover {
    background-color: #373737;
  }
   */
  .activeTab{
    background-color:var(--activeTab);
 
    color:#5bb99a;
  }

.table-section{
    background-color: var(--bg-table-section); 
    color: var(--coleur-table-section);
}


  .spanEditable:focus {
    outline: 1px solid rgb(82, 162, 232);
    border-radius: 0;
    z-index: 2;
    /* background-color: transparent !important; */
  }
  
  /* .spanEditable:focus-visible {
    outline:  1px solid rgb(82, 162, 232);
    border-radius: 0;

  } */


  .savefileToForm {
    background-color: rgb(45, 140, 235);
    border-radius: 5px;
    height: 26px;
    color: #fff;
    /* border: 1px solid #797979; */
    border: none;
}

  .newFormAppended{
    background-color: #355e04;
    border: 1px solid #3afe04;
    border-radius: 3px;
    color: #fff;
    border-bottom: 1px solid #3afe04 !important;
  }

  .headerTreeSection{
    display: flex;    height: 34px; align-items: center; justify-content: space-between; cursor: pointer; padding: 10px; margin-left: 0px; margin-top: 7px; width: 100%;
    background-color:var(--headerTreeSection-bg) ;
  }
  .headerTreeSection:hover{
    /*background-color:var(--channel-item-hover);*/
    /*border-radius: 4px;*/
    }
  .event-fileUploadForm{padding-left:28px;}
  .form-control:focus{background-color: transparent;}
  input.form-control:focus {
    outline: none;
    border: none;
    box-shadow: 0 0 0 2px #6c6c6c;
}
input.form-control::placeholder {
    color: #888;
    opacity: 1;
}


.ListViewForm{
    border-radius: 8px;
}
.input-eye{height: 30px;border-radius: 4px;color: #d0d0d0;}
.fields-container{
    display: grid;
        border-radius: 7px;
        padding: 0;
        padding-bottom: 35px;
        background-color: transparent;
        font-size: 12px;
        font-family: 'Ubuntu';
        gap: 18px;
        height: 100%;
}
.SectionsVertically{
    width: 98%;
    margin-left: 0;
    transition: none;
    margin-bottom: 5px;
    border-radius: 4px !important;
}
.sectionsHorizontally{
    display: flex;
    justify-content: space-between;
    padding: 18px 18px 0 18px;
}
.Sections-flex-column {
    margin-right: 3rem;
    /* background-color: #2D2E31; */
    background-color: var( --Sections-flex-column);
    max-height: 80%;
    height: auto;
    padding: 5px;
    border-radius: 6px;
    margin-top: 0;
    width: 130px;
    border: 1px solid var(--border-tabs);
}

.nav-link.active {
    background-color: var( --nav-link-active-bg) !important;
}
.form-group {
    padding-top: 18px;
    padding-left: 18px;
    padding-right: 18px;
}
/* #formTitleForListView {
    display: flex;
        height: 36px;
        padding: 7px;
        font-size: 20px;
        color: #ccc;
} */

.input-label-Name{margin-bottom:4px;    width: 94%;}
.right-align {
    font-size: 12px;color: var(--items-field);margin-bottom: 32px;
}
.header-tabs{height: 40px;background: var( --header-tabs-bg);width: auto;}

.contentListView{
    margin-right: 30px;
    padding-top: 30px;margin-left: 30px; display: flex;
}
.contentListViewAllScreen{
    margin: 0 16%;
}
.field-row{
    margin-bottom: -32px;
    display: grid; 
    gap: 18px;
}
.form-field-eye{
    display: flex;
    flex-direction: column;
    grid-column-end: span 1;
    position: relative;

}

.table .thead-dark{
    color: #8f8e8e;
    background-color: #2d2d2d;
}


.table .thead-dark .TheadTh {
    color: #8f8e8e;
    width: 8%;
    align-content: center;
    height: 28px;
    padding: 5px;
    font-weight: normal;
}

.table .TbodyTr{
    border-color:var(--TbodyTr-border);
}
.dropdown-container{
    padding: 10px;
    display: flex;
    background: var(--dropdown-container-color);
}
.custom-checkbox .checkmark {
    width: 13px;
    height: 13px;
    border: 2px solid #5c5c5c;
    border-radius: 0;
    /* background-color: #323232; */
    display: inline-block;
    position: relative;
}
.valueDataTableStyle {
    color: #b0b0b0;
    height: 20px;
    max-width: 65px;
    align-content: center;
    font-size: 11px;
}
/* .table-bordered>:not(caption)>*>* {
    padding: 0px;align-content: center;border-right: 1px solid #323232;
} */
.table-bordered>:not(caption)>*>* {
    padding: 0px;
    align-content: center;
    border: 1px solid #323232;
}
.headerContainerlisteView {
    border: none;
    display: flex;
    border-radius: 4px;
    margin-bottom: -8px;
}
.headerlisteView{
    border: none;
    padding: 18px;
    padding-bottom: 0;
}
.table-bordered>:not(caption)>*> {
    padding: 3px;
    align-content:center;
}
/* .table-responsive::-webkit-scrollbar {
    width: 9px;
    background-color: #fff;
  }
  .table-responsive::-webkit-scrollbar-thumb {
    background-color: #fff;
    border-radius: 4px;
  }
.table-responsive::-webkit-scrollbar-corner {
    background-color: #fff;
  }
.table-responsive::-webkit-scrollbar-track{
    box-shadow: inset 0 0 5px #fff !important;
    border-radius: 10px;
  } */

  .FomsContent-color{
    background: var( --color-FomsContent);   
    height: 75vh;
  }

  .content-concepteur{
    padding: 0;
    background-color: #323232;
    height: 95vh;
  }
  
  /* .fieldbyGroup{
    background-color:#fff!important ;
    color:#e9e9e9 ;
  } */

  .checkedFieldLine{
    padding: 0; 
    width:25px;
     border:1px solid var(--border-fieldSpan); 
     display: flex;
  }
  .checkedFieldLine span{
    width: 100%; 
    /* border: 1px solid var(--border-fieldSpan);  */
    background-color: var(--checkedFieldLine-span);
  }


  .backoftable{
    background: var(--backoftable-bg);
    border-radius: 4px;
}

  .ULInputType {
    position: absolute;
    bottom: 100%;
     left: 203px;
    background: #111214;
    border-radius: 5px;
    padding: 10px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px;
    z-index: 100;
    width: 210px;
  }



  .switch {
    position: relative;
    display: inline-block;
    width: 34px;
    height: 20px;
    /* top: 4px;
    left: 10px; */
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--fieldSectionDetails-bg);
    transition: 0.1s; /* Shorten for quicker response or remove completely */
    border-radius: 20px;
    will-change: transform, background-color;
}

.slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
    background-color: var(--nav-link-active-bg);
    transition: 0.1s; /* Shorten for quicker response or remove completely */
    border-radius: 50%;
}


input:checked + .slider {
    background-color:rgb(45, 140, 235);
}

input:checked + .slider:before {
    transform: translateX(14px);
}


.input-eye-datePicker {
    padding-right: 20px;
    width: 100%;
    -webkit-appearance: none;
    appearance: none;
    position: relative;
    z-index: 1;
}

.input-eye-datePicker::-webkit-calendar-picker-indicator {
    font-size: 20px;
    z-index: 3;
    /* filter: invert(100%) sepia(100%) saturate(500%) hue-rotate(180deg); */
   -webkit-filter: invert(1);
}

.SectionsVertically{padding-left: 0 !important;}


.concepteur-area {
    display:none;
    width: 100%;
    transition: width 0.5s ease;    margin-right: 0;
    overflow-y: auto;
    overflow-x: hidden;
}
.concepteurAndRelations{
    width: 100%;
}
.concepteurAndRelations, .formContent {
    flex-grow: 1;
}
.formContent{overflow: auto;}
.resizeContent {
    background: var(--resizeContent-bg);
    cursor: ew-resize;
    width: 10px;
    flex-shrink: 0  ;
    z-index: 1;
}
.concepteur-area, .relations-container {
    min-height: 100px;
}

.resizerConcepteurRelation {
    cursor: ns-resize;
    height: 10px;
    background: var(--resizeContent-bg);
}
.right-sidebar-concepteur {
    height: 100%; 
    position: absolute; 
    right: -319px;
    top: 40px;
    transition: right 0.5s ease-in-out;
}

.table .thead-dark th {
    background-color: var(--highlight-color);
}



.modal-event .form-select {
    color: var(--text-color);
}

.modal-event .form-select option {
    background-color: var(--option-bg, #333);
    color: var(--text-color);
}

.RolesOption:hover {
    background-color: var(--option-hover-bg, #555);
    color: var(--text-color);
}



.customTab {
    overflow: hidden;
    background-color: var(--bg-tabs);
    color: #5e5e5e;
    border: 1px solid var(--border-fieldSpan);
    padding: 2px;
  }

  .customTab button.customTabLinks {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 9px 16px;
    transition: 0.3s;
    font-size: 15px;
    color: var(--channel-item-color);
  }

  /* .customTab button.customTabLinks:hover {
    background-color: #999999;
  } */

  .customTab button.customTabLinks.active {
    background-color: var(--activeTab);
    color: #5bb99a;
    border-radius: 8px;
  }

  .customTabContent {
    display: none;
    border-top: none;
  }
  .dragging {
    opacity: 1;
}


/* 
.skeleton {
    background-color: var(--skeleton-bg, #e0e0e0);
    position: relative;
    overflow: hidden;
    border-radius: 4px;
}


.skeleton::before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--skeleton-shimmer, #cccccc), transparent);
    animation: shimmer 1.5s infinite;
}


@keyframes -moz-loading {
    0% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
    100% {
        left: -100%;
    }
} */


/* .sectionDivider.skeleton {
    height: 2px;
    margin: 5px 0;
    background-color: var(--skeleton-bg, #e0e0e0);
}


.Tree-Froms.skeleton,
.Tree-Roles.skeleton {
    margin-top: 5px;
    border-radius: 4px;
}


.tree-view.skeleton {
    background-color: var(--tree-view-bg);
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    padding: 10px;
    gap: 2px;
}

.tree-view.skeleton ul li {
    background-color: var(--skeleton-bg, #e0e0e0);
    border-radius: 4px;
    height: 20px;
    margin-bottom: 10px;
    animation: shimmer 1.5s infinite;
}


.formListItem.skeleton {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding: 10px;
    background-color: var(--skeleton-bg, #e0e0e0);
    border-radius: 4px;
}


.formListItem .formSpan::before {
    content: '';
    display: block;
    height: 8px;
    background-color: var(--skeleton-bg, #e0e0e0);
    border-radius: 4px;
    margin-bottom: 2px;
    animation: shimmer 1.5s infinite;
}


.formListItem .input-skeleton {
    background-color: var(--skeleton-bg, #e0e0e0);
    border-radius: 4px;
    margin-bottom: 10px;
    animation: shimmer 1.5s infinite;
} */


.draggingField {
    opacity: 0.5;
    background-color: #f0f0f0;
}

.draggingField .fieldNameDisplay,
.draggingField .typeField {
    opacity: 0.7;
}
/* .concepteur-area, .relations-container {
    transition: height 0.3s ease-in-out;
} */





.paneliconsConcepteur{
    width: 38px;
    height: 100vh;
    background: var(--panel-background)  !important;
    position: absolute;
    
}
.rightPannel{right:42px;}
.paneliconsConcepteur .icon{
        justify-self: center;
        left: 50%;
        color: var(--icon-color);
        margin-top: 10px;
        padding: 10px;
}


/* :root {
    --background-color: #e1e1e1;
    --panel-background: #f0f0f0;
    --border-color: transparent;
    --icon-color: #e1e1e1; 
    --text-color: #000;
    --highlight-color: #f0f0f0;
    --hover-border-color: #fff;
    --schedule-select-input: #e1e1e1;
    --border-schedule-select-input: #e1e1e1;
    --schedule-select-input-cl: #333;
    --first-color: #5e5e5e;
    --header-color: #333;
    --bg-day: #5e5e5e;
    --bg-active-day: #0078d4;
    --color-day: #B0B0B0;
    --color-active-day: #fff;
}

[data-theme=dark] {
    --background-color: #262626;
    --panel-background: #262626;
    --border-color: #212121;
    --icon-color: #5e5e5e;
    --header-color: #5e5e5e;
    --first-color: #fff;
    --text-color: white;
    --highlight-color: #323232;
    --hover-border-color: #fff;
    --schedule-select-input: #222;
    --border-schedule-select-input: #333;
    --schedule-select-input-cl: #B0B0B0;
    --bg-day: #222;
    --bg-active-day: #0078d4;
    --color-day: #5e5e5e;
    --color-active-day: #B0B0B0;
}

[data-theme=darkblue] {
    --background-color: #1f2c34;
    --panel-background: #2b3942;
    --border-color: #1f2c34;
    --icon-color: #1f2c34;
    --header-color: #e9e9e9;
    --first-color: #88969f;
    --text-color: white;
    --highlight-color: #2b3942;
    --hover-border-color: #fff;
    --schedule-select-input: #0f181f;
    --border-schedule-select-input: #1f2c34;
    --schedule-select-input-cl: #e9e9e9;
    --bg-day: #222;
    --bg-active-day: #0078d4;
    --color-day: #5e5e5e;
    --color-active-day: #88969f;
}

[data-theme=darkpurple] {
    --background-color: #251e2e;
    --panel-background: #342c3d;
    --border-color: #251e2e;
    --icon-color: #251e2e;
    --header-color: #e9e9e9;
    --first-color: #b0b0b0;
    --text-color: white;
    --highlight-color: #342c3d;
    --hover-border-color: #fff;
    --schedule-select-input: #0f181f;
    --border-schedule-select-input: #251e2e;
    --schedule-select-input-cl: #e9e9e9;
    --bg-day: #222;
    --bg-active-day: #0078d4;
    --color-day: #5e5e5e;
    --color-active-day: #b0b0b0;
}

[data-theme=lightsand] {
    --background-color: #eee9e5;
    --panel-background: #fbfaf9;
    --border-color: transparent;
    --icon-color: #e1e1e1; 
    --text-color: #000;
    --highlight-color: #fbfaf9;
    --hover-border-color: #fff;
    --schedule-select-input: #eee9e5;
    --border-schedule-select-input: #eee9e5;
    --schedule-select-input-cl: #333;
    --first-color: #5e5e5e;
    --header-color: #333;
    --bg-day: #5e5e5e;
    --bg-active-day: #0078d4;
    --color-day: #B0B0B0;
    --color-active-day: #fff;
}

[data-theme=darklight] {
    --background-color: #dbdbdb;
    --panel-background: #f0f0f0;
    --border-color: transparent;
    --icon-color: #e1e1e1; 
    --text-color: #000;
    --highlight-color: #f9f9f9;
    --hover-border-color: #fff;
    --schedule-select-input: #dbdbdb;
    --border-schedule-select-input: #dbdbdb;
    --schedule-select-input-cl: #333;
    --first-color: #5e5e5e;
    --header-color: #333;
    --bg-day: #5e5e5e;
    --bg-active-day: #0078d4;
    --color-day: #B0B0B0;
    --color-active-day: #fff;
} */

.rightpanl {
    /* width: 240px; */
    width: auto;
    /* background: var(--background-color);
    border: 1px solid var(--border-color); */
    color: var(--text-color);

    height: 100%;
    display: flex;
    z-index: 1;
}

.panel {
    /* width: 38px; */
    height: 100vh;
    /* background: var(--panel-background) !important;
    background: repeating-linear-gradient(to bottom, var(--first-color) 0px, var(--first-color) 1px, transparent 1px, transparent 41px); */
    position: relative;
}

.panel .icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: var(--icon-color);
    font-size: 18px;
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding: 10px;
}

.panel .icon:nth-child(2) {
    color: var(--first-color);
}

.icon:nth-child(1) { top: 0px; }     /* Calendrier */
.icon:nth-child(2) { top: 40px; }   /* Crayon */
.icon:nth-child(3) { top: 80px; }   /* Graphique */
.icon:nth-child(4) { top: 120px; }  /* Table */
.icon:nth-child(5) { top: 160px; }  /* Utilisateur */
.icon:nth-child(6) { top: 200px; }  /* Dossier */
.icon:nth-child(7) { top: 240px; }  /* Cadenas */
.icon:nth-child(8) { top: 280px; }  /* Clé */
.icon:nth-child(9) { top: 320px; }  /* Paramètres */

@media (max-width: 1366px) {
    .panel {
        background: repeating-linear-gradient(to bottom, var(--first-color) 0px, var(--first-color) 1px, transparent 1px, transparent 40px);
    }
}

.interface {
    width: 315px;
    background: var(--sidebar-left-right-color);
    color: var(--text-color);
    /* border: 2px solid var(--border-color); */
}
/* .interface {
    visibility: hidden;
    opacity: 0;
    width: 200px;
    background: var(--background-color);
    color: var(--text-color);
    border: 2px solid var(--border-color);
    transition: transform 0.5s, opacity 0.5s, visibility 0.5s;
    transform: translateX(100%);
    position: fixed;

    right: 0;
}

.interface.active {
    visibility: visible;
    opacity: 1;
    transform: translateX(0);
} */



.cardcol, .cards {
    /*width: 370px;*/
    /* background: var(--background-color); */
    color: var(--text-color);
    margin: 0 auto;
    border-radius: 0 !important;
}

.cardsSturct{
    width: 370px;
    /* background: var(--background-color); */
    color: var(--text-color);
    margin: 0 auto;
    border-radius: 0 !important; 
}
.cardcol {
    margin-top: 3px;
}

.cards {
    margin-top: -1px;
}

.headers {
    display: flex;
    justify-content: space-around;
    /* width: 200px; */
    font-size: 16px;
}

.headers span {
    padding: 5px 9px;
    cursor: pointer;
    width: 200px;
    height: 27px;
    color: var(--header-color);
    border: 1px solid var(--border-color);
    font-size: 16   px;
}

.headers span.active,
.headers span.activejourne,
.headers span.actives {
    background-color: var(--highlight-color);
    color: var(--text-color);
}

.color-palette {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
}

.color-palette div {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
}

.color-palette div:hover {
    border: 2px solid var(--hover-border-color);
}

.content-color-side {
    display: none;
    height: 200px;
}

.content-color-side.active {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    width: 200px;
    gap: 14px;
    height: 158px;
}

.content-color-side div {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
}

.content-color-side div:hover {
    border: 2px solid var(--hover-border-color);
}

.pictogram {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    color: var(--text-color);
}

.carousel {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    background-color: #fff;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dropdown-icon {
    padding: 0;
}

.color-palette.active {
    background-color: var(--highlight-color);
    padding-top: 10px;
}

.active, .activeed {
    /* background-color: var(--highlight-color); */
    padding: 10px;
}

.activeed {
    width: 200px;
    height: 88px;
}

.cardCan {
    width: 240px;
    color: #B0B0B0;
    margin: 0 auto;
    border-radius: 0 !important;
    margin-top: 3px;
}

.content-jour-sides {
    width: 200px;
    background-color: #323232;
    padding: 10px;
}

.schedule-panele {
    border-radius: 8px;
}

.schedule-panele select,
.schedule-panele input {
    margin-bottom: 10px;
    border: 1px solid var(--border-schedule-select-input);
    border-radius: 5px;
    background-color: var(--schedule-select-input);
    color: var(--schedule-select-input-cl);
    font-size: 14px;
    padding: 3px;
}

/* Styles for Schedule Panel */
.schedule-panele label {
    display: block;
    margin-top: 6px;
    margin-bottom: 6px;
    font-size: 12px;
}

.schedule-panele .repetition-summary {
    font-size: 12px;
    line-height: 1.5;
}

.chaque {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
    font-size: 12px;
}

.choix {
    display: flex;
    gap: 5px;
}

.choix input {
    width: 17%;
}

.choix .jour {
    width: 32%;
}

.choix .semaine {
    width: 49%;
}

.choix .mois {
    width: 34%;
}

.choix .checkboxs {
    width: 8.5%;
    background-color: #222;
}

.content-jour-sides,
.content-Sem-side,
.content-mois-side,
.content-annee-side {
    display: none;
    width: 200px;
    background-color: #323232;
    padding: 10px;
}

.active-section {
    display: block;
    background-color: var(--highlight-color);
}

.select-container {
    display: flex;
    flex-direction: column;
    padding-left: 30px;
}

.choix .select-container .anneeData {
    width: 68%;
}

.choix .select-container .anneeDatas {
    width: 90%;
}

.choix .Permier {
    width: 70%;
}

.activejourne {
    background-color: var(--highlight-color);
    padding: 10px;
}

.choix .calendarant {
    width: 70%;
}

.days {
    display: flex;
    gap: 2px;
}

.day {
    width: 22px;
    height: 20px;
    border: none;
    border-radius: 50%;
    background: var(--bg-day);
    color: var(--color-day);
    text-align: center;
    cursor: pointer;
    font-size: 12px;
}

.day.activeday {
    background: var(--bg-active-day);
    color: var(--color-active-day);
}

[data-theme=dark] {
    /* Personalization of the date field */
    input.calendarant {
        padding: 5px;
        background-color: #222;
        color: #B0B0B0;
        border-radius: 4px;
    }

    .flatpickr-calendarbefore, 
    .flatpickr-calendarafter {
        border: none;
    }

    /* Personalization of the calendar */
    .flatpickr-calendar {
        background-color: #333 !important;
        color: #B0B0B0 !important;
        width: 260px !important;
        box-shadow: none !important;
    }

    .flatpickr-weekdays {
        color: #B0B0B0 !important;
    }

    span.flatpickr-weekday {
        color: #B0B0B0 !important;
    }

    .flatpickr-day {
        color: #B0B0B0 !important;
    }

    .flatpickr-monthDropdown-months {
        background-color: #333 !important;
    }

    .flatpickr-next-month {
        color: #B0B0B0 !important; /* Ensure months are visible */
    }

    .flatpickr-months .flatpickr-month {
        color: #B0B0B0 !important; /* Ensure months are visible */
    }
}

.color-1 {
    background-color: #f0f0f0;
}

.color-2 {
    background-color: #d9d9d9;
}

.color-3 {
    background-color: #a6a6a6;
}

.color-4 {
    background-color: #808080;
}

.color-5 {
    background-color: #000000;
}

.color-6 {
    background-color: #ffd8b1;
}

.color-7 {
    background-color: #ffe599;
}

.color-8 {
    background-color: #d9ead3;
}

.color-9 {
    background-color: #cfe2f3;
}

.color-10 {
    background-color: #d9c2e9;
}

.color-11 {
    background-color: #ea9999;
}

.color-12 {
    background-color: #f9cb9c;
}

.color-13 {
    background-color: #b6d7a8;
}

.color-14 {
    background-color: #a2c4c9;
}

.color-15 {
    background-color: #b4a7d6;
}

.color-16 {
    background-color: #cc0000;
}

.color-17 {
    background-color: #38761d;
}

.color-18 {
    background-color: #0b5394;
}

.color-19 {
    background-color: #20124d;
}

.color-20 {
    background-color: #990000;
}



 /* Example colors for Contour 


 Contour palette with border only 


 Contour palette with only border and transparent background 


 Contours spécifiques  */
 .contour-1 {
    border: 3px solid #ffcccc;
}

.contour-2 {
    border: 3px solid #ffe6b3;
}

.contour-3 {
    border: 3px solid #d9ead3;
}

.contour-4 {
    border: 3px solid #cfe2f3;
}

.contour-5 {
    border: 3px solid #d9c2e9;
}

.contour-6 {
    border: 3px solid #ffe0b3;
}

.contour-7 {
    border: 3px solid #b3e2cd;
}

.contour-8 {
    border: 3px solid #cce2f4;
}

.contour-9 {
    border: 3px solid #e4c3e3;
}

.contour-10 {
    border: 3px solid #ffd700;
}

.contour-11 {
    border: 3px solid #c2e69c;
}

.contour-12 {
    border: 3px solid #cfe2f3;
}

.contour-13 {
    border: 3px solid #e6c3a3;
}

.contour-14 {
    border: 3px solid #d9d2e9;
}

.contour-15 {
    border: 3px solid #a2b9bc;
}

.contour-16 {
    border: 3px solid #b3cde0;
}

.contour-17 {
    border: 3px solid #e6d8b3;
}

.contour-18 {
    border: 3px solid #f4b9e4;
}

.contour-19 {
    border: 3px solid #a5c0d5;
}

.contour-20 {
    border: 3px solid #e2d1c7;
}


.picto-1::before {
    content: "\f1fc"; /* Font Awesome code for 'brush' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-2::before {
    content: "\f031"; /* Font Awesome code for 'eye-dropper' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-3::before {
    content: "\f125"; /* Font Awesome code for 'crop' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-4::before {
    content: "\f040"; /* Font Awesome code for 'pencil' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-5::before {
    content: "\f12d"; /* Font Awesome code for 'eraser' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-6::before {
    content: "\f27a"; /* Font Awesome code for 'object-group' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-7::before {
    content: "\f55d"; /* Font Awesome code for 'magic' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-8::before {
    content: "\f09c"; /* Font Awesome code for 'paint-roller' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-9::before {
    content: "\f00a"; /* Font Awesome code for 'adjust' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-10::before {
    content: "\f1d8"; /* Font Awesome code for 'filter' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-11::before {
    content: "\f56e"; /* Font Awesome code for 'magic' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-12::before {
    content: "\f0c2"; /* Font Awesome code for 'paintbrush' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-13::before {
    content: "\f1b9"; /* Font Awesome code for 'paint-brush' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-14::before {
    content: "\f12e"; /* Font Awesome code for 'scissors' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-15::before {
    content: "\f1b2"; /* Font Awesome code for 'circle' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-16::before {
    content: "\f0ad"; /* Font Awesome code for 'circle-thin' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-17::before {
    content: "\f07a"; /* Font Awesome code for 'circle-o' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-18::before {
    content: "\f030"; /* Font Awesome code for 'clone' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-19::before {
    content: "\f0b0"; /* Font Awesome code for 'rocket' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.picto-20::before {
    content: "\f01d"; /* Font Awesome code for 'magic' */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}


 /* Carrousel styles 


 calander


 styles.css  */

 .repetition-summary span {
    color: #86c69c;
}

.checkboxs {
    position: relative;
    appearance: none;
    width: 15px;
    height: 15px;
    background-color: #0078d4;
    border-radius: 5px;
    cursor: pointer;
}

.form-check-input {
    position: relative;
    appearance: none;
    width: 15px;
    height: 15px;
    background-color: var(--bg-checkbox);
    border: 0.1px solid var(--bg-checkbox-border);
    border-radius: 5px;
    cursor: pointer;
}

.form-check-input:checked {
    background-color: transparent;
    border-color: none !important;
}

.form-check-input[type="checkbox"] {
    border-color: none !important;
}

.form-check-input:checked::after {
    content: "";
    position: absolute;
    left: 2px;
    top: 2px;
    width: 11.4px;
    height: 9px;
    background-color: #0078d4;
    border-color: none !important;
}

.form-check-inputfocus {
outline: none;
}
.sidebar {
    width: 72px;
    background-color: var(--sidebar-color);
    color: var(--text-color);
    display: flex
;
    flex-direction: column;
    align-items: center;
    padding-top: 20px;
    position: relative;
}
.sidebar-item {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--sidebar-item-color);
    display: flex
;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer;
    transition: background-color 0.3s, border-radius 0.3s;
    position: relative;
}
.main-content {
    flex-grow: 1;
    display: flex
;
    background-color: #313338;
}   
.theme-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.current-theme {
    color: var(--text-color);
    padding: 8px 10px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 12%;
    /* left: 41px; */
    right: 40px;
}
.theme-options {
    padding: 10px;
    /* list-style: none; */
    position: absolute;
    background: var(--lefttopbar-borderbottom-color);
    /* width: 15%; */
    border-radius: 5px;
    overflow: hidden;
    display: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    bottom: 69%;
    /* left: -131px; */
    right: -82px;
}
.theme-options li {
    padding: 8px 10px;
    color: var(--text-color);
    cursor: pointer;
    display: flex
;
    align-items: center;
    gap: 20px;
}
.theme-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.left-block {
    flex: 0 0 240px;
    display: flex;
    flex-direction: column;
    background-color: var(--sidebar-left-right-color);
    z-index: 3;
}
.form-check-inline .form-check-input {
    width: 15px;
    height: 15px;
}
.TopPanelDistrubution{
    color: var(--bootom-colortext);
    display: flex    ;
        position: absolute;
        top: 23px;
        left: 30px;
        gap: 12px;
}
.form-check-input:checked::after{
    width: 0;
}
.topbar-left-block {
    display: flex
;
    position: relative;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: var(--sidebar-left-right-color);
    padding: 20px;
    border-bottom: 1px solid var(--lefttopbar-borderbottom-color);
    height: 10px;
    cursor: pointer;
}
.dropdown-icon {
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
}
.dropdown-content a{
    color:var(--tree-view-color, #000);
}
.dropdown-content {
    background-color: var(--headerTreeSection-bg);
    color:var(--tree-view-color, #000);
    width: 315px;
    /* height: 455px; */
  
    border-radius: 5px;
    /* margin-left: 45px; */
    overflow: hidden;
    position: absolute;
    box-shadow: var(--shadow-high);
}
.blocMotifEchec{
    gap: 5px;
    flex-direction: row;
    
}
.dropdown-content a {
    /* color: var(--dropdown-content-a); */
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    letter-spacing: normal;
    cursor: pointer;
    box-sizing: border-box;
    width: 92%;
    height: 6%;
    padding: 17px 5px;
    margin-left: 8px;
    margin-top: 5px;
    margin-bottom: 5px;
}
.dropdown-content a span {
    flex-grow: 1;
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: block;
}
.dropdown-content a svg {
    width: 16px;
    height: 16px;
    fill: #CBCDD0;
    flex-shrink: 0;
}

.content {
    display: flex
;
    flex-direction: column;
    overflow: hidden;
    border-right: 1px solid var(--sidebar-color);
    height: 89vh;
    flex-grow: unset;
    padding: 0px;
    font-family: Arial, sans-serif;
    color: #ddd;
    width: 315px;
    height: 100vh;
    /* overflow-y: auto; */
    border: 2px solid var(--bottom-barder-color);
    flex-grow: 1;
    /* overflow-y: auto; */
    padding: 0px;
    background-color: var(--sidebar-left-right-color);
}


.bottombar {
    background-color: var(--bottom-bar-color);
    color: #fff;
    padding: 0 8px 1px;
    border-top: 2px solid var(--bottombar-bordertop-color);
    font-weight: 500;
    height: 52px;
    display: flex
;
    align-items: center;
    justify-content: space-between;
    width: 315px;
    /* margin-left: -8px; */
    position: absolute;
    bottom: 0%;
    z-index: 9;
}

.user-section {
    display: flex;
    align-items: center;
    width: 122px;
    height: 39px;
    cursor: pointer;
    gap: 4px;
}

.avatar-container {
    position: relative;
    display: inline-block;
}

.user-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
}

img, svg {
    vertical-align: baseline;
}
.status-indicator {
    position: absolute;
    bottom: 0;
    right: 8px;
    width: 8px;
    height: 8px;
    background-color: #43B581;
    border-radius: 50%;
    border: 3px solid #2f313600;
}


.user-info {
    display: flex
    ;
        flex-direction: column;
        align-content: center;
        align-items: center;
}
.username, .user-tag, .user-tag2 {
    margin: 2px 0;
}

.username {
    font-size: 14px;
    color: var(--bootom-colortext);
}

.user-tag, .user-tag2 {
    font-size: 12px;
    color: #b5bac1;
    line-height: 13px;
}

.controls-section {
    display: flex;
    align-items: center;
    width: 120px;
    height: 32px;
    gap: 10px;
}

.svg-section {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.settings-modal {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: black;
}

.settings-modal-content {
    background-color: black;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    height: 100%;
    animation: scaleUp 0.5s forwards;
}

.close-settings {
    position: absolute;
    /* float: right; */
    font-size: 28px;
    cursor: pointer;
    color: white;
    z-index: 1010;
    /* display: none; */
    right: 20px;
    top: 20px;
}

.right-block {
    flex-grow: 1;
    width: calc(100% - 240px);
    position: relative;
    overflow: hidden;
    background-color: var(--rightblock-color);
}


.topbar-right-blcok {
    display: flex
;
    gap: 15px;
    justify-content: space-between;
    align-items: center;
    z-index: 1;
}
.topbar{
    color: var(--bootom-colortext);
    padding: 20px;
    /* border-bottom: 1px solid var(--topbar-borderbottom-color); */
    height: 10px;
}

.left-side-topbar {
    display: flex
;
    align-items: center;
}
.right-side-topbar {
    display: flex    ;
    align-items: center;
    gap: 10px;
    width: 58%;
    flex-direction: row-reverse;
    margin-inline-start: auto;
    margin-right: -3px !important;
    
}
.searchContainerTopbar{
    margin: 0;
    padding: 0;
    margin-top: 3px !important;
    width: 100%;
    max-width: 100%;
}
.searchContainerTopbarInput:focus,.forminputPanel:focus{
    box-shadow: none !important;
    background: var(--headerTreeSection-bg);
}
.right-side-topbar {
    /* Allows the right side topbar to take up all remaining space */
    flex-grow: 1;       /* Grows to take up remaining space */
    flex-shrink: 0;     /* Prevents shrinking */
    flex-basis: 0%;     /* Starts from zero width and grows */
}
.input-group-text,.searchContainerTopbarInput{
    background-color: var(--search-container-input);
    border:none;
    outline: none;
    box-shadow: none !important;
    height: 30px;
}
.svg-topbar {
    width: 100%;
    height: 34px;
    background-color: var(--search-container-input);
    background: var(--headerTreeSection-bg);
    position: relative;
}
.topbar .svg-topbar {
    background: none;
    height: 40px;
}

.topbar .search-container {
    height: calc(100% - 12px);
    top: 6px;
}
.search-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: var(--search-container-input);
    border: 1px solid var(--border-fieldSpan);
    width: calc(100% - 20px);
    height: calc(100% - 8px);
    position: relative;
    top: 4px;
    left: 10px;
}

.search-container input {
    width: 100%;
    height: 100%;
    background-color: var(--search-container-input);
    border: none;
    font-family: "Ubuntu", system-ui;
    color: var(--tree-view-color);
    padding: 0 25px;
    position: absolute;
    left: 0;
    top: 0;
}

.search-container input::placeholder {
    color: #aaa;
}
.search-container input:focus-visible {
    outline: 1px solid var(--active-field-border);
    border-radius: 0;
}

.sidebar-item {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--sidebar-item-color);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer;
    transition: background-color 0.3s, border-radius 0.3s;
    position: relative;
  }
  
  /* Add hover effect */
  .sidebar-item:hover {
    background-color: #5865F2;
    color:white;
    border-radius: 17px;
  }
  
  /* Add active class behavior */
  .sidebar-item.active {
    background-color: #5865F2;
    color:white;
    border-radius: 17px;
  }
  
  /* Custom hover color for specific items */
  .param-items ~ .sidebar-item:hover {
    background-color: #23A559;
  }
  .sidebar-item:hover svg path {
    fill: white;
  }
  .param-items ~ .sidebar-item.active {
    background-color: #23A559;
    border-radius: 17px;
  }
  .param-items ~ .sidebar-item.active svg path {
    fill: white;
  }
  
  .sidebar-item.active::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: white;
    border-radius: 0 8px 8px 0;
  }
  
  /* Tooltip styling */
  .sidebar-item::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 110%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #000000;
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 5px;
    white-space: nowrap;
    font-size: 14px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease;
    pointer-events: none;
  }
  
  /* Display tooltip on hover or active */
  .sidebar-item:hover::after{
    opacity: 1;
    visibility: visible;
  }
  
  
  .sidebar-item svg {
    fill: #ffffff;
  }
  


body {
    margin: unset;
    font-family: Arial, sans-serif;
    font-size: 16px;
    font-weight: normal;
    line-height: normal;
    color: black;
    text-align: left;
    background-color: white;
    -webkit-text-size-adjust: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.sidebar-title {
    font-size: 16px;
    color: var(--bootom-colortext);
    margin-left: 6px;
    line-height: 0;
    margin-top: 16px;
}



.trait-seperator {
    background-color: var(--progress-container);
    margin-top: 8px;
    height: 1px;
    width: 98%;
}
.chart-header-details{color:var(--chart-spans2);}
.livedashboard {
    margin: 25px;
    margin-right: 20px;
    position: absolute;
    top: 315px;
    max-width: 98%;
    z-index: 1000;
}
.charts{
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.chart-container {
    width: 306px;
    /* height: 402px; */
     /* Set a reasonable height that fits the content and the header */
    padding: 10px;
    background: var(--Mainchart-background);
   border:1px solid #3e4e54;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    /* border: 1px solid var(--chart-border); */
    /* color: var(--chart-spans); */
    margin-right: 0;
    /* border: 1px solid #87CEEB; */
    border-radius: 6px;
    
}
.chart-content {
height: 49px;
width: 100%;
flex-direction: row;
align-items: stretch;
margin-top: -3px;
}

.chart-header span{
    border-bottom: 1px dashed #909090;
}
.bonustechno-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.chart-content-header {
    flex: 2;  /* Adjust this value to give more or less space to the header relative to the chart */
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
}

.audienceChart {
    flex: 3;  /* Adjust this value to control how much space the chart takes relative to the header */
    /*background: #333;*/
}


.chart-title { font-size: 16px;padding: 5px;margin:10px;color:var(--chart-spans2);}
.chart-total{ font-size: 22px;color: var(--chart-spans2); padding-top: 5px;}
.chart-percentage { font-size: 12px; padding: 4px;}




.list-container {
    width: 100%;
    background: var(--Mainchart-background);
    color: var(--chart-spans);
    padding: 0px;
    border-radius: 10px;
    margin-top: 0px;

}

.statistic {
    margin-bottom: 2px;
    display: flex;
    flex-direction: column;
}

.statistic-content {
    display: flex;

}
.NumberSpanCards{
    font-size: 20px;
    color: var(--coloredTextPrimary);
    justify-self: center;
}
.containermotifechec {
    height: 65%;
    width: 100%;
}
.statistic-header{
    display: flex; /* Use Flexbox */
    justify-content: space-between; /* Align items on both ends */
    align-items: center;
}
.statistic-header span {
    display: inline-block;
    border-bottom: 1px dashed #909090;
    font-size: 12px;
    font-family: roboto, arial;
}



.static-header-number{
    margin-right: 10px; /* Margin as per your requirement */
}
.stat-title {
    flex-grow: 1;font-size: 13px;
}

.stat-number {
    margin-right: 10px;    font-size: 15px;
}

.progress-container {
    background-color: var(--progress-container);
    width: 100%;
    height: 2px;
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #539AF8;
    border-radius: 5px;
}


.Main-chart {
    display: flex;
    width: 626px;
    height: 262px;
    background-color: var(--Mainchart-background) ;
    flex-direction: column;
    padding: 7px;
    border-radius: 11px;
    border: 1px solid var(--chart-border);
    color: var(--chart-spans);
    margin-bottom: 10px;
}

.content-description{
    display:flex;
    font-size:12px;
    color:var(--chart-spans);
    /*font-family: roboto, arial;
    flex:2;*/
}

.description-number{
    font-size: 27px;
    margin-left: 20px;
    font: 400 36px / 44px Google Sans, Arial, Helvetica, sans-serif;
    color: var(--chart-spans2);
    }
.description-title{
    font: 500 12px / 21px ROBOTO,arial;
    letter-spacing: .5px;
    text-transform:uppercase;
}
.bottom-panel-toggle{
    height: 20px;
    justify-content: center;
    justify-self: center;
    width: 60px;
    background-color: var(--headerTreeSection-bg);
    display: flex;
    /* flex-wrap: nowrap; */
    /* position: absolute; */
    left: 50px;
    /* left: 45%; */
    /* top: 28px; */
    /* z-index: 10000; */
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    align-items: center;
    border: 3px solid var(--sidebar-left-right-color);
    border-bottom: none;
    color: #ccc;
    cursor: pointer;
}
.bottom-panel-toggle i {font-size: 13px;}
.bottomPanel{
    width: 100%;
    height: 355px;
    position: fixed;
    bottom: 0px;
    transition: bottom 0.3s;
    overflow-y: hidden;
    overflow-x: hidden;
    display: block;
    z-index: 1;
    background: transparent;
    padding: 0;
}
.bottomPaneltimeline{
    width: 100%;
    height: 245px;
    position: fixed;
    bottom: 0px;
    position: absolute;  
    transition: bottom 0.3s;
    overflow-y: hidden;
    overflow-x: hidden;
    display: block;
    z-index: 1;
    background: transparent;
    padding: 0;
}
body .d-band-0 .d-range {
    background-color: var(--d-band-0-bg) !important;
}
body .d-band-1 .d-range {
    background-color: var(--d-band-1-bg) !important;
}
body .d-range.d-before,
body .d-range.d-after {
    border-color: var(--d-band-border) !important;
}
body .d-marker{
    color: var(--coloredTextPrimary) !important;
    font-weight: 600;
}
body .d-pin{
    background-color: var(--coloredTextPrimary) !important;
    width: 6px;
    height: 6px;
}
.ChartPannel{
    width: 100%;
    height: 335px;
    /* gap: 20px; */
    display: flex;
    /* background-color: var(--sidebar-left-right-color); */
    background-color: var(--GraphePanel);
    /* margin-top: 20px; */
    flex-direction: column;
}
.bonustechno-chart-container {
    height: 66px;
    width: 66px;
    display: flex;
    
}
.bonustechno-chart-and-legend {
    display: flex;
    align-items: center;
    /* margin-top: 5%; */
    gap: 20px;
    flex-direction: column;
}
.RightPanelToggle {
    margin-top: 10px;
    display: flex
;
    align-content: center;
    align-items: center;
    height: 60px;
    width: 20px;
    /* position: absolute; */
    z-index: 1;
    /* justify-self: center; */
    /* align-self: center; */
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
    border: 3px solid var(--sidebar-left-right-color);
    background-color: var(--headerTreeSection-bg);
    color: #ccc;
    cursor: pointer;
    justify-content: center;
    /* top: 50px; */
    /* position: absolute; */
}
.RightPanelToggle i {transform: rotate(-90deg);font-size: 13px;}

.IcontoggleChart{transform: rotate(180deg);}

.dropdown-content {
    position: absolute;

    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    padding: 10px;
    z-index: 1000;
    border-radius: 5px;
}
.MonthSelector,.MonthSelector:hover{
    color: var(--bootom-colortext) !important;
    border: none;
}

.dropdown-content a {
    text-decoration: none;
    display: block;
    padding: 8px 12px;
    color: var(--tree-view-color, #000);
}



.dropdown-icon {
    font-size: 16px;
    cursor: pointer;
}



.dropdown-content hr {
    margin: 8px 0;
    border: none;

}

.quittez span {
    color: red;
    font-weight: bold;
}
/*modal event**/
.modal-event {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    
  }
  
  .modal-content-event{
    background-color: var(--headerTreeSection-bg);
    margin: 15% auto;
    /* border-radius: 10px; */
    /* width: 600px;height:400px; */
    color: #FFF;
    margin-top:130px;
    width: 315px;
  }
  
  .close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    margin-right:23px;
  }
  
  .close-btn:hover, .close-btn:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer;
  }
  
  .create-event-btn {
    background-color: #5865F2;
    color: white;
    border: none;
    padding: 3px 5px;
    cursor: pointer;
    border-radius: 5px;
    margin-left:10px;
  }
  
  .event-placeholder {
    text-align: center;
    padding: 77px;
  }
  
  .event-placeholder img {
    width: 50px;
  }
  
  .no-event {
    font-size: 20px;
    font-weight: bold;
  }
  
  .modal-header-event{display: flex; justify-content: space-between; align-items: center;}
  .hr-modal{    width: 1px;
    height: 14px;
    background-color: #666666;
    margin-left:13px;}
  .bloc1{display: flex; justify-content: space-between; align-items: center;}
  .svg-bloc{display: flex; justify-content: center; align-items: center;}

  .checkedFieldLine span {
    width: 100%;
    border: 1px solid var(--border-fieldSpan);
    background-color: var(--checkedFieldLine-span);
}

.fieldLiSpan:hover {
    color: #e9ecef;
  
    
}

.formSpan:hover {
    color: #e9ecef;
    background-color: rgb(4, 57, 94); /* Reste avec le hover classique sur la ligne */
}
.caret:hover {
    background-color: var(--hover-field-bg);
    color: var(--hover-field-color);
}

.formListItem .total-cluster:hover {
    background-color: var(--coloredTextPrimary);
    padding: 0 4px;
    border-radius: 5px;
}
.formListItem .total-cluster:hover svg{
    fill: var(--hover-field-color) !important;
}

.formListItem .total-cluster {
    transition: color 0.3s ease; /* Smooth transition effect */
}
.field-list-item .total-place {
    transition: background-color 0.3s ease; /* Smooth transition for background color */
}

.field-list-item .total-place:hover {
    color: #e9ecef !important;
    background-color: var(--coloredTextPrimary);
    padding: 0 4px;
    border-radius: 5px;
    
}

/* .formListItem .total-street{
    transition: background-color 0.3s ease; 
}

.formListItem .total-street:hover {
    background-color: #272727; 
} */
.caret  {
    transition: color 0.3s ease; /* Smooth transition effect */
}

.info-card {
    position: absolute;
    top:20%;
    left: 50px;
    width: 215px;
    background:var(--headerTreeSection-bg);
    /* color: #55C5D0; */
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
}
.info-card-Hiarchy {
    position: absolute;
    top:20%;
    left: 50px;
    width: 215px;
    background:var(--tree-view-bg);
    /* color: #55C5D0; */
   
    border-radius: 12px;
    font-size: 14px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
}
.info-breadcrumbs  {
    position: absolute;
    top:4%;
    
   

    /* color: #55C5D0; */
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
    display: flex;
}



.info-card-iris {
    position: absolute;
    top:20%;
    left: 270px;
    width: 215px;
    background:var(--headerTreeSection-bg);
    /* color: #55C5D0; */
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
}
.info-card-micro {
    position: absolute;
    top:20%;
    left: 270px;
    width: 215px;
    background:var(--headerTreeSection-bg);
    color: #55C5D0;
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
}

#displayproductionPanel {
    position: fixed;
    bottom: -100%;
    height: 97%;
    width: 100%;
    box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.2);
    transition: bottom 0.3s ease-in-out;
    z-index: 1000;
    padding: 10px;
    background: var(--channel-item-bg);
}
.DetailsCAlenderContainer {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    /* gap: 40px; */
    padding: 10px;
    font-size: 14px;
    margin-top: 25px;
    width: 100%;
}
.ClosingBigPanel {
    position: absolute;
    z-index: 1000;
    top: 0px;
    color: #B0B0B0;
    right: 60%;
}
.info-card-Rues {
    position: absolute;
    top:20%;
    left: 270px;
    width: 215px;
    background:var(--headerTreeSection-bg);
    /* color: #55C5D0; */
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
}
.info-card-Villes {
    position: absolute;
    top:20%;
    left: 270px;
    width: 215px;
    background:var(--headerTreeSection-bg);
    /* color: #55C5D0; */
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
}
.info-card-Cluster {
    position: absolute;
    top:20%;
    left: 270px;
    width: 215px;
    background:var(--headerTreeSection-bg);
    /* color: #55C5D0; */
    padding: 12px;
    border-radius: 12px;
    font-size: 14px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    cursor: grab;
    user-select: none;
    transition: left 0.1s ease-out, top 0.1s ease-out;
    font-family: Consolas, monospace;
}
.isCheckedsomme{
    color: var(--coloredTextPrimary);
}
.info-card:active {
    cursor: grabbing;
}
#displayproductionPanel {
    position: fixed;
    bottom: -100%;
    height: 97%;
    width: 100%;
   
    box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.2);
    transition: bottom 0.3s ease-in-out;
    z-index: 1000; /* Mettre au-dessus des autres éléments */
    padding: 10px;
}

.NameClickedPlace{
    width: 140px;
    height: 20px;
    background: #cccccc0a;
    border-radius: 10px;
}
.NameClickedPlaceConsole{
    width: 140px;
    height: 20px;
    background: #cccccc0a;
    border-radius: 10px;
}
#displayproductionPanelConsole {
    position: fixed;
    bottom: -100%;
    height: 97%;
    width: 100%;
   
    box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.2);
    transition: bottom 0.3s ease-in-out;
    z-index: 1000; /* Mettre au-dessus des autres éléments */
    padding: 10px;
}
.info-item {
    display: flex;
    justify-content: space-between;
    padding: 2px 0;
}
.info-item img {
    width: 20px; /* Ajustez la largeur à vos besoins */
    height: auto; /* Maintenir les proportions */
    margin-right: 5px; /* Espacement entre l'image et le texte */
}
.BreadcrumpOptionSelected:hover {
    background-color: var(--active-field-bg);
    border: 1px solid var(--coloredTextPrimary);
    color:var(--coloredTextPrimary);
}

.BreadcrumpOptionSelected{
    height: 30px;
    padding: 5px;
    border: 1px solid var(--tree-view-color, #000);
    border-radius: 14px;
    background: var(--PopUpBackground);
    color: var(--tree-view-color, #000);
    font-size: 14px;
    margin:5px;
    cursor: pointer;
}



.TypeOptionSelector:hover {
    
    background-color: var(--headerTreeSection-bg);
    border: 1px solid var(--coloredTextPrimary);
    color: var(--coloredTextPrimary);
}

.TypeOptionSelector {
    height: 30px;
    padding: 5px;
    border: 1px solid var(--headerTreeSection-bg);
    border-radius: 14px;
    background: none;
    color: var(--tree-view-color, #000);
    font-size: 14px;
    margin: 5px;
    cursor: pointer;
}


.clickedType{
    background-color: var(--active-field-bg);
    border: 1px solid var(--coloredTextPrimary);
    color:var(--coloredTextPrimary);
    cursor: pointer;
}
.TypeOptionSelectortest:hover {
    background-color: var(--active-field-bg);
    border: 1px solid var(--coloredTextPrimary);
    color:var(--coloredTextPrimary);
}
.SuccessUpdateDistribution{
        display: flex;
        /* display: none; */
        width: 100%;
        align-content: center;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
        margin-bottom: 8px;
        position: absolute;
        top: 50%;
}
.blink_me {
    animation: blinker 1.5s linear infinite;
  }
  
  @keyframes blinker {
    from {opacity: 1.0;}
    to {opacity: 0.3;}
  }
.TypeOptionSelectortest {
    height: 30px;
    padding: 5px;
    border: 1px solid var(--tree-view-color, #000);
    border-radius: 14px;
    background: var(--headerTreeSection-bg);
    color: var(--tree-view-color, #000);
    font-size: 14px;
    margin:5px;
    cursor: pointer;
}
.code-iris-container {
    transition: background-color 0.3s ease;
}
.code-iris-container {
    position: relative;
    cursor: pointer;
}


.code-iris-container:hover {
    background-color: var(--tree-view-bg) !important;
    color: var(--coloredTextPrimary);
}
.code-iris-container:hover::after {
    content: "TOTAL POUR CE CODE IRIS " attr(data-sum) " PRISES";
    position: fixed;
    top: 63%; /* Adjusted to center vertically */
    left: 7%; /* Added to center horizontally */
    transform: translate(-50%, -50%); /* Center on both axes */
    background-color: rgba(0, 0, 0, 0.75);
    color: #ffa500;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    white-space: nowrap;
    z-index: 100000;
}
.Distribuer{
    padding: 5px 9px;
    cursor: pointer;
    width: 100%;
    height: 27px;
    color: var(--header-color);
    border: 1px solid var(--border-color);
    font-size: 13px;
    background-color: var(--search-container-input);
    justify-content: center;
    display: flex;
    border-radius: 7px;
}

/*********************** */
.tree-node:hover {
    border: 1px solid var(--border-color);
    cursor: pointer; /* Optional: Add a pointer cursor to enhance interactivity */
}

/* canvas {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;} */

.custom-progress-container {
    padding: 2px; /* Reduced padding */
    width: 100%;
}

.custom-progress-row {
    display: flex;
    flex-direction: column;
    gap: 10px; /* Reduced gap */
    max-width: 350px;
    width: 100%;
}

.progress-bar-section {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    flex-direction: column;
}
.text-header4s{
    /* color: var(--chart-spans); */
    font-size: 14px;
}
.progress-label-start,
.progress-label-end {
    font-size: 13px;
    /* color: var(--chart-spans); */
    /* color: var(--text-colors4); */
    width: 50px;
    font-weight: bold;
    text-align: center;
    flex-shrink: 0;
}

.progress-bar-wrapper {
    display:flex;
    height: 17px; /* Reduced bar height */
    padding: 3px;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.progress-bar-primary,
.progress-bar-secondary {
    height: 100%;
    
}

.progress-bar-primary {
    /* background: var( --primary-bar-bg); */
    background-color: #9a4eca;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.progress-bar-secondary {
    /* background: var( --secondary-bar-bg); */
    background: #343434;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

/* Center label style */
.progress-label-center {
    top: 50%;
    left: 13%;
    font-weight: bold;
    font-size: 16px;
    /* color: var(--chart-spans); */
    width: 30px;
    font-weight: bold;
    text-align: center;
}
.AnnukationStyle{
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.type-annulation-text {
    font-size: 14px; /* Reduced font size */
    font-weight: 500;
    text-align: left;
    /* color: var(--chart-spans); */
    /* color: var( --text-colors4); */
}

/* Responsive adjustments */
@media (max-width: 500px) {
    .progress-label-start,
    .progress-label-end {
        font-size: 10px;
        width: 50px;
    }

    .progress-label-center {
        font-size: 10px;
    }

    .type-annulation-text {
        font-size: 12px;
    }

    .progress-bar-wrapper {
        height: 12px;
    }
}
.salesdashboard2-row2, .salesdashboard2-row3 {
    border: 1px solid var(--border-fieldSpan);
    /* color: var(--chart-spans); */
    background: var(--Mainchart-background);
    border-radius: 10px;
    padding: 10px;
    border-radius: 6px;
    width: 295px;
}

.salesdashboard2-row {
    height: 112px;
    display: flex;
    align-items: center;
    
}
.progress-bar-custom {
	height: 4px;
	background-color: #515254;
	border-radius: 2px;
	width: 100%;
}
.progress-bar-fill {
	height: 100%;
	background-color: var(--progress-bar);
	border-radius: 2px;
	width: 0%;
}
:root {
    --background-color: #fff;
    --text-color: #989898;
    --border-color: rgba(225, 224, 225, 0.7);
    --alert-success-bg: #b5afce;
    --alert-pending-bg: #b7cb8b;
    --alert-cancelled-bg: #dc3545;
    --status-fulfilled-color: #c794ad;
    --status-unfulfilled-color: #dc3545;
    --status-cancelled-color: #ffc107;
    --nav-active-bg: #e1e1e0;
    --nav-active-text-color: #3a3b3b;
    --table-th-color: #989898;
    --table-td-color: #989898;
    --strong-text: #5b5a5b;
    --bg-checkbox: #e4e4e4;
    --background-table-erea: #ffffff;
    --bg-checkbox-border: none;
}

:root {
    --statuspreco-color-background-light: #C0E7F7;
    --statuspreco-color-background-dark: #e9f5fd;
}
:root {
    --pargamme-color-background-light: #C0E7F7;
    --pargamme-color-background-dark: #e9f5fd;
    --4text-colors: #1d4d6b;
}
:root {
    --pargamme-color-background-light: #C0E7F7;
    --pargamme-color-background-dark: #e9f5fd;
    --4text-colors: #1d4d6b;
}
:root {
    --pargamme-color-background-light: #C0E7F7;
    --pargamme-color-background-dark: #e9f5fd;
    --4text-colors: #1d4d6b;
}
:root {
    --color-background-light: #C0E7F7;
    --color-background-dark: #e9f5fd;
    --color-titlesaleshistory: #254a64;
    --color-table-text: #333333;
}
:root {
    --color-bartext-light: #1a2327;
}
:root {
    /* --background-color: #1a1a1a; */
    --text-color: #ffffff;
    --bar-background: #8a8889;
    --progress-complete-background: linear-gradient(90deg, #28b1f4, #63c9f2);
    --legend-color: #555;
    --text-colors4: #1d4d6b;
}
:root {
    /* --background-color: #1a1a1a; */
    --text-colorlabel: #cbd8e3;
    --label-color: #888;
    --primary-bar-bg: linear-gradient(90deg, #005f93, #234a62);
    --secondary-bar-bg: linear-gradient(90deg, #47abe9, #59caf2);
    --label-colorstart: #155274;
    --text-colors4: #1d4d6b;
}
:root {
    --color-background-row2sales: #e4e4ea;
    --color-background-row2salesdark: #e4e4ea;
}
:root {
    /* --background-color: #1a1a1a; */
    --text-color: #ffffff;
    --primary-color: #34b1ed;
    --card-background: #2c2c2c;
    --score-color: #888;
    --note-background: #fff;
    --note-text-color: #888;
    --light-background: #e4e5ea;
    --heading-color: #294f61;
}
:root {
    --color-background-light: #e6f7ff;
    --color-background-light4: #c0e8f7;
    --color-background-dark4: #e7f5fd;
    --color-background-lightdetailsmotif: #abdef2;
    --color-background-darkdetailsmotif: #e7f5fe;
    --text-colors4: #1d4d6b;
}

:root {
    /* --background-color: #1a1a1a; */
    --text-color: #ffffff;
    --bar-background: #8a8889;
    --progress-complete-background: linear-gradient(90deg, #28b1f4, #63c9f2);
    --legend-color: #555;
    --text-colors4: #1d4d6b;
}

[data-theme="dark"] {
    --text-colorlabel: #cbd8e3;
    --color-background-light: #1e1e1e;
    --color-background-dark: #2a2a2a;
    --text-colors4: #ffffff;
}


.row3dashsales-progress-container {
    width: 100%;
    margin: 2px;
}

.row3dashsales-progress-bar-wrapper {
    width: 263px;
    /* background-color: var(--bar-background); */
    background-color: #343434;
    height: 11px;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    align-items: center;
    margin: 0 auto;
}

.row3dashsales-progress-bar-complete {
    height: 100%;
    /* background: var(--progress-complete-background); */
    background: #62eade;
    border-radius: 10px 0 0 10px;
    position: relative;
}

.row3dashsales-legend {
    display: flex;
    justify-content: space-between;
    margin-top: 13px;
}

.row3dashsales-legend-item {
    font-size: 12px;
    /* color: var(--text-colors4); */
    /* color: var(--chart-spans); */
    display: flex;
    font-weight: bold;
    align-items: center;
    margin: 0 5px;
}

.row3dashsales-legend-color {
    width: 18px;
    height: 18px;
    border-radius: 25%;
    display: inline-block;
    margin-right: 3px;
}

.row3dashsales-legend-blue {
     /* background-color: #63c9f2; */
     background: #62eade;
     }
.row3dashsales-legend-gray {
     background-color: #343434; 
    
    }

.motifs-echec-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
    text-align: left;
    /* color: var(--text-colors4); */
    /* color: var(--chart-spans); */
    position: relative;
    top: -13px;
}

/* Style for the numbers inside the progress bar */
.progress-bar-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
}

.percentage {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
    display: none;
}

.checkbox-voie {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--bg-table-section);
    
    background-color: var(--nav-link-active-bg);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.checkbox-voie:checked {
    background-color: #2D8CEB;
    border: 2px solid #1A6FB6;
}

.checkbox-voie:checked::after {
    content: ' ';
    font-size: 14px;
    color: white;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
/* Style for the select element */
#categorySelect {
  
  
    font-size: 12px; /* Smaller font size */
    
    border-radius: 3px; /* Rounded corners */
    background-color: #fff; /* White background */
    color: #333; /* Text color */
    transition: all 0.3s ease; /* Smooth transition */
}

/* Style for the select on focus */
#categorySelect:focus {
    border-color: #539AF8; /* Border color on focus */
    outline: none; /* Remove default outline */
}

/* Optional: Style for the select options */
#categorySelect option {
   
    font-size: 14px; /* Option font size */
}
.mapboxgl-popup-content{
background-color: #0f181f;
}




:root {
    --statuspreco-color-background-light: #C0E7F7;
    --statuspreco-color-background-dark: #e9f5fd;
}

[data-theme="dark"] {
    --statuspreco-color-background-light: #1e1e1e;
    --statuspreco-color-background-dark: #2a2a2a;
}


.statDash,.ChiffreDash {
    font-family: Consolas, monospace !important;
	color:var(--tree-view-color, #000) !important;
}
.ChiffreDash{
	height: 90px;
	    display: flex;
}


.close-btnInitialDropdown{
    font-size: 30px;
    position: absolute;
    right: 6px;
    top: 2px;
    cursor: pointer;
}




.destributionPanel{
    height: 100%;
    position: absolute;
    top: 40px;
    transition: left 0.5s ease-in-out;
    background: #ccc;
    width: 314px;
    z-index: 2;
    background: var(--tree-view-bg);
}
.destributionPanelToggle{
    margin-top: 10px;
    display: flex;
    align-content: center;
    align-items: center;
    height: 60px;
    width: 20px;
    position: relative;
    left: 312px;
    z-index: 1;
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
    border: 3px solid var(--sidebar-left-right-color);
    background-color: var(--headerTreeSection-bg);
    color: #ccc;
    cursor: pointer;
    justify-content: center;
}
.destributionPanelToggle i {
    transform: rotate(-90deg);
    font-size: 13px;
}


.villeNodeSpan{
    text-overflow: ellipsis;white-space: nowrap;overflow: hidden;display: flex; align-items: center;color:#b0b0b0;
    width: 66%;
}
.forminputSearchPanel{
    background-color: var(--headerTreeSection-bg);
    border: none;
    box-shadow: none;
    font-size: 22px;
}
.command-line {
	font-size: 13px;
	width: 90%;
    max-height:80vh;
   height: 70%;
	overflow-x: hidden;
	overflow-y: scroll
}

.command-line.light {
	background-color: #fff
}

.command-line.light .command-row {
	position: relative;
	margin-bottom: 5px
}

.command-line.light .command-row.active {
	background: #f5f5f5
}

.command-line .command-row {
	position: relative;
	margin-bottom: 5px
}


.command-line .command-row .command-time,
.command-line .command-row .command-user {
	color: var(--tree-view-color);
	display: inline-block;
	padding-right: 5px
}

.command-line .command-row .command-user {
	font-weight: 700
}

.command-line .command-row .command-entry {
	padding-right: 5px;
	color: #fff;
	display: inline;
	overflow-wrap: break-word;
	word-wrap: break-word;
	-ms-word-break: break-all;
	word-break: break-all;
	-ms-hyphens: auto;
	-webkit-hyphens: auto;
	hyphens: auto
}

.command-line .command-row .command-entry.command-entry-protected:empty {
	display: none
}

.command-line .command-row .command-entry.block {
	display: block
}

.command-line .command-row .command-entry:focus {
	outline: none
}

.command-line .command-row .secret {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	opacity: 0
}

.command-line .command-row.error .command-entry {
	font-weight: 700;
	color: red
}

.command-line .command-row.success .command-entry {
	font-weight: 700;
	color: #00c300
}

.command-line .command-row.info .command-entry {
	font-weight: 700;
	color: #00a9ff
}

.command-line .command-row.warning .command-entry {
	font-weight: 700;
	color: orange
}


.forminputPanel {
    background-color: var(--headerTreeSection-bg);
    border: none;
    box-shadow: none;
    font-size: 35px;
}

.IconDeleteHiarchyCard{
    color: var(--coloredTextPrimary);
    font-size: 16px;
    margin-left: 6px;
    border-radius: 2px;
}


.CmdInterventionTable {
    width: 100%;
    height: 100%;
      max-height: 90vh;
       overflow-y: auto;
      overflow-x: hidden;
    
     
    font-family: Arial, sans-serif; /* Set a clean font */
  }
  
  .CmdInterventionTable .contract-item {
  
    padding: 14px; /* Space inside each contract item */
    margin-bottom: 10px; /* Space between contract items */
  
    border-radius: 4px; /* Rounded corners for each item */
  }
  
  .CmdInterventionTable .contract-item p {
    margin: 4px 0; /* Margin for each paragraph */
    font-size: 14px; /* Font size */
  }
  
  .CmdInterventionTable .contract-item p  {
   color:#88969F;
  }
  .productionTable {
    width: 100%;
    border-collapse: collapse;
    color: var(--channel-item-color);
    border-radius: 4px;
    overflow: hidden;
    font-family: Consolas, monospace;
  }
  
  .productionTable thead tr {
    background: var(--sidebar-left-right-color);
  }
  
  .productionTable th, 
  .productionTable td {
    padding: 4px;
    text-align: left;
    border-bottom: 1px solid var(--border-fieldSpan);
  }
  
  /* Largeur spécifique pour chaque colonne */
  .productionTable th:nth-child(1) { width: 2%; }
  .productionTable th:nth-child(2) { width: 9%; }
  .productionTable th:nth-child(3) { width: 9%; }
  .productionTable th:nth-child(4) { width: auto; } /* Ajustement automatique pour le statut */
  
  .productionTable tbody tr td {
    padding: 4px;
  }
  
  @media screen and (max-width: 1367px) and (min-width: 800px) {
    /* Largeur spécifique pour chaque colonne */
  .productionTable th:nth-child(1) { width: 2%; }
  .productionTable th:nth-child(2) { width: 9%; }
  .productionTable th:nth-child(3) { width: 15%; }
  .productionTable th:nth-child(4) { width: auto; } /* Ajustement automatique pour le statut */
  }
  .SpanStreetHiarchy{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 73%;
  }