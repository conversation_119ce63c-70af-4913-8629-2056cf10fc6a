const rightPanel = document.getElementById('rightPanel');
const toggleButton = document.querySelector('.toggle-button');

function toggleRightPanel() {
  rightPanel.classList.toggle('open');
  toggleButton.classList.toggle('open');
}

document.addEventListener('click', (event) => {
  // Close the right panel if clicked outside it
  if (!rightPanel.contains(event.target) && !event.target.classList.contains('toggle-button')) {
    rightPanel.classList.remove('open');
    toggleButton.classList.remove('open');
  }
});




// document.addEventListener("DOMContentLoaded", () => {


//     // Initialisation de la carte pour chaque élément
//     if (polygonsData && Array.isArray(polygonsData) && polygonsData.length > 0) {
//         const mapElement = document.getElementById("mapprise");
//         const coords = JSON.parse(mapElement.dataset.coords);
//         const zoomLevel = parseInt(mapElement.dataset.zoom);

//         // Initialisation de la carte
//         const map = L.map(mapElement.id, {
//             center: coords,
//             zoom: zoomLevel,
//             zoomControl: false,
//             attributionControl: false,
//             maxBounds: L.latLngBounds(L.latLng(41.0, -5.0), L.latLng(51.5, 9.0)),
//             maxBoundsViscosity: 1.0,
//             minZoom: zoomLevel,
//             maxZoom: 16,
//         });

//         // Couche de carte sombre
//         const darkTileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
//             maxZoom: 18,
//         });

//         // Style des polygones
//         const geoJsonStyle = {
//             color: "#ea7362",
//             weight: 1,
//             opacity: 1,
//             fillColor: "#ea7362",
//             fillOpacity: 0.3,
//         };

//         // Ajout des polygones
//         polygonsData.forEach((polygon) => {
//             const polygonData = JSON.parse(polygon.polygon);
//             const geoJsonFeature = {
//                 type: "Feature",
//                 geometry: {
//                     type: "Polygon",
//                     coordinates: polygonData.coordinates,
//                 },
//                 properties: {
//                     name: polygon.nomCommune || "Polygon",
//                 },
//             };

//             L.geoJSON(geoJsonFeature, { style: geoJsonStyle }).addTo(map);
//         });

//         // Ajout de la couche sombre par défaut
//         map.addLayer(darkTileLayer);

//         // Définir la couleur de fond de la carte en mode sombre
//         map.getContainer().style.backgroundColor = "#0f181f"; // Dark background color
//     } else {
//         console.error("No polygon data available.");
//     }
// });



document.addEventListener("DOMContentLoaded", function() {


    const htmlContent = generateHierarchyHtml(HierarchyData);
    const treeRoot = document.getElementById('tree-root');
    treeRoot.innerHTML = htmlContent;
    setupCaretListeners('tree-root');
});


function generateHierarchyHtml(data) {
let htmlContent = '';
const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');  
const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
data.forEach(cluster => {
    let placesContent = '';
    cluster.villes.forEach(place => {
        placesContent += `
            <li class="field-list-item">
<span class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
    <span style="flex-grow: 1; display: flex; align-items: center;">
        <img src="${flecheFields}" style="width: 12px; margin-right: 6px;">
        <div style="height:10px; width:10px; background:#5090ff; border-radius: 2px; margin-right: 5px;"></div>
        ${place.ville}
    </span>
    <span>${place.total_prises}</span>
</span>
</li>
`;
    });

    htmlContent += `
       <li class="formListItem">
            <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <span style="display: flex; align-items: center;">
                    <img src="${fleche}" class="flecheimg" style="margin-right: 10px;">
                    <div style="height:10px;width:10px;background:red;margin-right: 5px;"></div>
                    ${cluster.libelle_cluster}
                </span>
                <div style="display: flex; align-items: center;margin-right: 15px;border-radius: 2px;">
                    ${cluster.total_prises}
                    <i class="bi bi-plugin" style="color: red; margin-left: 10px;"></i>
                </div>
            </span>
            <ul class="nested">${placesContent}</ul>
        </li>
        `;
});
return htmlContent;
}


function setupCaretListeners(rootElementId) {
const rootElement = document.getElementById(rootElementId);
const togglers = rootElement.querySelectorAll(".caret, .formSpan");

togglers.forEach(caret => {
    caret.addEventListener("click", function(event) {
        event.stopPropagation();

        let nestedUl = this.closest('li').querySelector('.nested');
        if (nestedUl) {
            nestedUl.classList.toggle("active");
        }

        document.querySelectorAll('.active-field').forEach(el => {
            el.classList.remove('active-field');
        });
        this.classList.add('active-field');

        this.classList.toggle("caret-down");
    });
});
}





document.addEventListener('DOMContentLoaded', function () {
    const rightCollapseBtn = document.getElementById('collapse-right-btn');
    const rightSidebarForms = document.getElementById('right-sidebar-forms');

    if (rightCollapseBtn && rightSidebarForms) {
        rightCollapseBtn.addEventListener('click', function () {
            rightSidebarForms.classList.toggle('collapsed');
        });
     }
     // else {
    //     console.error('Right collapse button or sidebar not found'); 
    // }
});