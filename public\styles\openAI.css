:root
		{
			/* Couleurs par défaut */
			--background-color: #f3f4f6;
			--text-color: #202123;
			--navbar-bg-color: #007bff; /* Couleur de fond pour la barre de navigation */
			--sidebar-bg-color: #fff;
			--card-bg-color: #f7f7f8;
			--button-bg-color: #f7f7f8;
			--link-color: #6e6e80;
			--progress-bar-bg: #ececf1;
			--progress-bar-fill: #353740;
			--small-cards-bg-colors: #e0e0e0;
			--icons-colors-i: #555;
			--bigtext-color: #333;
			--smaller-text-color-numbers: #666;
			--navbackground-color: #ddd;
			--list-bg-color: #f2f2f5;
			--list-text-color: #333;
			--list-margin-bottom: 8px;
			--list-text-color-span: #222;
            --button--color:#8e8ea0;
		}

		[data-theme="dark"] {
			/* Couleurs pour le mode sombre */
			--background-color: #121212;
			--text-color: #e0e0e0;
			--navbar-bg-color: #1f1f1f;
			--sidebar-bg-color: transparent;
			--card-bg-color: #1e1e1e;
			--button-bg-color: #555;
			--link-color: #fff;
			--progress-bar-bg: #444;
			--progress-bar-fill: #fff;
			--small-cards-bg-colors: #333;
			--icons-colors-i: #4ec9b0;
			--bigtext-color: #e0e0e0;
			--smaller-text-color-numbers: #bbb;
			--navbackground-color: #444;
			--list-bg-color: #444;
			--list-text-color: #ddd;
			--list-text-color-span: #fff;
            --button--color:#fff;
		}

		/* Réinitialisation générale */
		* {
			box-sizing: border-box;
			margin: 0;
			padding: 0;

		}

		body {
			font-family: "Söhne", helvetica, sans-serif;
			background-color: var(--background-color);
			color: var(--text-color);
			display: flex;
			justify-content: center;
			scroll-behavior: smooth;
		}

		.container {
			display: flex;
			max-width: 1200px;
			width: 100%;
			border-radius: 8px;
			overflow-y: auto;
			margin-top: 60px; 
          
		}

		/* Styles pour la barre de navigation */
		.navbar {
			position: fixed;
			top: 0;
			width: 100%;
			background-color: var(--navbar-bg-color);
			color: white;
			display: flex;
			justify-content: space-between;
			padding: 15px;
			z-index: 1000;
		}

		.navbar .menu-link {
			color: white;
			text-decoration: none;
			margin: 0 10px;
		}

		.navbar .menu-link.active {
			font-weight: bold;
			border-bottom: 2px solid #fff;
		}

        .sidbars {
            width: 250px;
            color: var(--text-color);
            padding: 20px;
            border-radius: 8px 0 0 8px;
            background-color: var(--sidebar-bg-color);
            display: flex;
            position: fixed; /* Fixe l'élément */
            top: 10%; /* Position en haut de la page */
            left: 80%; /* Aligné à gauche */
            height: 100vh; /* Occupe toute la hauteur de la fenêtre */
            overflow-y: auto; /* Scrolling vertical si nécessaire */
            z-index: 1000; /* S'assure qu'il est au-dessus des autres éléments */
        }
        
        

		.sidbars h3 {
			margin-bottom: 20px;
			font-size: 1.2em;
		}

		.sidbars ul {
			list-style: none;
			padding: 0;

		}

		ul {
			padding-left: 0;
		}
		.sidbars ul li {
			margin-bottom: 10px;

		}

		.sidbars ul li a {
			color: var(--link-color);
			text-decoration: none;
		}

		.sidbars ul li a:hover {
			text-decoration: underline;
		}

		.sidbars .progress-bar-custom {
			background-color: var(--progress-bar-bg);
			width: 5px; /* Barre fine */
			height: 15%; /* Pleine hauteur */
			margin-right: 20px;
		}

        .sidbars .progress-bar-fill {
            background-color: var(--progress-bar-fill);
            width: 100%; /* Largeur initiale à 50% */
            height: 16%; /* Hauteur de la barre */
            transition: width 0.3s ease-in-out; /* Animation de la largeur */
            border-radius: 20px;
          }
          

		.content {
			flex: 1;
			padding: 100px 80px;
			overflow-y: auto;
			scrollbar-width: none;
			background-color: transparent;
            position: relative;
            top: 30%;
		}

		.content h1,
		.content h2 {
			color: var(--text-color);
			font-size: 30px;
			line-height: 1.3;
			letter-spacing: -0.0167em;
			font-weight: 500;

		}

		.content p {
			line-height: 1.6;
			color: var(--text-color);
			margin-bottom: 0.9rem;
		}

		.button {
			display: inline-block;
			padding: 10px 20px;
			background-color: var(--button-bg-color);
			color: var(--button--color);
			border: none;
			border-radius: 5px;
			cursor: pointer;
			margin-top: 15px;
			text-decoration: none;
		}

		/* Style des cartes */
		.card {
			background-color: var(--card-bg-color);
			border-radius: 8px;
			width: 90%;
			max-width: 600px;
			overflow: hidden;
			
			font-family: "Söhne Mono", sfmono-regular, consolas, liberation mono, menlo, courier, monospace;
		}

		.card-header {
			padding: 5px 15px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			color: var(--text-color);
			font-size: 0.9em;
			font-weight: bold;
			font-family: "Söhne Mono", sfmono-regular, consolas, liberation mono, menlo, courier, monospace;
		}

		.code-container {
			background-color: var(--card-bg-color);
			padding: 15px;
			overflow-x: auto;
			font-size: 1em;
			line-height: 1.5;
			color: var(--text-color);
		}

		.keyword {
			color: #569cd6;
		}

		.type {
			color: #4ec9b0;
		}

		.func-name {
			color: #dcdcaa;
		}

		.attr-name {
			color: #9cdcfe;
		}

		.class-name {
			color: #c586c0;
		}
		.list li {
			display: flex;
			align-items: flex-start;
			margin-bottom: var(--list-margin-bottom);
			color: var(--list-text-color);
		}

		.list li span {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			width: 25px;
			height: 25px;
			border-radius: 50%;
			background-color: var(--list-bg-color);
			color: var(--list-text-color-span);
			font-weight: bold;
			margin-right: 15px;
		}

