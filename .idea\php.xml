<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/vendor/twig/twig" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/vendor/symfony/runtime" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php84" />
      <path value="$PROJECT_DIR$/vendor/doctrine/cache" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/vendor/doctrine/orm" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client" />
      <path value="$PROJECT_DIR$/vendor/doctrine/doctrine-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-client-contracts" />
      <path value="$PROJECT_DIR$/vendor/doctrine/collections" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/vendor/doctrine/persistence" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/vendor/doctrine/sql-formatter" />
      <path value="$PROJECT_DIR$/vendor/symfony/doctrine-bridge" />
      <path value="$PROJECT_DIR$/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/vendor/doctrine/migrations" />
      <path value="$PROJECT_DIR$/vendor/symfony/dependency-injection" />
      <path value="$PROJECT_DIR$/vendor/doctrine/doctrine-migrations-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/config" />
      <path value="$PROJECT_DIR$/vendor/symfony/filesystem" />
      <path value="$PROJECT_DIR$/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/twig-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/maker-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/stopwatch" />
      <path value="$PROJECT_DIR$/vendor/symfony/flex" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/framework-bundle" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/symfony/twig-bridge" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/vendor/symfony/dotenv" />
      <path value="$PROJECT_DIR$/vendor/symfony/asset" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/composer" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.0">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
</project>