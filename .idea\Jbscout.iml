<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/twig/twig" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/event-manager" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/runtime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/instantiator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php84" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/orm" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/doctrine-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-client-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/collections" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/persistence" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/sql-formatter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/doctrine-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/migrations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dependency-injection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/doctrine-migrations-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/config" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/twig-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/maker-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/stopwatch" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/flex" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/framework-bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/twig-bridge" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/dotenv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/asset" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>