<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css">
		<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
		<link rel="stylesheet" href="{{ asset('styles/priseStyle.css') }}">

		<style>:root
		{
			--background-color: #ffffff;
			--text-color: #000000;
			--footer-color: #333333;
			--col-bg-color: transparent;
			--map-bg-color: #e0e0e0;
		}

		/* Dark Theme Colors */
		[data-theme="dark"] {
			--background-color: #121212;
			--text-color: #e0e0e0;
			--footer-color: #1f1f1f;
			--col-bg-color: #313338;
			--map-bg-color: #4d254d;
		}
		footer {
			background-color: var(--footer-color);
			color: var(--text-color);
			width: 100%;
			bottom: 0;
			position: absolute;
		}

		@media(min-width: 1367px) {
			footer {
				width: 79%;
			}
		}


		body {

			transition: background-color 0.3s, color 0.3s; /* Smooth transition */
		}
		.top-flop-cluster {
			width: 47% !important;
		}
		.top-flop-cluster,
		.bonus-cluster {
			height: 240px;
		}

		.col {
			background-color: var(--col-bg-color);
			padding-left: 18px;
			padding-right: 18حء;
		}
		body.dark-mode .clusters-info,

		body.dark-mode .bonus-cluster,
		body.dark-mode .map-analytics {
			background-color: #4d254d; /* Dark background for columns */
			border-color: transparent;
			background-image: linear-gradient(to top, transparent,#4d254d); /* Texture fumée et dégradé vers le haut */
			background-blend-mode: overlay; /* Fusionner les deux arrière-plans */
			border-radius: 20px 20px 0 0;

		}
	</style>
</head>
<body>

	<div class="dashboard-container" style="font-family: 'Ubuntu'">


		<div class="row">
			<div class="col map-analytics">
				{% include 'prise/DashboardItems/mapanalytics.html.twig' %}
			</div>
			<div class="col-3 clusters-info" style="padding-left: 0; padding-right: 0;">
				{% include 'prise/DashboardItems/Prises.html.twig' %}
			</div>
			<div class="col-3 chat" style="padding-left: 5px;!important; padding-right: 5px;!important; border-radius: 5px!important; ">
				{% include 'prise/DashboardItems/chat.html.twig' %}
			</div>
		</div>
		<div class="row">
			<div class=" top-flop-cluster">
				{% include 'prise/DashboardItems/volumetrieprise.html.twig' %}
			</div>
			<div class="col bonus-cluster" style="padding: 0;!important">
				{% include 'prise/DashboardItems/chartographieprise.html.twig' %}
			</div>
		</div>
		<div class="row">
			<div class="full-tabledata">
				{% include 'prise/DashboardItems/table.html.twig' %}
			</div>
		</div>
	
	</div>
	<footer>
			<div class="">
				{% include 'prise/DashboardItems/footer.html.twig' %}
			</div>
		</footer>
	<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/js/bootstrap.min.js"></script>
	<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

	<script>
		const checkbox = document.getElementById('chk');
function applyTheme(theme) {
if (theme === 'dark') {
document.body.setAttribute('data-theme', 'dark');
localStorage.setItem('theme', 'dark');
} else {
document.body.setAttribute('data-theme', 'light');
localStorage.setItem('theme', 'light');
}
}
checkbox.addEventListener('change', () => {
applyTheme(checkbox.checked ? 'dark' : 'light');
});

const currentTheme = localStorage.getItem('theme') || 'light';
checkbox.checked = currentTheme === 'dark';

applyTheme(currentTheme);

document.getElementById('load-cluster-dashboard').addEventListener('click', function (e) {
e.preventDefault();

fetch('{{ path('clusters_dashboard') }}').then(response => {
if (!response.ok) {
throw new Error('Network response was not ok');
}
return response.text();
}).then(html => {
document.getElementById('dynamic-content').innerHTML = html;
}).catch(error => {
console.error('There was a problem with the fetch operation:', error);
});
});

document.getElementById('load-ventes-dashboard').addEventListener('click', function (e) {
e.preventDefault();

fetch('{{ path('ventes_dashboard') }}').then(response => {
if (!response.ok) {
throw new Error('Network response was not ok');
}
return response.text();
}).then(html => {
document.getElementById('dynamic-content').innerHTML = html;
}).catch(error => {
console.error('There was a problem with the fetch operation:', error);
});
});
	</script>
</body></html>
