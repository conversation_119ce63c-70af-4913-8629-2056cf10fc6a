
    body {

        background-color: #23272a;
        color: #fff;
			scrollbar-width: thin;
				scrollbar-color: #2B2D31 transparent;
    }
	.colorback{
     background: linear-gradient(145deg, #0b168e, #23272a);
	 scrollbar-width: thin;
	}

    /* Navbar */
    .navbar {
       
        padding: 1rem;
    }

    .navbar .nav-link {
        color: #b9bbbe;
        font-weight: 500;
    }

    /* Header */
    header {
     
        color: #fff;
        padding: 3rem 1rem;
		padding-left: 40px;
   
    }

    header h1 {
        font-weight: 1200;
        font-size: 2.5rem;
		font-family: fantasy;
	
    }

    header p {
        color: #b9bbbe;
        font-size: 1.1rem;
    }

    /* Section des Cartes de Serveur */
    .server-list h2 {
        color: #b9bbbe;
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }

    .server-card {
        background-color: #2f3136;
        color: #fff;
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        overflow: hidden;
		padding: 0px;
    }

    .server-card img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }

    .server-card .card-title {
        font-size: 1.2rem;
        font-weight: 700;
    }

    .server-card .card-text {
        font-size: 1rem;
        color: #b9bbbe;
    }

    .online-info {
        font-size: 0.9rem;
        color: #828282;
        margin-top: 10px;
    }
    .custom-navbar {
        background: linear-gradient(145deg, #0b168e, #23272a);
        padding: 20px 0;
        position: fixed;
        width: 100%;
        z-index: 1000;
        transition: background-color 0.3s ease, padding 0.3s ease;
    }
    
    .custom-navbar.navbar-scrolled {
        background: #23272a;
        padding: 10px 0; /* Réduit la hauteur lors du défilement */
    }
    
    .custom-navbar .navbar-brand {
        color: #fff !important; /* Changement de couleur en blanc */
        font-weight: 600;
    }
    
    .custom-navbar .navbar-toggler {
        border-color: transparent;
    }
    
    .custom-navbar .navbar-toggler:active,
    .custom-navbar .navbar-toggler:focus {
        box-shadow: none;
        outline: none;
    }
    
    @media (min-width: 992px) {
        .custom-navbar .custom-navbar-nav li {
            margin: 0 15px;
        }
    }
    
    .custom-navbar .custom-navbar-nav li a {
        font-weight: 500;
        color: #fff !important; /* Changement de couleur en blanc */
        opacity: 0.5;
        transition: 0.3s all ease;
        position: relative;
    }
    
    .custom-navbar .custom-navbar-nav li a:hover {
        opacity: 1;
    }
    
    @media (min-width: 768px) {
        .custom-navbar .custom-navbar-nav li a:before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 2px;
            right: 2px;
            background: #6a76f3;
            height: 5px;
            visibility: visible;
            width: 0;
            transition: 0.15s all ease-out;
        }
    
        .custom-navbar .custom-navbar-nav li a:hover:before,
        .custom-navbar .custom-navbar-nav li.active a:before {
            width: calc(100%);
        }
    }
    
    .custom-navbar .custom-navbar-cta {
        margin-left: 0 !important;
        display: flex;
        flex-direction: row;
    }
    
    @media (min-width: 768px) {
        .custom-navbar .custom-navbar-cta {
            margin-left: 40px !important;
        }
    }
    
    .custom-navbar .custom-navbar-cta li {
        margin: 0;
    }
    
    .custom-navbar .custom-navbar-cta li:first-child {
        margin-right: 20px;
    }
    
.avatar-wrappers {
	position: relative;
	margin-right: 10px;
}
.popup-header {
	display: flex;
	margin-top: -30px;
	align-items: end;
	margin-left: 20px;
}
.popup-avatar {
	width: 55px;
	height: 55px;
	border-radius: 20%;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 30px;
	font-weight: bold;
	border-color: var(--popup-avatar);
	border: 5px solid #313338;
	z-index: -10;
}
.status-dots {
	width: 24px;
	height: 24px;
	background-color: green;
	border-radius: 50%;
	position: absolute;
	bottom: 5px;
	right: 0;
	border: 5px solid var(--popup-avatar);
}
