<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Hiérarchie Cible</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 2rem 0;
        }
        
        .hierarchy-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .continent-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 5px solid #007bff;
        }
        
        .continent-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .continent-header:hover {
            color: #007bff;
        }
        
        .continent-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .continent-name {
            font-size: 1.4rem;
            font-weight: bold;
            margin: 0;
        }
        
        .competitions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .competition-card {
            background: white;
            border-radius: 10px;
            padding: 1.25rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .competition-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
            border-color: #007bff;
        }
        
        .competition-card.expanded {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .competition-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .competition-title {
            display: flex;
            align-items: center;
        }
        
        .competition-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 8px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2d3436;
            font-weight: bold;
            font-size: 14px;
        }
        
        .competition-name {
            font-weight: bold;
            color: #2d3436;
            margin: 0;
        }
        
        .competition-country {
            font-size: 0.9rem;
            color: #636e72;
        }
        
        .expand-icon {
            color: #007bff;
            transition: transform 0.3s ease;
        }
        
        .expand-icon.rotated {
            transform: rotate(90deg);
        }
        
        .clubs-section {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .clubs-section.active {
            display: block;
        }
        
        .clubs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.75rem;
        }
        
        .club-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 0.75rem;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .club-item:hover {
            background: #fff8e1;
            transform: translateX(5px);
        }
        
        .club-logo {
            width: 20px;
            height: 20px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .club-name {
            font-weight: 500;
            color: #495057;
            margin: 0;
        }
        
        .loading-clubs {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 1rem;
        }
        
        .stats-bar {
            display: flex;
            justify-content: space-between;
            margin-top: 0.75rem;
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            margin-right: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="hierarchy-card">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-sitemap text-primary"></i>
                        Hiérarchie Cible - Compétitions par Continent
                    </h1>
                    <p class="text-center text-muted mb-4">
                        Voici comment votre hiérarchie devrait fonctionner : Continents → Compétitions → Clubs
                    </p>
                    
                    <div class="text-center mb-4">
                        <button class="btn btn-primary btn-sm me-2" onclick="expandAllCompetitions()">
                            <i class="fas fa-expand"></i> Tout Développer
                        </button>
                        <button class="btn btn-secondary btn-sm me-2" onclick="collapseAllCompetitions()">
                            <i class="fas fa-compress"></i> Tout Réduire
                        </button>
                        <button class="btn btn-success btn-sm" onclick="loadAllClubs()">
                            <i class="fas fa-download"></i> Charger Tous les Clubs
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div id="hierarchy-container">
                    <!-- Généré par JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Données de test pour la hiérarchie cible
        const hierarchyData = {
            "UEFA": {
                "name": "UEFA (Europe)",
                "icon": "🇪🇺",
                "competitions": {
                    "GB1": {
                        "name": "Premier League",
                        "country": "England",
                        "logo": "EPL",
                        "clubs": [
                            "Manchester City", "Arsenal", "Liverpool", "Chelsea", 
                            "Manchester United", "Newcastle", "Brighton", "Tottenham"
                        ]
                    },
                    "ES1": {
                        "name": "LaLiga",
                        "country": "Spain", 
                        "logo": "LLG",
                        "clubs": [
                            "Real Madrid", "Barcelona", "Atletico Madrid", "Sevilla",
                            "Real Sociedad", "Villarreal", "Valencia", "Athletic Bilbao"
                        ]
                    },
                    "IT1": {
                        "name": "Serie A",
                        "country": "Italy",
                        "logo": "SRA",
                        "clubs": [
                            "Juventus", "Inter Milan", "AC Milan", "Napoli",
                            "Roma", "Lazio", "Atalanta", "Fiorentina"
                        ]
                    },
                    "L1": {
                        "name": "Bundesliga",
                        "country": "Germany",
                        "logo": "BUN",
                        "clubs": [
                            "Bayern Munich", "Borussia Dortmund", "RB Leipzig", "Bayer Leverkusen",
                            "Eintracht Frankfurt", "Wolfsburg", "Borussia M'gladbach", "Union Berlin"
                        ]
                    },
                    "FR1": {
                        "name": "Ligue 1",
                        "country": "France",
                        "logo": "L1",
                        "clubs": [
                            "Paris Saint-Germain", "Marseille", "Monaco", "Lyon",
                            "Lille", "Rennes", "Nice", "Lens"
                        ]
                    }
                }
            },
            "CONMEBOL": {
                "name": "CONMEBOL (Amérique du Sud)",
                "icon": "🌎",
                "competitions": {
                    "BR1": {
                        "name": "Brasileirão",
                        "country": "Brazil",
                        "logo": "BRA",
                        "clubs": [
                            "Flamengo", "Palmeiras", "São Paulo", "Corinthians",
                            "Santos", "Grêmio", "Internacional", "Atlético Mineiro"
                        ]
                    },
                    "AR1": {
                        "name": "Liga Argentina",
                        "country": "Argentina",
                        "logo": "ARG",
                        "clubs": [
                            "Boca Juniors", "River Plate", "Racing", "Independiente",
                            "San Lorenzo", "Estudiantes", "Vélez", "Lanús"
                        ]
                    }
                }
            }
        };
        
        // Initialisation
        document.addEventListener("DOMContentLoaded", function () {
            console.log("🚀 Génération de la hiérarchie cible");
            generateHierarchy();
        });
        
        function generateHierarchy() {
            const container = document.getElementById('hierarchy-container');
            let html = '';
            
            Object.keys(hierarchyData).forEach(continentId => {
                const continent = hierarchyData[continentId];
                
                html += `
                    <div class="hierarchy-card">
                        <div class="continent-section">
                            <div class="continent-header" onclick="toggleContinent('${continentId}')">
                                <div class="continent-icon">${continent.icon}</div>
                                <h2 class="continent-name">${continent.name}</h2>
                                <i class="fas fa-chevron-right expand-icon ms-auto" id="continent-icon-${continentId}"></i>
                            </div>
                            <div class="competitions-grid" id="continent-${continentId}" style="display: block;">
                `;
                
                Object.keys(continent.competitions).forEach(competitionId => {
                    const competition = continent.competitions[competitionId];
                    
                    html += `
                        <div class="competition-card" onclick="toggleCompetition('${continentId}', '${competitionId}')">
                            <div class="competition-header">
                                <div class="competition-title">
                                    <div class="competition-logo">${competition.logo}</div>
                                    <div>
                                        <h4 class="competition-name">${competition.name}</h4>
                                        <div class="competition-country">${competition.country}</div>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right expand-icon" id="comp-icon-${competitionId}"></i>
                            </div>
                            <div class="stats-bar">
                                <div class="stat-item">
                                    <i class="fas fa-users stat-icon"></i>
                                    ${competition.clubs.length} clubs
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-flag stat-icon"></i>
                                    ${competition.country}
                                </div>
                            </div>
                            <div class="clubs-section" id="clubs-${competitionId}">
                                <div class="loading-clubs">
                                    <i class="fas fa-spinner fa-spin"></i> Chargement des clubs...
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += `
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function toggleContinent(continentId) {
            const section = document.getElementById(`continent-${continentId}`);
            const icon = document.getElementById(`continent-icon-${continentId}`);
            
            if (section.style.display === 'none') {
                section.style.display = 'block';
                icon.classList.add('rotated');
            } else {
                section.style.display = 'none';
                icon.classList.remove('rotated');
            }
        }
        
        function toggleCompetition(continentId, competitionId) {
            const clubsSection = document.getElementById(`clubs-${competitionId}`);
            const icon = document.getElementById(`comp-icon-${competitionId}`);
            const card = event.currentTarget;
            
            if (clubsSection.classList.contains('active')) {
                clubsSection.classList.remove('active');
                icon.classList.remove('rotated');
                card.classList.remove('expanded');
            } else {
                clubsSection.classList.add('active');
                icon.classList.add('rotated');
                card.classList.add('expanded');
                
                // Charger les clubs si pas encore fait
                if (!clubsSection.dataset.loaded) {
                    loadClubs(continentId, competitionId);
                }
            }
        }
        
        function loadClubs(continentId, competitionId) {
            const clubsSection = document.getElementById(`clubs-${competitionId}`);
            const competition = hierarchyData[continentId].competitions[competitionId];
            
            // Simuler un délai de chargement
            setTimeout(() => {
                let clubsHtml = '<div class="clubs-grid">';
                
                competition.clubs.forEach(clubName => {
                    clubsHtml += `
                        <div class="club-item">
                            <div class="club-logo"></div>
                            <h6 class="club-name">${clubName}</h6>
                        </div>
                    `;
                });
                
                clubsHtml += '</div>';
                clubsSection.innerHTML = clubsHtml;
                clubsSection.dataset.loaded = 'true';
                
                console.log(`✅ Clubs chargés pour ${competition.name}`);
            }, 800);
        }
        
        function expandAllCompetitions() {
            document.querySelectorAll('.competition-card').forEach(card => {
                const competitionId = card.onclick.toString().match(/'([^']*)', '([^']*)'/);
                if (competitionId) {
                    const clubsSection = document.getElementById(`clubs-${competitionId[2]}`);
                    const icon = document.getElementById(`comp-icon-${competitionId[2]}`);
                    
                    if (!clubsSection.classList.contains('active')) {
                        clubsSection.classList.add('active');
                        icon.classList.add('rotated');
                        card.classList.add('expanded');
                        
                        if (!clubsSection.dataset.loaded) {
                            loadClubs(competitionId[1], competitionId[2]);
                        }
                    }
                }
            });
        }
        
        function collapseAllCompetitions() {
            document.querySelectorAll('.clubs-section.active').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelectorAll('.expand-icon.rotated').forEach(icon => {
                icon.classList.remove('rotated');
            });
            document.querySelectorAll('.competition-card.expanded').forEach(card => {
                card.classList.remove('expanded');
            });
        }
        
        function loadAllClubs() {
            console.log("🔄 Chargement de tous les clubs...");
            
            Object.keys(hierarchyData).forEach(continentId => {
                Object.keys(hierarchyData[continentId].competitions).forEach(competitionId => {
                    const clubsSection = document.getElementById(`clubs-${competitionId}`);
                    if (!clubsSection.dataset.loaded) {
                        loadClubs(continentId, competitionId);
                    }
                });
            });
        }
    </script>
</body>
</html>
