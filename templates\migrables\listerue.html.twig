
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/all.css" />
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/duotone.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

   <style>
     :root {
            --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 320px;
            --prisescardcard-height: 165px;
            --navbackground-color: #edecfc;
            --card-background-color: #edecfd;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #fcfbff;
            --icons-colors-i: #949fa9;
            --smaller-text-color-numbers: #c1c9ce;
            --bigtext-color: #394d5f;
        }

        [data-theme="dark"] {
              --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 300px;
            --prisescardcard-height: 165px;
            --navbackground-color: #2b3942;
            --card-background-color:#2b3942;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #1f2c34;
            --icons-colors-i: #6e6f71;
            --smaller-text-color-numbers: #6e6f71;
            --bigtext-color: #e9ecf0;
        }

        .prisesdashcard-container {
             display: flex;
            flex-direction: column;
            overflow: hidden;
            scrollbar-width: thin;
            scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
            overflow-y: auto; 
            max-height: 98vh; 
           
        }

@media screen and (min-width: 1400px) {
    #prisescard {
        display: grid;
       grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 10px;
    }
}

@media screen and (max-width: 1366px) and (min-width: 400px) {
    #prisescard {
        display: grid;
        grid-template-columns: repeat(3, minmax(300px, 340px)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 10px;
    }
}

        #prisescard .custom-card {
            background-color: var(--card-background-color);
            border-radius: 12px;
            padding: 10px;
            color: white;
            width: var(--prisescardcard-width);
            height: var(--prisescardcard-height);
            opacity: 0; /* Initial state for lazy loading */
            transition: opacity 0.3s ease-out;
        }

        #prisescard .card-header {
            background-color: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
    
            border: none;
            box-shadow: none;
        }

        #prisescard .card-header i {
            color: #bb86fc;
            margin-right: 5px;
        }

        #prisescard .card-title {
            font-weight: bold;
            color: var(--bigtext-color);
            font-size: 0.8em;
        }

        #prisescard .number {
            color: var(--bigtext-color);
            font-weight: bold;
            font-size: 0.8em;
            margin-top: -3%;
        }

        #prisescard .data-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 0.85em;
        }

        #prisescard .data-row {
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }

      #prisescard .data-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 12px;
            background-color: var(--small-cards-bg-colors);
            border-radius: 6px;
            width: 160px;
            font-size: 1.1em;
        }
            #prisescard .data-boxs {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 12px;
            background-color: var(--small-cards-bg-colors);
            border-radius: 6px;
            width: 100px;
            font-size: 1.1em;
        }

        #prisescard .data-box i {
            color: var(--icons-colors-i);
        }

        #prisescard .data-box .texticon {
            color: var(--icons-colors-i);
            font-weight: bold;
        }

        #prisescard .data-box .number {
            color: var(--smaller-text-color-numbers);
            font-size: 1em;
        }

        /* Navbar Styles */
        #smallprisesnav .navbar {
            display: flex;
            gap: 8px;
            padding: 20px;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
        }

        #smallprisesnav .nav-button {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 1rem;
            font-weight: bold;
            color: var(--bigtext-color);
            background-color: transparent;
            border: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        #smallprisesnav .nav-button.active {
            background-color: var(--navbackground-color);
            color: var(--bigtext-color);
        }

        #smallprisesnav .nav-button .icon {
            width: 24px;
            height: 24px;
        }

</style>
<style>
 :root {
    /* Default Variables */

    --skeleton-bg: #e0e0e0;
    --skeleton-highlight: #f0f0f0;
    --prisescardcard-width: 300px;
    --prisescardcard-height: 150px;
}

/* Styles for dark mode */
[data-theme="dark"] {

    --skeleton-bg: #1f2c34;
    --skeleton-highlight: #2b3942;
}

.skeleton-card {
    background-color: var(--card-background-color);
    border-radius: 12px;
    padding: 10px;
    width: var(--prisescardcard-width);
    height: var(--prisescardcard-height);
    display: flex;
    flex-direction: column;
    gap: 10px;
    animation: shimmer 1.5s infinite;
}

/* Header Skeleton */
.skeleton-card-header {
    height: 20px;
    width: 80%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
    margin-bottom: 8px;
}

/* Data Container Skeleton */
.skeleton-data-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.skeleton-data-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.skeleton-data-row div {
    height: 30px;
    width: 45%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
}

/* Animation for Shimmer Effect */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: 200px 0;
    }
}

.skeleton-card-header,
.skeleton-data-row div {
    background: linear-gradient(
        90deg,
        var(--skeleton-bg) 25%,
        var(--skeleton-highlight) 50%,
        var(--skeleton-bg) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Hide actual cards during loading */
.custom-card.loading {
    display: none;
}

.custom-card.loaded {
    opacity: 1;
    display: block;
}

/* Responsive Design */
@media (max-width: 600px) {
    :root {
        --prisescardcard-width: 100%;
        --prisescardcard-height: auto;
    }
    
    .skeleton-card {
        padding: 8px;
    }
    
    .skeleton-data-row div {
        height: 20px;
    }
}

[data-theme="dark"] .data-container .data-boxs img[src*="adsl-violet.svg"] {
  content: url('{{ asset('image/icon/adsl-gris.svg') }}');
}

[data-theme="dark"] .data-container .data-boxs img[src*="Fibre-violet.svg"] {
  content: url('{{ asset('image/icon/Fibre-gris.svg') }}');
}

[data-theme="dark"] .data-container .data-boxs img[src*="Mobile-violet.svg"] {
  content: url('{{ asset('image/icon/Mobile-gris.svg') }}');
}

[data-theme="dark"] .data-container .data-box img[src*="mobile-fibre-violet.svg"] {
  content: url('{{ asset('image/icon/mobile-fibre-gris.svg') }}');
}

[data-theme="dark"] .data-container .data-box img[src*="mobile-adsl-violet.svg"] {
  content: url('{{ asset('image/icon/mobile-adsl-gris.svg') }}');
}
</style>
 

<div class="prisesdashcard-container">
 {% set total_nb_fyr_adsl = 0 %}
        {% set total_nb_fyr_mob_mono = 0 %}
        {% set total_nb_fyr_mob_multi_thd = 0 %}
        {% set total_nb_fyr_mob_multi_adsl = 0 %}
        {% set total_nb_fyr_thd = 0 %}
                  {% for cluster in clusterDetailsRuess.nom_voie %}
    {% for date, values in cluster %}


                    {# Accumulate the sum of each value #}
                    {% set total_nb_fyr_adsl = total_nb_fyr_adsl + values.data.nb_fyr_adsl %}
                    {% set total_nb_fyr_mob_mono = total_nb_fyr_mob_mono + values.data.nb_fyr_mob_mono %}
                    {% set total_nb_fyr_mob_multi_thd = total_nb_fyr_mob_multi_thd + values.data.nb_fyr_mob_multi_thd %}
                    {% set total_nb_fyr_mob_multi_adsl = total_nb_fyr_mob_multi_adsl + values.data.nb_fyr_mob_multi_adsl %}
                    {% set total_nb_fyr_thd = total_nb_fyr_thd + values.data.nb_fyr_thd %}
                        
            {% endfor %}
        {% endfor %}
   <div id="smallprisesnav">
      {% include 'migrables/header.html.twig' %}

    </div>
   <div id="prisescard">
    {% if clusterDetailsRuess is empty %}
        <div class="no-data-message">
            <i class="fas fa-exclamation-circle no-data-icon"></i>
            <p>No Rue details found for this cluster.</p>
            <a href="{{ path('detail_cluster_datalist', { 'code_cluster': code_cluster }) }}" class="btn btn-secondary mt-2">View Data List Version</a>
        </div>
    {% else %}
        <!-- Skeleton Cards -->
        {% for detail in clusterDetailsRuess.nom_voie %}
        <div class="custom-card skeleton-card">
            <div class="skeleton-card-header"></div>
            <div class="skeleton-data-container">
                <div class="skeleton-data-row">
                    <div></div>
                    <div></div>
                </div>
                <div class="skeleton-data-row">
                    <div></div>
                    <div></div>
                </div>
            </div>
        </div>
        {% endfor %}

  <!-- Loop for Actual Cards -->
    {% for cluster in clusterDetailsRuess.nom_voie %}
    {% for date, values in cluster %}

  

    
        <div class="custom-card real-card loading" data-lazy-load="true" > 
      
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-map-marker-alt"></i> {{ date}}
                </div>
                <div class="number">
                    <i class="fa-solid fa-plug"></i> {{ values.data.nb_fyr_adsl + values.data.nb_fyr_mob_mono + values.data.nb_fyr_mob_multi_thd + values.data.nb_fyr_mob_multi_adsl + values.data.nb_fyr_thd }}
                </div>
            </div>
        
            <div class="data-container">
                            <div class="data-row">
                                <div class="data-boxs">
                                    <img src="{{ asset('image/icon/adsl-violet.svg') }}" alt="Router Icon" class="icon" width="30" height="30" />
                                    <span class="number">{{ values.data.nb_fyr_adsl }}</span>
                                </div>
                                <div class="data-boxs">
                                    <img src="{{ asset('image/icon/Fibre-violet.svg') }}" alt="Plug Icon" class="icon" width="30" height="30" />
                                 
                                       <span class="number">{{ values.data.nb_fyr_thd }}</span>
                                </div>
                                <div class="data-boxs">
                                    <img src="{{ asset('image/icon/Mobile-violet.svg') }}" alt="Mobile Icon" class="icon" width="30" height="30" />
                                  
                                       <span class="number">{{ values.data.nb_fyr_mob_mono }}</span>
                                </div>
                            </div>
                            <div class="data-row">
                                <div class="data-box">
                                    <img src="{{ asset('image/icon/mobile-fibre-violet.svg') }}" alt="Plug Icon" class="icon" width="30" height="30" />
                               
                                     <span class="number">{{ values.data.nb_fyr_mob_multi_thd }}</span>
                                </div>
                                <div class="data-box">
                                    <img src="{{ asset('image/icon/mobile-adsl-violet.svg') }}" alt="Plug Icon" class="icon" width="30" height="30" />
                              
                                       <span class="number">{{ values.data.nb_fyr_mob_multi_adsl }}</span>
                                </div>
                            </div>
                        </div>


        </div>
    {% endfor %}
{% endfor %}
    {% endif %}
</div>

</div>
<script>

document.addEventListener("DOMContentLoaded", function() {
  const theme = document.body.getAttribute("data-theme");
  
  if (theme === "dark") {
    const images = document.querySelectorAll(".data-container img.icon");
    
    images.forEach(img => {
      if (img.src.includes("adsl-violet.svg")) {
        img.src = "{{ asset('image/icon/adsl-gris.svg') }}";
      } else if (img.src.includes("Fibre-violet.svg")) {
        img.src = "{{ asset('image/icon/Fibre-gris.svg') }}";
      } else if (img.src.includes("Mobile-violet.svg")) {
        img.src = "{{ asset('image/icon/Mobile-gris.svg') }}";
      } else if (img.src.includes("mobile-fibre-violet.svg")) {
        img.src = "{{ asset('image/icon/mobile-fibre-gris.svg') }}";
      } else if (img.src.includes("mobile-adsl-violet.svg")) {
        img.src = "{{ asset('image/icon/mobile-adsl-gris.svg') }}";
      }
    });
  }
});


</script>
<script>
document.addEventListener("DOMContentLoaded", () => {
    const skeletons = document.querySelectorAll('.skeleton-card');
    const realCards = document.querySelectorAll('.custom-card[data-lazy-load="true"]');

    // Show skeletons initially
    skeletons.forEach(skeleton => skeleton.style.display = 'block');
    realCards.forEach(card => card.style.display = 'none');

    // Simulate loading delay
    setTimeout(() => {
        // Hide skeletons
        skeletons.forEach(skeleton => skeleton.style.display = 'none');

        // Show real cards
        realCards.forEach(card => {
            card.style.display = 'block';
            card.classList.add('loaded'); // Optional for smooth transition
        });
    }, 2000); // 2 seconds delay
});
    
</script>
  {# <script>
 document.addEventListener('DOMContentLoaded', () => {
    const cards = document.querySelectorAll('.custom-card');
    cards.forEach(card => {
        card.addEventListener('click', () => {
            const nomrue = card.getAttribute('data-cluster-code');
            const code_cluster = "{{ code_cluster }}"; 
            const detailkpiinseeCode = "{{ detailkpiinseeCode }}"
            window.location.href = `/clusters/details/rue/${code_cluster}/${detailkpiinseeCode}/${nomrue}`;
        });
    });
});

</script> #}
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // IntersectionObserver to handle lazy loading
        const options = {
            root: null, // use the viewport
            rootMargin: '0px',
            threshold: 0.1 // trigger when 10% of the card is visible
        };

        const loadCard = (entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = 1; // Fade-in effect
                    observer.unobserve(entry.target); // Stop observing
                }
            });
        };

        const observer = new IntersectionObserver(loadCard, options);
        const cards = document.querySelectorAll('.custom-card');
        cards.forEach(card => {
            observer.observe(card); // Start observing each card
        });
    });
</script>

<style>
    .no-data-message {
        text-align: center;
        color: #555;
        margin-top: 20px;
        padding: 20px;
        background-color: #f9f9f9;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
    }

    .no-data-icon {
        font-size: 2rem;
        color: #ff6b6b;
        margin-bottom: 10px;
    }

    .no-data-message p {
        margin: 0;
        font-size: 1.2rem;
        color: #666;
    }

    .btn-secondary {
        background-color: #6c757d;
        border: none;
    }
</style>
