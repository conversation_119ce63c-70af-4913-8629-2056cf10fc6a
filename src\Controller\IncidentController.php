<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpFoundation\Request;

class IncidentController extends AbstractController
{
    #[Route('/incident', name: 'incident')]
    public function incident(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
            $userId = $dataUser['user_id'] ?? null;
            $cpv = $dataUser['cpv'] ?? null;
            $pointOfSaleId  = $dataUser['point_of_sale_id'] ?? null;
            if (!$userId || !$cpv) {
                throw new \Exception('User ID or CPV not found in the response.');
            }
    
            $userData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
            // $HierarchyData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/productions-consolidation/{$userId}/V", $jwt);

            // //$HierarchyData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/incidents-tickets/{$cpv}/THD20250327200132908?page=1", $jwt);
            // $effectifsData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}/tree/all?page=1", $jwt);
            // $effectifs = $effectifsData['children'] ?? [];
            // $migrableData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/{$cpv}?page=1", $jwt);
            // $arretcuivre = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/interventions-places-arret-cu/{$cpv}?page=1", $jwt);
            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/productions-by-lib-motif-instance/3?debut=01-10-2024&fin=31-10-2024&page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $productionsMotif = json_decode($response3->getContent(), true);
            
        } catch (\Exception $e) {
            $this->addFlash('error', $e->getMessage());
            return $this->redirectToRoute('app_dashboard');
        }
        $username = null;
        if ($jwt) {
            $decodedToken = $this->decodeJwt($jwt);
            $username = $decodedToken['username'] ?? 'Guest';
        }
    
        return $this->render('geomap/incident/incident.html.twig', [
            'cpv' => $cpv,
            'pointOfSaleId'=>$pointOfSaleId ,
            'jwt' => $jwt,
            'userId' => $userId,
            'username' => $username,
            'productionsMotif' => $productionsMotif,
        ]);
    }
    private function decodeJwt(string $jwt): array
    {
        try {
            $parts = explode('.', $jwt);
            if (count($parts) !== 3) {
                return [];
            }

            return json_decode(base64_decode($parts[1]), true) ?? [];
        } catch (\Exception $e) {
            return [];
        }
    }

    private function fetchData(HttpClientInterface $httpClient, string $url, string $jwt): ?array {
        try {
            $response = $httpClient->request('GET', $url, [
                'headers' => ['Authorization' => 'Bearer ' . $jwt]
            ]);

            if ($response->getStatusCode() === 200) {
                return json_decode($response->getContent(), true);
            } else {
                error_log('Response: ' . $response->getContent(false));
                throw new \Exception('Failed to fetch data. Status: ' . $response->getStatusCode());
            }
        } catch (\Exception $e) {
            error_log('Error in fetchData: ' . $e->getMessage());
            return null;
        }
    }
}
