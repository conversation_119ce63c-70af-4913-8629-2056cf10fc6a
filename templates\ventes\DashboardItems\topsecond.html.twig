<style>
    :root {
        --background-color: #1a1a1a; 
        --text-color: #ffffff; 
        --primary-color: #34b1ed; 
        --card-background: #2c2c2c; 
        --score-color: #888; 
        --note-background: #fff; 
        --note-text-color: #888; 
        --light-background: #e4e5ea; 
        --heading-color: #294f61; 
    }

    [data-theme="dark"] {
        --background-color: #1a1a1a;
        --text-color: #ffffff;
        --primary-color: #34b1ed;
        --card-background: #2c2c2c;
        --score-color: #888;
        --note-background: #102c3c;
        --note-text-color: #888;
        --light-background: #3e3e3e;
        --heading-color: #ffffff;
    }

    .salesdashboard2-container-fluid {
        display: flex;
        flex-direction: column;
        gap: 17px; 
    }

    .salesdashboard2-row {
        height: 100px; 
        display: flex;
        align-items: center;
        width: 100%; /* Ensure rows take full width */
    }

    .salesdashboard2-row1 {
        background-color: var(--primary-color);
        color: var(--text-color);
        border-radius: 10px;
        width: 100%;
        padding: 10px; /* Adjust padding as needed */
        justify-content: start;
    }

    .note-satisfaction {
        background-color: var(--note-background);
        border-radius: 15px;
        text-align: center;
        width: 50%;
        height: 100%;
        margin-left: auto;
    }

    .note-satisfaction h3 {
        color: var(--note-text-color);
        font-size: 10.4px;
        margin-top: 15%;
    }

    .salesdashboard2-score h3 {
        color: var(--heading-color);
        font-size: 40.8px;
        margin-top: -13%;
        font-weight: bold;
    }


    .salesdashboard2-row2,
    .salesdashboard2-row3 {
       background: linear-gradient(to bottom, var(--color-background-row2sales), var( --color-background-row2salesdark)); 
        border-radius: 10px;
        margin-top: 3%;
        padding: 10px; 
        width: 100%;
    }
    
    :root {
        --color-background-row2sales: #e4e4ea;
        --color-background-row2salesdark: #e4e4ea;
    }

    [data-theme="dark"] {
        --color-background-row2sales: #303238;
        --color-background-row2salesdark: #2c2d30 ;
    }
</style>


<div class="salesdashboard2-container-fluid">
    <div class="salesdashboard2-row salesdashboard2-row1">
        <div class="note-satisfaction">
            <h3>Note satisfaction</h3>
            <div class="salesdashboard2-score">
                <h3>8.6</h3>
            </div>
        </div>
    </div>
    <div class="salesdashboard2-row salesdashboard2-row2"> 
        {% include 'ventes/DashboardItems/topsecond/typeannulation.html.twig' %}
    </div>
    <div class="salesdashboard2-row salesdashboard2-row3">
        {% include 'ventes/DashboardItems/topsecond/motifechec.html.twig' %}
    </div>
</div>