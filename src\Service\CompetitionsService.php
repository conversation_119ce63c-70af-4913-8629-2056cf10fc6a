<?php

namespace App\Service;

use App\Entity\CompetitionClubs;
use App\Entity\Competitions;
use App\Entity\PlayersClub;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class CompetitionsService
{

    private $client;
    private $entityManager;

    public function __construct(HttpClientInterface $client, EntityManagerInterface $entityManager)
    {
        $this->client = $client;
        $this->entityManager = $entityManager;
    }

    public function getCompetitionByCountry(){

            $countries = [
        "Afghanistan",
        "Albania",
        "Algeria",
        "Andorra",
        "Angola",
        "Argentina",
        "Armenia",
        "Australia",
        "Austria",
        "Azerbaijan",
        "Bahamas",
        "Bahrain",
        "Bangladesh",
        "Barbados",
        "Belarus",
        "Belgium",
        "Belize",
        "Benin",
        "Bhutan",
        "Bolivia",
        "Bosnia and Herzegovina",
        "Botswana",
        "Brazil",
        "Brunei",
        "Bulgaria",
        "Burkina Faso",
        "Burundi",
        "Cabo Verde",
        "Cambodia",
        "Cameroon",
        "Canada",
        "Central African Republic",
        "Chad",
        "Chile",
        "China",
        "Colombia",
        "Comoros",
        "Congo (Congo-Brazzaville)",
        "Costa Rica",
        "Croatia",
        "Cuba",
        "Cyprus",
        "Czech Republic (Czechia)",
        "Democratic Republic of the Congo",
        "Denmark",
        "Djibouti",
        "Dominica",
        "Dominican Republic",
        "Ecuador",
        "Egypt",
        "El Salvador",
        "Equatorial Guinea",
        "Eritrea",
        "Estonia",
        "Eswatini",
        "Ethiopia",
        "Fiji",
        "Finland",
        "France",
        "Gabon",
        "Gambia",
        "Georgia",
        "Germany",
        "Ghana",
        "Greece",
        "Grenada",
        "Guatemala",
        "Guinea",
        "Guinea-Bissau",
        "Guyana",
        "Haiti",
        "Honduras",
        "Hungary",
        "Iceland",
        "India",
        "Indonesia",
        "Iran",
        "Iraq",
        "Ireland",
        "Israel",
        "Italy",
        "Jamaica",
        "Japan",
        "Jordan",
        "Kazakhstan",
        "Kenya",
        "Kiribati",
        "Kuwait",
        "Kyrgyzstan",
        "Laos",
        "Latvia",
        "Lebanon",
        "Lesotho",
        "Liberia",
        "Libya",
        "Liechtenstein",
        "Lithuania",
        "Luxembourg",
        "Madagascar",
        "Malawi",
        "Malaysia",
        "Maldives",
        "Mali",
        "Malta",
        "Marshall Islands",
        "Mauritania",
        "Mauritius",
        "Mexico",
        "Micronesia",
        "Moldova",
        "Monaco",
        "Mongolia",
        "Montenegro",
        "Morocco",
        "Mozambique",
        "Myanmar (formerly Burma)",
        "Namibia",
        "Nauru",
        "Nepal",
        "Netherlands",
        "New Zealand",
        "Nicaragua",
        "Niger",
        "Nigeria",
        "North Korea",
        "North Macedonia",
        "Norway",
        "Oman",
        "Pakistan",
        "Palau",
        "Palestine State",
        "Panama",
        "Papua New Guinea",
        "Paraguay",
        "Peru",
        "Philippines",
        "Poland",
        "Portugal",
        "Qatar",
        "Romania",
        "Russia",
        "Rwanda",
        "Saint Kitts and Nevis",
        "Saint Lucia",
        "Saint Vincent and the Grenadines",
        "Samoa",
        "San Marino",
        "Sao Tome and Principe",
        "Saudi Arabia",
        "Senegal",
        "Serbia",
        "Seychelles",
        "Sierra Leone",
        "Singapore",
        "Slovakia",
        "Slovenia",
        "Solomon Islands",
        "Somalia",
        "South Africa",
        "South Korea",
        "South Sudan",
        "Spain",
        "Sri Lanka",
        "Sudan",
        "Suriname",
        "Sweden",
        "Switzerland",
        "Syria",
        "Taiwan",
        "Tajikistan",
        "Tanzania",
        "Thailand",
        "Timor-Leste",
        "Togo",
        "Tonga",
        "Trinidad and Tobago",
        "Tunisia",
        "Turkey",
        "Turkmenistan",
        "Tuvalu",
        "Uganda",
        "Ukraine",
        "United Arab Emirates",
        "United Kingdom",
        "United States of America",
        "Uruguay",
        "Uzbekistan",
        "Vanuatu",
        "Vatican City",
        "Venezuela",
        "Vietnam",
        "Yemen",
        "Zambia",
        "Zimbabwe"
    ];

            foreach($countries as $country){
                try {
                    // Fetch user data
                    $response = $this->client->request('GET',
                        'https://transfermarkt-api.fly.dev/competitions/search/'.$country, [
                            'headers' => [
                                'accept' => 'application/json',
                            ]
                        ]);
                    $userData = json_decode($response->getContent(), true);

                    if(is_array($userData) && array_key_exists('results', $userData)){
                        foreach ($userData['results'] as $item){

                            $competion = $this->entityManager->getRepository(Competitions::class)
                                ->findOneBy(array(
                                    'competionId'=>$item['id']
                                ));
                            if($competion instanceof Competitions){}
                            else{$competion = new Competitions();}


                            $competion->setCompetionId($item['id']);
                            $competion->setName($item['name']);
                            $competion->setCompetitionName($item['name']);
                            $competion->setCountry($item['country']);
                            $competion->setClubs($item['clubs']);
                            $competion->setPlayers($item['players']);
                            $competion->setTotalMarketValue($item['totalMarketValue']);
                            $competion->setMeanMarketValue($item['meanMarketValue']);
                            $competion->setContinent($item['continent']);
                            $competion->setCompetionContinent($item['continent']);

                            $this->entityManager->persist($competion);
                            $this->entityManager->flush();
                        }
                    }

                } catch (\Exception $e) {

                }
            }


    }

    public function getClubsByCompetionId(){

        $competitions = $this->entityManager->getRepository(Competitions::class)
            ->findAll();

        foreach ($competitions as $competiion){
            try {
                // Fetch user data
                $response = $this->client->request('GET',
                    'https://transfermarkt-api.fly.dev/competitions/'.($competiion->getCompetionId()).'/clubs', [
                        'headers' => [
                            'accept' => 'application/json',
                        ]
                    ]);
                $userData = json_decode($response->getContent(), true);

                if(is_array($userData) && array_key_exists('clubs', $userData)){
                    $club = $this->entityManager->getRepository(CompetitionClubs::class)
                        ->findOneBy(array(
                            'competitionId'=>($competiion->getCompetionId())
                        ));
                    if($club instanceof CompetitionClubs){}
                    else{$club = new CompetitionClubs();}


                    $club->setCompetitionId(($competiion->getCompetionId()));
                    $club->setClubs($userData['clubs']);

                    $this->entityManager->persist($club);
                    $this->entityManager->flush();
                }

            } catch (\Exception $e) {

            }
        }


    }

    public function getPlayersByClubId(){

        $competitionsClubs = $this->entityManager->getRepository(CompetitionClubs::class)
            ->findAll();

        foreach ($competitionsClubs as $competiionClub){
            $clubs=$competiionClub->getClubs();
            foreach ($clubs as $club){
                try {
                    // Fetch user data
                    $response = $this->client->request('GET',
                        'https://transfermarkt-api.fly.dev/clubs/'.($club['id']).'/players', [
                            'headers' => [
                                'accept' => 'application/json',
                            ]
                        ]);
                    $userData = json_decode($response->getContent(), true);

                    if(is_array($userData) && array_key_exists('players', $userData)){
                        $players = $this->entityManager->getRepository(PlayersClub::class)
                            ->findOneBy(array(
                                'clubId'=>$club['id']
                            ));
                        if($players instanceof PlayersClub){}
                        else{$players = new PlayersClub();}


                        $players->setClubId($club['id']);
                        $players->setPlayers($userData['players']);

                        $this->entityManager->persist($players);
                        $this->entityManager->flush();
                    }

                } catch (\Exception $e) {

                }
            }

        }


    }

}