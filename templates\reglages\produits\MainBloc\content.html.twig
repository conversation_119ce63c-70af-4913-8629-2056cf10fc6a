<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('styles/MainBloc.css') }}">
<div class="table-container">
	<nav class="navbar table-navbar navbar-expand-lg">
		<ul class="navbar-nav me-auto mb-1 mb-lg-0">
			<li class="nav-item" id="tablenav">
				<a class="nav-link active" href="#">All</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unfulfilled</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unpaid</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Open</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Closed</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Local Delivery</a>
			</li>
		</ul>
		<div class="navbar-nav">
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-search"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-sliders"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-filter"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-arrow-down-up"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-three-dots"></i>
			</a>
		</div>
	</nav>
<div class="container mt-2">
    <div class="row mb-3 align-items-center">
        <div class="col-10 col-md-2">
            <div class="custom-select-container">
                <i class="bi bi-box icon"></i>
                <select class="form-select" id="categorySelect">
                    <option disabled selected>Category</option>
                    {% set displayedCategories = [] %}
                    {% for product in produits %}
                        {% if product.category.name not in displayedCategories %}
                            <option value="{{ product.category.name }}">{{ product.category.name }}</option>
                            {% set displayedCategories = displayedCategories | merge([product.category.name]) %}
                        {% endif %}
                    {% else %}
                        <option disabled>No products available</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div id="addfilter" class="col-12 col-md-2 d-flex align-items-center justify-content-center">
            <span>+ Add filter</span>
        </div>
    </div>
</div>

	<table class="table table-responsive" id="productTable">
    <thead>
        <tr>
            <th style="width:2%;">
                <label class="custom-checkbox">
                    <input type="checkbox" class="form-check-input" id="customCheck">
                </label>
            </th>
            <th>
                <i class="bi bi-hash"></i>ID
            </th>
            <th>
                <i class="bi bi-person"></i>Name
            </th>
            <th>
                <i class="bi bi-currency-dollar"></i>Price
            </th>
            <th>
                <i class="bi bi-credit-card"></i>Organization Category
            </th>
            <th>
                <i class="bi bi-wallet2"></i>Etat
            </th>
            <th>
                <i class="bi bi-box"></i>Category Name
            </th>
        </tr>
    </thead>
    <tbody>
        {% for product in produits %}
            <tr class="product-row" data-category="{{ product.category.name }}">
                <td><input type="checkbox" class="form-check-input" id="customCheck"></td>
                <td><strong>{{ product.id }}</strong></td>
                <td><strong>{{ product.name }}</strong></td>
                <td>${{ product.price }}</td>
                <td>{{ product.organizationCategory }}</td>
                <td>{{ product.is_enabled }}</td>
                <td>{{ product.category.name }}</td>
            </tr>
        {% else %}
            <tr>
                <td colspan="7">No products available.</td>
            </tr>
        {% endfor %}
    </tbody>
</table>
</div></div><script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.getElementById('categorySelect').addEventListener('change', function() {
        var selectedCategory = this.value;
        var rows = document.querySelectorAll('#productTable .product-row');

        rows.forEach(function(row) {
            if (row.getAttribute('data-category') === selectedCategory || selectedCategory === "") {
                row.style.display = ''; // Show row
            } else {
                row.style.display = 'none'; // Hide row
            }
        });
    });
</script>