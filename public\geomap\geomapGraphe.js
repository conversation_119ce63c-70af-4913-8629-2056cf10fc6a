let productionsData = {};
async function fetchDataCercleGraphe(url, jwt) {
    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${jwt}`,
                "Content-Type": "application/json"
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error("Erreur lors de la récupération des données :", error);
        return null;
    }
}

async function fetchAllDatatest(clusterCode,codeInsee) {
    //const clusterCode = localStorage.getItem('clusterCode');
    //const codeInsee = localStorage.getItem('codeInsee');
    const optionGroups = ['Tech', 'Produit', 'Categorie', 'PreCommande'];
    const date=formatDateRange(month,year);
    try {
        const dataPromises = optionGroups.map(optionGroup => {
            let url = `https://api.nomadcloud.fr/api/productions-kpi-by-option-group/${pointOfSaleId}/${Type}/${optionGroup}?${date}&page=1`;
            if (clusterCode) {
                url += `&codeCluster=${clusterCode}`;
            }
            if (codeInsee) {
                url += `&codeInsee=${codeInsee}`;
            }
            return fetchDataCercleGraphe(url, jwtToken);
        });

        const results = await Promise.all(dataPromises);
        results.forEach((data, index) => {
            productionsData[optionGroups[index]] = data;
        });

        var chartDataParGamme = productionsData["Produit"];
        var chartDataParStatutPreco = productionsData["PreCommande"];
        if (!chartDataParGamme || chartDataParGamme.length === 0) {
            console.warn("Aucune donnée disponible pour le graphique.");
            return;
        }
        ParGamme(chartDataParGamme);
        ParParcourrChart(productionsData);
        ParTechno(productionsData);
        ParStatutPreco(chartDataParStatutPreco);
    } catch (error) {
        console.error("Error fetching data:", error);
    }
}

document.addEventListener('DOMContentLoaded', function () {
    // Chain your fetches if order is important, otherwise run in parallel if possible.
    Promise.all([
        fetchAllDatatest(),
        fetchData(clusterCode),
        //fetchDatacohorte(clusterCode),
        fetchProductionKPI(),
        fetchProductionsIntervention(clusterCode),
        fetchClientSatisfactionAverages(clusterCode)
    ]).then(() => {
        fetchProductionsAnnulation(clusterCode);
    }).catch(error => {
        console.error('Error in initial data fetching:', error);
    });
});

var clusterCode ='';
function fetchdataCluster(clusterCode) {
    localStorage.setItem('clusterCode', clusterCode);
    clusterCode=clusterCode;
    fetchAllDatatest();
    fetchData(clusterCode);
    fetchAndDrawChartsCohorte(clusterCode),
    fetchProductionKPI();
    fetchProductionsIntervention(clusterCode);
    fetchProductionsAnnulation(clusterCode);
    fetchClientSatisfactionAverages(clusterCode);
  }

  let dataByCPV;

async function fetchProductionKPI() {
    const url = `https://api.nomadcloud.fr/api/objectifs-clusters-productions?mois=${month}&annee=${year}&page=1`;
    fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${jwtToken}`,
            'Content-Type': 'application/json'
        }
    }).then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();  // Convert the response body to JSON
    }).then(data => {
        dataByCPV = data.find(item => item.cpv === cpv);
        // getClustersWithoutSales(); 
        return data;
    }).catch(error => {
        console.log("Error in API fetchProductionKPI:", error.message);
    });
}


function ProductionKPIByPointOfSaleId(clusterCode) {
    let clusterDetails = dataByCPV.clusters.find(item => item.codeCluster === clusterCode);
    const container = document.querySelector('.list-containers');
    container.innerHTML = '';
    if(!clusterDetails){
        return;
    }
    const statuses = clusterDetails.status;
   
    var libelleCluster=document.getElementById('libelleCluster');
    libelleCluster.innerHTML = clusterDetails.libelleCluster;
    for (const [key, value] of Object.entries(statuses)) {
        const statusHtml = `
            <div class="statistic">
                <div class="statistic-content">
                    <div class="stat-title">${key}</div>
                    <div class="stat-number">${value}</div>
                </div>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${(value/ clusterDetails.nombreVentesCluster) * 100}%"></div>
                </div>
            </div>
        `;
        container.innerHTML += statusHtml;

    }
    container.innerHTML += `
    <div class="pourcentageVente" style="width: 80px; height: 10px; color: #55C5D0;"></div>
       <span>R/O: ${clusterDetails.objectifCluster.pourcentageVente}</span>
    </div>
 `;
}

function fetchProductionsIntervention(clusterData,codeInsee) {
    const date=formatDateRange(month, year);
    let url = `https://api.nomadcloud.fr/api/productions-by-intervention-status/${pointOfSaleId}?${date}&page=1`;

    if (clusterData) {
        url += `&codeCluster=${clusterData}`;
    }
    if (codeInsee) {
        url += `&codeInsee=${codeInsee}`;
    }

    fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + jwtToken
        }
    })
    .then(response => response.json())
    .then(data => {
        const firstResult = data[0];
        updateProgressBars(firstResult);
    })
    .catch(error => console.error('Error fetching data:', error));
}



function updateProgressBars(data) {
   
    const totalXapRDV = parseInt(data.totalXapRDV, 10);
    const totalXavRDV = parseInt(data.totalXavRDV, 10);

    if (totalXapRDV + totalXavRDV > 0) { // Ensure no division by zero
        const xapPercentage = (totalXapRDV / (totalXapRDV + totalXavRDV)) * 100;
        const xavPercentage = (totalXavRDV / (totalXapRDV + totalXavRDV)) * 100;

        document.querySelector('.progress-bar-primary').style.width = `${xapPercentage}%`;
        document.querySelector('.progress-label-center.xap').textContent = totalXapRDV.toString();
        document.querySelector('.progress-label-end.xap').textContent = `${xapPercentage.toFixed(2)}%`;

        document.querySelector('.progress-bar-secondary').style.width = `${xavPercentage}%`;
        document.querySelector('.progress-label-center.xav').textContent = totalXavRDV.toString();
        document.querySelector('.progress-label-end.xav').textContent = `${xavPercentage.toFixed(2)}%`;
    } else {
        document.querySelectorAll('.progress-bar').forEach(bar => bar.style.width = `0%`);
        document.querySelectorAll('.progress-label-center').forEach(label => label.textContent = `0`);
        document.querySelectorAll('.progress-label-end').forEach(label => label.textContent = `0%`);
    }
}

async function fetchProductionsAnnulation(clusterData) {
    const date =formatDateRange(month, year);
    const url = new URL(`https://api.nomadcloud.fr/api/productions-by-annulation-types/${pointOfSaleId}`);
    url.search = date;

    if (clusterData) {
        url.searchParams.append("codeCluster", clusterData);
    }
    url.searchParams.append("page", 1);

    try {
        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`
            }
        });
        const data = await response.json();
        TotalVEnte = data[0].totalVentes; // Assuming data is structured this way
        updateProgressBar(data[0]); // Update progress bar with fetched data
        return data[0]; // Returning this for potential future use
    } catch (error) {
        console.error('Error fetching data:', error);
        throw error; // Re-throw to handle it in the calling function
    }
}


function updateProgressBar(data) {
    const totalVentes = data.totalVentes || 0;
    const totalReseau = data.totalReseau || 0;
    const progress = totalReseau > 0 ? (totalVentes / totalReseau) * 100 : 0;

    const progressBar = document.querySelector('.row3dashsales-progress-bar-complete');
    progressBar.style.width = `${progress}%`;
    progressBar.setAttribute('aria-valuenow', progress);

    const progressText = document.querySelectorAll('.progress-bar-text.percentage');
    progressText[0].textContent = totalVentes;
    progressText[1].textContent = totalReseau;
}


let SatisfactionAverages;

function fetchClientSatisfactionAverages(clusterData) {
    const cardNumber = document.querySelector('.MoyGeneral');
    const url = new URL(`https://api.nomadcloud.fr/api/satisfaction-clients-averages/${pointOfSaleId}`);
    
    if (clusterData) { // Ajoute codeCluster uniquement si clusterData est défini
        url.searchParams.append("codeCluster", clusterData);
    }

    url.searchParams.append("page", 1);

    fetch(url.toString(), {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${jwtToken}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        SatisfactionAverages = data;

        // Vérifie si les données existent avant d'accéder aux valeurs
        if (SatisfactionAverages["2024"] && SatisfactionAverages["2024"]["12"]) {
            const yearData = SatisfactionAverages["2024"]["12"];
           // cardNumber.textContent = `${parseFloat(yearData.moyenneTotal).toFixed(2)}`;
        }
    })
    .catch(error => {
        console.error('Error fetching data:', error);
    });
}


function findClusterData(clusterCode) {
    let numericMonth = Number(month);
    let numericyear = Number(year);
    console.log('SatisfactionAverages',SatisfactionAverages);
    //const yearData = SatisfactionAverages[numericyear][numericMonth];
    //let clusterDetails = dataByCPV.clusters.find(item => item.codeCluster === clusterCode);
    // const clusters = yearData.clusters;
    // const cluster = clusters.find(c => c.codeCluster === clusterCode);
    var MoyGeneral=document.querySelector('.MoyGeneral');
    var MoyParCluster=document.querySelector('.MoyParCluster');
    // MoyGeneral.style.display='none';
    // MoyParCluster.style.display='block';
    // if (cluster) {
    //     if(cluster.moyenneParCluster) {
    //     MoyParCluster.innerHTML = `${parseFloat(cluster.moyenneParCluster).toFixed(2)}`;
    //     }
    // }else{
    //     MoyParCluster.innerHTML = `-`;
    // }
}



let fetchedData; // Déclarez la variable globalement pour stocker les données récupérées

function fetchData(clusterData,codeInsee) {
    // const now = new Date();
    // const month = now.getMonth() + 1;
    // const year = now.getFullYear();

    const url = new URL(`https://api.nomadcloud.fr/api/productions-by-category/${pointOfSaleId}`);
    url.searchParams.append("month", month);
    url.searchParams.append("year", year);

    if (clusterData) {
        url.searchParams.append("codeCluster", clusterData);
    }
    if (codeInsee) {
        url.searchParams.append("codeInsee", codeInsee);
    }

    url.searchParams.append("page", 1);

    fetch(url.toString(), {
        method: 'GET',
        headers: { 'Authorization': `Bearer ${jwtToken}` }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {

        fetchedData = data; // Stocke les données récupérées
        const selectedCategory = document.getElementById("categorySelect").value;
        processCategoryData(fetchedData, selectedCategory); // Appelle processCategoryData avec les données récupérées
    })
    .catch(error => {
        console.error('Error fetching data:', error);
    });
}

document.getElementById("categorySelect").addEventListener("change", handleCategoryChange);

function handleCategoryChange() {
    const selectedCategory = document.getElementById("categorySelect").value;
    if (fetchedData) {
        processCategoryData(fetchedData, selectedCategory);
    }
}

google.charts.load('current', {packages: ['corechart']});
google.charts.setOnLoadCallback(init);

function init() {
    fetchData();
    setupThemeSwitcher();
}


let categoryTotalsForDrawChart;
function processCategoryData(data, selectedCategory) {
    const categoryTotals = [];
    let brutTotal = 0;
    let raccordeTotal = 0;
    let valideTotal = 0;
    let overallTotal = 0;
    let sommeCONQUETE = 0;
    let sommeMIGRATION = 0;
    let sommeMOBILES = 0;
    if (Type==='B') {
        dataType='Brut';
    }else if(Type==='R'){
        dataType='Raccordé';
    }else if(Type==='V'){
        dataType='Validé';
    }
    const today = new Date();
    const currentMonth = `${year}-${month}`;
    let updateTotals = selectedCategory !== "" && selectedCategory !== null;
    Object.entries(data).forEach(([month, categories]) => {
        let monthBrut = 0, monthRaccorde = 0, monthValide = 0;

        Object.entries(categories).forEach(([status, statusData]) => {
            Object.entries(statusData).forEach(([category, value]) => {
                const parsedValue = Number(value) || 0;

                if (category === "CONQUETE") {
                    if (status === "Brut") monthBrut += parsedValue;
                    else if (status === "Raccordé") monthRaccorde += parsedValue;
                    else if (status === "Validé") monthValide += parsedValue;
                } else if (category === "MIGRATION") {
                    if (status === "Brut") monthBrut += parsedValue;
                    else if (status === "Raccordé") monthRaccorde += parsedValue;
                    else if (status === "Validé") monthValide += parsedValue;
                } else if (category === "MOBILES") {
                    if (status === "Brut") monthBrut += parsedValue;
                    else if (status === "Raccordé") monthRaccorde += parsedValue;
                    else if (status === "Validé") monthValide += parsedValue;
                }
            });
        });

        if (updateTotals) {
            // Seulement mettre à jour si la catégorie est sélectionnée
            if (selectedCategory === "Brut") {
                categoryTotals.push([month, monthBrut]);
                if (month === currentMonth) sommeCONQUETE += monthBrut;
            } else if (selectedCategory === "Raccordé") {
                categoryTotals.push([month, monthRaccorde]);
                if (month === currentMonth) sommeMIGRATION += monthRaccorde;
            } else if (selectedCategory === "Validé") {
                categoryTotals.push([month, monthValide]);
                if (month === currentMonth) sommeMOBILES += monthValide;
            } else {
                // Si aucune catégorie sélectionnée, additionner Brut, Raccordé et Validé
                categoryTotals.push([month, monthBrut + monthRaccorde + monthValide]);
                if (month === currentMonth) {
                    sommeCONQUETE += monthBrut;
                    sommeMIGRATION += monthRaccorde;
                    sommeMOBILES += monthValide;
                }
            }
        } else {
            // Si la catégorie est vide ou null, additionner Brut, Raccordé et Validé
            categoryTotals.push([month, monthBrut + monthRaccorde + monthValide]);
            if (month === currentMonth) {
                sommeCONQUETE += monthBrut;
                sommeMIGRATION += monthRaccorde;
                sommeMOBILES += monthValide;
            }
        }

        if (month === currentMonth) {
            sommeCONQUETE = (categories[dataType]?.CONQUETE || 0);
            sommeMIGRATION = (categories[dataType]?.MIGRATION || 0);
            sommeMOBILES = (categories[dataType]?.MOBILES || 0);

            overallTotal = monthBrut + monthRaccorde + monthValide;
        }
    });
    // Mettez à jour les éléments pour les sommes des catégories
    updateElement("totalsommeCONQUETE", sommeCONQUETE);
    updateElement("totalsommeMIGRATION", sommeMIGRATION);
    updateElement("totalsommeMOBILES", sommeMOBILES);
    categoryTotalsForDrawChart=categoryTotals;
    drawAudienceChart();
    // updateElement("totalsomme", overallTotal);
    let TotalVEnte=sommeCONQUETE+sommeMIGRATION+sommeMOBILES;
    const totalVente = document.querySelector('.totalVente');
    const totalVenteCluter = document.querySelector('.totalVenteCluter');
    // Assuming TotalVEnte is updated globally by fetchProductionsAnnulation
    //totalVente.innerHTML = TotalVEnte;
    totalVenteCluter.innerHTML = TotalVEnte;
    updateProgressBarstest();
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) element.textContent = value;
}


function drawAudienceChart() {
    let data = categoryTotalsForDrawChart;
    const selectedTheme = localStorage.getItem("theme");
    const chartColor = selectedTheme === "light" ? "#145177" : "#54C5d0";

    const options = {
        title: '',
        legend: { position: 'none' },
        hAxis: {
            textPosition: 'none',
            gridlines: { color: 'transparent' },
            baselineColor: 'transparent'
        },
        vAxis: {
            minValue: 0,
            textPosition: 'none',
            gridlines: { color: 'transparent', count: 4 },
            baselineColor: 'transparent'
        },
        backgroundColor: 'transparent',
        colors: [chartColor],
        bar: { groupWidth: '100%' },
        chartArea: {
            left: 0,
            top: '30px',
            width: '100%',
            height: '100%'
        },
        tooltip: { 
            isHtml: true,
            textStyle: { color: 'white' }
        }
    };

    const chart = new google.visualization.ColumnChart(document.getElementById('audienceChartStat'));
    chart.draw(google.visualization.arrayToDataTable([
        ['Month', 'Total'],
        ...data
    ]), options);
}




function drawAudienceOverview() {
    fetchData(); // Ensure data is fetched when charts are ready
    setupThemeSwitcher();
}


function setupThemeSwitcher() {
    const themeOptionItems = document.querySelectorAll(".theme-options li");
    themeOptionItems.forEach((item) => {
        item.addEventListener("click", () => {
            const selectedTheme = item.getAttribute("data-theme");
            document.documentElement.setAttribute("data-theme", selectedTheme);

            // Update active class
            themeOptionItems.forEach((el) => el.classList.remove("active"));
            item.classList.add("active");

            // Redraw the chart with the new theme
            fetchData();
        });
    });
}
function updateProgressBarstest() {
        // Get the total values for each status
        const brutTotal = parseInt(document.getElementById("totalsommeCONQUETE").textContent);
        const raccordeTotal = parseInt(document.getElementById("totalsommeMIGRATION").textContent);
        const valideTotal = parseInt(document.getElementById("totalsommeMOBILES").textContent);

        // Find the maximum total sum to normalize the width percentages
        const maxTotal = Math.max(brutTotal, raccordeTotal, valideTotal);


    // Calculate the width percentages for each progress bar
    const brutWidth = (brutTotal / maxTotal) * 100;
    const raccordeWidth = (raccordeTotal / maxTotal) * 100;
    const valideWidth = (valideTotal / maxTotal) * 100;

    // Set the width of the progress bars
    document.getElementById("progress-bar-CONQUETE").style.width = `${brutWidth}%`;
    document.getElementById("progress-bar-MIGRATION").style.width = `${raccordeWidth}%`;
    document.getElementById("progress-bar-MOBILES").style.width = `${valideWidth}%`;
    
}





let ProductionSalesByDays;
async function fetchProductionSalesByDays(clusterCode){
    const url = `https://api.nomadcloud.fr/api/productions-zero-sales-days/${pointOfSaleId}/${clusterCode}?optionSelect=${Type}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        ProductionSalesByDays = data;
        document.querySelector('.nbrVilleSupCentJrs').textContent=`${ProductionSalesByDays.nombre_villes_sup_100_jrs}`;
        return data;  // Return data for further processing
    } catch (error) {
        console.error('Error fetching production sales by days:', error);
        throw error;  // Rethrow or handle error as needed
    }
}

async function sendRightdataforChartDAys(code,isVille){
    if(isVille){
        days=ProductionSalesByDays.villes.find(ville => ville.code_insee==code).nombre_jours;
        document.querySelector('.annotationChart').textContent=days+' jrs';
    }else{
        clusterData= await fetchProductionSalesByDays(code);
        days=clusterData.max_nb_jours;
        document.querySelector('.annotationChart').textContent=days+' jrs';
    }
    var echelle=100;
    DisplayChartDays(days,echelle);
}
 
let chartInstance = null;

function DisplayChartDays(days, echelle) {
    const canvas = document.getElementById('cpuUtilizationChart');
    
    // Set canvas dimensions
    canvas.style.width = '160px';
    canvas.style.height = '80px';
    
    // These ensure the drawing buffer matches the physical size.
    canvas.width = 160;
    canvas.height = 80;

    const ctx = canvas.getContext('2d');

    // Destroy the existing chart instance if it exists
    if (chartInstance) {
        chartInstance.destroy();
    }

    const COLORS = ['#54C5d0', '#e8a60f', '#e80f0f'];
    const percentage = (days / echelle) * 100;
    const index = (value) => {
        if (value < 33) return 0;
        else if (value < 66) return 1;
        return 2;
    };

    // Get the CSS variable color from the root (document body)
    const backgroundColor = getComputedStyle(document.body).getPropertyValue('--tree-view-bg').trim() || 'rgb(234, 234, 234)';

    const data = {
        datasets: [{
            data: [percentage, 100 - percentage],
            backgroundColor: [
                COLORS[index(percentage)],
                backgroundColor
            ],
            borderColor: '#a09a9a',
            borderWidth: 2
        }]
    };

    const config = {
        type: 'doughnut',
        data: data,
        options: {
            circumference: 180,
            rotation: -90,
            aspectRatio: 16 / 8, // Adjust to maintain shape
            plugins: {
                legend: {
                    display: false  // Hides the legend
                },
                tooltip: {
                    enabled: true,  // Enables tooltips
                    callbacks: {
                        label: function(tooltipItem) {
                            return tooltipItem.parsed + '%';  // Tooltip as percentage
                        }
                    }
                },
            },
            cutout: '80%',  // Doughnut thickness
            responsive: false,  // Disable responsiveness to maintain fixed size
            maintainAspectRatio: false  // Prevent distortion to maintain aspect ratio
        }
    };

    // Create a new chart instance
    chartInstance = new Chart(ctx, config);
}
async function productionsTopFlop() {
    var date = `${year}-${String(month).padStart(2, '0')}`;
    try {
        let url = `https://api.nomadcloud.fr/api/productions-top-flop-clusters/${pointOfSaleId}?optionSelect=${Type}&yearMonth=${date}&page=1`;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        displayClusters(data);

    } catch (error) {
        console.error('Fetch error:', error);
    }
}

function displayClusters(data) {
    const topContainer = document.getElementById("top-clusters");
    const flopContainer = document.getElementById("flop-clusters");

    if (!data || !data.top_clusters || !data.flop_clusters) return;

    // Nettoyer les conteneurs avant d'ajouter les nouvelles données
    topContainer.innerHTML = "";
    flopContainer.innerHTML = "";

    // Affichage des TOP clusters avec flèche vers le haut 🔼
    data.top_clusters.forEach(cluster => {
        const clusterDiv = document.createElement("div");
        clusterDiv.classList.add("statistic");
        clusterDiv.innerHTML = `
            <div class="statistic-content">
                <div class="stat-title">${cluster.libelle_cluster} </div>
                <div class="stat-number">
                    ${cluster.total_ventes} <span ><i class="bi bi-caret-up-fill"></i></span>
                </div>
            </div>
        `;
        topContainer.appendChild(clusterDiv);
    });

    // Affichage des FLOP clusters avec flèche vers le bas 🔽
    data.flop_clusters.forEach(cluster => {
        const clusterDiv = document.createElement("div");
        clusterDiv.classList.add("statistic");
        clusterDiv.innerHTML = `
            <div class="statistic-content">
                <div class="stat-title">${cluster.libelle_cluster}</div>
                <div class="stat-number">
                    ${cluster.total_ventes} <span ><i class="bi bi-caret-down-fill"></i></span>
                </div>
            </div>
        `;
        flopContainer.appendChild(clusterDiv);
    });
}
productionsTopFlop();
let fetchedDataR = [];
let fetchedDataV = [];

async function fetchDatas(cohortType) {
    const date = `${String(month).padStart(2, '0')}`;
    const codeCluster = localStorage.getItem("clusterForOnedays") || "";
    const codeInsees = localStorage.getItem("codeinseeForOnedays") || "";
    const url = new URL(`https://api.nomadcloud.fr/api/productions-cohorte/${pointOfSaleId}/${year}/${cohortType}`);
    url.searchParams.append("mois", date);
    url.searchParams.append("codeCluster", codeCluster);
    url.searchParams.append("codeInsee", codeInsees);
    url.searchParams.append("page", 1);



    try {
        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: { 'Authorization': `Bearer ${jwtToken}` },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        if (!data || data.length === 0) {
            console.warn("⚠️ Aucune donnée reçue de l'API.");
            return [];
        }

        return data;
    } catch (error) {
        console.error("❌ Erreur lors de la récupération des données:", error);
        return [];
    }
}

const chartInstances = {};

function formatMonth(month) {
    const monthMap = {
        "01": "JAN", "02": "FEB", "03": "MAR", "04": "APR",
        "05": "MAY", "06": "JUN", "07": "JUL", "08": "AUG",
        "09": "SEP", "10": "OCT", "11": "NOV", "12": "DEC"
    };
    if (!month.includes("-")) return month;
    const [year, monthNum] = month.split("-");
    return `${monthMap[monthNum]} ${year}`;
}
function drawAudienceCharts(data, chartElementId) {
    if (!data || data.length === 0) {
        console.warn(`⚠️ Pas de données disponibles pour ${chartElementId}`);
        return;
    }

    if (chartInstances[chartElementId]) {
        chartInstances[chartElementId].destroy();
    }

    const labels = data.map(item => formatMonth(item.mois_raccordement || item.mois_validation));

    const months = new Set();
    data.forEach(item => {
        if (item.ventes_par_mois_validation) {
            Object.keys(item.ventes_par_mois_validation).forEach(mois => months.add(mois));
        }
        if (item.ventes_par_mois_raccordement) {
            Object.keys(item.ventes_par_mois_raccordement).forEach(mois => months.add(mois));
        }
    });
    const sortedMonths = [...months].sort();

    const datasets = sortedMonths.map((mois, index) => {
        return {
            label: formatMonth(mois),
            data: data.map(item =>
                (item.ventes_par_mois_validation?.[mois]?.ventes || 0) +
                (item.ventes_par_mois_raccordement?.[mois]?.ventes || 0)
            ),
            backgroundColor: generateColor(index),
            borderColor: generateColor(index),
            borderWidth: 1
        };
    });

    function generateColor(index) {
        const selectedTheme = localStorage.getItem("theme");
        const baseColor = selectedTheme === "light" ? "145, 177, 235" : "84, 197, 208"; 
        const opacity = Math.min(1, 0.3 + (index * 0.1));
        return `rgba(${baseColor}, ${opacity})`;
    }

    const ctx = document.getElementById(chartElementId).getContext('2d');
    chartInstances[chartElementId] = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            scales: {
                x: {
                    stacked: true,
                    grid: { display: false },
                    ticks: { color: 'transparent', font: { size: 10 } }
                },
                y: {
                    stacked: true,
                    grid: { display: false },
                    ticks: { color: '#808080', font: { size: 10 } }
                }
            },
            plugins: {
                legend: { display: false },
                tooltip: {
                    enabled: true,
                    intersect: false,
                    callbacks: {
                        title: tooltipItem => `Mois: ${tooltipItem[0].dataset.label}`,
                        label: tooltipItem => `Ventes : ${tooltipItem.raw}`
                    },
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: 'white',
                    bodyColor: 'white'
                }
            }
        },
        plugins: [{
            id: 'totalPlugin',
            afterDatasetsDraw(chart) {
                const { ctx, data, chartArea } = chart;
                ctx.font = 'bold 12px Arial';
                ctx.fillStyle = '#808080';
                ctx.textAlign = 'right'; // Aligner le texte à droite
        
                data.datasets[0].data.forEach((_, index) => {
                    let total = 0;
                    data.datasets.forEach(dataset => {
                        total += dataset.data[index] || 0;
                    });
        
                    const meta = chart.getDatasetMeta(0);
                    const dataElement = meta.data[index];
        
                    if (dataElement) {
                        const x = chartArea.right + 8; // Position à droite
                        const y = dataElement.y + 5;
                        ctx.fillText(total, x, y);
                    }
                });
            }
        }]
        
    });
}



async function fetchAndDrawChartsCohorte() {
    console.log("📡 Récupération des données...");
    fetchedDataR = await fetchDatas('R');
    fetchedDataV = await fetchDatas('V');



    // Supprime les anciens graphiques si les données sont vides
    if (fetchedDataR.length === 0) {
        
        if (chartInstances['audienceChartStatcohorter']) {
            chartInstances['audienceChartStatcohorter'].destroy();
            chartInstances['audienceChartStatcohorter'] = null;
        }
    } else {
        
        drawAudienceCharts(fetchedDataR, 'audienceChartStatcohorter');
    }

    if (fetchedDataV.length === 0) {
        
        if (chartInstances['audienceChartStatcohortev']) {
            chartInstances['audienceChartStatcohortev'].destroy();
            chartInstances['audienceChartStatcohortev'] = null;
        }
    } else {
       
        drawAudienceCharts(fetchedDataV, 'audienceChartStatcohortev');
    }
}

// 🔄 Détection des changements dans localStorage (codeCluster & codeInsee)
let previousCodeCluster = localStorage.getItem("clusterForOnedays");
let previousCodeInsee = localStorage.getItem("codeinseeForOnedays");

setInterval(() => {
    const currentCodeCluster = localStorage.getItem("clusterForOnedays");
    const currentCodeInsee = localStorage.getItem("codeinseeForOnedays");

    if (currentCodeCluster !== previousCodeCluster || currentCodeInsee !== previousCodeInsee) {
        console.log("🔄 Changement détecté ! Mise à jour des données...");
        previousCodeCluster = currentCodeCluster;
        previousCodeInsee = currentCodeInsee;

        fetchAndDrawChartsCohorte();
    }
}, 2000);



// // Charger les graphiques une fois Google Charts prêt
// google.charts.load('current', { packages: ['corechart'] });
// google.charts.setOnLoadCallback(fetchAndDrawCharts);
