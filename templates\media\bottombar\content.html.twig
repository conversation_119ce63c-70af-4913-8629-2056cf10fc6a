<style>:root
{

	--dark-background-color: #fff;
	--popup-background-button: #f2f2f2;
	--popup-color-button: #333;
	--user-names-color: #333;
	--popup-but-background: white;
	--popup-but-color: #333;
	--popup-avatar: white;
	--popup-paddingg: 5px;
}


[data-theme="dark"] {

	--dark-background-color: #000;
	--popup-background-button: #111;
	--popup-color-button: #BFBFBF;
	--user-names-color: #fff;
	--popup-but-background: #404040;
	--popup-but-color: #BFBFBF;
	--popup-avatar: #000;
	--popup-paddingg: 0;
}

.user-profile {
	display: flex;
	align-items: center;
	cursor: pointer;
	width: 300px;
	padding: 10px;

}

.avatar-wrapper {
	position: relative;

}
.avatar-wrappers {
	position: relative;
	margin-right: 10px;
}

.user-avatar {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: #ff4f4f;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 30px;
	font-weight: bold;
}

.status-dot {
	width: 12px;
	height: 12px;
	background-color: green;
	border-radius: 50%;
	position: absolute;
	bottom: 5px;
	right: 5px;
	border: 2px solid white;
}
.status-dots {
	width: 24px;
	height: 24px;
	background-color: green;
	border-radius: 50%;
	position: absolute;
	bottom: 5px;
	right: 0;
	border: 5px solid var(--popup-avatar);
}
.status-enligne {
	width: 14px;
	height: 14px;
	background-color: green;
	border-radius: 50%;
	margin-top: 4px;
}


.circules {
	width: 12px;
	height: 12px;
	background-color: #008000;
	border-radius: 50%;
	position: absolute;
	bottom: 5px;
	right: 5px;
	border: 2px solid white;
}
.popup {
	display: none; /* Hidden by default */
	position: fixed; /* Stay in place */
	z-index: 1000; /* Sit on top */
	left: 15%;
	top: 75%;
	transform: translate(-50%, -50%); /* Center the popup */
	width: 300px; /* Set width */
	height: 395px; /* Set height */
	background-color: var(--dark-background-color); /* White background */
	border: 1px solid #ccc; /* Gray border */
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Shadow effect */
	border-radius: 8px; /* Rounded corners */

}


.popup-avatar {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	background-color: #ff4f4f;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 30px;
	font-weight: bold;
	border-color: var(--popup-avatar);
	border: 5px solid var(--popup-avatar);
	z-index: -10;
}

.popup-user-info {
	text-align: left;
}

.popup-user-info .user-names {
	font-size: 1.4em;
	font-weight: bold;
	color: var(--user-names-color);
}

.popup-user-info .user-id {
	color: var(--user-names-color);
}

.popup-body {
	margin-top: 10px;
	text-align: left;
}
.popup-body button {
	width: 100%;
	padding: 15px;
	background-color: var(--popup-background-button);
	border: none;
	text-align: left;
	font-size: 14px;
	color: var(--popup-color-button);
	border-radius: 5px;
	cursor: pointer;

}
.popup-but button {
	width: 80%;
	padding: 10px;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
	background-color: var(--popup-but-background);
	border: none;
	text-align: left;
	font-size: 1em;
	color: var(--popup-but-color);
	border-radius: 10px;
	cursor: pointer;
	margin-top: -10px;
}


.cards {
	border: 1px solid #ccc;

	background-color: #ffcfcf;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
	width: 100%;
	height: 110px;
	gap: 5px;
	position: relative;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-direction: column;
	flex-direction: column;
	min-width: 0;
	border: 1px solid rgba(0, 0, 0, 0.125);
	border-radius: 0.25rem 0.25rem 0 0;

}
.card {
	width: 300px; /* Largeur de la carte */
	padding: 16px;
	border: 1px solid #ccc;
	border-radius: 8px;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
	margin: 20px auto; /* Centrer la carte */
}
.popup-header {
	display: flex;
	margin-top: 60px;
	align-items: end;
	margin-left: 10px;
}
</style>
<div id="userName">
<div class="user-profile" onclick="showUserModal('Mansour', 80, -200, 'owner')">
	<div class="avatar-wrapper">
		<img src="https://placehold.co/40" alt="User Avatar" class="user-avatar">
		<div class="status-dot"></div>
	</div>
	<div class="user-details">
	
		<span class="user-status">Online</span>
	</div>
</div>


<!-- Popup Element -->
<div class="popup" id="userPopup">


	<div class="cards">
		<div class="popup-header">
			<div class="avatar-wrappers">
				<div class="popup-avatar">R</div>
				<div class="status-dots"></div>
			</div>
			<div class="cirules"></div>
		</div>

	</div>

	<div style="padding: 15px;">

		<div style="margin-left: 80px;" class="popup-but">

			<button style="display: flex;justify-content: space-between;">
				<div style="margin-top: 4px;" class="fa fa-plus-circle" aria-hidden="true"></div>
				<div>
					Ajoute un statut</div>
			</button>
		</div>
		<div class="popup-user-info">
			<span class="user-names">rachid</span><br>
			<span class="user-id">rachid6618</span>
		</div>
		<div class="popup-body">
			<button onclick="window.location.href='{{ path('app_profile') }}'">
				<i class="bi bi-pencil-fill"></i>
				Modifier le profil
			</button>
			<button style="display: flex; justify-content: space-between; ">
				<div class="status-enligne"></div>
				<div style="margin-right: 120px;!important">En ligne
				</div>

				<i class="bi bi-chevron-right "></i>
			</button>

		</div>
		<div class="popup-body">

			<button style="display: flex; justify-content: space-between; ">
				<div class="bi bi-person-circle"></div>
				<div style="margin-right: 50px;!important">Changer de compte</div>
				<i style="" class="bi bi-chevron-right"></i>
			</button>
		</div>

	</div>
</div></div><div class="controls-section">
<div class="icon-wrapper">
	<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewbox="0 0 24 24" width="24" height="24" preserveaspectratio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
		<defs>
			<clipPath id="__lottie_element_5">
				<rect width="24" height="24" x="0" y="0"></rect>
			</clipPath>
			<clipPath id="__lottie_element_7">
				<path d="M0,0 L600,0 L600,600 L0,600z"></path>
			</clipPath>
			<clipPath id="__lottie_element_11">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<clipPath id="__lottie_element_18">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<clipPath id="__lottie_element_28">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<mask id="__lottie_element_29">
				<rect fill="var(--icon-topbar)" width="600" height="600" transform="matrix(1,0,0,1,200,200)"></rect>
				<path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M681.219970703125,212.79600524902344 C681.219970703125,212.79600524902344 215.69700622558594,681.75 215.69700622558594,681.75 C215.69700622558594,681.75 151.75999450683594,749.2319946289062 201.13699340820312,798.8619995117188 C255,853 319.67999267578125,785.0560302734375 319.67999267578125,785.0560302734375 C319.67999267578125,785.0560302734375 785.2030029296875,316.10198974609375 785.2030029296875,316.10198974609375 C785.2030029296875,316.10198974609375 868,234 816.2150268554688,184.4459991455078 C764.781005859375,135.22799682617188 681.219970703125,212.79600524902344 681.219970703125,212.79600524902344" fill-opacity="1"></path>
				<path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M698,405 C698,405 642,405 642,405 C642,405 642,479 642,479 C642,479 698,479 698,479 C698,479 698,405 698,405" fill-opacity="1"></path>
			</mask>
		</defs>
		<g clip-path="url(#__lottie_element_5)">
			<g clip-path="url(#__lottie_element_7)" transform="matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)" opacity="1" style="display: block;">
				<g clip-path="url(#__lottie_element_28)" transform="matrix(1,0,0,1,-200,-200)" opacity="1" style="display: block;">
					<g mask="url(#__lottie_element_29)">
						<g style="display: none;" transform="matrix(-25,0,0,25,800,173)" opacity="1">
							<g opacity="1" transform="matrix(1,0,0,1,12,8.5)">
								<path fill="#F23F42" fill-opacity="1" d=" M-4,-1.3799999952316284 C-4,-3.5889999866485596 -2.2090001106262207,-5.380000114440918 0,-5.380000114440918 C2.2090001106262207,-5.380000114440918 4,-3.5889999866485596 4,-1.3799999952316284 C4,-1.3799999952316284 4,2.509999990463257 4,2.509999990463257 C4,4.718999862670898 2.2090001106262207,6.5 0,6.5 C-2.2090001106262207,6.5 -4,4.718999862670898 -4,2.509999990463257 C-4,2.509999990463257 -4,-1.3799999952316284 -4,-1.3799999952316284z"></path>
							</g>
							<g opacity="1" transform="matrix(1,0,0,1,12,14)">
								<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-7,-2.990000009536743 C-7,0.8759999871253967 -3.865999937057495,4.010000228881836 0,4.010000228881836 C3.865999937057495,4.010000228881836 7,0.8759999871253967 7,-2.990000009536743"></path>
							</g>
							<g opacity="1" transform="matrix(1,0,0,1,12,20)">
								<path fill="#F23F42" fill-opacity="1" d=" M-1,-2 C-1,-2.2760000228881836 -0.7760000228881836,-2.5 -0.5,-2.5 C-0.5,-2.5 0.5,-2.5 0.5,-2.5 C0.7760000228881836,-2.5 1,-2.2760000228881836 1,-2 C1,-2 1,2 1,2 C1,2.2760000228881836 0.7760000228881836,2.5 0.5,2.5 C0.5,2.5 -0.5,2.5 -0.5,2.5 C-0.7760000228881836,2.5 -1,2.2760000228881836 -1,2 C-1,2 -1,-2 -1,-2z"></path>
							</g>
							<g opacity="1" transform="matrix(1,0,0,1,12,22)">
								<path fill="#F23F42" fill-opacity="1" d=" M3,-1 C3.552000045776367,-1 4,-0.5519999861717224 4,0 C4,0.5519999861717224 3.552000045776367,1 3,1 C3,1 -3,1 -3,1 C-3.552000045776367,1 -4,0.5519999861717224 -4,0 C-4,-0.5519999861717224 -3.552000045776367,-1 -3,-1 C-3,-1 3,-1 3,-1z"></path>
							</g>
						</g>
						<g transform="matrix(-25,0,0,25,800,173)" opacity="1" style="display: block;">
							<g opacity="1" transform="matrix(1,0,0,1,12,8.5)">
								<path fill="#F23F42" fill-opacity="1" d=" M-4,-1.4600000381469727 C-4,-3.6689999103546143 -2.2090001106262207,-5.460000038146973 0,-5.460000038146973 C2.2090001106262207,-5.460000038146973 4,-3.6689999103546143 4,-1.4600000381469727 C4,-1.4600000381469727 4,2.5 4,2.5 C4,4.709000110626221 2.2090001106262207,6.5 0,6.5 C-2.2090001106262207,6.5 -4,4.709000110626221 -4,2.5 C-4,2.5 -4,-1.4600000381469727 -4,-1.4600000381469727z"></path>
							</g>
							<g opacity="1" transform="matrix(1,0,0,1,12,14)">
								<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-7,-3 C-7,0.8659999966621399 -3.865999937057495,4 0,4 C3.865999937057495,4 7,0.8659999966621399 7,-3"></path>
							</g>
							<g opacity="1" transform="matrix(1,0,0,1,12,20)">
								<path fill="#F23F42" fill-opacity="1" d=" M-1,-2 C-1,-2.2760000228881836 -0.7760000228881836,-2.5 -0.5,-2.5 C-0.5,-2.5 0.5,-2.5 0.5,-2.5 C0.7760000228881836,-2.5 1,-2.2760000228881836 1,-2 C1,-2 1,2 1,2 C1,2.2760000228881836 0.7760000228881836,2.5 0.5,2.5 C0.5,2.5 -0.5,2.5 -0.5,2.5 C-0.7760000228881836,2.5 -1,2.2760000228881836 -1,2 C-1,2 -1,-2 -1,-2z"></path>
							</g>
							<g opacity="1" transform="matrix(1,0,0,1,12,22)">
								<path fill="#F23F42" fill-opacity="1" d=" M3,-1 C3.552000045776367,-1 4,-0.5519999861717224 4,0 C4,0.5519999861717224 3.552000045776367,1 3,1 C3,1 -3,1 -3,1 C-3.552000045776367,1 -4,0.5519999861717224 -4,0 C-4,-0.5519999861717224 -3.552000045776367,-1 -3,-1 C-3,-1 3,-1 3,-1z"></path>
							</g>
						</g>
					</g>
				</g>
				<g clip-path="url(#__lottie_element_18)" transform="matrix(1,0,0,1,-200,-200)" opacity="1" style="display: none;">
					<g transform="matrix(25,0,0,25,200,200)" opacity="1" style="display: none;">
						<g opacity="1" transform="matrix(1,0,0,1,12,12)">
							<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-10,10 C-10,10 10,-10 10,-10"></path>
						</g>
					</g>
					<g style="display: none;">
						<g>
							<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"></path>
						</g>
					</g>
				</g>
				<g clip-path="url(#__lottie_element_11)" style="display: block;" transform="matrix(1,0,0,1,-200,-200)" opacity="1">
					<g style="display: block;" transform="matrix(25,0,0,25,200,200)" opacity="1">
						<g opacity="1" transform="matrix(1,0,0,1,12,12)">
							<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-10,10 C-10,10 10,-10 10,-10"></path>
						</g>
					</g>
				</g>
			</g>
		</g>
	</svg>
</div>
<div class="icon-wrapper">
	<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewbox="0 0 24 24" width="24" height="24" preserveaspectratio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
		<defs>
			<clipPath id="__lottie_element_42">
				<rect width="24" height="24" x="0" y="0"></rect>
			</clipPath>
			<clipPath id="__lottie_element_44">
				<path d="M0,0 L600,0 L600,600 L0,600z"></path>
			</clipPath>
			<clipPath id="__lottie_element_51">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<clipPath id="__lottie_element_61">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<mask id="__lottie_element_62">
				<rect fill="var(--icon-topbar)" width="600" height="600" transform="matrix(0.9615384936332703,0,0,0.9615384936332703,211.53846740722656,211.53846740722656)"></rect>
				<path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M67.22000122070312,823.7960205078125 C67.22000122070312,823.7960205078125 -126.3030014038086,1017.75 -126.3030014038086,1017.75 C-126.3030014038086,1017.75 -76.63800048828125,1067.092041015625 -76.63800048828125,1067.092041015625 C-76.63800048828125,1067.092041015625 -22.31999969482422,1121.0560302734375 -22.31999969482422,1121.0560302734375 C-22.31999969482422,1121.0560302734375 171.2030029296875,927.1019897460938 171.2030029296875,927.1019897460938 C171.2030029296875,927.1019897460938 239.677001953125,860.3419799804688 186.88099670410156,810.114013671875 C130,756 67.22000122070312,823.7960205078125 67.22000122070312,823.7960205078125" fill-opacity="1"></path>
			</mask>
			<clipPath id="__lottie_element_65">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<path></path>
			<clipPath id="__lottie_element_72">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<path d=" M0.5009999871253967,-0.10000000149011612 C0.5009999871253967,-0.10000000149011612 -0.09300000220537186,-0.10000000149011612 -0.09300000220537186,-0.10000000149011612 C-0.09300000220537186,-0.10000000149011612 -0.09300000220537186,1.0759999752044678 -0.09300000220537186,1.0759999752044678 C-0.09300000220537186,1.0759999752044678 0.5009999871253967,1.0759999752044678 0.5009999871253967,1.0759999752044678 C0.5009999871253967,1.0759999752044678 0.5009999871253967,-0.10000000149011612 0.5009999871253967,-0.10000000149011612" fill-opacity="1"></path>
			<clipPath id="__lottie_element_79">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<mask id="__lottie_element_80">
				<rect fill="var(--icon-topbar)" width="600" height="600" transform="matrix(0.9615384936332703,0,0,0.9615384936332703,211.53846740722656,211.53846740722656)"></rect>
				<path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M67.44400024414062,823.5819702148438 C67.44400024414062,823.5819702148438 -126.11100006103516,1017.5659790039062 -126.11100006103516,1017.5659790039062 C-126.11100006103516,1017.5659790039062 -76.44599914550781,1066.907958984375 -76.44599914550781,1066.907958984375 C-76.44599914550781,1066.907958984375 -22.128000259399414,1120.8719482421875 -22.128000259399414,1120.8719482421875 C-22.128000259399414,1120.8719482421875 171.427001953125,926.8880004882812 171.427001953125,926.8880004882812 C171.427001953125,926.8880004882812 239.9010009765625,860.1279907226562 187.10499572753906,809.9000244140625 C130.2239990234375,755.7860107421875 67.44400024414062,823.5819702148438 67.44400024414062,823.5819702148438" fill-opacity="1"></path>
			</mask>
			<clipPath id="__lottie_element_83">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<path></path>
			<clipPath id="__lottie_element_90">
				<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
			</clipPath>
			<path d=" M0.5009999871253967,-0.10000000149011612 C0.5009999871253967,-0.10000000149011612 -0.09300000220537186,-0.10000000149011612 -0.09300000220537186,-0.10000000149011612 C-0.09300000220537186,-0.10000000149011612 -0.09300000220537186,1.0759999752044678 -0.09300000220537186,1.0759999752044678 C-0.09300000220537186,1.0759999752044678 0.5009999871253967,1.0759999752044678 0.5009999871253967,1.0759999752044678 C0.5009999871253967,1.0759999752044678 0.5009999871253967,-0.10000000149011612 0.5009999871253967,-0.10000000149011612" fill-opacity="1"></path>
		</defs>
		<g clip-path="url(#__lottie_element_42)">
			<g clip-path="url(#__lottie_element_44)" transform="matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)" opacity="1" style="display: block;">
				<g clip-path="url(#__lottie_element_79)" transform="matrix(1.0399999618530273,0,0,1.0399999618530273,-220,-220)" opacity="1" style="display: none;">
					<g mask="url(#__lottie_element_80)">
						<g clip-path="url(#__lottie_element_90)" transform="matrix(1,0,0,1,0,0)" opacity="1" style="display: block;">
							<g transform="matrix(25,0,0,25,200,200)" opacity="1" style="display: block;">
								<g opacity="1" transform="matrix(1,0,0,1,12,12.288000106811523)">
									<path fill="oklab(0.786807 -0.0025776 -0.0110238)" fill-opacity="1" d=" M-8.000072479248047,-0.2881828546524048 C-7.999939918518066,-4.706087589263916 -4.418000221252441,-8.288000106811523 0,-8.288000106811523 C4.418000221252441,-8.288000106811523 7.999929904937744,-4.706122875213623 8.000077247619629,-0.2881803512573242 C8.000102043151855,0.40481603145599365 7.954133987426758,1.0718117952346802 7.854161262512207,1.7118114233016968 C7.854161262512207,1.7118114233016968 6.000174522399902,1.7118886709213257 6.000174522399902,1.7118886709213257 C5.05618143081665,1.7119280099868774 4.167206287384033,2.156961679458618 3.6002418994903564,2.911979913711548 C3.6002418994903564,2.911979913711548 1.6273654699325562,5.542043209075928 1.6273654699325562,5.542043209075928 C1.1583948135375977,6.168058395385742 1.03643000125885,6.988057613372803 1.3044586181640625,7.72304105758667 C1.8895213603973389,9.332005500793457 3.8875467777252197,10.287915229797363 5.482487678527832,9.131856918334961 C8.839362144470215,6.700735092163086 10.00019359588623,3.3797173500061035 10.000062942504883,-0.28826361894607544 C9.999899864196777,-5.811119556427002 5.5229997634887695,-10.288000106811523 0,-10.288000106811523 C-5.5229997634887695,-10.288000106811523 -9.99986457824707,-5.81110954284668 -10.000057220458984,-0.2882695198059082 C-10.000213623046875,3.3797030448913574 -8.83936882019043,6.700726509094238 -5.482500076293945,9.131853103637695 C-3.8875625133514404,10.28791332244873 -1.8895366191864014,9.33200740814209 -1.304471492767334,7.7230448722839355 C-1.036441683769226,6.988062381744385 -1.1584053039550781,6.168063640594482 -1.627374529838562,5.54204797744751 C-1.627374529838562,5.54204797744751 -3.600245475769043,2.911982774734497 -3.600245475769043,2.911982774734497 C-4.167208194732666,2.156964063644409 -5.056182384490967,1.7119290828704834 -6.0001749992370605,1.7118881940841675 C-6.0001749992370605,1.7118881940841675 -7.854179859161377,1.7118425369262695 -7.854179859161377,1.7118425369262695 C-7.954162120819092,1.071841835975647 -8.000097274780273,0.40481364727020264 -8.000072479248047,-0.2881828546524048z"></path>
								</g>
							</g>
						</g>
						<g clip-path="url(#__lottie_element_83)" style="display: none;">
							<g style="display: none;">
								<g>
									<path></path>
								</g>
							</g>
						</g>
					</g>
				</g>
				<g clip-path="url(#__lottie_element_61)" style="display: block;" transform="matrix(1.0399999618530273,0,0,1.0399999618530273,-220,-220)" opacity="1">
					<g mask="url(#__lottie_element_62)">
						<g clip-path="url(#__lottie_element_72)" style="display: block;" transform="matrix(1,0,0,1,0,0)" opacity="1">
							<g style="display: block;" transform="matrix(25,0,0,25,200,200)" opacity="1">
								<g opacity="1" transform="matrix(1,0,0,1,12,12.288000106811523)">
									<path fill="oklab(0.786807 -0.0025776 -0.0110238)" fill-opacity="1" d=" M-8,-0.2879999876022339 C-8,-4.705999851226807 -4.418000221252441,-8.288000106811523 0,-8.288000106811523 C4.418000221252441,-8.288000106811523 8,-4.705999851226807 8,-0.2879999876022339 C8,0.4050000011920929 7.953999996185303,1.0720000267028809 7.854000091552734,1.7120000123977661 C7.854000091552734,1.7120000123977661 6,1.7120000123977661 6,1.7120000123977661 C5.056000232696533,1.7120000123977661 4.166999816894531,2.1570000648498535 3.5999999046325684,2.9119999408721924 C3.5999999046325684,2.9119999408721924 1.6269999742507935,5.541999816894531 1.6269999742507935,5.541999816894531 C1.1579999923706055,6.168000221252441 1.0360000133514404,6.98799991607666 1.3040000200271606,7.7230000495910645 C1.8890000581741333,9.331999778747559 3.88700008392334,10.288000106811523 5.48199987411499,9.131999969482422 C8.83899974822998,6.701000213623047 10,3.380000114440918 10,-0.2879999876022339 C10,-5.810999870300293 5.5229997634887695,-10.288000106811523 0,-10.288000106811523 C-5.5229997634887695,-10.288000106811523 -10,-5.810999870300293 -10,-0.2879999876022339 C-10,3.380000114440918 -8.83899974822998,6.701000213623047 -5.48199987411499,9.131999969482422 C-3.88700008392334,10.288000106811523 -1.8890000581741333,9.331999778747559 -1.3040000200271606,7.7230000495910645 C-1.0360000133514404,6.98799991607666 -1.1579999923706055,6.168000221252441 -1.6269999742507935,5.541999816894531 C-1.6269999742507935,5.541999816894531 -3.5999999046325684,2.9119999408721924 -3.5999999046325684,2.9119999408721924 C-4.166999816894531,2.1570000648498535 -5.056000232696533,1.7120000123977661 -6,1.7120000123977661 C-6,1.7120000123977661 -7.854000091552734,1.7120000123977661 -7.854000091552734,1.7120000123977661 C-7.953999996185303,1.0720000267028809 -8,0.4050000011920929 -8,-0.2879999876022339z"></path>
								</g>
							</g>
						</g>
						<g clip-path="url(#__lottie_element_65)" style="display: none;">
							<g style="display: none;">
								<g>
									<path></path>
								</g>
							</g>
						</g>
					</g>
				</g>
				<g clip-path="url(#__lottie_element_51)" transform="matrix(1,0,0,1,-200,-200)" opacity="1" style="display: none;">
					<g style="display: none;">
						<g>
							<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"></path>
						</g>
					</g>
					<g style="display: none;">
						<g>
							<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"></path>
						</g>
					</g>
				</g>
				<g style="display: none;">
					<g>
						<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"></path>
					</g>
				</g>
			</g>
		</g>
	</svg>
</div>
<div class="icon-wrapper settings" id="settingsButton">
	<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewbox="0 0 24 24" width="24" height="24" preserveaspectratio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
		<defs>
			<clipPath id="__lottie_element_97">
				<rect width="24" height="24" x="0" y="0"></rect>
			</clipPath>
			<clipPath id="__lottie_element_99">
				<path d="M0,0 L600,0 L600,600 L0,600z"></path>
			</clipPath>
		</defs>
		<g clip-path="url(#__lottie_element_97)">
			<g clip-path="url(#__lottie_element_99)" transform="matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)" opacity="1" style="display: block;">
				<g transform="matrix(25,0,0,25,300,300)" opacity="1" style="display: block;">
					<g opacity="1" transform="matrix(1,0,0,1,0,0)">
						<path fill="oklab(0.786807 -0.0025776 -0.0110238)" fill-opacity="1" d=" M-1.4420000314712524,-10.906000137329102 C-1.8949999809265137,-10.847000122070312 -2.1470000743865967,-10.375 -2.078000068664551,-9.92300033569336 C-1.899999976158142,-8.756999969482422 -2.265000104904175,-7.7210001945495605 -3.061000108718872,-7.390999794006348 C-3.8570001125335693,-7.060999870300293 -4.8480000495910645,-7.534999847412109 -5.546000003814697,-8.484999656677246 C-5.816999912261963,-8.852999687194824 -6.329999923706055,-9.008999824523926 -6.691999912261963,-8.730999946594238 C-7.458000183105469,-8.142999649047852 -8.142999649047852,-7.458000183105469 -8.730999946594238,-6.691999912261963 C-9.008999824523926,-6.329999923706055 -8.852999687194824,-5.816999912261963 -8.484999656677246,-5.546000003814697 C-7.534999847412109,-4.8480000495910645 -7.060999870300293,-3.8570001125335693 -7.390999794006348,-3.061000108718872 C-7.7210001945495605,-2.265000104904175 -8.756999969482422,-1.899999976158142 -9.92300033569336,-2.078000068664551 C-10.375,-2.1470000743865967 -10.847000122070312,-1.8949999809265137 -10.906000137329102,-1.4420000314712524 C-10.968000411987305,-0.9700000286102295 -11,-0.48899999260902405 -11,0 C-11,0.48899999260902405 -10.968000411987305,0.9700000286102295 -10.906000137329102,1.4420000314712524 C-10.847000122070312,1.8949999809265137 -10.375,2.1470000743865967 -9.92300033569336,2.078000068664551 C-8.756999969482422,1.899999976158142 -7.7210001945495605,2.265000104904175 -7.390999794006348,3.061000108718872 C-7.060999870300293,3.8570001125335693 -7.534999847412109,4.8470001220703125 -8.484999656677246,5.546000003814697 C-8.852999687194824,5.816999912261963 -9.008999824523926,6.328999996185303 -8.730999946594238,6.691999912261963 C-8.142999649047852,7.458000183105469 -7.458000183105469,8.142999649047852 -6.691999912261963,8.730999946594238 C-6.329999923706055,9.008999824523926 -5.816999912261963,8.852999687194824 -5.546000003814697,8.484999656677246 C-4.8480000495910645,7.534999847412109 -3.8570001125335693,7.060999870300293 -3.061000108718872,7.390999794006348 C-2.265000104904175,7.7210001945495605 -1.899999976158142,8.756999969482422 -2.078000068664551,9.92300033569336 C-2.1470000743865967,10.375 -1.8949999809265137,10.847000122070312 -1.4420000314712524,10.906000137329102 C-0.9700000286102295,10.968000411987305 -0.48899999260902405,11 0,11 C0.48899999260902405,11 0.9700000286102295,10.968000411987305 1.4420000314712524,10.906000137329102 C1.8949999809265137,10.847000122070312 2.1470000743865967,10.375 2.078000068664551,9.92300033569336 C1.899999976158142,8.756999969482422 2.2660000324249268,7.7210001945495605 3.062000036239624,7.390999794006348 C3.8580000400543213,7.060999870300293 4.8480000495910645,7.534999847412109 5.546000003814697,8.484999656677246 C5.816999912261963,8.852999687194824 6.328999996185303,9.008999824523926 6.691999912261963,8.730999946594238 C7.458000183105469,8.142999649047852 8.142999649047852,7.458000183105469 8.730999946594238,6.691999912261963 C9.008999824523926,6.328999996185303 8.852999687194824,5.816999912261963 8.484999656677246,5.546000003814697 C7.534999847412109,4.8480000495910645 7.060999870300293,3.8570001125335693 7.390999794006348,3.061000108718872 C7.7210001945495605,2.265000104904175 8.756999969482422,1.899999976158142 9.92300033569336,2.078000068664551 C10.375,2.1470000743865967 10.847000122070312,1.8949999809265137 10.906000137329102,1.4420000314712524 C10.968000411987305,0.9700000286102295 11,0.48899999260902405 11,0 C11,-0.48899999260902405 10.968000411987305,-0.9700000286102295 10.906000137329102,-1.4420000314712524 C10.847000122070312,-1.8949999809265137 10.375,-2.1470000743865967 9.92300033569336,-2.078000068664551 C8.756999969482422,-1.899999976158142 7.7210001945495605,-2.265000104904175 7.390999794006348,-3.061000108718872 C7.060999870300293,-3.8570001125335693 7.534999847412109,-4.8480000495910645 8.484999656677246,-5.546000003814697 C8.852999687194824,-5.816999912261963 9.008999824523926,-6.329999923706055 8.730999946594238,-6.691999912261963 C8.142999649047852,-7.458000183105469 7.458000183105469,-8.142999649047852 6.691999912261963,-8.730999946594238 C6.328999996185303,-9.008999824523926 5.817999839782715,-8.852999687194824 5.546999931335449,-8.484999656677246 C4.848999977111816,-7.534999847412109 3.8580000400543213,-7.060999870300293 3.062000036239624,-7.390999794006348 C2.2660000324249268,-7.7210001945495605 1.9010000228881836,-8.756999969482422 2.0789999961853027,-9.92300033569336 C2.1480000019073486,-10.375 1.8949999809265137,-10.847000122070312 1.4420000314712524,-10.906000137329102 C0.9700000286102295,-10.968000411987305 0.48899999260902405,-11 0,-11 C-0.48899999260902405,-11 -0.9700000286102295,-10.968000411987305 -1.4420000314712524,-10.906000137329102z M4,0 C4,2.2090001106262207 2.2090001106262207,4 0,4 C-2.2090001106262207,4 -4,2.2090001106262207 -4,0 C-4,-2.2090001106262207 -2.2090001106262207,-4 0,-4 C2.2090001106262207,-4 4,-2.2090001106262207 4,0z"></path>
					</g>
				</g>
			</g>
		</g>
	</svg>
</div></div><link rel="stylesheet" href="{{ asset('styles/userBarstyle2.css') }}"><script>
var popup = document.getElementById("userPopup");
var userName = document.getElementById("userName");

// Fonction pour afficher le popup
function showPopup() {
popup.style.display = "block";
document.addEventListener("click", hidePopupOnClickOutside);
}

// Fonction pour cacher le popup lorsqu'on clique en dehors
function hidePopupOnClickOutside(event) {
if (! popup.contains(event.target) && event.target !== userName) {
popup.style.display = "none";
document.removeEventListener("click", hidePopupOnClickOutside);
}
}

// Afficher le popup lors du clic sur userName
userName.onclick = function (event) {
event.stopPropagation(); // Empêche le clic de se propager
showPopup();
};</script>
