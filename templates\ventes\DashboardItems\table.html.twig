<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('styles/VentesTableStyle.css') }}">
<style>
body {
    font-family: 'Ubuntu', sans-serif;
}

.full-tabledataclusters  {
    box-shadow: 2px 2px 4px 2 rgba(0, 0, 0, 0.1);
        width: 985px;
    border-radius: 8px;
}
.pagination {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
}

.pagination li {
    margin: 0 5px;
    cursor: pointer;
}

.pagination .active {
    font-weight: bold;
    text-decoration: underline;
    color: #9f8a7d;
}

</style>
<div id="salestable" >
<div class="full-tabledataclusters">
	<nav class="navbar table-navbar navbar-expand-lg">
		<ul class="navbar-nav me-auto mb-1 mb-lg-0">
			<li class="nav-item" id="tablenav">
				<a class="nav-link active" href="#">All</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unfulfilled</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unpaid</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Open</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Closed</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Local Delivery</a>
			</li>
		</ul>
		<div class="navbar-nav">
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-search"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-sliders"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-filter"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-arrow-down-up"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-three-dots"></i>
			</a>
		</div>
	</nav>

	<div class="container mt-2">
		 <div class="row mb-3 align-items-center">
        <div class="col-10 col-md-2">
            <div class="custom-select-container">
                <i class="bi bi-box icon"></i>
                <select class="form-select" id="categorySelect">
                    <option disabled selected>Select City</option>
                    {% set displayedCities = [] %}
                    {% for IntPlace in IntPlaces %}
                        {% if IntPlace.VILL is defined and IntPlace.VILL not in displayedCities %}
                            <option value="{{ IntPlace.VILL }}">{{ IntPlace.VILL }}</option>
                            {% set displayedCities = displayedCities | merge([IntPlace.VILL]) %}
                        {% endif %}
                    {% else %}
                        <option disabled>No cities available</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div id="addfilter" class="col-12 col-md-2 d-flex align-items-center justify-content-center">
            <span>+ Add filter</span>
        </div>
    </div>
	</div>

<table class="table">
        <thead>
            <tr>
                <th><input type="checkbox" class="form-check-input" id="customCheck"></th>
                <th><i class="bi bi-hash"></i>Libellé</th>
                <th><i class="bi bi-person"></i>Cluster</th>
                <th><i class="bi bi-currency-dollar"></i>Ville</th>
                <th><i class="bi bi-stack"></i>Nom de voix</th>
                <th><i class="bi bi-calendar-week"></i>Numero de voix</th>
                <th><i class="bi bi-credit-card"></i>CP</th>
            </tr>
        </thead>
        <tbody id="tableBody">
        </tbody>
    </table>
    <ul class="pagination" id="paginationControls"></ul>
</div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    const rowsPerPage = 6;
    const tableData = [
        {% for IntPlace in IntPlaces %}
            {
                cod_hex: "",
                cluster: "{{ IntPlace.LBL_CLUSTER | default('N/A') }}",
                ville: "{{ IntPlace.VILL | default('N/A') }}",
                nom_voie: "{{ IntPlace.NOM_VOIE | default('N/A') }}",
                numr_voie: "{{ IntPlace.NUMR_VOIE | default('N/A') }}",
                cp: "{{ IntPlace.CP | default('N/A') }}"
            },
        {% endfor %}
    ];
    let currentPage = 1;

    function renderTable(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;
        const rows = tableData.slice(start, end);
        const tableBody = document.getElementById("tableBody");
        tableBody.innerHTML = "";

        rows.forEach(row => {
            const tr = document.createElement("tr");
            tr.innerHTML = `
                <td><input type="checkbox" class="form-check-input" id="customCheck"></td>
                <td><strong>${row.cod_hex}</strong></td>
                <td><strong>${row.cluster}</strong></td>
                <td>${row.ville}</td>
                <td>${row.nom_voie}</td>
                <td>${row.numr_voie}</td>
                <td>${row.cp}</td>
            `;
            tableBody.appendChild(tr);
        });
    }

    function setupPagination() {
        const totalPages = Math.ceil(tableData.length / rowsPerPage);
        const paginationControls = document.getElementById("paginationControls");
        paginationControls.innerHTML = "";

        for (let i = 1; i <= totalPages; i++) {
            const li = document.createElement("li");
            li.textContent = i;
            li.classList.add("page-item");
            if (i === currentPage) li.classList.add("active");
            li.onclick = () => {
                currentPage = i;
                renderTable(currentPage);
                setupPagination();
            };
            paginationControls.appendChild(li);
        }
    }

    renderTable(currentPage);
    setupPagination();
</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
