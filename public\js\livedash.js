// Initialize Map
const map = L.map('map').setView([
    20, 0
    ], 2);
    
    // Define light and dark tile layers
    const tileLayerLight = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {attribution: '&copy; OpenStreetMap contributors'});
    
    const tileLayerDark = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {attribution: '&copy; OpenStreetMap contributors, &copy; CartoDB'});
    
    // Add the default light mode layer
    tileLayerLight.addTo(map);
    
    // Marker data
    const markers = [
    {
    coords: [
    48.8566, 2.3522
    ],
    label: "Paris"
    }, {
    coords: [
    40.7128, -74.006
    ],
    label: "New York"
    }, {
    coords: [
    35.6895, 139.6917
    ],
    label: "Tokyo"
    }
    ];
    
    // Add markers to the map
    markers.forEach(marker => {
    L.marker(marker.coords).addTo(map).bindPopup(marker.label);
    });
    
    // Toggle dark mode
    function toggleDarkMode() {
    const isChecked = document.getElementById('chk').checked;
    if (isChecked) {
    map.removeLayer(tileLayerLight);
    tileLayerDark.addTo(map);
    document.body.classList.add('dark-mode');
    } else {
    map.removeLayer(tileLayerDark);
    tileLayerLight.addTo(map);
    document.body.classList.remove('dark-mode');
    }
    }
    
    // Event listener for the checkbox
    document.getElementById('chk').addEventListener('change', toggleDarkMode);
    
    // Users in the Last 30 Minutes Chart
    const users30MinutesCtx = document.getElementById('users30MinutesChart').getContext('2d');
    new Chart(users30MinutesCtx, {
    type: 'bar',
    data: {
    labels: Array.from(
    {
    length: 30
    },
    (_, i) => `-${
    30 - i
    } `
    ), // Création des labels
    datasets: [
    {
    label: 'Utilisateurs actifs',
    data: Array.from(
    {
    length: 30
    },
    () => Math.floor(Math.random() * 100)
    ),
    backgroundColor: '#539AF8',
    borderColor: 'rgba(66, 133, 244, 1)',
    borderWidth: 0
    }
    ]
    },
    options: {
    responsive: true, // Chart responsive
    scales: {
    y: {
    beginAtZero: false,
    ticks: {
    display: false // Cacher les ticks sur l'axe Y
    },
    grid: {
    display: false // Cacher les lignes de la grille Y
    }
    },
    x: {
    ticks: {
    display: true, // Assurez-vous que les ticks sont affichés
    font: {
    
    size: 14, // Taille de police lisible
    style: 'normal', // Style normal (pas en italique)
    weight: '400', // Poids de police normal
    lineHeight: 1.5 // Hauteur de ligne
    },
    
    
    // Utiliser maxRotation et minRotation pour garantir des labels horizontaux
    maxRotation: 0, // Pas de rotation maximum
    minRotation: 0 // Pas de rotation minimum
    },
    grid: {
    display: false // Cacher les lignes de la grille X
    }
    }
    },
    plugins: {
    legend: {
    display: false // Cacher la légende
    }
    }
    }
    });
        </script>
        <script>
            // Users in the Last 30 Minutes Chart
    const users30MinutesCtxz = document.getElementById('users30MinutesCharts').getContext('2d');
    new Chart(users30MinutesCtxz, {
    type: 'bar',
    data: {
    labels: Array.from(
    {
    length: 20
    },
    (_, i) => ` `
    ),
    datasets: [
    {
    label: 'Utilisateurs actifs',
    data: Array.from(
    {
    length: 20
    },
    () => Math.floor(Math.random() * 100)
    ),
    backgroundColor: '#539AF8',
    borderColor: 'rgba(66, 133, 244, 1)',
    borderWidth: 1
    }
    ]
    },
    options: {
    responsive: false,
    scales: {
    y: {
    beginAtZero: false,
    ticks: {
    display: false // Set to true to display y-axis ticks
    },
    grid: {
    display: false // Hide grid lines
    }
    },
    x: {
    ticks: {
    display: true // Set to true to display x-axis ticks
    },
    grid: {
    display: false // Hide grid lines
    }
    }
    },
    plugins: {
    legend: {
    display: false // Hide legend
    },
    // Ensure smilePlugin is properly defined if used
    smilePlugin: {
    display: false
    }
    }
    }
    });
    const users30MinutesCtxzs = document.getElementById('users30MinutesChartes').getContext('2d');
    new Chart(users30MinutesCtxzs, {
    type: 'bar',
    data: {
    labels: Array.from(
    {
    length: 20
    },
    (_, i) => ` `
    ),
    datasets: [
    {
    label: 'Utilisateurs actifs',
    data: Array.from(
    {
    length: 20
    },
    () => Math.floor(Math.random() * 100)
    ),
    backgroundColor: '#539AF8',
    borderColor: 'rgba(66, 133, 244, 1)',
    borderWidth: 1
    }
    ]
    },
    options: {
    responsive: false,
    scales: {
    y: {
    beginAtZero: false,
    ticks: {
    display: false // Set to true to display y-axis ticks
    },
    grid: {
    display: false // Hide grid lines
    }
    },
    x: {
    ticks: {
    display: true // Set to true to display x-axis ticks
    },
    grid: {
    display: false // Hide grid lines
    }
    }
    },
    plugins: {
    legend: {
    display: false // Hide legend
    },
    // Ensure smilePlugin is properly defined if used
    smilePlugin: {
    display: false
    }
    }
    }
    });
    const users30MinutesCtxzes = document.getElementById('users30MinutesChartees').getContext('2d');
    new Chart(users30MinutesCtxzes, {
    type: 'bar',
    data: {
    labels: Array.from(
    {
    length: 5
    },
    (_, i) => ` `
    ),
    datasets: [
    {
    label: 'Utilisateurs actifs',
    data: Array.from(
    {
    length: 5
    },
    () => Math.floor(Math.random() * 50)
    ),
    backgroundColor: '#539AF8',
    borderColor: 'rgba(66, 133, 244, 1)',
    borderWidth: 1
    }
    ]
    },
    options: {
    responsive: false,
    scales: {
    y: {
    beginAtZero: false,
    ticks: {
    display: false // Set to true to display y-axis ticks
    },
    grid: {
    display: false // Hide grid lines
    }
    },
    x: {
    ticks: {
    display: true // Set to true to display x-axis ticks
    },
    grid: {
    display: false // Hide grid lines
    }
    }
    },
    plugins: {
    legend: {
    display: false // Hide legend
    },
    // Ensure smilePlugin is properly defined if used
    smilePlugin: {
    display: false
    }
    }
    }
    });