<style>
   :root {
    --color-background-light: #C0E7F7; /* Light gradient start */
    --color-background-dark: #e9f5fd; /* Light gradient end */
    --color-titlesaleshistory: #254a64; /* Color for titles (default: black) */
    --color-table-text: #333333; /* Color for table text (default: dark gray) */
}

[data-theme="dark"] {
    --color-background-light: #113a50; /* Dark gradient start */
    --color-background-dark: #113a50; /* Dark gradient end */
   --color-titlesaleshistory: #3e80ac; /* Color for titles in dark mode (white) */
    --color-table-text: #ffffff; /* Color for table text in dark mode (white) */
}

.sales-history-container {
    display: flex;
    align-items: center;
}



.sales-history-title {
    font-weight: bold;
    margin: 0;
    margin-right: 30px;
    white-space: nowrap;
    font-size: 14px;
    color: var(--color-titlesaleshistory); /* Title color using variable */
}

.sales-history-table {
    margin: 0;
    white-space: nowrap;
}

.sales-history-table th {
    text-align: center;
    border: none;
    padding: 1px 6px;
    font-size: 12px;
    color: var(--color-table-text); /* Table text color using variable */
}

.sales-history-table td {
    text-align: center;
    border: none;
    padding: 1px 6px;
    font-size: 12px;
font-weight: bold;    
color: var(--color-titlesaleshistory); /* Table text color using variable */
}
</style>
<script>
var productionscategory= {{ productionscategory }}
console.log('productionscategory:',productionscategory);
</script>
<div class="historique-ventes-nord">
    <div class="sales-history-container">
        <span class="sales-history-title">Historique Ventes nord</span>
        <table class="table table-borderless sales-history-table">
            <thead>
                <tr>
                {% for month, categories in productionscategory %}
    <th>{{ month|date('m') }}</th>
{% endfor %}

                </tr>
            </thead>
            <tbody>
                <tr>
                    {% for month, total in categoryTotals %}
                        <td>{{ total }}</td>
                    {% endfor %}
                </tr>
            </tbody>
        </table>
    </div>
</div>

