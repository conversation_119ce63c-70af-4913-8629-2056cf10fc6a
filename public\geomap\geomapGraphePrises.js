// let productionsData = {};
// async function fetchDataCercleGraphe(url, jwt) {
//     try {
//         const response = await fetch(url, {
//             method: "GET",
//             headers: {
//                 "Authorization": `Bearer ${jwt}`,
//                 "Content-Type": "application/json"
//             }
//         });
//         if (!response.ok) {
//             throw new Error(`HTTP error! Status: ${response.status}`);
//         }
//         return await response.json();
//     } catch (error) {
//         console.error("Erreur lors de la récupération des données :", error);
//         return null;
//     }
// }

// // async function fetchAllDatatest() {
// //     const clusterCode = localStorage.getItem('clusterCode');
// //     const optionGroups = ['Tech', 'Produit', 'Categorie', 'PreCommande'];
// //     const date=formatDateRange(month,year);
// //     try {
// //         const dataPromises = optionGroups.map(optionGroup => {
// //             let url = `https://api.nomadcloud.fr/api/productions-kpi-by-option-group/${pointOfSaleId}/${Type}/${optionGroup}?${date}&page=1`;
// //             if (clusterCode) {
// //                 url += `&codeCluster=${clusterCode}`;
// //             }
// //             return fetchDataCercleGraphe(url, jwtToken);
// //         });

// //         const results = await Promise.all(dataPromises);
// //         results.forEach((data, index) => {
// //             productionsData[optionGroups[index]] = data;
// //         });

// //         var chartDataParGamme = productionsData["Produit"];
// //         var chartDataParStatutPreco = productionsData["PreCommande"];
// //         if (!chartDataParGamme || chartDataParGamme.length === 0) {
// //             console.warn("Aucune donnée disponible pour le graphique.");
// //             return;
// //         }
// //         ParGamme(chartDataParGamme);
// //         ParParcourrChart(productionsData);
// //         ParTechno(productionsData);
// //         ParStatutPreco(chartDataParStatutPreco);
// //     } catch (error) {
// //         console.error("Error fetching data:", error);
// //     }
// // }

// document.addEventListener('DOMContentLoaded', function () {
//     Promise.all([
      
//         fetchData(clusterCode),
//         fetchProductionKPI(),
//         fetchProductionsIntervention(clusterCode),
//         fetchClientSatisfactionAverages(clusterCode)
//     ]).then(() => {
//         fetchProductionsAnnulation(clusterCode);
//     }).catch(error => {
//         console.error('Error in initial data fetching:', error);
//     });
// });

// var clusterCode ='';
// function fetchdataCluster(clusterCode) {
//     localStorage.setItem('clusterCode', clusterCode);
//     clusterCode=clusterCode;

//     fetchData(clusterCode);
//     fetchProductionKPI();
//     fetchProductionsIntervention(clusterCode);
//     fetchProductionsAnnulation(clusterCode);
//     fetchClientSatisfactionAverages(clusterCode);
//   }



//   let dataByCPV;
//   async function fetchProductionKPI() {
//         const date = `mois=${month}&annee=${year}`;
//         try {
//           const response = await fetch(`https://api.nomadcloud.fr/api/objectifs-clusters-productions?${date}&page=1`, {
//               method: 'GET',
//               headers: {
//                   'Authorization': `Bearer ${jwtToken}`,
//                   'Content-Type': 'application/json'
//               }
//           });
  
//           const data = await response.json();
//           dataByCPV = data.find(item => item.cpv === cpv);
//           return data;
//         } catch (error) {
//           console.error('Error:', error);
//           return null;
//         }
//   }

// function ProductionKPIByPointOfSaleId(clusterCode) {
//     let clusterDetails = dataByCPV.clusters.find(item => item.codeCluster === clusterCode);
//     const container = document.querySelector('.list-containers');
//     container.innerHTML = '';
//     if(!clusterDetails){
//         return;
//     }
//     const statuses = clusterDetails.status;
   
//     var libelleCluster=document.getElementById('libelleCluster');
//     libelleCluster.innerHTML = clusterDetails.libelleCluster;
//     for (const [key, value] of Object.entries(statuses)) {
//         const statusHtml = `
//             <div class="statistic">
//                 <div class="statistic-content">
//                     <div class="stat-title">${key}</div>
//                     <div class="stat-number">${value}</div>
//                 </div>
//                 <div class="progress-container">
//                     <div class="progress-bar" style="width: ${(value/ clusterDetails.nombreVentesCluster) * 100}%"></div>
//                 </div>
//             </div>
//         `;
//         container.innerHTML += statusHtml;

//     }
//     container.innerHTML += `
//     <div class="pourcentageVente" style="width: 80px; height: 10px; color: #55C5D0;"></div>
//        <span>R/O: ${clusterDetails.objectifCluster.pourcentageVente}</span>
//     </div>
//  `;
// }

// function fetchProductionsIntervention(clusterData) {
//     const date=formatDateRange(month, year);
//     let url = `https://api.nomadcloud.fr/api/productions-by-intervention-status/${pointOfSaleId}?${date}&page=1`;

//     if (clusterData) {
//         url += `&codeCluster=${clusterData}`;
//     }

//     fetch(url, {
//         method: 'GET',
//         headers: {
//             'Authorization': 'Bearer ' + jwtToken
//         }
//     })
//     .then(response => response.json())
//     .then(data => {
//         const firstResult = data[0];
//         updateProgressBars(firstResult);
//     })
//     .catch(error => console.error('Error fetching data:', error));
// }



// function updateProgressBars(data) {
//     const totalXapRDV = parseInt(data.totalXapRDV, 10);
//     const totalXavRDV = parseInt(data.totalXavRDV, 10);

//     if (totalXapRDV + totalXavRDV > 0) { // Ensure no division by zero
//         const xapPercentage = (totalXapRDV / (totalXapRDV + totalXavRDV)) * 100;
//         const xavPercentage = (totalXavRDV / (totalXapRDV + totalXavRDV)) * 100;

//         document.querySelector('.progress-bar-primary').style.width = `${xapPercentage}%`;
//         document.querySelector('.progress-label-center.xap').textContent = totalXapRDV.toString();
//         document.querySelector('.progress-label-end.xap').textContent = `${xapPercentage.toFixed(2)}%`;

//         document.querySelector('.progress-bar-secondary').style.width = `${xavPercentage}%`;
//         document.querySelector('.progress-label-center.xav').textContent = totalXavRDV.toString();
//         document.querySelector('.progress-label-end.xav').textContent = `${xavPercentage.toFixed(2)}%`;
//     } else {
//         document.querySelectorAll('.progress-bar').forEach(bar => bar.style.width = `0%`);
//         document.querySelectorAll('.progress-label-center').forEach(label => label.textContent = `0`);
//         document.querySelectorAll('.progress-label-end').forEach(label => label.textContent = `0%`);
//     }
// }

// async function fetchProductionsAnnulation(clusterData) {
//     const date =formatDateRange(month, year);
//     const url = new URL(`https://api.nomadcloud.fr/api/productions-by-annulation-types/${pointOfSaleId}`);
//     url.search = date; // Set the date directly

//     if (clusterData) {
//         url.searchParams.append("codeCluster", clusterData);
//     }
//     url.searchParams.append("page", 1);

//     try {
//         const response = await fetch(url.toString(), {
//             method: 'GET',
//             headers: {
//                 'Authorization': `Bearer ${jwtToken}`
//             }
//         });
//         const data = await response.json();
//         TotalVEnte = data[0].totalVentes; // Assuming data is structured this way
//         updateProgressBar(data[0]); // Update progress bar with fetched data
//         return data[0]; // Returning this for potential future use
//     } catch (error) {
//         console.error('Error fetching data:', error);
//         throw error; // Re-throw to handle it in the calling function
//     }
// }


// function updateProgressBar(data) {
//     const totalVentes = data.totalVentes || 0;
//     const totalReseau = data.totalReseau || 0;
//     const progress = totalReseau > 0 ? (totalVentes / totalReseau) * 100 : 0;

//     const progressBar = document.querySelector('.row3dashsales-progress-bar-complete');
//     progressBar.style.width = `${progress}%`;
//     progressBar.setAttribute('aria-valuenow', progress);

//     const progressText = document.querySelectorAll('.progress-bar-text.percentage');
//     progressText[0].textContent = totalVentes;
//     progressText[1].textContent = totalReseau;
// }


// let SatisfactionAverages;

// function fetchClientSatisfactionAverages(clusterData) {
//     const cardNumber = document.querySelector('.MoyGeneral');
//     const url = new URL(`https://api.nomadcloud.fr/api/satisfaction-clients-averages/${pointOfSaleId}`);
    
//     if (clusterData) { // Ajoute codeCluster uniquement si clusterData est défini
//         url.searchParams.append("codeCluster", clusterData);
//     }

//     url.searchParams.append("page", 1);

//     fetch(url.toString(), {
//         method: 'GET',
//         headers: {
//             'Authorization': `Bearer ${jwtToken}`
//         }
//     })
//     .then(response => {
//         if (!response.ok) {
//             throw new Error(`HTTP error! Status: ${response.status}`);
//         }
//         return response.json();
//     })
//     .then(data => {
//         SatisfactionAverages = data;

//         // Vérifie si les données existent avant d'accéder aux valeurs
//         if (SatisfactionAverages["2024"] && SatisfactionAverages["2024"]["12"]) {
//             const yearData = SatisfactionAverages["2024"]["12"];
//             cardNumber.textContent = `${parseFloat(yearData.moyenneTotal).toFixed(2)}`;
//         }
//     })
//     .catch(error => {
//         console.error('Error fetching data:', error);
//     });
// }


// function findClusterData(clusterCode) {
//     let numericMonth = Number(month);
//     let numericyear = Number(year);

//     const yearData = SatisfactionAverages[numericyear][numericMonth];
//     //const yearData = SatisfactionAverages["2024"]["12"];
//     let clusterDetails = dataByCPV.clusters.find(item => item.codeCluster === clusterCode);
//     // var totalVente=document.querySelector('.totalVente');
//     // totalVente.innerHTML = clusterDetails.nombreVentesCluster;
//     const clusters = yearData.clusters;
//     const cluster = clusters.find(c => c.codeCluster === clusterCode);
//     var MoyGeneral=document.querySelector('.MoyGeneral');
//     var MoyParCluster=document.querySelector('.MoyParCluster');
//     MoyGeneral.style.display='none';
//     MoyParCluster.style.display='block';
//     if (cluster) {
//         if(cluster.moyenneParCluster) {
//         MoyParCluster.innerHTML = `${parseFloat(cluster.moyenneParCluster).toFixed(2)}`;
//         }
//     }else{
//         MoyParCluster.innerHTML = `-`;
//     }
// }



// let fetchedData; // Déclarez la variable globalement pour stocker les données récupérées

// function fetchData(clusterData) {
//     // const now = new Date();
//     // const month = now.getMonth() + 1;
//     // const year = now.getFullYear();

//     const url = new URL(`https://api.nomadcloud.fr/api/productions-by-category/${pointOfSaleId}`);
//     url.searchParams.append("month", month);
//     url.searchParams.append("year", year);

//     if (clusterData) {
//         url.searchParams.append("codeCluster", clusterData);
//     }

//     url.searchParams.append("page", 1);

//     fetch(url.toString(), {
//         method: 'GET',
//         headers: { 'Authorization': `Bearer ${jwtToken}` }
//     })
//     .then(response => {
//         if (!response.ok) {
//             throw new Error(`HTTP error! Status: ${response.status}`);
//         }
//         return response.json();
//     })
//     .then(data => {

//         fetchedData = data; // Stocke les données récupérées
//         // const selectedCategory = document.getElementById("categorySelect").value;
//         // processCategoryData(fetchedData, selectedCategory); 
//     })
//     .catch(error => {
//         console.error('Error fetching data:', error);
//     });
// }

// // document.getElementById("categorySelect").addEventListener("change", handleCategoryChange);

// // function handleCategoryChange() {
// //     const selectedCategory = document.getElementById("categorySelect").value;
// //     if (fetchedData) {
// //         processCategoryData(fetchedData, selectedCategory);
// //     }
// // }

// google.charts.load('current', {packages: ['corechart']});
// google.charts.setOnLoadCallback(init);

// function init() {
//     fetchData();
//     setupThemeSwitcher();
// }


// function fetchData(clusterData) {
//     // const now = new Date();
//     // const month = now.getMonth() + 1;
//     // const year = now.getFullYear();

//     const url = new URL(`https://api.nomadcloud.fr/api/productions-by-category/${pointOfSaleId}`);
//     url.searchParams.append("month", month);
//     url.searchParams.append("year", year);

//     if (clusterData) {
//         url.searchParams.append("codeCluster", clusterData);
//     }

//     url.searchParams.append("page", 1);

//     fetch(url.toString(), {
//         method: 'GET',
//         headers: { 'Authorization': `Bearer ${jwtToken}` }
//     })
//     .then(response => {
//         if (!response.ok) {
//             throw new Error(`HTTP error! Status: ${response.status}`);
//         }
//         return response.json();
//     })
//     .then(data => {
//         fetchedData = data;
//         // const selectedCategory = document.getElementById("categorySelect").value;
//         // processCategoryData(fetchedData, selectedCategory);
//     })
//     .catch(error => {
//         console.error('Error fetching data:', error);
//     });
// }

// function processCategoryData(data, selectedCategory) {
//     const categoryTotals = [];
//     let brutTotal = 0;
//     let raccordeTotal = 0;
//     let valideTotal = 0;
//     let overallTotal = 0;
//     let sommeCONQUETE = 0;
//     let sommeMIGRATION = 0;
//     let sommeMOBILES = 0;
//     if (Type==='B') {
//         dataType='Brut';
//     }else if(Type==='R'){
//         dataType='Raccordé';
//     }else if(Type==='V'){
//         dataType='Validé';
//     }
//     const today = new Date();
//     const currentMonth = `${year}-${month}`;
//     let updateTotals = selectedCategory !== "" && selectedCategory !== null;
//     Object.entries(data).forEach(([month, categories]) => {
//         let monthBrut = 0, monthRaccorde = 0, monthValide = 0;

//         Object.entries(categories).forEach(([status, statusData]) => {
//             Object.entries(statusData).forEach(([category, value]) => {
//                 const parsedValue = Number(value) || 0;

//                 if (category === "CONQUETE") {
//                     if (status === "Brut") monthBrut += parsedValue;
//                     else if (status === "Raccordé") monthRaccorde += parsedValue;
//                     else if (status === "Validé") monthValide += parsedValue;
//                 } else if (category === "MIGRATION") {
//                     if (status === "Brut") monthBrut += parsedValue;
//                     else if (status === "Raccordé") monthRaccorde += parsedValue;
//                     else if (status === "Validé") monthValide += parsedValue;
//                 } else if (category === "MOBILES") {
//                     if (status === "Brut") monthBrut += parsedValue;
//                     else if (status === "Raccordé") monthRaccorde += parsedValue;
//                     else if (status === "Validé") monthValide += parsedValue;
//                 }
//             });
//         });

//         if (updateTotals) {
//             // Seulement mettre à jour si la catégorie est sélectionnée
//             if (selectedCategory === "Brut") {
//                 categoryTotals.push([month, monthBrut]);
//                 if (month === currentMonth) sommeCONQUETE += monthBrut;
//             } else if (selectedCategory === "Raccordé") {
//                 categoryTotals.push([month, monthRaccorde]);
//                 if (month === currentMonth) sommeMIGRATION += monthRaccorde;
//             } else if (selectedCategory === "Validé") {
//                 categoryTotals.push([month, monthValide]);
//                 if (month === currentMonth) sommeMOBILES += monthValide;
//             } else {
//                 // Si aucune catégorie sélectionnée, additionner Brut, Raccordé et Validé
//                 categoryTotals.push([month, monthBrut + monthRaccorde + monthValide]);
//                 if (month === currentMonth) {
//                     sommeCONQUETE += monthBrut;
//                     sommeMIGRATION += monthRaccorde;
//                     sommeMOBILES += monthValide;
//                 }
//             }
//         } else {
//             // Si la catégorie est vide ou null, additionner Brut, Raccordé et Validé
//             categoryTotals.push([month, monthBrut + monthRaccorde + monthValide]);
//             if (month === currentMonth) {
//                 sommeCONQUETE += monthBrut;
//                 sommeMIGRATION += monthRaccorde;
//                 sommeMOBILES += monthValide;
//             }
//         }

//         if (month === currentMonth) {
//             sommeCONQUETE = (categories[dataType]?.CONQUETE || 0);
//             sommeMIGRATION = (categories[dataType]?.MIGRATION || 0);
//             sommeMOBILES = (categories[dataType]?.MOBILES || 0);

//             overallTotal = monthBrut + monthRaccorde + monthValide;
//         }
//     });
//     // Mettez à jour les éléments pour les sommes des catégories
//     updateElement("totalsommeCONQUETE", sommeCONQUETE);
//     updateElement("totalsommeMIGRATION", sommeMIGRATION);
//     updateElement("totalsommeMOBILES", sommeMOBILES);
//     // updateElement("totalsomme", overallTotal);
//     let TotalVEnte=sommeCONQUETE+sommeMIGRATION+sommeMOBILES;
//     const totalVente = document.querySelector('.totalVente');
//     const totalVenteCluter = document.querySelector('.totalVenteCluter');
//     // Assuming TotalVEnte is updated globally by fetchProductionsAnnulation
//     totalVente.innerHTML = TotalVEnte;
//     totalVenteCluter.innerHTML = TotalVEnte;
//     updateProgressBarstest();
//     drawAudienceChart(categoryTotals);
// }

// function updateElement(id, value) {
//     const element = document.getElementById(id);
//     if (element) element.textContent = value;
// }


// function drawAudienceChart(data) {
//     const selectedTheme = document.documentElement.getAttribute("data-theme") || "light";
//     const chartColor = selectedTheme === "dark" ? "#FFA500" : "#539AF8";

//     const options = {
//         title: '',
//         legend: { position: 'none' },
//         hAxis: {
//             textPosition: 'none',
//             gridlines: { color: 'transparent' },
//             baselineColor: 'transparent'
//         },
//         vAxis: {
//             minValue: 0,
//             textPosition: 'none',
//             gridlines: { color: 'transparent', count: 4 },
//             baselineColor: 'transparent'
//         },
//         backgroundColor: 'transparent',
//         colors: [chartColor],
//         bar: { groupWidth: '100%' },
//         chartArea: {
//             left: 0,
//             top: '30px',
//             width: '100%',
//             height: '100%'
//         }
//     };

//     const chart = new google.visualization.ColumnChart(document.getElementById('audienceChartStat'));
//     chart.draw(google.visualization.arrayToDataTable([
//         ['Month', 'Total'],
//         ...data
//     ]), options);
// }


// function drawAudienceOverview() {
//     fetchData(); // Ensure data is fetched when charts are ready
//     setupThemeSwitcher();
// }


// function setupThemeSwitcher() {
//     const themeOptionItems = document.querySelectorAll(".theme-options li");
//     themeOptionItems.forEach((item) => {
//         item.addEventListener("click", () => {
//             const selectedTheme = item.getAttribute("data-theme");
//             document.documentElement.setAttribute("data-theme", selectedTheme);

//             // Update active class
//             themeOptionItems.forEach((el) => el.classList.remove("active"));
//             item.classList.add("active");

//             // Redraw the chart with the new theme
//             fetchData();
//         });
//     });
// }
// function updateProgressBarstest() {
//         // Get the total values for each status
//         const brutTotal = parseInt(document.getElementById("totalsommeCONQUETE").textContent);
//         const raccordeTotal = parseInt(document.getElementById("totalsommeMIGRATION").textContent);
//         const valideTotal = parseInt(document.getElementById("totalsommeMOBILES").textContent);

//         // Find the maximum total sum to normalize the width percentages
//         const maxTotal = Math.max(brutTotal, raccordeTotal, valideTotal);


//     // Calculate the width percentages for each progress bar
//     const brutWidth = (brutTotal / maxTotal) * 100;
//     const raccordeWidth = (raccordeTotal / maxTotal) * 100;
//     const valideWidth = (valideTotal / maxTotal) * 100;

//     // Set the width of the progress bars
//     document.getElementById("progress-bar-CONQUETE").style.width = `${brutWidth}%`;
//     document.getElementById("progress-bar-MIGRATION").style.width = `${raccordeWidth}%`;
//     document.getElementById("progress-bar-MOBILES").style.width = `${valideWidth}%`;
    
// }


