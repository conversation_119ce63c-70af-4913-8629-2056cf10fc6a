<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class LivedashController extends AbstractController
{
    #[Route('/livedash', name: 'app_livedash')]
    public function index(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {

        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
        try {
     
            // Récupération des données utilisateur
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }

            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;

            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }

            // Récupération des détails utilisateur
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }

            $userData = json_decode($response->getContent(), true);

    
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());

        }
        return $this->render('livedash/index.html.twig', [
            'controller_name' => 'LivedashController',
            'user' => $userData,
        ]);
    }
}
