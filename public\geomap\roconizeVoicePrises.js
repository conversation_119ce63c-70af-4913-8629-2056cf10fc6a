let recognition;
let finalTranscript = '';
const keywords = {
    "zoom sur": 8,
    "vas à": 8,
    "montre-moi": 8,
    "lance": 8,
    "affiche le cluster de": 8,
    "montre moi le cluster de": 8
}
function extractCityName(text) {
    let patterns = [
        /zoom sur (.+)/,
        /va sur la ville de (.+)/,
        /vas à (.+)/,
        /montre-moi (.+)/,
        /lance (.+)/,
        /affiche le cluster de (.+)/,
        /montre moi le cluster de (.+)/,
        /^(.+)$/
    ];

    for (let pattern of patterns) {
        let match = text.match(pattern);
        if (match) {
            return match[1].trim();
        }
    }

    return null;
}
function startListening() {
    recognition.start();
    console.log(' Commencez à parler...');
}
function stopListening() {
    recognition.stop();
    console.log('Arrêt de la reconnaissance vocale...');
}
function updateTextArea(message) {
    let output = document.getElementById("microCard");
    output.innerHTML = message + "\n"; 
    output.scrollTop = output.scrollHeight;
}



if ('webkitSpeechRecognition' in window) {
    recognition = new webkitSpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = false;
    recognition.lang = 'fr-FR';

    recognition.onresult = (event) => {
        let transcript = event.results[event.resultIndex][0].transcript.trim().toLowerCase();
        console.log(`🎤 Texte détecté: "${transcript}"`);
        updateTextArea(`🎤 Détection: "${transcript}"`);
        InputSettingValue(transcript);

        detectNumberRange(transcript);
    };

    recognition.onerror = (event) => {
        console.error("Erreur de reconnaissance vocale:", event.error);
    };

} else {
    alert("Votre navigateur ne supporte pas la reconnaissance vocale.");
}

function detectNumberRange(text) {
    let regexFullRange = /(-?\s*\d+)\s*[aà]\s*(-?\s*\d+)/gi;
    let regexSingleAnciennete = /ancienneté\s*de\s*(-?\s*\d+)/gi;
    let regexSinglePrises = /prises\s*de\s*(-?\s*\d+)/gi; // Nouveau regex pour "prises de -49"

    let matchFull = regexFullRange.exec(text);
    let matchAnciennete = regexSingleAnciennete.exec(text);
    let matchPrises = regexSinglePrises.exec(text);

    const searchInput = document.getElementById("TreeSearchs");

    if (matchFull) {
        let num1 = parseInt(matchFull[1].replace(/\s+/g, ''));
        let num2 = parseInt(matchFull[2].replace(/\s+/g, ''));
        console.log(`✅ Plage détectée: ${num1} à ${num2}`);
        updateTextArea(`✅ Plage détectée: ${num1} à ${num2}`);
     
        if (searchInput) {
            searchInput.value = `${num1} à ${num2}`;
            searchInput.dispatchEvent(new Event('input'));
        }
    } else if (matchAnciennete) {
        let num1 = parseInt(matchAnciennete[1].replace(/\s+/g, ''));
        let num2 = 0;
        console.log(`✅ Ancienneté détectée: ${num1} → Défaut: ${num2}`);
        updateTextArea(`✅ Ancienneté détectée: ${num1} → Défaut: ${num2}`);
     
        if (searchInput) {
            searchInput.value = `${num1} à ${num2}`;
            searchInput.dispatchEvent(new Event('input'));
        }
    } else if (matchPrises) { 
        let num1 = parseInt(matchPrises[1].replace(/\s+/g, ''));
        let num2 = 0; // Valeur par défaut
        console.log(`✅ Plage détectée: ${num1} à ${num2}`);
        updateTextArea(`✅ Plage détectée: ${num1} à ${num2}`);
     
        if (searchInput) {
            searchInput.value = `${num1} à ${num2}`;
            searchInput.dispatchEvent(new Event('input'));
        }
    }

}

function InputSettingValue(queryWord) {
    const searchInput = document.getElementById("SearchInputTopBar");
    if (searchInput) {
        searchInput.value = queryWord;
        searchInput.dispatchEvent(new Event('input'));
    }
}


document.addEventListener("DOMContentLoaded", function () { const card = document.getElementById("microCard"); const micIcon = document.getElementById("micIcon");
    //const terminalIcon = document.getElementById("terminalicon");
    const hiddenDiv = document.getElementById("hiddendiv");

    // Afficher/cacher la carte microCard
    micIcon.addEventListener("click", function () {  
        card.style.display = (card.style.display === "none" || card.style.display === "") ? "block" : "none";
        if (card.style.display === "none"|| card.style.display === ""){
              stopListening();
        }else {
            startListening();
        }
        

    });
    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifie que c'est bien un clic gauche
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
        document.body.style.userSelect = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
        document.body.style.userSelect = "auto";
    });
});
