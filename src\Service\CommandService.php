<?php

namespace App\Service;

use Symfony\Contracts\HttpClient\HttpClientInterface;

class CommandService
{

    private $competitionsService;

    public function __construct(CompetitionsService $competitionsService)
    {
        $this->competitionsService = $competitionsService;
    }

    public function integrateCompetions(){

        $this->competitionsService->getCompetitionByCountry();
    }

    public function integrateClubs(){

        $this->competitionsService->getClubsByCompetionId();
    }

    public function integratePlayers(){

        $this->competitionsService->getPlayersByClubId();
    }



}