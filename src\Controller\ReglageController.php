<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

class ReglageController extends AbstractController
{

    #[Route('reglages/role/apps', name: 'role_apps')]
    public function role(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        // Initialize the $productions variable to ensure it's defined
        $productions = [];

        try {
            // Fetch user data
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
     

            // Fetch product data
            // Fetch product data
            $response2 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/role_apps?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $productData = json_decode($response2->getContent(), true);
          

            // Check if product data is received
            if (!isset($productData['hydra:member'])) {
                $this->addFlash('error', 'No products found.');
                return $this->redirectToRoute('error_route');
            }

            // Access products directly
            $products = $productData['hydra:member'] ?? []; // Use null coalescing operator


            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/products?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            $productionData = json_decode($response3->getContent(), true);
    
            if (isset($productionData['hydra:member'])) {
                $productions = $productionData['hydra:member'];
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }




        return $this->render('reglages/role_apps/index.html.twig', [
            'controller_name' => 'ReglageController',
            'user' => $userData,
            'produits' => $products,

        ]);
    }
    #[Route('reglages/users/tree', name: 'users_tree')]
    public function usersTree(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        // Initialize the variables to ensure they're defined
        $userData = [];
        $roleApps = [];

        try {
            // Fetch user data
            $userResponse = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($userResponse->getContent(), true);


            // Fetch role app data
            $roleAppResponse = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/tree/first', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $roleAppData = json_decode($roleAppResponse->getContent(), true);
       

            if (isset($roleAppData['hydra:member'])) {
                $roleApps = $roleAppData['hydra:member'];
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('reglages/users_tree/index.html.twig', [
            'controller_name' => 'ReglageController',
            'user' => $userData,
            'roleapps' => $roleApps,
        ]);
    }
    #[Route('reglages/produits', name: 'produits')]
    public function produits(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }


        $productions = [];
        $categroy = [];

        try {
            // Fetch user data
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
         

            // Fetch product data
            // Fetch product data
            $response2 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/products', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $productData = json_decode($response2->getContent(), true);
     

            // Check if product data is received
            if (!isset($productData['hydra:member'])) {
                $this->addFlash('error', 'No products found.');
                return $this->redirectToRoute('error_route');
            }

            // Access products directly
            $products = $productData['hydra:member'] ?? []; // Use null coalescing operator


            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/products?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            $productionData = json_decode($response3->getContent(), true);
       
            if (isset($productionData['hydra:member'])) {
                $productions = $productionData['hydra:member'];
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }



        return $this->render('reglages/produits/index.html.twig', [
            'controller_name' => 'ReglageController',
            'user' => $userData,
            'produits' => $products,
            'productions' => $productions
        ]);
    }
    #[Route('reglages/operateurs', name: 'operateurs')]
    public function operateurs(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {

        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        // Initialize the $productions variable to ensure it's defined
        $productions = [];

        try {
            // Fetch user data
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
           

            // Fetch product data
            $response2 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/operators?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $productData = json_decode($response2->getContent(), true);
          

            // Check if product data is received
            if (!isset($productData['hydra:member'])) {
                $this->addFlash('error', 'No products found.');
                return $this->redirectToRoute('error_route');
            }


            $products = $productData['hydra:member'];

            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/operators?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);

            $productionData = json_decode($response3->getContent(), true);
          
            if (isset($productionData['hydra:member'])) {
                $productions = $productionData['hydra:member'];
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }


        return $this->render('reglages/operateurs/index.html.twig', [
            'controller_name' => 'ReglageController',
            'user' => $userData,
            'produits' => $products,
            'productions' => $productions
        ]);
    }
    #[Route('reglages/effectifs', name: 'effectifs')]
    public function effectifs(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
    
        if (!$jwt) {
            // If no JWT token, redirect to login page
            return $this->redirectToRoute('app_login');
        }
    
        try {
            // Fetch user-connected data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                throw new \Exception('Failed to fetch user-connected data.');
            }
    
            $dataUser = json_decode($response1->getContent(), true);
            $userId = $dataUser['user_id'] ?? null;
    
            if (!$userId) {
                throw new \Exception('User ID not found in the response.');
            }
    
            // Fetch user details
            $response2 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userId}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            if ($response2->getStatusCode() !== 200) {
                throw new \Exception('Failed to fetch user details.');
            }
    
            $userData = json_decode($response2->getContent(), true);
            $cpv = $dataUser['cpv'] ?? null;
    
            if (!$cpv) {
                throw new \Exception('CPV not found in the user data.');
            }
    
            // Get effectifs data (single user with children)
            $response3 = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userId}/tree/all?page=1", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
    
            if ($response3->getStatusCode() !== 200) {
                throw new \Exception('Failed to fetch effectifs data.');
            }
    
            $effectifsData = json_decode($response3->getContent(), true);
            $effectifs = $effectifsData['children'] ?? [];
    
        } catch (\Exception $e) {
            // Add flash error and log the exception for debugging
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
            $effectifs = [];
        }
    
        // Render the template with the fetched data
        return $this->render('reglages/effectifs/index.html.twig', [
            'controller_name' => 'ReglageController',
            'effectifs' => $effectifs,
        ]);
    }
    
    
    #[Route('reglages/effectifs/{id}', name: 'effectifs_user')]
    public function showUser(HttpClientInterface $httpClient, SessionInterface $session, int $id, Request $request): Response
    {
        $jwt = $session->get('jwt');
    
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            // Handle GET request to fetch user details
            if ($request->getMethod() === 'GET') {
                $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$id}", [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $jwt,
                    ]
                ]);
                $userDetails = json_decode($response->getContent(), true);
                
                return $this->json($userDetails); // Return the user details as a JSON response
            }
    
            // Handle PATCH request to update user details
            if ($request->getMethod() === 'PATCH') {
                $data = json_decode($request->getContent(), true);
    
                // Validate data
                if (!isset($data['nom']) || !isset($data['prenom']) || !isset($data['email'])) {
                    return $this->json(['error' => 'Missing required fields'], 400);
                }
    
                $response = $httpClient->request('PATCH', "https://api.nomadcloud.fr/api/users/{$id}", [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $jwt,
                        'Content-Type' => 'application/merge-patch+json',
                        'accept' => 'application/ld+json',
                    ],
                    'json' => $data,
                ]);
    
                if ($response->getStatusCode() === 200) {
                    return $this->json(['message' => 'User updated successfully']);
                } else {
                    return $this->json(['error' => 'Failed to update user'], 500);
                }
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching or updating user details: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }
    
        return $this->json(['error' => 'Invalid request method'], 405);
    }
    
    
    
    #[Route('ventes/ventes/liste', name: 'listevente')]
    public function listevente(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        try {
            // Récupération des données utilisateur
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);

            // Récupération des paramètres de requête
            $categoryId = $request->query->get('categoryId', '');
            $productId = $request->query->get('productId', '');
            $etatId = $request->query->get('etatId', ''); // Nouveau paramètre ajouté

            // Appel à l'API avec les nouveaux paramètres
            $response2 = $httpClient->request('GET', 'http://api.nomadcloud.fr/api/productions-by-point-sale/3', [
                'query' => [
                    'categoryId' => $categoryId,
                    'productId' => $productId,
                    'etatId' => $etatId, // Ajout du paramètre etatId
                    'debut' => '01-10-2024',
                    'fin' => '31-10-2024',
                    'page' => 1,
                ],
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $productions = json_decode($response2->getContent(), true);

            if ($request->isXmlHttpRequest()) {
                return new JsonResponse([
                    'productions' => $productions,
                    'user' => $userData,
                ]);
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
            return $this->redirectToRoute('home');
        }
        try {
            $response6 = $httpClient->request('GET', 'http://api.nomadcloud.fr/api/productions_states?page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $productionsStates = json_decode($response6->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching motif productionscategory: ' . $e->getMessage());
            $productionsStates = [];
        }

        return $this->render('reglages/listevente/index.html.twig', [
            'controller_name' => 'ReglageController',
            'productions' => $productions,
            'user' => $userData,
            'productionsStates' => $productionsStates['hydra:member'] ?? [],
        ]);
    }
}
