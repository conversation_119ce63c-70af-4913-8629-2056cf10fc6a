
<div class="bonustechno-container">
    <h5 class="text-header4s">Par Statut Preco</h5>
    <div class="ChartsByType bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="statusprecoPieChart"></canvas>
        </div>
        <ul class="bonustechno-legendChart">
            <!-- Legend will be populated dynamically -->
        </ul>
    </div>
</div>

<script>
var statusprecoPieChart; // Global reference to the chart instance

function ParStatutPreco(chartData) {
  function createChart() {
    var bonustechnoCtx = document.getElementById('statusprecoPieChart').getContext('2d');

    // Destroy the existing chart if it exists
    if (statusprecoPieChart) {
        statusprecoPieChart.destroy();
    }

    // Process chartData for the chart's labels and data
    var labels = [];
    var data = [];
    var backgroundColors = []; // Array to hold background colors for each segment

    chartData.forEach(function(item, index) {
      labels.push(item.categorie); // Assuming 'categorie' contains the category name
      data.push(item.totalVentes); // Add totalVentes as data
    });

    // Create pie chart with the processed data
    statusprecoPieChart = new Chart(bonustechnoCtx, {
      type: 'doughnut',
      data: {
        labels: labels, // Category names as labels
        datasets: [{
          data: data, // Sales data for each category
           backgroundColor: [
                        '#9a4eca',
                        '#62eade',
                        '#145277'
                    ],
                    borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false // Hide the default legend (you've created a custom one)
          }
        },
        cutout: '80%'
      }
    });
  }

  function createLegend() {
    const legendContainer = document.querySelector('.bonustechno-legendChart');
    legendContainer.innerHTML = ''; // Clear existing legend items
    chartData.forEach(function(item, index) {
     // console.log('chartData//',item);
      const legendItem = document.createElement('li');

      // Create legend color block
      const legendColor = document.createElement('span');
      legendColor.classList.add('bonustechno-legend-color');

      // Assign colors based on index
      if (index % 3 === 0) {
        legendColor.style.background = '#9a4eca'; // Gradient A
      } else if (index % 3 === 1) {
        legendColor.style.background = '#62eade'; // Gradient B
      } else {
        legendColor.style.background = '#145277'; // Gradient C
      }

      // Append color block and category name
      legendItem.appendChild(legendColor);
      legendItem.appendChild(document.createTextNode(item.categorie)); // Category name
      legendContainer.appendChild(legendItem);
    });
  }

  createChart();
  createLegend();
}

</script>
