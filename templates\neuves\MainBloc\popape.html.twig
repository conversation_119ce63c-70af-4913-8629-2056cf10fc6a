<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Popup with Overlay</title>
	<style>
:root {
    --popup-button: #fff;
    --popup-col: #222;
    --popup-border: #222;
    --background-color: #fff;
    --text-color: #333;
    --header-bg: #fff;
    --header-border: #ccc;
    --comparison-hover-bg: #f1f1f1;
    --search-bg: #f1f1f1;
    --search-border: #ccc;
    --button-border: #dadce0;
    --button-color: #1a73e8;
    --overlay-bg: rgba(0, 0, 0, 0.32);
    --button-border-popup:#1a73e8;
}

[data-theme="dark"] {
    --popup-button: transparent;
    --popup-col: white;
    --popup-border: white;
    --background-color: #1c1e21;
    --text-color: #fff;
    --header-bg: #292b2f;
    --header-border: #444;
    --comparison-hover-bg: #383c42;
    --search-bg: #444;
    --search-border: #666;
    --button-border: #dadce0;
       --button-border-popup:#1a73e8;
    --button-color: #1a73e8;
    --overlay-bg: rgba(255, 255, 255, 0.32);
}

.map-popup-button {
    position: absolute;
    background-color: var(--popup-button);
    color: var(--popup-col);
    border: 1px dashed var(--popup-border);
    border-radius: 25px;
    cursor: pointer;
    z-index: 1000;
    font-size: 12px;
}

@media screen and (min-width: 1400px) {
    .map-popup-button {
        top: 150px;
        left: 100px;
    }
    .overlay {
    position: fixed;
    top: 11.8%;
    left: 16.9%;
    right: 30%;
    width: 19%;
    height: 100%;
    background-color: var(--overlay-bg);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}
.popup-content {
    position: fixed;
    top: 11.8%;
    right: -100%;
    height: 100%;
    width: 70%;
    background-color: var(--background-color);
    color: var(--text-color);
    overflow-y: auto;
    z-index: 10000;
    border: 1px solid var(--header-border);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.2);
    transition: right 0.3s ease-in-out;
}

}

@media screen and (max-width: 1366px) and (min-width: 400px) {
    .map-popup-button {
        top: 170px;
        left: 100px;
    }
    .overlay {
    position: fixed;
    top: 13.8%;
    left: 23.5%;
    right: 30%;
    width: 19%;
    height: 100%;
    background-color: var(--overlay-bg);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}
.popup-content {
    position: fixed;
    top: 13.8%;
    right: -100%;
    height: 100%;
    width: 70%;
    background-color: var(--background-color);
    color: var(--text-color);
    overflow-y: auto;
    z-index: 10000;
    border: 1px solid var(--header-border);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.2);
    transition: right 0.3s ease-in-out;
}
}

.map-popup-buttons {
    padding: 4px;
    margin: 0;
    font-size: 13px;
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--button-border-popup);
    border-radius: 25px;
}



.popup-content.open {
    right: 0;
}

/* Overlay */


.overlay.visible {
    opacity: 1;
    visibility: visible;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: var(--header-bg);

}

.popup-header h2 {
    margin: 0;
    font-weight: 500;
    font-size: 18px;
}

.close-popup {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-color);
    cursor: pointer;
}

.table-container {
  width: 100%;
  margin: 20px auto;
  font-family: Arial, sans-serif;
  font-size: 12px;
}

.table-controls {
  display: flex;

  align-items: center;
  margin-top: 10px;
  justify-content:end;
  padding-right: 50px;
}

.table-controls select {
  padding: 5px;
  border-radius: 4px;
  border: 1px solid var(--header-border);
}
.pagination{
margin: 5px 10px; 
display: block;

    
}
.pagination button {
  padding: 5px 10px;
  margin: 0 2px;
 
  background-color: white;
  cursor: pointer;
  border-radius: 3px;
  width: 0;
      
     
}

.pagination button:disabled {
  background-color: #f1f1f1;
  cursor: not-allowed;
}

#pageInfo {
  margin: 0 10px;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  color: var(--text-color);
}

.comparison-table th,
.comparison-table td {
  padding: 8px 10px;
  text-align: left;
  border-bottom: 1px solid var(--header-border);
  font-size: 14px;
}

.comparison-table tbody tr:hover {
  background-color: var(--comparison-hover-bg);
}

.comparison-table input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.mdx-search-input {
    background-color: var(--search-bg);
    color: var(--text-color);
    border: 1px solid var(--search-border);
    border-radius: 5px;
    padding: 5px;
    width: 40%;
}

.create {
    background-color: transparent;
    color: var(--button-color);
    border: 1px solid var(--button-border);
    border-radius: 5px;
    padding: 5px;
    width: 85px;
}

.appliquer {
    background-color: var(--search-bg);
    color: var(--text-color);
 border: 1px solid var(--search-bg);
    border-radius: 5px;
    padding: 5px;
    width: 85px;
}

.contents {
    padding-right: 15px;

}

.popup-header {
    margin-top: 0;
    margin-left: 0;
    padding: 10px;
}
/* Style de la boîte englobante */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
    width: 40%;

}

.input-container i {
  position: absolute;
  left: 10px; /* Ajustez pour contrôler la position horizontale */
  color: #888; /* Couleur de l'icône */
  pointer-events: none; /* Empêche l'icône d'être cliquable */
}

.mdx-search-input {
  width: 100%; /* Ajustez la largeur selon vos besoins */
  padding: 10px 10px 10px 35px; /* Ajout d'un espace à gauche pour éviter le chevauchement avec l'icône */
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 5px;
  outline: none;
  transition: border-color 0.3s;
}

.mdx-search-input:focus {
  border-color: #007bff; /* Couleur de la bordure au focus */
}

</style>

</head>
<body>

	<button id="openPopup" class="map-popup-button">
		Ajouter une comparaison
		<i class="fa fa-plus"></i>
	</button>

	<div id="popupOverlay" class="overlay"></div>

	<!-- Contenu du pop-up -->
	<div id="popupContent" class="popup-content">
		<div class="popup-header">
			<h2>
				<button id="closePopup" class="close-popup">&times;</button>
				Appliquer une comparaison
			</h2>
<div class="input-container">
  <i class="bi bi-search"></i>
  <input 
    type="text" 
    aria-label="Rechercher" 
    class="mdx-search-input" 
    placeholder="Rechercher" 
  />
</div>

			<div class="contents">
				<button class="create">
					<i class="fa fa-plus"></i>
					Creer
				</button>
				<button class="appliquer">
					Appliquer
				</button>
			</div>
		</div>
		<div style="background-color: #f3f4f5;" class="popup-header">
			<h2>
				<button id="openPopup" class="map-popup-buttons">
					<i style="color: #1a73e8;" class="fa fa-plus"></i>
					tous les utilisateurs
				</button>
			</h2>
		</div>
		<div class="popup-body">
			<table class="comparison-table">
				<thead style="font-size: 12px;">
					<tr>
						<th>
							<label class="custom-checkbox">
								<input type="checkbox" class="form-check-input" id="customCheck">
							</label>
						</th>
						<th>Nom</th>
						<th>Description</th>
						<th>Résumé</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td class="checkbox-cell">
							<input type="checkbox" class="form-check-input custom-check">
						</td>
						<td>Tous les utilisateurs</td>
						<td>Inclut toutes vos données.</td>
						<td></td>
					</tr>
					<tr>
						<td class="checkbox-cell">
							<input type="checkbox" class="form-check-input custom-check">
						</td>
						<td>Accès directs</td>
						<td>Sessions acquises directement.</td>
						<td>Groupe de canaux par défaut pour la session correspond exactement à 'Direct'</td>
					</tr>
					<tr>
						<td class="checkbox-cell">
							<input type="checkbox" class="form-check-input custom-check">
						</td>
						<td>Trafic généré par les résultats naturels</td>
						<td>Sessions acquises grâce aux canaux naturels.</td>
						<td>Groupe de canaux par défaut pour la session correspond exactement à 'Organic Search|Organic Video|Organic Social|Organic Shopping'</td>
					</tr>
					<tr>
						<td class="checkbox-cell">
							<input type="checkbox" class="form-check-input custom-check">
						</td>
						<td>Trafic généré par les liens commerciaux</td>
						<td>Sessions acquises grâce aux canaux payants.</td>
						<td>Groupe de canaux par défaut pour la session correspond exactement à 'Paid Search|Paid Social|Paid Other|Paid Video|Display|Cross-network|Audio'</td>
					</tr>
				</tbody>
			</table>
             <div class="table-controls">
    <label>
      Éléments par page:
      <select id="itemsPerPage" onchange="updateTable()">
        <option value="5">5</option>
        <option value="10">10</option>
        <option value="25" selected>25</option>
      </select>
    </label>

    <div class="pagination">
     <span id="pageInfo">1–9 sur 9</span>
      <button onclick="changePage('first')">«</button>
      <button onclick="changePage('prev')">‹</button>
     
      <button onclick="changePage('next')">›</button>
      <button onclick="changePage('last')">»</button>
    </div>
  </div>
		</div>
	</div>

	<script>
		document.addEventListener("DOMContentLoaded", function () {
const popup = document.getElementById("popupContent");
const overlay = document.getElementById("popupOverlay");
const openPopupButton = document.getElementById("openPopup");
const closePopupButton = document.getElementById("closePopup");

// Ouvrir le pop-up avec l'overlay
openPopupButton.addEventListener("click", function () {
popup.classList.add("open");
overlay.classList.add("visible"); // Afficher l'overlay
});

// Fermer le pop-up et cacher l'overlay
closePopupButton.addEventListener("click", function () {
popup.classList.remove("open");
overlay.classList.remove("visible"); // Masquer l'overlay
});

// Fermer le pop-up si on clique sur l'overlay
overlay.addEventListener("click", function () {
popup.classList.remove("open");
overlay.classList.remove("visible");
});
});
	</script>

</body></html>
