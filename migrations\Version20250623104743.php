<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250623104743 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE competition_clubs (id INT AUTO_INCREMENT NOT NULL, competition_id VARCHAR(255) DEFAULT NULL, clubs JSON DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE competitions (id INT AUTO_INCREMENT NOT NULL, competion_id VARCHAR(255) DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, clubs VARCHAR(10) DEFAULT NULL, players INT DEFAULT NULL, continent VARCHAR(255) DEFAULT NULL, total_market_value VARCHAR(255) DEFAULT NULL, mean_market_value VARCHAR(255) DEFAULT NULL, competition_id VARCHAR(255) DEFAULT NULL, competion_continent VARCHAR(255) DEFAULT NULL, competition_name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE players_club (id INT AUTO_INCREMENT NOT NULL, club_id VARCHAR(255) DEFAULT NULL, players JSON DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE competition_clubs
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE competitions
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE players_club
        SQL);
    }
}
