.modal-event {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content-event {
    background-color: #2B2D31;
    margin: 15% auto;
    border-radius: 10px;
    width: 600px;
    height: auto;
    max-height: 80vh;
    overflow-y: auto;
    color: #FFF;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
}

.modal-header-event {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    background-color: #1E1F22;
    padding: 0 20px;
}


.dropdown-icon {
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.hr-modal {
    width: 1px;
    height: 14px;
    background-color: #666666;
    margin-left: 13px;
}

.create-event-btn {
    background-color: #5865F2;
    color: white;
    border: none;
    padding: 4.5px 10px;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 20px;
}

.close-btn {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.event-placeholder {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
}

.no-event {
    font-size: 20px;
    font-weight: bold;
    margin-top: 10px;
}

.info-text {
    font-size: 12px;
    color: #ccc;
}
