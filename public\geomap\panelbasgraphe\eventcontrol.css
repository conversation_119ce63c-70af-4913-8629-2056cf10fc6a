.eventcontrol {
    background: #eee;
    color: var(--channel-item-color);
    background: var(--headerTreeSection-bg);
    border-radius: 3px;
    /* border: 1px solid #e0e0e0; */
    overflow: hidden;
    position: relative;
    width: 100%;
    background: none;
}
.eventcontrol .ec-region-label {
    position: absolute;
    padding: 0;
    font-size: 10px;
    font-weight: bold;
    text-align: left;
    white-space: nowrap;
    color: var(--bs-heading-color,inherit);
}
.eventcontrol .ec-markers .ec-ticks .ec-tick {
    position: absolute;
    width: 0px;
    border-left: 1px solid var(--first-color);
    opacity: 0.5;
}
.ec-markers.ec-draggable {
    background: rgb(15 24 31);      
}
.ec-items.ec-draggable {
    background: rgb(15 24 31/40%);
}
