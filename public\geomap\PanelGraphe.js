async function fetchCityDetailsFromINSEE(inseeCode) {
    const apiUrl = `https://geo.api.gouv.fr/communes?code=${inseeCode}&fields=nom,superficie,codesPostaux,population,maire,altitude`;
    try {
        const response = await fetch(apiUrl);
        const data = await response.json();
        if (data.length > 0) {
            return {
                name: data[0].nom,
                superficie: data[0].superficie || "N/A",
                codePostal: data[0].codesPostaux ? data[0].codesPostaux[0] : "N/A",
                altitude: data[0].altitude || "N/A",
                maire: data[0].maire ? data[0].maire.nom : "Inconnu",
                population: data[0].population || "N/A"
            };
        } else {
            alert("Aucune ville trouvée pour ce code INSEE.");
            return null;
        }
    } catch (error) {
        console.error("Erreur lors de la récupération des infos :", error);
        alert("Erreur lors de la récupération des données.");
        return null;
    }
}

async function fetchWikidataInfo(inseeCode) {
    const sparqlQuery = `
        SELECT ?item ?article ?image WHERE {
            ?item wdt:P374 "${inseeCode}".
            OPTIONAL { ?article schema:about ?item; schema:isPartOf <https://fr.wikipedia.org/>. }
            OPTIONAL { ?item wdt:P18 ?image. }
        } LIMIT 1
    `;
    const url = `https://query.wikidata.org/sparql?query=${encodeURIComponent(sparqlQuery)}&format=json`;

    try {
        const response = await fetch(url, { headers: { "Accept": "application/json" } });
        const data = await response.json();

        if (data.results.bindings.length === 0) {
            return { wikipediaUrl: null, imageUrl: null };
        }

        let wikipediaUrl = data.results.bindings[0].article?.value || null;
        let imageUrl = data.results.bindings[0].image?.value || null;

        return { wikipediaUrl, imageUrl };
    } catch (error) {
        console.error("Erreur lors de la récupération des infos Wikidata :", error);
        return { wikipediaUrl: null, imageUrl: null };
    }
}

async function fetchWikipediaDescription(wikipediaUrl) {
    if (!wikipediaUrl) return "Aucune description trouvée sur Wikipédia.";

    const wikiTitle = wikipediaUrl.split('/').pop();
    const summaryUrl = `https://fr.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(wikiTitle)}`;

    try {
        const summaryResponse = await fetch(summaryUrl);
        const summaryData = await summaryResponse.json();

        if (summaryData.extract) {
            return summaryData.extract;
        }

        return "Aucune description disponible sur Wikipédia.";
    } catch (error) {
        console.error("Erreur lors de la récupération de la description Wikipédia :", error);
        return "Impossible de charger la description Wikipédia.";
    }
}

async function fetchCityData(inseeCode) {

    if (!inseeCode) {
        alert("Veuillez entrer un code INSEE !");
        return;
    }

    const cityDetails = await fetchCityDetailsFromINSEE(inseeCode);
    console.log(cityDetails);
    if (!cityDetails) return;

    // document.getElementById("cityName").innerText =`Ville : ${cityDetails.name}` ;
    document.getElementById("cityHabitant").innerText =`populations : ${cityDetails.population}` ;
    const { wikipediaUrl, imageUrl } = await fetchWikidataInfo(inseeCode);

    if (imageUrl) {
        document.getElementById("cityImage").src = imageUrl;
        document.getElementById("cityImage").style.display = "block";
    } else {
        document.getElementById("cityImage").style.display = "none";
    }

    const wikiDescription = await fetchWikipediaDescription(wikipediaUrl);
}

// let Interventionsmigrable;

// async function fetchdataMigrable(clusterCode) {
//     console.log('Fetching data for cluster code:', clusterCode);
//     const url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/${cpv}?codeCluster=${clusterCode}&page=1`;
//     try {
//         const response = await fetch(url, {
//             method: 'GET',
//             headers: {
//                 'Authorization': `Bearer ${jwtToken}`, // Ensure jwtToken is defined and valid
//                 'Content-Type': 'application/json'
//             }
//         });
//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }
//         Interventionsmigrable = await response.json();
//     } catch (error) {
//         console.error('Error fetching interventions migrable:', error);
//         return null;
//     }
// }

async function displayInterventionsMigrable(Code, isVille) {
    console.log('Code:', Code);
    console.log('Interventionsmigrable:', Interventionsmigrable);
    let dataToDisplay;

    if (!isVille) {
        await fetchdataMigrable(Code);
        dataToDisplay = Interventionsmigrable;
    } else {
        if (!Interventionsmigrable || !Interventionsmigrable.villes) {
            console.error("Missing 'villes' data or 'Interventionsmigrable' is not loaded");
            return;
        }
        // Convert Code to a number if it's provided as a string
        const codeNumeric = parseInt(Code, 10);
        dataToDisplay = Interventionsmigrable.villes.find(v => v.cod_insee === codeNumeric);
        if (!dataToDisplay) {
            console.error("No ville found with the INSEE code:", Code);
            return;
        }
    }

    console.log('Data to display:', dataToDisplay);
    updateDisplayValues(dataToDisplay);
}

function updateDisplayValues(data) {
    const keys = ['nb_fyr_fttb', 'nb_fyr_adsl', 'nb_fyr_mob_mono', 'nb_fyr_mob_multi_thd', 'nb_fyr_mob_multi_adsl', 'nb_fyr_thd'];
    keys.forEach(key => {
        const element = document.querySelector(`.${key}`);
        if (element) {
            element.textContent = data[key] || 'N/A';  // Providing a fallback if data[key] is undefined
        } else {
            console.error(`Element with class ${key} not found.`);
        }
    });
}

let ProductionSalesByDays;
async function fetchProductionSalesByDays(clusterCode){
    const url = `https://api.nomadcloud.fr/api/productions-zero-sales-days/${pointOfSaleId}/${clusterCode}?optionSelect=${Type}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        ProductionSalesByDays = data;
        document.querySelector('.nbrVilleSupCentJrs').textContent=`${ProductionSalesByDays.nombre_villes_sup_100_jrs}`;
        return data;  // Return data for further processing
    } catch (error) {
        console.error('Error fetching production sales by days:', error);
        throw error;  // Rethrow or handle error as needed
    }
}

async function sendRightdataforChartDAys(code,isVille){
    console.log('sendRightdataforChartDAys:', code, isVille);
    if(isVille){
        days=ProductionSalesByDays.villes.find(ville => ville.code_insee==code).nombre_jours;
        console.log('days:',days);
        document.querySelector('.annotationChart').textContent=days+' jrs';
    }else{
        clusterData= await fetchProductionSalesByDays(code);
        console.log('clusterDataDays',clusterData);
        days=clusterData.max_nb_jours;
        console.log('days:',days);
        document.querySelector('.annotationChart').textContent=days+' jrs';
    }
    var echelle=100;
    DisplayChartDays(days,echelle);
}
 
let chartInstance = null;

function DisplayChartDays(days, echelle) {
    const canvas = document.getElementById('cpuUtilizationChart');
    
    // Set canvas dimensions
    canvas.style.width = '160px';
    canvas.style.height = '80px';
    
    // These ensure the drawing buffer matches the physical size.
    canvas.width = 160;
    canvas.height = 80;

    const ctx = canvas.getContext('2d');

    // Destroy the existing chart instance if it exists
    if (chartInstance) {
        chartInstance.destroy();
    }

    const COLORS = ['#54C5d0', '#e8a60f', '#e80f0f'];
    const percentage = (days / echelle) * 100;
    const index = (value) => {
        if (value < 33) return 0;
        else if (value < 66) return 1;
        return 2;
    };

    // Get the CSS variable color from the root (document body)
    const backgroundColor = getComputedStyle(document.body).getPropertyValue('--tree-view-bg').trim() || 'rgb(234, 234, 234)';

    const data = {
        datasets: [{
            data: [percentage, 100 - percentage],
            backgroundColor: [
                COLORS[index(percentage)],
                backgroundColor
            ],
            borderColor: '#a09a9a',
            borderWidth: 2
        }]
    };

    const config = {
        type: 'doughnut',
        data: data,
        options: {
            circumference: 180,
            rotation: -90,
            aspectRatio: 16 / 8, // Adjust to maintain shape
            plugins: {
                legend: {
                    display: false  // Hides the legend
                },
                tooltip: {
                    enabled: true,  // Enables tooltips
                    callbacks: {
                        label: function(tooltipItem) {
                            return tooltipItem.parsed + '%';  // Tooltip as percentage
                        }
                    }
                },
            },
            cutout: '80%',  // Doughnut thickness
            responsive: false,  // Disable responsiveness to maintain fixed size
            maintainAspectRatio: false  // Prevent distortion to maintain aspect ratio
        }
    };

    // Create a new chart instance
    chartInstance = new Chart(ctx, config);
}



