{% extends 'base.html.twig' %}

{% block body %}
	<style>

		.container-fluid,
		.main-content,
		.chat-area,
		.table-area {
			background-color: transparent;

		}
		.col {
			padding-left: 30px;
			padding-right: 30px; 
		}
	</style>
	<body data-theme="light">
		<div class="container-fluid d-flex" style="margin: 0; padding: 0; min-height: 100vh; ">
			<div class="sidebar">
				{% include 'objectif/LeftSideBar/content.html.twig' %}
			</div>
			<div id="leftSidebar" style="max-width: 240px;" class="left-block col-auto col-md-3 col-xl-2 px-sm-2 px-0">
				<div class="content">
					{% include 'objectif/LeftSideBar2/content.html.twig' %}

					<div style="bottom: 0px; position: absolute;" class="bottombar footer">
						{% include 'objectif/bottombar/content.html.twig' %}
					</div>
				</div>
			</div>
			<div class="main-content col ">
				<div class="right-block col-24">
					<div class="topbar topbar-right-blcok">
						{% include 'objectif/Navbar/content.html.twig' %}
					</div>
					<div class="chat-areablock" id="right-sidebar">
						<div class="chat-areablock" id="right-sidebar">
							<div class="table-area" id="dynamic-content">
								{% if showClusters %}
									{% include 'mainObjectif/clusterdash.html.twig' %}
								{% elseif distribution %}
									{% include 'distributions/distdash.html.twig' %}
								{% elseif showPrise %}
									{% include 'prise/prisedash.html.twig' %}
									
								{% else  %}
									{% include 'prise/prisedash.html.twig' %}
								{% endif %}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	<script>
		const checkbox = document.getElementById('chk');
function applyTheme(theme) {
if (theme === 'dark') {
document.body.setAttribute('data-theme', 'dark');
localStorage.setItem('theme', 'dark');
} else {
document.body.setAttribute('data-theme', 'light');
localStorage.setItem('theme', 'light');
}
}
checkbox.addEventListener('change', () => {
applyTheme(checkbox.checked ? 'dark' : 'light');
});

const currentTheme = localStorage.getItem('theme') || 'light';
checkbox.checked = currentTheme === 'dark';

applyTheme(currentTheme);

document.getElementById('load-cluster-dashboard').addEventListener('click', function (e) {
e.preventDefault();

fetch('{{ path('clusters_dashboard') }}').then(response => {
if (!response.ok) {
throw new Error('Network response was not ok');
}
return response.text();
}).then(html => {
document.getElementById('dynamic-content').innerHTML = html;
}).catch(error => {
console.error('There was a problem with the fetch operation:', error);
});
});
	</script>
{% endblock %}
