{"name": "@expo/image-utils", "dist-tags": {"alpha": "0.3.10-alpha.0", "sdk-50": "0.4.1", "sdk-51": "0.5.1", "latest": "0.5.1", "canary": "0.6.0-canary-20241008-90b13ad"}, "versions": {"0.1.1": {"name": "@expo/image-utils", "version": "0.1.1", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.9.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.9.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.2"}, "dist": {"shasum": "eb21933d6c611bc3b39584eab8f9f0fc0c18107c", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.1.1.tgz", "fileCount": 3, "integrity": "sha512-QmypUsEJlSpvautpHH0Gi9KGHtvCZg6Y6ivANLP0eqRTsLViEdR4GqEA7GMt4ESpmfe46ZXR9vJSdLdgkTa5gQ==", "signatures": [{"sig": "MEQCIHCtUvxkJvLYCi7GjGhcC0+IPaVjsbQE+pt3CSQcH8VVAiByNZUvgzpvgP2zDk+IbYU9aKQzMnEO6ymDsu4wnrIvSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEPohCRA9TVsSAnZWagAAbKQP/iRQPVCz2wSlElEdA31Y\nbfvJ+TmdPEo7NGSmbCuLKQCpi9sAHhICa3nZgk3U305zs3x4HqXSfYUnxGjh\noj4jxm0DJKjDVtkZucnpcAwE6Zd4bS7bAxk5IwdUgnDtH2qO6Yo+abIhXzMV\n2vlqxG0jmXscgbhuf98tn1naC9OGir0ArAdORbht4yoghaxd5Y17UBoI5kIl\n00C8EAf5X4ZYU0v8ncjhuMvqI2DVSD0E8Vq6XjSSUU1LkDZczuSE2EcxFeZk\nhlOAulfNGP8lVrTVBER+ITyCX/oSCq/pwVf434J69LZRagqEPzeLHiLTztKL\nupOr5MdlvdPsIQCp064QjKzzT14gvltEOn5BFMTw4QUatvkyJDUS0YZzv3Ms\nJ/xpkojWl17j/jG4vdjt59BgUSxNAdHu5Hh3S1PVBb6p3l0dwYGulxEfu3Zy\nZf7xXXTABcpa+BUguc/PZh9SGEamLiVqq7dZ/d1yWYHdfph27oujrSUd6qXa\ng6Xn1aJ9p6BshSguY96KLyvgpsoViupTNxO2Xibfy0PcQwOtnbwLp45pBrTf\n0FYjzQ32Wo8TXFlWZvMo0+p++bUIQWfSgLVnQYUt4hxOZJJg2OjmKrOCSPYz\nNa2QSgVzvM+CRy7c2a95Gzqml5dwInDjxlIVpVS2HSAa6irbAKOnPXigxlH2\np0oj\r\n=JU0T\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@expo/image-utils", "version": "0.1.2", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.9.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.9.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.2"}, "dist": {"shasum": "8f7488241c62ccfa3e874fd32e90df11501fdf36", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.1.2.tgz", "fileCount": 3, "integrity": "sha512-M83j+3gunpfHFAfD1PRbuRLY36K5fEF00fBIGU2o5y6PKAm+Locf+8MTWtBWXV9H11iqz6KXXx8kie7ADL7B4g==", "signatures": [{"sig": "MEQCIFN/zTDX0iUz5CA89XQs5wDSYiFdgN3LRAPm4OJ0RX+CAiBi5Z9DTbjwjWCtRNrDxZJiHiDqUuTrELndpRnn2xNL+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdETX1CRA9TVsSAnZWagAANJYP/29O1viQhbqH5dxKWaUe\niyTKo0ATJdE6d8WZkZ1nIngj9jARY97qGqakU4fCqJnxOpIwixs7KjLMGuf1\nKc7176YwNHiBJIu1TSgCpSpjHUQup+npEkl0qNDyBYGmHHNw/01FCw3DCl3e\nDM5IrBCY4ziCw81h2Ec6yLRybMBvo9Bp7alRoJxUNPc3m8K+0tDlCzVu60kh\nvZtnsia+covk3gV+yANLjqqMDupmnmzK/+AyZt9abjSWRiMdfqKfrBpHuarN\nOFZ/mLpiOaCwkFQ1aAV3MVdJNGdsTfyCDEz8zzdE3LhNhk0TEBu3s1117xUC\n6hyiMSgKlsmswuv86ZP2+vf/y29eNroapIR334RKcuCVIpHjQHFrQDQbl7FE\nsPFjlF0Xa4Z7Lg4mmSbLk0VL2hTPZjwDWm/ApkANeaQ+rdLu0u3i70Acv8Y3\n4joAZoAAye4niHyf3vN8oSl0df+NVF/Rb3Y2gwsIiZcQPlbuXiVfI8l/katR\n2rRmH6g+6pkR7BTzX0WddhvHCBQEsg9UBuLkj5qjSePddwI6L5JEOLMNEEF7\nTkIHSDcQAa8Mc20aWTpXlekYxBHhjVurfqwANUwXfcpjVfFLGUDu2YFEfeY4\nL6eu7HLm+rhn65mQqtS2lmSo+ltUawYljkh5pgAn8DR+z22bYmFKFkKruCJY\nUa2K\r\n=LL84\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-beta": {"name": "@expo/image-utils", "version": "0.1.3-beta", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.9.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.9.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.2"}, "dist": {"shasum": "9430cd8f8e3632a5dedcba1ae355faae2ba2629c", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.1.3-beta.tgz", "fileCount": 3, "integrity": "sha512-D/YWC9qLM2+hYLNgwDW5lTO3sUbjOWo1JH6kaOvqEQxVHgrUsrrfzsXEVHbmuV2kQjh1nhNfs7Kc/fm6Oi2BQA==", "signatures": [{"sig": "MEUCIQDVAHvjhZbbnDXQewCLJl6y29MKLMmv6p/gLMV6hpUX7gIgRGbqNBQOlupdCPA/AC+iH/uGQQiAW04CdJLqz7Ofd98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdETgbCRA9TVsSAnZWagAA04IQAIpUvyLED3GIBxMKr2n5\nVlwW3YDd/vHqEz9za7al3xYZoi/6WDyRnYkZ6Cez5ssK8KpiBCr6SpVXSeO0\n86msWGtYocknHCX603ZmN2sOcC766PWW3yoXc4ErKksMVMzysg6H/Me9i47y\nKeo8tl11aJssCjDOW0W0OFrjqChznR18M991G5n9cQ5ccfVPjb5VqnESAQEu\nIcnps9wvm5fMBJ3KSBvDnkwOAsJ+K1q7SoUHmUAVIpXQ9r6BiATru2sSWimp\n0wzRNN9san2ND+yVvzMqN0Fg1+YXfS/YO44NEUF5DvhgNcaSc5v78zaQ1rmw\nEww+Zw8yjU0YVjSuqxGcUPIgwPrEiMtdjkpEHbNNXJ+rAr8hAolXXCZHgQsv\nDPgfPxSG+FQNrQH1Jqho361wrYSPSo2Q6st3CAs/8HfBJPEoOrSt6/CfT4KN\nilwKuimJo035B+2TGhdi5si3uZJJexS2BKOEaPkQIrfJUm9aps4PADJxiw72\nqz/IxkFY0I+FTRa5jjXqJidJa7oQo6W8WiIxNRx8QHg2zyB9KZKHVGju5ZvU\n0mHVRwFkg0jEkb+L1oq+igitxYrZJk/R5KhsLzttY8M0/nd9ZgbzJ2WS+gZl\nGMOEkieQWD1SBvxu424AwpTpSGSozQSerr9wa8gLj560xaNfOGyQwHMYhsKS\n4IGC\r\n=XNGz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-beta.2": {"name": "@expo/image-utils", "version": "0.1.3-beta.2", "dependencies": {"semver": "6.1.1", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.9.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.2"}, "dist": {"shasum": "e8dbc77a16ad82830b0ebabee79ebd6b60787960", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.1.3-beta.2.tgz", "fileCount": 13, "integrity": "sha512-Wtbj0FlJf9z9hwy6lUlHz4Vocxu1RGAp6reQvh2+okFxA1ScanzjTOAJsbzgpyg82EStI/GC0hNbYGfunSSz+A==", "signatures": [{"sig": "MEQCIG5N+0fycyP10QAl0wwr8568TaP5NvOdD37gzD2UGFsxAiAC+unuOtdYSMphFbQVcyDhUFtnvy2vD+M2KtGMiCs9SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdETjrCRA9TVsSAnZWagAAVn4P/iyEW/JXoD4n1f8+r4+i\nhqffYsBDglLVMOZdcqPGtum9rw4zPPuwZgZzv7wU6IIJaKyup4LNQx8ejJ8v\nfUI0af/4wIOwlf7H5Sl217SbHi3rMPybfmqabdYUsbTPE01tWGWplKhk/M49\nT5X2Tv6dZsnCko6cDthtUZbTFkqxYAJWRiaGNSNfuP3TStJqd+OpJyX0ouRl\nflCvT5h9QJIA2EbXYg7SkYcCJ7ENqfnxrXz+ivVDxUYIaKSP0m/DA2bQfzo7\nRGDGkmfgPrRFP3x7xUUKJD8+1g5G6Oa14ZNPiB3TbQjtz5WlCPBB+/2f/5km\nhtPI6fTzSMQCRjynlrNc4qUYpcfPCG0k++VpF7Yy7IT/uPoBhYQi6RUdwrPc\nIG2S2aCv1EOujDLbPorQPOq+aa/ZOb/QcIIlK1ynr5D3/1TVoONQ921/S4d6\nw5xoFTQT8maQFqEfLvdOFeP/qWsWaAfjp9omKbpGSjgvG8mrv3JuwXPWEt0B\nTmskz/PLQ5Wo6sToiQbYzUFi9ZcZwNAOa1ZJlyODjt/Fg55OCZdZ9sKtzxEa\nLTS4inhS0XGfo8IMiWzxZIcvY3m04fB6Mju+O/L8zd71gYahnCY/uSZ9IeQv\nEGcQc4JbL8ELKAD49QjScEUzUAgX9+nYH9I1Stubl7iIoj573RFpIOqF43RD\n7EA7\r\n=1Qhe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "@expo/image-utils", "version": "0.2.0", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.2"}, "dist": {"shasum": "6a77b0acc5fc97f3264f9d5d656e9c6080bbf8be", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.0.tgz", "fileCount": 3, "integrity": "sha512-iZpkbaTwj1uiOC3siNUIx/l6UA+QiMhHS080//no3Na/Dqxc9MS0oK2t6oUcjYNs0bpBqI7qK5vCamBFZdJkGg==", "signatures": [{"sig": "MEUCIGUuf+ghbatjREJoFSuT3LdJ+AYrXBIpvZ89NVEVjfOhAiEAtUUN0Yi0FBGzx+a990gstKlO40Azxl29WZHhbKxC0kA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdFPu1CRA9TVsSAnZWagAAtacP/01blP81JG+dEU9aeITM\nLDshzoQ0H91+HmA+G8j4GX0HPxYCCE3ih8olgVH4QXxlOxpqxr9v3kXLqmfI\ntGjgI869RRURW82HHrNpVdCQTqXxVB+Xjfil9yeTE/YbhKjux+QM0PIFNjKg\nCKgK/9Iau5+k1gfhq+oaFJonvyXV5srTky3cZTOZ/p+qiWNN5qxHBgg+F1IL\nz/3wRLNU+I6SOiyulj/2OawLdIZjvKKumM/MPvwgoPGygkaxKDAv9ObM6/gn\nYzvlC0nXxrRTXGt8MBDd9oErCTdHXQeTnLlqX7XU72Ez8lBbc4t7Wx8P9Rye\njTJbBlrh1NTbjBt/hNQhtI7faNxv1F2HN/QXMk0QteTWbi+8mYahvn8Yrfp4\nZaAIoEbJd4+Fg/ws8nwZ7AdK/IRaXE1FLdlwQfptDsPkmkur8pSXcj+sx/xo\nXOC/MMY+Cz28R++795cwtI7cLKW18JnUjrzRZsnUu9nE0rC5LvBvncT86gqs\n2DNL4AMycyB546xxUUNLK2Io1n36dzuUqhvxwx9aAng+KqtNXGzc1BJfskRm\nr+pwVCHBWa5BDXNdp0yrzcKkow9Yi0atgbUk/89vlxNlihHstw69OaluhksD\nJL7mAlTu1jRxjSdirjrTJ6Fpvt0AvMSiAlFNL9nKsqxftSlSC30k3vchBzPx\nufP7\r\n=nYQJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.1": {"name": "@expo/image-utils", "version": "0.2.1", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.2"}, "dist": {"shasum": "b73dc445c256ee0af1bdbb38dd511b0cfee9876f", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.1.tgz", "fileCount": 9, "integrity": "sha512-cyWjMLCb4t7GXE9vDyw/wvO7buxndkGnUdD2ZdwTJW8Kh/3IVULbqHhcj10w9z/BXhXDxsSx/wAMgma6OGHrfg==", "signatures": [{"sig": "MEUCIHtMopACMRwuzj7VjATJpv3aMxsRtvUI7M13FKWSIRKDAiEAu52yiSpjgYpzILTafOkc7J+o9TdSerIpDjPIAbuouwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdFQFHCRA9TVsSAnZWagAAWeEQAJ96bXD9uYYkJcbmmVUq\nZtvQIvE4QHskFIbOVbPlAMTZ6PN18CAbNa+6ycOpZlRuex9Ou5WhswEBjzHI\n5jm75rx8od3m533f+ZDbY1gYY7CF7+tNPNLxjJfrG0JFVjnkKJOimmcYe6us\nMWYej3SFNeKAJCgPePrwyJkgUIMOiBhYMU+GTUimGJuYXoDWJaBBsbXdm7Wg\nn9846C2ReQmze8OjAsgIbFWgpIaxqBpfDAjnK+BCPoLKp4SWu50h+5FO1+n0\nlutI8mDj1AVjQ8xVKoVHAMyaFg1q66Ystp2C9d06ZcVtZJW3L4mm0JP5jbCm\nbModUSU9lxVbgZTRZm140qT6yXmlGX6mCn+0ieAfFTlovv6LXBCP4XAZS/3o\nxtcEdia5UqN33D8zMUu/HkVzfEukpqlAsOAaoitevY+j1s/X1CvYVKdQz7Bd\n1rAsCARJa4ms9Ma5JQ4dubnGFIyy0QYGLNNS7G/zhY3vNj55xGtW9mLye6XF\nh2ZS3Gmd3J7h1QjaXWW5j+ZyXQOIPquXFjjzVWB0+82S8bFkrr0mT0nmeERG\n/ilDGnNXLrwQa0+xmQApiWnCNp7vvG6QDQ5p5MF6cGw6JJHTlOTbUBIpHIAO\nzakAAjGvPeQLwMEsvgG/4TZn5CsaBajycAx6J3V1Aw3LiztBzQVB2DE/4drt\nm93i\r\n=jdCg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.2": {"name": "@expo/image-utils", "version": "0.2.2", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.3"}, "dist": {"shasum": "29f6c2fed7b03669189e7cdd22f9d3b8b37173fb", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.2.tgz", "fileCount": 9, "integrity": "sha512-GTQ37sfCmTt5dspdP0R+Pek45YG4inZA30joLtNgBjZLLXdQDHMJ9nF9Pvm8E0YgQW2HaV8vouxyp6C+Y1QTxg==", "signatures": [{"sig": "MEYCIQCGkPQIEb0Hc4f5qvTBcitt9igYJbSBqBd5cto/bpkRNwIhAJZTwYB+w5tnVEpo9gthLE+ssTMkyR7ZpJNneEzBjSZg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH2UUCRA9TVsSAnZWagAAg6AP/ixarNUxHNYN1lw/IyP4\nVWXXbqFn+PTFHDGlZ/4Vmqprda6CBcJl7piyBIPg5yONLbSUKfFhtQaIB/aV\noET+7MBSzblt9usZrvptAWYCHGWRIKJBhg+TxflIm0OBQ6rYxxYEw2Xw0jUL\nYPS9OCEPEz0llGLkm6cgY8SfTJXhVppNmEzXHLMdcAXCsSsl0C1xjmkHA/Ab\nUzqCwO/nVn8mruDNjFFWi/H6vXoMN6EjsD7kbDANyztNiiEhioKmEq34JMfl\nYOyu/vy4SVD6i4hq9eCMy83mGWoRyQILYjKBtxEeaEhRBZoNmMVBQU9xs+uZ\nfOMyFLsrtCTUuGKec0+SqFCE3DJ78+MG48NRgIAPoSGu3I/AqBgduIt3U4W3\nB4tDmYAEpPknclbxzv6mVs1hoE2DxFSqvIAYhEFuIeIHffiW1fc3uUXNfUIf\nTrvA7/ehFH8//ZZj4YFF1x2FcMIbdOs4tcVui0KSlej04arZ/50YgCdDQ9tc\nzW490x5R/2j3YHmW21S0HzQUM80c/DgAA8Vo6xLZ8Qid+q6qkG/hIb3TfyJK\nteGVq2Jn9isAoT9ttNxk1dapJ9Gl5emRdO6WcSu4LXGdIcYuokXUJD0FeKI5\n31qWTs4DtJ5wdyksLcCt+AhG1t3Hm4TB/9fyfvcWOOZ3ew8MR5y4jzUqvJmL\nV0UU\r\n=9V5f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.3": {"name": "@expo/image-utils", "version": "0.2.3", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.4"}, "dist": {"shasum": "d380ad8de20a885c82455379645eb99775ad9a24", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.3.tgz", "fileCount": 9, "integrity": "sha512-VxbK0RfO0asI7aSdxTL7TGRyI/pqolnOoCxXe3tk4S1x1YBGl8U03i6sNQfAFcYGMmzEGs50RcIttjMAb0OHvQ==", "signatures": [{"sig": "MEUCIHjvs2dt6o3WAJbnHr4o0HeaZJgOBxlJmnNfOPkud2SIAiEA3be+zjke2ameQyQ3cWFE/xdqTrKILRKTWuz9lNI02QY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMXLTCRA9TVsSAnZWagAA0WEP/3rvk4MtXbKtuf4vjTfE\nvOevN8V2ZN0ZQ54cyYPaU4beBKUDviI9XB8UQYGxCeV8DAL4k01awFfROlAj\neTEAczTGYCO40X2wJA9Rh7vKbaPB+0qAu90ICqDSi/ZPoVD3SwjhLJsdi338\nrfjo4ie4W6wWUNl8vgf1umBTs6t7hXY0BfsYFgILKESw7HRQVsneaE8Jvr6p\nSTCKhCnKytwoYt7C0dyHWH+7rDXXvFWrXwfrsRCqt0LcGU3mKoXdkklGwHd4\nuzJN+fmJ1o4N7jQVf7geX/x+6S5UVEhTgTJonyFwpHVr4nIUBQbkJVGGbjJM\nvvAqRIzEY6sK4cv3VLuRr2EN5ZhftuWqSlKBYLzm6+GLxSOpUwaDg86+wl2d\nqoY7nxgFBogJQymEaGb0zBurT7CM3D/flzR6iMirT32NbB8xtF1er273lLr1\nWU1Do32h96cTHtTJxtMuEBO7MQLPkBz38x0C9x8wsYSzYx4uEvdznHNdv7nC\nxXxf7e94EgL1tNtmisKve+JCevLjF2IoPoiUfqUZNqa2Af32ivH4kmMokiwH\n4KIVXkhhLlErIcNG5SjD8mjYXhUpzp3gbKnBe1hE6iOK85/liYVyj8Z3dHjj\nIu46ohlwpc9N8HaLDR+r8eFimH0SbA8hBlWyJhfqu+zrPGKKShlgUcjpM3kx\niEKI\r\n=19sK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.4": {"name": "@expo/image-utils", "version": "0.2.4", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.1.5"}, "dist": {"shasum": "20dbde2ea0bc1fba8edbbbebe56524db787ef7ea", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.4.tgz", "fileCount": 9, "integrity": "sha512-tj2f1tbZzZHSOz9uPABdR4nztVxHP3HLwHiHZZKLkrP51j3paQzNXpL3KJ2QV29SwepX1KCkEUO9qleu6zeblA==", "signatures": [{"sig": "MEUCIQCfVcq3Hk32vjz1E8etcpmgDstAHZZg3UU5WCnqMjcLRgIgJHtiWwTFAN4VIf0QpVorqE8NU8G7py+9BxvbalU73oI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMcGpCRA9TVsSAnZWagAAkiIP/20MiXHAIQmj4i9NYPJN\nPZ0hiG2wsOYDIFCeK6MtB+wnCaCjFz200lvi2MqM5iCkiQjX3/KR1NS1B6fB\nbBzWtpbFFm/R+PQ+iMMifLBz5m/oWI9TJkEd/uJWpb6GQlFUSLR4m/jYGvyg\nRLP9Aw2eYl3haCD9hdm4qNM+meQLDKnTg/PxdD7GTHKwmFbF/oWs+v5c8Ss9\n6kCuOxy+2DZIrmQP49HiLyv2jcUjwqMImkof82ol+xrkt5US70fwZfd46VuQ\nEGrKj9z0gFpPh9+tHxoNo7KDVXv3dUDMGhQ/cE0VvARTnvTraT8Gj7M1jR3F\nNvOteypS5qDTrfq8U1AybMSWJVfsoHrTIICZJNoNXHOdEMcUSdookJBNWABU\nXS5/8mtCCQJXHa4q9Q99DM42rheO5ebU/JdrG4SBDnUt6IW9SvbjKV4PlQ7j\nl6RwdOVBN2Dcc89mfa8zJQDds2JWe/yrXKCvYh/5jMCyCIWKim9jHSYgAav+\nH1xtOi+CDHtEXqNL53hvGRwTzeAjW3hCjbBrh1DbBKG86INsEoNKzC8+Sn62\nAkS8wCmXWw2nm90+KKgaiyoLkhZLWDDrO9/3s2Ce40GDyU5RfiOa+/thmL4n\nlhHI2rakFZkfOtrbFrpwVJfJ2g0+ZJeMm5IZnTCR/lZGcmES6PxrN6NyewTY\nzeQp\r\n=tfGv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.5": {"name": "@expo/image-utils", "version": "0.2.5", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.0"}, "dist": {"shasum": "dc7a5696fdbda5968cec6c8a7dcec1ac8bf69352", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.5.tgz", "fileCount": 9, "integrity": "sha512-gG/3gwwOH5ETtBbRBoKOZIiEUAxy0xpGb6cmodEZycvzmiPTB+SjDbS/LqCMMAdvjWbA+9HuJhPX30PDqnas6g==", "signatures": [{"sig": "MEUCIA72aUXh/t2WNKSQQKQWGtYOJ0r9yqGiLyPvHKK7NMt4AiEAvP9l5robhxlkxzs57fq6ppKNYnUF3Kb9YYfG6ULzmWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSaQHCRA9TVsSAnZWagAA4JQP/RPHwzpk6xxZQSzA6OZr\n1BygbLEuBhpdpYysAehnc91ZfGnMRYJYMuAI4jXnydF4/AxPTeI8d/T1xicA\n4QbyO5fmjz/btLj0MEP3ygCZiRG8fLuEDEUgDfG7Q2PeGt5KiDJCMLEAn2Fa\nxSgn9/Zzfn86yMyOgenp2SRUd7EOwxBnjbQ4e9Uk67AZEsrQSo4S8c9pHo2O\nkuIJLwmWLXdLiBdRQGGYDrky3mcMrkl3q4D3kSmNFSo7Sk0tULLtOMIJ8IeC\nqxx96nw2UX3Y2y5jFT40ml3NFX4D3Zauldu9uRweRbPhsRIj9vLyFGmdKYUU\nRBfu1D+t0lyLOd5WNLlwHlJUZxAwpbSPzKReO6MN4js0PIYLMbsf9IzFFlTb\njYCrNVZc1KLyVcMLZCBLnwm2Xw994ePLaUFeJfouC095RfG+6vUqIL7nALbc\nwQOxQ5SPru4VOqnpkyxlC3m+QD3MwyN/nJzL2WlEnyNfn3UGUFZmUg5o4hSr\nxz4d+Hu533/wY0NZA8g4LMVcWut6f2SPdiLGOOkXTpxoYuhHRsM2CSiXy/ZM\nQVx22UrHkKTXKQuK6k5g8hDUNbvAvvep+sakYNAz8Ql+Wnle/4jbtEuhpWfp\nm5yymrC4FHD+qGp4wBJCFzAAy2vNGJJNSEboZvnusCk4hClNKm0wjY/Y0SYt\nBp1G\r\n=jSCy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.6-alpha.0": {"name": "@expo/image-utils", "version": "0.2.6-alpha.0", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.1-alpha.0"}, "dist": {"shasum": "527c1da79815132c02018aa230608ee6252e794d", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.6-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-0FhSQkNWAZSiC2OeIl+y905Vgj6mz5ruBGxNjfH3x7RoSIfODuNCG4Hb6gwgEUMjGz6lZB4HNaCc6TJV9gSjBQ==", "signatures": [{"sig": "MEUCIH7LSWLeOXBeMq9l0k74AT7LcV5zwxyGDiJjY7ly1B8zAiEAxOp4D0VZ+IqNLPbQRDECNSAtjCZrDsw8oXNPFwxP/eM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgklcCRA9TVsSAnZWagAAp/YP+wU5OvyIHMS/ReRxS5Mi\nfzcxXCx/X+g8O9AFScMavEWFPP5Ti6ycPm3ACwP7o6xgyAVN2n4u/E5jQLPW\nzceHgvc12Nb5Uy1dmKqqicCIHM5TPJXDRHnIf13mjThq8tdduNpPnDCmWcdZ\nSgCFtpzBveN1eLYdgXRVBzWce1I7qXOGkpui7WQXe0TWesbOqUaWmrhRFzny\nF8Vu/KqxFS3NZDsarXhyD/KIZb0nHQnvLPYJRekDmgxYJkX/NomNzqrl8cwZ\nfuBOY40qNiO0hSV8eeD7rMvVz4E7JflSuwVzGBdffNg8eR+BpMZVYwHHtWhL\ncoviSUXdVhLTwbZp5ytzURmWTzEcAxjD6ZdGXLR0kNik5ciG8OPGq1FuskJ0\nyFs6RBrLDEonl8M+9wQCcc5dDvnPN785EKy4OtVAHfK0ewrwdW9Nqb6jOK6m\nxNFq0ZUh3Ctm05JSWsfCNa2mtUOWNDmdDfzrY2Gxr/oTsDUOLxC2/s6tVVp9\n4qqqcrcAeSO6p+p6b9OYGLJXuFDf+sVYJS0CScq453qWK9DE/my1fNj9ehId\n29pe2ktNji99diStOgOo2xuL/w7Lm18LBxw9rLtgUEPanyczUkEnATvxcXJZ\npzyLGp4YYFoPSbugz6bdiIskVrt+iknrF/jnXtwS4kYgU99+Elphv8Ksajvj\nUV46\r\n=d7AT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.6": {"name": "@expo/image-utils", "version": "0.2.6", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.1-alpha.0"}, "dist": {"shasum": "3f1c19b2f373402181be570ca6ff2a2b437f95ce", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.6.tgz", "fileCount": 9, "integrity": "sha512-wfbch5y3HvDkOvEpxzyTm8sgaO5+EBMiAcvTVFmiwg5/PTW+dllWeX/j/K7WYLHyCxdnp9B7NXtdyjhGsSAsGg==", "signatures": [{"sig": "MEQCIDiC9LwPcEX0KDcLov1zP1vIT694Ohdr6lZ4Bz4qM5xSAiAbfwkhDbsg7/yIFS4GFPvbNLiHa9NQdYRw0EBJ9dZfJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhTnvCRA9TVsSAnZWagAAISMP/0vlqv8TLwgNyLJB/ZF6\nanal7IMBshkppKruZcJbBwx//Axvd4HCif9obCm4T/ONXxJ359ttUhogsa9d\nTehvIcGIs/lpcU/UafjvWG9qXaExoFwbJhHewaOVrx1RvS2tWcQZ1AaMQysA\nIQ2Rkdp69Yk7xE6gUB3UbNnt9HhoXeMMdC/mVhgn5sQuCf4qTbhfDWIYAhsd\nXcjDYknYbJFi5EOLR9FHgkAovlhWnSBkkI+/I4/YNUOt3zEMwbiUw9e1fCMp\nHZE/L9lx+cgMNnOEQ/AuGVnkq0Bwv45iD+xxUwLfeJNx8aOz7h0GaSsQcQKL\numTqVUb+Nyf9ae+f1id+xSq8REo9q2EpUs7dqf8vxxvayub36kZMOBD7i9dG\npbGDCks4I8EciNNoI8OaEwxSbz553OZDtPPxHWefrLoiIujdPlj5tvbJpEXj\ncTkib1+oEWstjsZZF26yoeZ/LQXjogN7R6GWIezLNMvasobPy/PXlDDaSofb\nC5cZeKUQZF1pOZvHVwXZTHEV1TtKc1/GAwzs/DWxfQX5Nby2xXfdnw7sMmjM\n7zxGdVxNjOoUno8JAR1txzotQtFlxLTFYYqOhgBKIKNKkM29EPvYRq9mW9Pd\nQEMKIgvoqM2Tfw9lyWXMTy2KSgT6TVkYneA/IHwd+5ikZ0kBA1W/CLNlHBbQ\n4S5f\r\n=2X5i\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.7": {"name": "@expo/image-utils", "version": "0.2.7", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.1"}, "dist": {"shasum": "1669aad4e4d8e923d91ba612974cfc30db6c2661", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.7.tgz", "fileCount": 9, "integrity": "sha512-fideaPyR8w3XykLcck/2Tu7aWsAFmaAaqOjMAOCuuarUYKnFusDqxaicNWp5yLPufxG63p8d3TFKLxWpL7YD3A==", "signatures": [{"sig": "MEUCIQCsU/X5BYfk+43aeczJrHxw2e4dKW84OguN9nmgTT/IRAIgJUPJIvX8dnJE3AahOb+UBbVq1GkQP9FYpG+/hyVu11Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpgHTCRA9TVsSAnZWagAANpkP/1s9iQmiwa8ww/686+Tr\nlTJBdla/HSI3D/T6QvlZlxrsk74fsAYLoWWQCBttVs8BSvSzes1J6p9Badrz\n1hzPOEiJVQm1mIhAKcHV4Y95sBALjA1XsE8OJ2xLNBy+OhXbZ76HBWqHeJeF\nPZRmpYr3MoSpJ2HMV1Lsg28gw2YKQbTyErDtg2S/LNp7R5LwRgLm0aYhW0UN\ndBDHZN8h6ibFEQ7Fae18gs64VTzuVkg75cbVsUDJhmLfTcuk4NhGrZ1uJhER\nfHt1VtGoRGTvWeWmzzkVsIWhTJpHDC3iazzzVcpvFrDcwH5VmrX3/GMdEaU6\nylQ9f9iWTkz5VCnrfP/F2vPDNYF6kv+r0i3aG71F7/ZX9wCTZGAOxqtXvRG3\nWRBc2tn0mC4gyK9ZeDL3qIA5nGNaGJVH6Bgz6T0WyDYctB471TrBhf7zUngd\nHIyi4LgIUEukT3JY8S6/FYDE8jkRg2Xvbt2wJUz7AC0OlyZuVenDKITxowTG\nqRafUBguwvK222xrt+NvgNYiL1fUumJwq1uACn4ZBTsow4BNvfcYpr63LCmR\nQADiI12FCl3pINDYeC/n7Nk8XQ4ROW6BmpNIoWPWXFsDWL5Hk5zKh9/2IgMw\nLdEOQrVNJr1zuX6kZLMlC4noMtFzPFi+XCeEEYlpU5HH9VQItmAtpzzAlk9a\nfPpR\r\n=Nl4w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.8": {"name": "@expo/image-utils", "version": "0.2.8", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"typescript": "3.4.5", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.1"}, "dist": {"shasum": "26c452e70f6d796c2ac589efba9f05c3aaead795", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.8.tgz", "fileCount": 9, "integrity": "sha512-TH7n7IE42YbzJXOOS3KTnHTNtlrofNcg0Rn5Og0q0na6KxAXTZL4F3/y2VIHzlEdI27UUtAxoLpdgFrfMh3yow==", "signatures": [{"sig": "MEUCIFNVvn5bm3GLHMhkes4sdk+uxuJQyOfpf2blFmBlG9s3AiEAwVrArAGgohEEjpkK2BAGa8A+7QOv6MBhTE7WI6kFzRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzcNhCRA9TVsSAnZWagAAbfIQAJQOZaWV/qk7Eq88zf/r\n00LRojzmqxgRiGYW2+eL+B/3rV5ER038tYqH8psTrEsYJhUj6DEOqORN3GN9\nNjXFjWQ//IKBoY5PAK6cyEfcaLGDZLQb5nKZiHmbepl6JsLY6fIGjDrMtozQ\nkvTmGUqtylyGfFto448wOc9rngFPnRzWLu/+MZe/xHbXykmQl6uPvXjjHXW0\nNzhLnknmEby553SOl+3hqMwOhY9QQmFxV5NMj+Bp02wK9HVYPoKxQNGEmpo7\nThjsWrX5oSwV1U2l/vO+DXXg2v+uuWcA5eyc4rSKfbRMXtMwGqHIEQsKaM7w\nU7Zk1jL1m5+MoQ8rkKitR1LNZ+Ph1Y9costzMdQcK93LRUA+NZZi/gTU+hXT\nLLX+X7pM+fVclGh4efrY0FSE4maResZGCxvxkcf5y7JrXWaWqdH/YXE1zC3i\nWNl0Pi08BfgN3Kj4wxeSsW52fenNxcgmlDwMPrz4xsAmkYFWwqcV4OHcwatU\nUv4uQGXwtuJtzlW23hQnmhMb3vZIPBT8K+sJqwy6iqAVx2jVZgSm7EKXn/Ci\ncpoUaPD6PSGnzPafFS31knkNa+8lcx+DzqAhieV1ma15MEBqGTVD+BEzBUGE\nq0WPMYxJ+vKTmelRfhtG1m/DfwwUUCB/KbOUXXg/twqIh5/fI3wEGyVnGvg/\n1Ord\r\n=ZqbG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.9": {"name": "@expo/image-utils", "version": "0.2.9", "dependencies": {"semver": "6.1.1", "sharp-cli": "1.10.0", "@expo/spawn-async": "1.5.0"}, "optionalDependencies": {"sharp-cli": "1.10.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.2"}, "dist": {"shasum": "e06690e2e7c76ec77975a0d051d01d2c8332520f", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.9.tgz", "fileCount": 9, "integrity": "sha512-YVyaBXnumbhQpP0j3+snFLOJSCdDkbbJj+M7ZpX2WmMtos2Z9MELmjDJ8Kd1+lYcmbIg8I3kzF9rV9uvasbr5Q==", "signatures": [{"sig": "MEYCIQCTeGyT+pLj5UegFnRSIPJs8Gq0R9eCQS6lds9G/RLRZwIhALs5i9DcJlD93St1eApQOFSv+QZ4s5EHj2RzkZv255Ot", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6rO7CRA9TVsSAnZWagAAYwoP/Rgpt3eVjcaizeYLr4vL\nR4AIK/AEZ3BKyboRGytF3NEDtnM45VVPiommNs+ZAZrCOjXwZbAN0xl5Qh/O\nqtRk4y4PklfU7KL1/7A3z6n+XcfYmT/d+e2AVLYIVYk/jwLb3lr3mxJgxzIN\n1T4+J1EjWie0ThqKg4mMIfnDMs1+Sd/Tc8Sc8sEdkRJdagJVThyzSmVZ4wWO\nTXw/47yhQh/kzphAPXimiHIrjhU/BYFK3BmmqmnkEE6Hufg6UB+Nh4c21No7\nXFEXyTec0xAK6SKSqHD8JBfvTTfoJKAmWfflhXH+axH77uullYPTHDOumTyV\nrTi5hemKZZhTVqQjLT6vndv1gvtqxQO8o5EaRyBOkEzc6A28TFyq/fw3tdu/\nMZ4POI/lh8VnpVu7Kg1VOCMD5ffusgueopwTPmv/cOcVIgkh1cxUxgI8i63V\nVq9C6fbGHEX3LGBlAffBVWALGxdH7gi9iC9lq81aEMPlbpRpYDnAbx7eBXOz\n74Q8K0BMQJYd5UYW0lb3fb1N/HAG03B1GSrdGj6Nbz8TTTOGqOXGz4EEAuvx\nM5UXY1Q/Kbq0BnA3QkpsETisPC6tYSBcurUSZ4yNDLzDPOSG0yFFXzJ+n7Je\nmhA1oT6uuVmErCt4YtRgq5bgtJY3NHBySPrP1bEJlCNJ05Tjjt/lcZoVtNWl\n/TKM\r\n=uOGP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.10": {"name": "@expo/image-utils", "version": "0.2.10", "dependencies": {"semver": "6.1.1", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.3"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "2ad9ac3f26bbe9924bc5025804351fbfea5945ce", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.10.tgz", "fileCount": 9, "integrity": "sha512-XTd97+ovo7wHkRM8/k2OWx0/WDaTclVRXyUEeJhor2msaHe5xw3SzmbEepjrwVPWr9AVD/vy5F2wkCRQjyfS0w==", "signatures": [{"sig": "MEYCIQCMtuEJa28Cv1t/js4ahIbBf92Z2alhrx+Ti/nVtLcs/QIhAN8PG2Vud36gSCMDUKhgneaWESCtTKPVa0nQ210jk7rW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+W6iCRA9TVsSAnZWagAA9tMP/izUXVqrknwEwOELMB75\nAHxfK4H/sWunse/UBwgQBF7o/vduUco2M4SBEKBXo0/aQMHCNUnvPOajYuVw\nak96xZJohsLRVohqIzDGh6/1jk0pKh+4Je0s/5X6l+8a8bPWxnpCkJ9CKBMv\n/ApiBiUWdwItCSTOmpRy5JYZT2j8Wk8vUPaHXSrGVQAZztYl3DJ8jQ/ozI1+\nBWbtL6LZtcGeRLUnsH41IkCtKDamyIV04zmh+rsq7znifXECxtvVY36GPMNN\n936TC8AfgYjuh9wzAiWZfcwOtOverD6hsdJlwEODLfS+r1VQl6VwuU8/JStH\nYsUeh2zB4fptHCqNTTHxkiRXYdNaW7mee49GrBvsq22lfHx/SvbUOTj/hKwF\nxz1iUYn7IdjPc/+4qLV849n9nJYbhxrb7/3dmXV0mS3blnq02dsu4XWuMzJH\nw6ZA0tnriYRBZc6+laifpLpLs0AuJhKuX9pax0LDZLC69AKmaH2U0+QMtL81\nHXsjuUUtlw5wMVtZjfDvWu3tqhpaKHIr/7Jg7RR4oCkQXMQQWfUKyCt/va2f\nBJiZX8/dfdqvCpyyFIAFdxN8RACbJu5eFCiQWT5cpp8/kNfBIICXzjVdK5GM\nwHAf0QHeWftyvzGej/U8go/Ffp+f671k7ydtcJu2Q67uA1glFuJMal8o0rqu\nhpYH\r\n=xjQj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.11": {"name": "@expo/image-utils", "version": "0.2.11", "dependencies": {"semver": "6.1.1", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.4"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "3e60cd3a2cb89bb8e5621d0e78af3634abe40123", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.11.tgz", "fileCount": 9, "integrity": "sha512-<PERSON><PERSON>ao9MDEsu51vFKOSyH8QaQ3xCJRJ+E/3xxEHwejrwMV69E/xFj86d9EIcBoG0+TBW5YkX8tamB+g2qMLIubQ==", "signatures": [{"sig": "MEQCIHtF7swSVAr4U2Eq14+9ZAIVfTrYSgfLVw7Rfd4vvCU1AiBf7WHus3SFQoBRFqF1mvzBZc4SAY8E4XS6V8olMWZZhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFh3mCRA9TVsSAnZWagAAulIP/19LK77d7PJ9ea/67RW7\nOKtsI84KB2vR+pHaD/+fmjcyFYRLGKqQGWiYtSbMIzePFOnu6EjdpSCtpmwz\nw0JA4lKw24yqAFo4lD4JkOZpRmsHimvvh/1POwRMZ8j35OEWQ5M63dk04CGr\nLwB+TY4OW4oR56NIIFrBt0wpWIVwBKeFvyZkAZvjAK70nl9PjJTRWga5ia9u\nL0bfuH+x3+BGkip2xJ+HpwQ9W0NfUMmnfQsOR54t7bKmDfGzjxZP6nzvvgKF\n2k68z9HiTySrxOhbK7Q4+VbsKctbIeq9HLwrhCuq7g1jCsSquvEOMgNw/QBH\nqqhW8zNpuidImetqZHehLNDTQzff7szrruU9TA0Xcr2zaFhg7lbhKmMjDpHA\nd3j3eYRXHCHIb4hL6SoboAYcH1DAhX8kxembB6wHhpLJxQEm3mm65uehZnQF\nF5TFZba8dMBBOoOe3OfXgHXMqoqah7/Av05/J35/RzR4Eo2TM8RSiIbImIjt\n76NucDyWdCe7+DPwDDhR8oGnkFzJ3Ny/LAOFDWEqjQPf9MqpK6wjBu468rhk\nhffGlNw7FJQ87POOZghGORJLbUzcGEVOL6AA+MJAgR0R2y0NXP0nTlnTuaru\nzQCoHHSGxk1mOeVKvjp4spTUSj2QjZ8big2swwDQsxHuLopoaZ24GvL/ji28\nZgcJ\r\n=kSmo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.12": {"name": "@expo/image-utils", "version": "0.2.12", "dependencies": {"semver": "6.1.1", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "^0.2.5"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "e70d4b3bdb292a233f2f73a59a644b4cefea876d", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.12.tgz", "fileCount": 9, "integrity": "sha512-nK4k42V4Qsz5i6fZFg76EhZUap7U2ZUX0H2PvTKhNCVSOep2/ivI1SAQ2v+qdwKBFbfg/oEYXzsDdWXGnfzIWA==", "signatures": [{"sig": "MEQCIAFc8W1QE/mDuTc+Z9JOfHId1NO26cq7aZjLuEVSTWNsAiAIoAcwKNzPsmSuPuMB6WUA6eWQDAy6fwZHWWWnxDcp0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHcHwCRA9TVsSAnZWagAAkIMP/AmytUyVK4zhPHs5obsm\nLcRNDkUJzIdDCmXNPcpS8nk+ohk6HR8Xs5BaY+N2K5HMBiY7bwAD3JxMJirZ\n5+hwN75FSYuQ0u/b37JJN9Lf5rtbU90Z3kNfekWFUXSDdQbalrAybTVmCqIr\n37oqFUsZbhj3DSDj/4Ae+BXWFctw21l+tVgWmvPKv7HhxN5FEGvOmh7K1Gt9\nGf4/FHMHN0aaF7M0mfEIGt4qq2j3O17WJM73Y3qO3HsBJMENQAZYAUjRhQnp\nDnrbuIi+m3vgo6e6jHSgksoraQleFJNkrNcALDCv3fy74Vws4ufY45kqYcO1\nCq2abvUt0TKQ6+UiOBJkXtch5wlrxJOw9s+mL92Pv23gR8xNJ6OViiAvshCY\nBGIzDaZ6ki3ozr7/NfwZbtLstOLNqHlAL6k1Wtd1YlucWWI2EBecHYs5YrLE\nA8TG8KljxI0/q0YdmgXuKvSSTU7nuA2bA9u9tnmbtJ1DpYWHUO+N4eYwkv5k\nqMxrG78oGPPvsW248IcvSLxCcNSadUX+hD8LIFfGV0ahkJvKN2K63u5vGJWs\nQTUMZO+JBh1Qiz7G19AueBFkP3afliNqoxMBUTjpmZwvV4ZFG9qSB+d53eeN\n+WaNtHVTb9/CtgB8HrETW+UOH+GgO/8KzH+iZ6jOo0ZKy9Z5u6Rd5tCmSbUy\nNuRK\r\n=fONV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.13": {"name": "@expo/image-utils", "version": "0.2.13", "dependencies": {"semver": "6.1.1", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.6"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "ae71851943e44af2ede0c8e4d6d846b473c508ce", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.13.tgz", "fileCount": 9, "integrity": "sha512-sY1LcSeDOgByzHPyt/41yewMxJQVHQXTT4rq8Sbjl4boY7LZBdUZ15nt07ezIONPPw+p75mIMPs49SYtLG4ZQw==", "signatures": [{"sig": "MEQCIDTXGxdjlC90T1m5Y/cicnOsuh7XgQP3IVou98602scmAiAPMqi19cUdJjcvqF8/mPihsqLdvCSlv+AdMvz4O/iwhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYv7FCRA9TVsSAnZWagAApoEP/jTCuSpA1+EiQ1TqVdnW\nzmfY4b25kClgpjfpCHlUoRT7oKATKyul95E0cIyN+G6GTL+gj8XFV5vpYHhT\nzyCRoPGsvvKb+xLorYQ3s3mv+1/6eSi14jY2SjtVIZr6hxYsX4wAYhiWNErt\nRF4FefDZNsbKOE7w3wNzVkWYsMKYl/lo7Fxq4OE0bi06qwC2/QVOmk0dwH16\ncHz4oY1H9P1ae03nnma2e5mYt3hnHeWKnRHt1xfQzpzephStKrHsLIHDYJ49\nmn9mRrJrQorDXccIYdy5ze38u6y0T2h1NMsWwg4l+2H7IAaDL4gYAtXtbO2l\n6gL9As6m5qcsqKR8M3N8gTLGMBkvc1fptq/TNaS6RllekQpcWl5CvP3tll9o\nwfu2YwJEFwi7Ncx2xR9H2EDb3toNqySsMDCOLQwrRTQjIEBsSUzYSl1SYSV6\nSduYZWihr4clDoEmrGUIcY7VpRxjluaFF20u9gGnZPLyTlVG5Cz0mEjugk4F\nMTQCTCwnLT/F46fFSva9F8/vImkzQ0VMvx4OzmM3dqdleWUnqCJeI5DqfDxg\npZ89k+ai0ZhTzKSnd8YQbhh6fGokp5oElUIJAJj+Kqfx5eWvVdD4BP+pXnxX\nJbcRDcAm6f6DoC+kwTHI4yrBDcbBzAkb+GZ+0RZfBBtlP3Eh2AiZq4STGe4R\nVQtt\r\n=NdJw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.14": {"name": "@expo/image-utils", "version": "0.2.14", "dependencies": {"jimp": "^0.9.3", "semver": "6.1.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.7"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "5f949d4a3becf9053f91f7fc10a2ddb35d738b17", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.14.tgz", "fileCount": 18, "integrity": "sha512-5vwlKgO8hi+3wNiPaeVYa9z0Ndv1SpEbSg8RjYGUhh/KQaKUPwMyTeoeMerAvzUSNGgH2DwDE40Y9rBWmLO3qA==", "signatures": [{"sig": "MEQCIF/fKwZlxtndoXgQG5Nbz8uMBjIvFxHXUfPZPRMUGkWfAiAoWlG8gqBUCn0d5PpP8LeAze506XNgB9JHvNRkwkZ2Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedWkvCRA9TVsSAnZWagAA/0oP/i0urb18qGSc2SFSLT3D\nyoKPGV0TznRyToFQ8arP+iUJI/WWcMjUR5VTOKMC09Pz0hHcCiylWVhzjQw5\n1lYe/S/R5rogRKlb5dLCpfmxiuqvt1modd8dtl/1IB0fPxRDJjqj71Ap6mrR\n1mc8WWgAxvWupXNDonn0NJXovTnwETvEzAnJKMJgApZzDM/waCQvquf+VNOy\nRbCdiQNUnYPpVllw+Cu/zEIniiVt7afuZaIShkj0X962MaoRD6bXbeyTIvV+\nPow+iTXid7hQQ7SaTzUmsjKWxuQNhvl2KDIU3/rPXXWYXZgLUP+IFYJ5opll\n5vL0qkmN0fmOuS8OI13mF0lHsOFV8q9jgUnneQgXKmybaKUzOjdXSzO7sgVT\neHns51vMmas4uqw2OA3J5ftLr3weQuiZnyyQB2IIZhRfcLShhQfUcxnsOZWO\npbNsBeS3nRHDItCWlhdSNSmoHS7USSNoc5dwG5fgRwV6U+LTXtIOOO/o3fOh\ngt7F+Od2280rBsX6qh4wSj/wl95W/mMlIlacvfj1kHA4xzXu0LQvL3npuAlG\n2oSk1lZWuD9vhqhqHHc6kjdWKRTDzdjnLWnJwraGE3OsPwd5PxdUwN1L2upP\nUFIO0y2bPJcex6peXRsL1SnXTPOfOa+Or8SXKVHgE0zaPe6558Yni5riTC5Q\n3JPH\r\n=2m24\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.15": {"name": "@expo/image-utils", "version": "0.2.15", "dependencies": {"jimp": "^0.9.3", "semver": "6.1.1", "parse-png": "^2.1.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.7"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "c99ee26b1dd8323ff5c9d90b4c84dc6b6cc38fe3", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.15.tgz", "fileCount": 30, "integrity": "sha512-h9YFjtohUaN6F1E8GxdQoYSqhXyQfk7i+LxiXshaTBJejkGI+Nl8ZEVWwWWa9IfBlrY8vmPwyHOyFwCODDTcDw==", "signatures": [{"sig": "MEYCIQC0ryAnMg6H1QV11DkUnCI0KBKFYvgcrFV/r5w70btp8QIhANJny1zfhQg32ll7QJxvjuh4ZPrTOgS9cH+fCJDaEyeN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefRJeCRA9TVsSAnZWagAAPJIQAJcShlHEltErhP/eLn0I\n4fFYpM6bqhf4F47meNJh+BEChBxXRaubnZ6IaIGA1sHKgNK7n2wzeg/FyYzb\nF9vrP1dH6AzOLkR+5pvVyzFeVn84xBQY5DCapMV/zW3dbVaTAmB7zpL4tvOo\n0y24OdEnhXL6eT4mIb7T2VArQSxS6irNHct+fk/VAzhUZ5jjxsFOx8wmbl54\noLRTa+4UZ+xTBhgqExNnXhTv2+7ZNt3G3iynbI3qZEbqjbobegP1E2zvyX/f\n2cjb7UDKoUluUzo3lvmFPjHcr2+4Rgf0AsGirwYbc2rGu7lriwhpTt9zElFA\nnrgqnxtbHdU6hLA9FI3QH9rRUykuTit3P2m+5ULq1LH7wXSvV9RhmXd+F1V4\npxMRgAuQWuEOhdpzLTxs80kGugON2Sntsk5lPYR30OddXCjZreS0CCuQfiHg\nkP4bpkLWycKjER1Hu/5hPe8tRdjU6Ly6K1Jm/rKPDxbosgAW/BWCFpLqKw0B\nWRH4fzSL4xoiR+5Y+uHlu4lYdNq5TLq9v4ZxnwYnN+5JgNHv7bi+CX4NRTOX\notJ0ngsyOXD8OcAMx9u2RfnLHQ230Aq+qJQv7I6vCO2Vi4YPmsj/v70kQJ1g\nQ1NvdgtIxi6ulAx05dOI+oG0RkBmIBM+8XePSDfk196xxdRsVJ8o2hKkN3no\nu4+t\r\n=MrtC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.16": {"name": "@expo/image-utils", "version": "0.2.16", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "^0.5.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.7"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "72b95127a96e126e806dac108dda62223602666a", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.16.tgz", "fileCount": 30, "integrity": "sha512-I2WjOiR3wAo/UnG+25Ay2EpaKd/6WBg1fTfGL7trLQhD4bPy6B+Wh48s6vZCLhY7rcwQUoM+5gXC/O3/oFWUnw==", "signatures": [{"sig": "MEQCIEpJhCJFln2uUfbwxqP79QL3+LEyryoHmFePUswYeZQNAiBAg5ox+RzVwmNg5qk3T72d7J5w9YcM7qpf+eYzMEoD4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefXdaCRA9TVsSAnZWagAA/0wQAIYSuLk/DFwHaj2a9hny\nkbLs9rqT0a/nDC6tgjV4No2BOdPPOzWbHeLTRtM5KNuV5Ka3S3HgiNkBSikx\nIRUdvTZ00a7VXSNuoJ86jpNLnfFxuShkqavWkxJeE5jiHUm0Z+i8NBvLKMrz\n87B+a7gCKDJqj1IEd8Ag4Lh6CVlBLd34AUFKFPqvYxPqtbwQYBiMHk6faqtd\nZV8fwPoHx5ArD6O2nOUh74iXpNaA4bbZwA29c8FUD8UjZoLeUCnG9UfeM7V8\nF1Klsw1sn15ADIY33oVgS92Gd3fpHdYS9yePqdu8bptS9MAFfsobetGiM5rJ\n3hOUojRhBmlPl1I/60SFBSDcWPG6FHhDaorrnFDaZ3dVRsuYJjNrtcDM7GEF\nt0DOgg0jETXj/dIjDF89VY5OdCDB/4jUce2BJsqSjSt7hsUrTgDQ3TQxaTBj\nsbdFnsdjZqnnk8UiNstHJIqKrslfCpzq17g20m0fpQM8l2187Iwq14nYAxME\nGu8e9CVVfhAF7QtabWuGiCUDAoe8ERuvrePcoXYoImDkXfoTnbodpKXNguV8\nyz6T+I++hK+0gthDZlld1ZQVSs/jNTGA+/2/tlpPTBJT6Kt4wuqGLIbk/lx9\n1Lyl32BN9wzxSs9hv/w/y8cAp9aqqVB88x8jnVoBHB9K7rR4U4YkmLGxtgyx\nImEo\r\n=Xzr6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.17": {"name": "@expo/image-utils", "version": "0.2.17", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.7"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "447cf4595d39db8c39d6aeb3c43cd9ac9c7ac972", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.17.tgz", "fileCount": 30, "integrity": "sha512-Px5+Ka6U6BWm8/QMenJIGqXTIl2LhuAp3xFyWnkf3FQ2MXxklVHWIxzGb9H0M+kpQh1O89e+1p25v7V3++xllw==", "signatures": [{"sig": "MEQCIApgZPbbXi4wkt7u4D7K5c3be1QS5180iDsPQBikCcYfAiANQGKht6aSuaa/LfB0PD14JEi7pys9yrrEqpafz9lOVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg7DRCRA9TVsSAnZWagAAr3EP/2ynaJO04yq3/LCjLMw7\nqrZLuZH7E1Yu5KToliV+6/VZozjCz8s+bNVw/oCk4aLSGZsriTERvInh1rI8\nZ737NwW/0Ihqgnb+VSlf27odUDJi3L0+2VI8ATtYXM8V6OJLZI6DANSwni+k\nGW04BnFQO+xBWhYegh17VcRiRo5fD5BKO9zfRe/zmK0mGM2dKFo3aCjs3zRQ\nfuiyDJ6g/jdMl7H3/t9N+C4du0hIj23QKi7kOEqNkAAmJDYL0nh1OaE1sw86\nqthuON7md9hDlpnRIUU9h1Zw/fwM3HTMBeF5zUQ3WV8RJ89xsK5YpTLsZa/L\n92Yf5rPBPMaDYPMtih6blmqAQlZfU5erTXPwIKHloUl69ieYEfggOj+e8HsH\nXiW90LIiwT8LdPcel/te0QzUb96WUYjh+Yuk/QvIodRyPxYFQXvw1ptsc+C5\nk+fsoFvUTFBzFAXnUQzRBVIMSH9giWCiQ54IpnqbWEiUYcaEjwEe/m62rZ8h\nn1xOlvC7/L0ckc6p5ERRDqS7JVaHNcaVuREeNWJqd0i7Wr+RaX1G/je0Jotv\npyvIONWjGyO0tXxr5/BRd8JXkG2gwOY+1EFo31ihVaOuJ7nnwAgDYoFWs5Cv\nwmUCyR8O/DF/9LI2Ur/N4HeTosnMSmH6RRhn/1ULxB4iMfRGOhXJRiGcMRaz\nq3hg\r\n=WVV6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.18": {"name": "@expo/image-utils", "version": "0.2.18", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.8"}, "peerDependencies": {"sharp-cli": "^1.10.0"}, "dist": {"shasum": "b4a454e150295808337f7cfa02ff9aad2c2f1e7f", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.18.tgz", "fileCount": 30, "integrity": "sha512-CEV/kOSxpK6BdzZbty9HO00+L5qEs4u1bi8yZGrV8P25Nf30VvY5Ff56KMQFuAEVucfEMcSMMAz5DNR+ndu36w==", "signatures": [{"sig": "MEUCIQDXhsuSMPAVMFlEzM5ycRXrBa8f9zKdfu3WX9sm13vZfgIgMki6jQR74TuL6QvGBrktOxt+VpTRy6UjNxOszwsPTD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei331CRA9TVsSAnZWagAAb/QP/0VHIh+t2AtT/RF+Ke4Q\nNjtu2e23VNSvEUThn8LlKXrBNL6bGltn5PyAkesiqxhb9vKOg09NgIbP4IeB\nJ7TiRXAMeRQiZP/jhqrqh4Gf+C/BAcZdvDw/cdBSWpgNB7T6x9jpBCAUfZ5l\nalVUjQjGv8uJZoqRaLd2FqmPHLl52PpO1jbxy18WVc6ZbZyhOg9hMt0iMqwD\nWYA/ko2K6w/XTkjYnZmxH1Zv6OJyul7/R9wwcY4wixA0omUQzpoog6MTGSfD\nqJ0CDurusoRSI9/Rv1e6V8F2pxaWkH5CvL8Ky8EDQLNOPZt77muOi5v76Onk\nE2vj+3slfjyA2pGg/RPHIkqAOTHmamdpCCV9LFF0ncku70Yb3+FTUzELXEeT\nZ1ZzNb9Hq/GqjZwlMpSogEXVXNfQM2RUqzKGmW95HZ6Ig8x6kmmhHxT8Yaz8\nMi/GCjGBrtmqkeT0PD6XVN2P2KVz0LwtZ3ei4DbEaX/hMJB5/XDpoMsWUDyL\np2a4zao/XhmMsWjThB1L+3zbW1gfgAQlQUOghvXq0hi1zPzKPKoO0b8rmpZa\ncrWoYhrC1S+57A0CLGrcivheX0TRNulEmJ9Ye40C416M6dPGmvl4Txe+IZhm\ntJrK9Rwi9cghVieq66ez0AxNZXjeYp6oi1VKcJLU88wVLXeswctApE+9ZXHy\n2CKO\r\n=ZYm2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.19": {"name": "@expo/image-utils", "version": "0.2.19", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.8"}, "dist": {"shasum": "bf9c8d84e9f5e1d448167141a49cbd08147bce6d", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.19.tgz", "fileCount": 30, "integrity": "sha512-DuxycU6ipJOmgPWV9PtQawThExwQmiTFF9LTBZDSCKMhn90ixxVj6YvxiqKjNoAjsMdl8LL4L7G1iFrWYVB6jA==", "signatures": [{"sig": "MEYCIQCIT0DqZ5mVQDwWQFuLqrscQZZFeh8nRCyj50tIxf9N7AIhAIJSSzZbKlIu1IyB0JgHsqlaHsNvxG0F+gnF20Uj+X7q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJellm4CRA9TVsSAnZWagAA4qkP/3yluqZiejW3lbH6S+Qr\n6lNnFu1+GukTB2Et0sPMHDs1AJfdUk2Fj9W/BHANq+xyZO7hafGdpKRRw7b0\n6QQQ1t/M+FjFo3rFm6IDzzsp+xusDCgvz27i7rIit+m6s5s/iY9YsOhRgrz9\n9uzFiCax1isxrPWMeAKhLKJJEwYOxk2yKg2xhL2NNNATfJq8cMevvpBxQ4eX\n6w0UIY1tQ/8sZeXzATR2ydm/60/wP3UyfglSVwJ9cjIfv9k+gCWxAgOEWOO5\n1+6y9QOVrpHYaAKBdQgqECImHFzPtQCf/CAy+5xVCkdiRO6SIcLmQX5hBHiY\nYMrHwXHsSXA0GClMUg/nFLTQwI2S72hQ04vebg1EXHcSngGEyDw5vFLPPcbH\n9z56sHSEFUNWzUqatUbZNphAMmk9p8uqrcXZKut2k85K/NXruvRLHFnCIVns\nzW38OB/IwCBDUvGnYGNvgctrSg+8mjO5sP1jLq6XMPme/GQJuatLSXihW1I0\nOfKIxK2TqyynJEnIodvwEwNDRPuTdcaO4AJNYdTYj71BbdnYGnKvpqsKzqqU\nARTaEDIlkUWTeADnC+qEL4DZy7CKPgSixzEIA6IMbskufeFMu7I1MYRh5wlZ\nLycBPiniZtTSS8vKqMtjQnbrzXulZL5+7BW1DLGQa3A+pvxxt+uGlWi9VXta\nxF9h\r\n=Cxqe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.20": {"name": "@expo/image-utils", "version": "0.2.20", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.9"}, "dist": {"shasum": "a5c5fca56331958999db546fbcc702051025b301", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.20.tgz", "fileCount": 30, "integrity": "sha512-OmC2kixID/SgmI5XS4SDTkoj2CzXWbLU2YjH4sjypjGe8ssrrFoxcoqHfqlv1njULnKbZzk2KAAoVW63XomfjQ==", "signatures": [{"sig": "MEYCIQC4ZhGJyXdG17BMGTF4VHauQ70FLee4RVW0c6HEkPTbTwIhAI0+GoGkVAaO6ABV6e3kFcuShXyQuR+48yLRUrcZkcN0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoPhtCRA9TVsSAnZWagAA4j4P/32Ki+KAbdM2xXzfyCe7\n2Nec3Hm+7Fa1SUZTB21B10Fk++8lEEzrkSuNkdQ2HuNI3UlhXB4aOuVGnpiq\n9HsshSVvztjdUy1/zN/SYEKH3WJ9xwBB5UNAHOxE7VvgFFiZ9+AaragMN5NQ\nvyHvUw6Fan8miiMoXjpKMQ8qBuIR44fFvIExld4yJMbaMpMmMpYlO05JGBdp\npUaogea1ngZDv4u7qnYTfHqQNRLd5ze7egwM1urfq0nJ3HZnSiM1Oueh9zgF\nVOYQfWS4DzaGuQ7KX2y3umkDg/+u5BwXZD0s0mZm8Odr7V2xfbLauJ7NKLks\nXqmRVFrwD/kQOlJZGZ6BKRy0Yg5HuDdOGnmaUWgccDAQGlsL20rn9c4uiYTK\nAJYIQg/Y7cbW8ct5sfMVX+9uQCbui4ut3Yimo3h0svfWP59c4jEl1onjHC4+\nbJCib79wFyuOciol5yovK4/n7eJjPLO7IJjQHmYhqO9VTItbxLCAaVJ7w4Ij\nW7ong9Fad6lF2K+MUdGVJfdl8alB2gjzBGhPb0QXcym4hvHv/DZahHaMO1oq\n6AN97hqHkdpUZxgF7tGn653ZkxxAyiiZiTw2UpH9NuN1NRHcU4vYIt1cUUYz\nPgdDqfR7Kzf0SgIFpMU/hp/sF9KGp7V783VyYm/fmTAgT1qINmrln7WChUB2\nLAVS\r\n=O9ng\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.21": {"name": "@expo/image-utils", "version": "0.2.21", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.10"}, "dist": {"shasum": "8043cd85fc8955a334f914e7f752dd716f8addcb", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.21.tgz", "fileCount": 30, "integrity": "sha512-uWXWP1chqHX57FgxYUQiS8OWcLnxJZUxTzeMEQdwEOtw1XbkNL6Bp0c4ikh1Tpn4+h9uMXp/OPxJOJniKxzw3Q==", "signatures": [{"sig": "MEUCIQDA//mVzhxuMyBVJ6mcA4vL4S0lPpsiFIcljPmDZ0/4hQIgYdgaeffhwbNy0SDbX/iNUmVFInTKsRJSp/HZnlVvyQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepMdRCRA9TVsSAnZWagAA/5YQAJJ+yThxGoPoAdpLf2x/\ngY7kY7PsmC6q+x4b00cFH3Siqv2E3iZdiwQVqf/0VFM1LWsPlrXhDhzEV4VM\nsuVG1af9krlwh83TuAa3RrqbmuM1jYZMeWY5Ag6jh7VE/7839lRPZKwyE9R3\ncYSXmZnVHJqV7u0n8a3cHGiROA6Vn6FNxu9lJWC1hP1j7+8trdNPa83nRU1w\ncwqd4SNybjy/GTPSpeyaEL1UwYLKutKWzNNVnSW3/wl+4cGtKrIyOJzwJXf9\nKrUHrfhwbVR2iFcu7t8kbN8FI/WUpCkpr1SaDB5LsdQL1F8IaSaPyIWKU0aO\ns7xRP6DmXuMHaI0hpfzRLUiJZ0PusM3nw4nfv9XjpGSBIH50FqdupiDqwrao\npN4NnoK8gUugxK8xCCm0mcUU+nggXqReociPIElK777EpLf6EDBY08jDMiy9\nbQHzFHRQyP80Ac/P+GYXs9uyUHED2gmoDiJehHlvyERzCuq0EwImeljgk3I0\ni2tgthe5m83GDDanaUYayN7QU6cmV2fi4UAtyCUfPri4EHR5/SByDUPvvpmp\nvhwO3uBpoZfnz8ZP3V3r4yfPw+/q+mNenXYreSH2zYNvRKBv9dPud35URl+M\nz4t80PRBhw4IgHVtmdqsO6UT1iieJctvXpK97I9GQarfuJGEFZICPvjKI8R/\ny+u9\r\n=wlOR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.22": {"name": "@expo/image-utils", "version": "0.2.22", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.11"}, "dist": {"shasum": "343373890953b957fc6b1b45cf8c47793c41078a", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.22.tgz", "fileCount": 30, "integrity": "sha512-2Y04nxoZ27NsN37lhjufhEg9AwXN2FXQJAdZ2hQX9QF7LRMU+lr393KEwCVVi24z5GjFueIy/wcZbt6onLmsrA==", "signatures": [{"sig": "MEUCIC9fLZ4xIDQoqrvsxF4FAKC2VPNFRbOIWQ8PRYbjf2nuAiEAob2iQlqvLO/jC9XY9jLlFcQbMzlEECSTxhDTk/WoC/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqfDSCRA9TVsSAnZWagAAZe0P/36UdTobO3plO5JQ7r8q\nx2D6b9U/xQwZHcHciXltijUNFNZ4WzPqm02MiwSv9WOdiweCdttdncEcPjSL\nEP3Mn5JKgdJyoa/W0UH3Zgl3P2ohiG05dJuhBwKRBx/0PojQizM6P0pf11Wq\n3qFW15SKb7yrEfVDWsJ51OM6pktVE4TG9m1yMStudrB3L66YstbNyw/4y8nd\n+shcgZKFFWtNgORETDbYc1yawEuKuiiDSXd5Kk17B5pBwworPJi3PCxKXiiD\nRG7630T/w5KWNzeRVfcpmtavV1Yb8ZtqQ887yu4RM77TwD+R45v1xNm67VBH\n2t6qwKqNIU51nE9WGtl+B4w6LX/Ffl1+V3xnOen9lAU/MDU9X9QhGjZ5hYGj\nT421OUo8j7CZ1j5+h0qdFlZ+A4+8v94Rx+5fGzQfRmc15aQvifMAKUP6Spmd\ngPFDC8Hmp8b22Evk6xffYstR0XzqOgsaxvibWdlvhUhvJ/qw23vJbFB8lsp3\nv09faIbGQyZVC1awunBkT4BihYKcLBW/LutlBxVTqknl4SUL4VGduxOf3eWz\nnDX6ZJ8YLcigwDGzFJIQtF0o1YuYOSdIwyMUKmFbFriTO0odxdP2NFG2eT7S\nLMM13ddR8lG2YtmaUxQS3I8iOrawfTrAGumakabR9EIxG2q3Xb6/8gPFMLPA\n4j+C\r\n=SuR4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.23": {"name": "@expo/image-utils", "version": "0.2.23", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.12"}, "dist": {"shasum": "0a90ee0b8ba6014a589a12ec483de664fdd80516", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.23.tgz", "fileCount": 30, "integrity": "sha512-nFxmJR11XFbVJ6X18Rr55VLae9j/BPecw18Y5zCt83dpzLdIs8Jx/Y+vUBGTFRc2EVUGem5ckJNYY2znCBBM0g==", "signatures": [{"sig": "MEQCIDgxViN8C17bR/3goQbFA+AzAkGNq+rjSbXxbFb8bnGjAiA00CTLU/8ntjyTuTXrptW+r2ra6TFCrLU3elgDAKZvLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeubR+CRA9TVsSAnZWagAAFXQP/0JjKM4XF08gn4eM/Omb\nRQyyCivO3vSdm79xd1HdoFLSY6GwUQUBS8hQ0h692MTmaPT5nXTUrMLYhhua\nTaIfimY8MnZKHpH+93qTAnUnRthONcCEkFSlPf5lh94cE2X1cGX+LIHlEkXW\nkkSJQzQO0fkzv/obYauKOe53C5xDjVGJG5NkaCJMv4BMcHGfK5oHjpJ7E00/\nLuFTMLM6pfYj8SR+G/u9h2MiCX/YS3Stsn9XAv8390kL8owdT8cYmTEjdjpE\nzh7IFM3VyR7HbcN7+SbBWH66332TQNVFJgPlWkzyiuBXbESfyrK7S6gKF+dr\n2tCr/Ak0062VA8JMjLciprYaIOs0pTnXYdXrZWff+yxhXLPVEHm9eqdbVe9e\n75AB+siN2RRxWI2PLfKABXL7+38Qi1vxiXxOu+7v5lZbtqPsiZOuKLXceJJ/\nf/p8g2WTTKNXD/lsm/52SLtUGch4d5Egsp+tvUtvlP/uoGWxF2WyWbcZaAQK\nI8NJcrDryBcFpEzUvHt1Vpz3uHK+cwPSdryJ4zq57SDc7Vza4MN/WuaNZMfO\nYlp6eJ+PfQXPh2ST4+Mai2ctMNUynkB769QeksKOph2N1Wr7fPRnQ+2ChdS3\n2aELtv3gFZvl9CL7pV9khOPHvTMALqI0yQq9LojBRzdJtmF4wLSyxMp0YCim\nZ9Mh\r\n=yhtu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.24": {"name": "@expo/image-utils", "version": "0.2.24", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.13"}, "dist": {"shasum": "ef5a85cbe35c5d0e1d514486bfeea234465c2148", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.24.tgz", "fileCount": 30, "integrity": "sha512-iI2HE8UslZuMw0CTcHaPSbw9YZmLS3/ufdbVXexBoEZV+3WdZzOAIFKhU1vJSuV4Tr6MmCK1A6cy19a1wHPhbQ==", "signatures": [{"sig": "MEYCIQDxwx6uKLpCioXydJ1kvXxOV9WLb6Yg3PMPxmmI0OOUkAIhAPmrgGnrAidnQgIAEPJgD2aa7JRjDk/xu8Y9g2zdZezI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuy07CRA9TVsSAnZWagAAs3kP+QAL/IHJkg3M31cxGoPx\n4aIEHlwtXZktRizHWjBlBt2UqqYAfJzxitFsPLukHF13bqlFoSb9xO9wxRHw\nF9OPOTebLf9xLXJSY+ZJMVaW2f4dUBZUvYgy9trAYNpDDuF0B6vZfqoBoEAj\nEz3xkrpDEQCotqsWD5ktColFAjfpw7PzDGfydtiPEhtRg+9iYKq+fRkae+OA\n+JAIT1SkN1a8RSGeAn2GBoZ0dKw0v3oySFaUhC/yEEsFPfUpXIp+dB+4V06O\nGrs5o6B/wz6+tUNPNEfzXIjHyTq8qp6PnHhQz6YNoe8Nw6mQ6Zqfe9jgGI0Z\nKpggtDFjfa8WoDVrz0mZoVW6Hba7a+paI7vWQHBCEdgRQhKPEODSsbCOLr2J\nTrVNyC9kVAycOI+JXlV5fh0R014BndcRSBJlEaaQdgZmTdFXxu+xZqf5TkMF\nabQI3hqUxAxmRdGoAzgXraCxGYlzj6V4uzeFu8TZJ9MCK6ig+WHgDTh3QT/B\n18Xz6wbVd78j3KpV3yuL0MwIwtERrhNrZGYiLg4ItysZfuL6N8SBTr7/bymE\nlw05WeuvWbNm9zjXM9WkV2AzNkI/tTysiCLKwNxblOfkspvpVT9HxKbHkJvs\nx519ztOCbKCn9+v9NSQxQRBK6IOO1Gd1H2LLaIHO0a1+KH7504PBiveocldM\nz0vy\r\n=GUM3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.25": {"name": "@expo/image-utils", "version": "0.2.25", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.13"}, "dist": {"shasum": "1b986a1db23358232d7cff48d43f3b15f55905d4", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.25.tgz", "fileCount": 30, "integrity": "sha512-WxBxkKZE5pRgOf66Mt9PZGmxahVvE2acqnvpXZqp9PuJePhGuiHBbsQkmc42C+RzuHpzPhUmafCKPaNZLBGbzg==", "signatures": [{"sig": "MEQCIAzhP34Tw2bH0XoyVbV28LkO3n5e6VCH0CcDCXW1CxtwAiAIrKnR0ypOrSSgW1YDHU+OOLyqx4x0jXPTJzwjpkhZ7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevfJmCRA9TVsSAnZWagAAYhwP+wWhpHmjlGId7JrbsnwK\nAE/Cmfh4m7Vyx71d/wa5EsuRq9B13smClSQPLM4OE8AvFgqGZdsaTgECTRzs\nnRN+yec1x9aFrFw2AKdDM9tKxHdP+wSlkYvYm++OqNXSQE2EbyTgXUl1gHra\nIb3HP6E5WeGAIxh9IoinddafAHmZdHG57DJHe6ftUyRYWM1Qon2STqiNkgIn\n8DVwzmpjCcP2yROMQEBfm7SVW/Dt12OeCr5drmiT76U0j+M57kBXv+ve7oKN\noBQAO2bp2GyLnBkgdldRP3bciNlb7an7eLAQ9+xNcnjmg+t5pjSVHtGJ9x9W\nDRUm+bOcBQDkoJWWqNi9MBPjazp5mWSxz1HSHissHAUQzK5aveKnig0js0N3\nkoRTe+Bb3qCITzdDDirgRcJ5Vt6tDlkPPf9wg2YzTYXGVneFpafFWbwZZ6lZ\nFonyrqDNjOM3cTa8/V5wghSQ32aaYjjzyqo9/KcdLLu9I8R3Z6DjX41tPonR\nSaR7zhdm/biudC2Jt86wieNe6R4Qb0e4KOqDrl/QCGc8PZK1uT/5qtQOX8pv\nmKA7GLdhdDv+ab7xNHBXBcQSNv9CDRUY+h/x1HdGVWEqy+QytcsRUtk1fgBJ\n7pJK6BuYAf5PP/SjGbJx5xbzJL6mo99jHxnZ39lSGL5lTPCgu93o0NTLnSCC\nG6O1\r\n=cKKq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.26": {"name": "@expo/image-utils", "version": "0.2.26", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.14"}, "dist": {"shasum": "651720d157f53ce13c04efb3db6c582a71211093", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.26.tgz", "fileCount": 30, "integrity": "sha512-2vJ5MK7W7LBMTieNMgR4HKQPrIg5tyrsU0boeEwY0Q59e4jZyR2zQXzD0sA0bjLihQtlMZaNydx/PWrbFDd7gw==", "signatures": [{"sig": "MEYCIQDvsrLhHLk6nFdGAQf4IDDh5hA0N/Kus80hE3EX1YuXSwIhAIhp0TewHOJqIfV8aZQ7bXbjOrDuo6fqs+D8RwFOZ65H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevipCCRA9TVsSAnZWagAAGRcP/jfGJUuABlxB3hlBSUyY\nHzhrx7+nimwczH3O6eRZIVTW7eCDCQagjkbwSqtvyFjeGQTobkmmC09LFt9h\nM1X7c/s+Yawr9MsgJP24abbdlzi0XORxdwoXwgfgKlTeiwObsyvI2SZjJPoE\n/UKw5DI7fZQeIp8BBUI6cE1Wxp4lLuZWN+3VchSBdNiYAtURENjDEOLus8PT\nAc7nMHqnV9czE0U7Kh+QYPAJVB6LwVidoPCO2GVdczaBALQsmeroKnEnUEIJ\n7lyjxTHHmDs8FNnHGqzNE4FTOuuddvcHHbmmakV5ZGe/ikEfvAEAudtLiVnf\nu0QG64PfAuU9jWYX5dhZV/LoOPOtklV919IaRH6yIkS2yUF6rACTCGOvVOE0\nL+gU6DbHCMyhbH1K95E6zTLXyu8SBc/cfuGzwX/squWzME7sLxQeMGqJlgXg\noqeyFLL5v9EghHyu5BtaeLQiTrGqC2dLFI7lStbp1QSXerTIoTz9vVju0xM4\nfqoCzKqA46Nz1vAd8PK3RbmamJoZsuCSIIUguEeYCP49khzrND6VSTxR6mr8\niE1bMgFDWDu1dsoYQ/ao4wyxR4wzj2ky+tGoyjt2Mk6H7YAxsDyRhxanBVWn\n6zN5MtwFT82pSaHdH3sKjpvbLjqPVaJ6lhBzghZp1NMJD9DPE+YSm3KSAosz\nf6Vm\r\n=b93o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.27": {"name": "@expo/image-utils", "version": "0.2.27", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.15"}, "dist": {"shasum": "c6b9fd61b2e9496c4eda061399cc1b15e91daf7f", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.27.tgz", "fileCount": 30, "integrity": "sha512-MLAaleH+cwWVbRE65jWYwJlfcWwDztr+WO2PMPSbqln7RQMDP5w51E/yUokSYMvnLn5dnmsl0cBCGDzL9l7biA==", "signatures": [{"sig": "MEUCIC+LVleH7iHRjTMOuOtDYsQW253hApKgb8ksOG3fLmhHAiEA03UMVwXngl8NO3G/cYLu2Qd1jrsd0vF1eSlE083maN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevu31CRA9TVsSAnZWagAAR3sP/jRRfhGyt0PDHmZYSBKI\n5trCRqhgMrFbkoMqUhpt56/An0tqIGo2IqG7PGLs0N/EIGinSAa7m6KaSIqe\n6uGQVaymErbwSgSGjZ1xj1yPUQnBtiJsCGdaLSB6pYyfhQKSj+rPQAlxoOiY\nZDRdPqWWaJM+wAEll3aThdm4pFiLcAZi9sC0qxoIUtNpq0ypVuIPwe10kJlL\nT32s7wocCoy/nrfQkadHZKgKSmK/vey8LPj1ge9p7Ama9VnLUJ2lu2aszQZj\nf+cwDA3V2i93PZa6WK7Rk41FYA92p56R9MKVOvx3iu9vo0B1n8Z4GSCIuaQP\nspnOiiLtnkFziLm9VW3HkElNMwY9Gbj5fb99JOdKytVg93mNV8243fPHsUUT\nB9HANvyYAwujchT8chkr/h+HQt1k4sknhL92iDv1iAnYqmHDs1/BejWCDXwy\nZM8h0096kcr7U7zgkYBgFDI4Hzl8cqqttT9+O72dKcRHLBB1ewAxOoYmeldp\nSfzYTv9mpftYvaIwrJQzo9RajdX28lSndfdwa9kPdlVs+Hsn71pYyFXWnaGL\ng/Ge7UpZAtTzC+mhVeeXpqZp/cfuN6U7QYRpf3uSWzkksea1j8ax5TqfMIrg\n6zbMTixhCmGp5Te/1VVL4uGpbhz+Jsz1tTMifUsMC3NReCOJKhQZgl3uLmeN\nW7Vs\r\n=Y9ep\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.28": {"name": "@expo/image-utils", "version": "0.2.28", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "^9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@expo/babel-preset-cli": "0.2.16"}, "dist": {"shasum": "9a78489635cdc4a70e885bde255e37aa0d3b8459", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.28.tgz", "fileCount": 30, "integrity": "sha512-cP+NQiRGhuEBz8TS8p5Z6kO1H9GovtFaSyOVnYO6PnpPdKHnsZR+ncTjRa5Bl2Vdla+6tCPakpuzs+zxGyShOA==", "signatures": [{"sig": "MEUCIEvHdktKdpnMb6xQT9EktPHTkSOagNBX4JCOQu+zhfydAiEAyEdD3uGKx0/d3+zKwncxFziS8hG2EwoVjoiMw5SGa9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezQgkCRA9TVsSAnZWagAAVo8P/iCTEHKCkTH5w5f0S9x8\nu0i/Css5Lsp2/TPApxtI04sMbnSdlZbYpYc1gocWmJ8uizrgGKBS3JXERoGo\nXkySx0LJOUPqs2bSIBqW8VUIyLjGSrXmYLAvt4cLIQ+WctW98ZEMCK/e10yE\nkqpz16sqQsfz7TTFW+cJG7RUPpq+aftuzYcNIP45GHsFXjM0CyL5qRuvyGDt\ntsqJk65O6rABhxFPphK2YFdUB9zRBRaVI3xuaaj2HCYUNzjIlhxrypYPoZfB\n5yDnE1ULocQUnvlsW9IYsiLGimmP49hIM9+VyI0nMHZLHTJsXVxflyvB9Twy\nJGTMfLLLfpY8hmzlTEGntZdGlNBt/ujG3I9taQiEaBanaACpmX8k3Cw2eSZa\nua1yDjZpQR+9MAYt1cChBHP+Fr6tbWauAxGGHzeIM/w0RPSDbUyWQR1NAsaY\nMl5ynu/k8YK3hl9V2efy867oLPm9oBNc2RCxBmNRdFPxVjwxMis3qne6izaq\nL6t1SoomPYRyOxJRMQ7kH8HWQ6yqZLnZ1IkUTuSl8jajRxZo+kSjjm4/Rv/+\nW0d7uceaz6yeqLVw+5XMqX8qeaA+wa/8VU2PgPFkOQgjI2P2kbR8olkFJgl5\nJrUi7dnKnHHXO5Ga9sHR6la4Nd3OR9pPedf3ToYo2pokImzhfVZX+XH59B3U\nbOt5\r\n=YEFt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.29": {"name": "@expo/image-utils", "version": "0.2.29", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.16"}, "dist": {"shasum": "7794932e025050578dd76c3fd366916be3ae2f01", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.2.29.tgz", "fileCount": 30, "integrity": "sha512-MwfdAdSwyoMkGagchafPnesTbkMXvHEFhIfw1MbTqxkbv6jKAfFSFRRRenYDMc3ZeG5xWTFJVMsa1dT5vzbS/w==", "signatures": [{"sig": "MEYCIQCVO9wxw24gnMvCGqdLl03O3laRagn4o2X7eueINIKN9QIhANzHxrxyqiZEPxzRLk2iuQTl9+LA9KRfYreTcJcIVO9E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1jIvCRA9TVsSAnZWagAAr1wQAIpn7JDh8Dq8QKVxgs2G\n52Jz6xKazoAK9fI+K6WNXs2t8gvYC6EFUiLJ3MR4LsbmwmqGIbeOJlqNcqhV\n8pdT4Xtgk4T8ZvrvZiuV0n9ei+8miQyjY+miR2y02GDpjQgVhB0B2UqwsGVN\nDHB1N0L0n7JQdPYDWJ1ex9SSaKveJp/9XrXuxud5VP2FVPAzxt2jAByoK5Z0\nfSU76gorFngXrlFxrO4qWweh+duGvDaPrBk/Spf9XPLKe04DjCgvJKf3eLnk\novKRekWq0monkJbHHFVXrb8cm4t6S2nFT5cD2rZquf3uULXAUhqBl6ebLESs\newhorhw6qwMRlRS8z7ZPxIkE3rLmowqYlFVAqP5jzyzCGWvQQVlfiNwwAoMZ\n8rk5/UiVA0XnASiGNtJltO0wXNcIjLEpDvoacdlec2Oi/q5t4X/4m8D1qCww\ndXyMhHBIBkf+loTnfnTuDuWtSRoKSQYaf3mDfEP1sf22vA9CR4zqBYwCcqc6\ncIlNmxR4rFsmIv0IvtstavFbXgXytOTsTeyMRY4Mki6VJ6RtrsFNPR4OaHNe\nFVn0fm/3V2ZgG1+btsNkm6RGCTlDidbsDWhiMN/zTZbPtF/23rLilI6PTJnV\nRTgPBilE74MYKy74k/FmYZe4FiFgQ4uZcdqC/9FBB3sOpw67TixAUPnFZL11\nXMtl\r\n=hMNG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.0": {"name": "@expo/image-utils", "version": "0.3.0", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.16"}, "dist": {"shasum": "ef5994e0e15268bf47f1c89978fbd0cce89d1af5", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.0.tgz", "fileCount": 30, "integrity": "sha512-vQK9a3oOfX9JKwI09FVeZ3w7dN6DtJSUOZwEpONIkL3tGzJlOtD3bGht1PTxjhe6FZn9g6E7Z57vVfgCs7KWUg==", "signatures": [{"sig": "MEQCIEJHkrCJnDbHBIVX8aTJ6ENARBkREQlQrZ5JQ5pbVLlWAiBo3nb1yH1c9K4SqT5MTVCj0ua9z6ajZaUpjFt8So+PUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6yJqCRA9TVsSAnZWagAAK2oP/iy7/CsMAik27eZ3KVLy\nPiEsoNR3zkQ3Tm+DHmRtWCsnW1qAN5AOhW3XYPNO17zsMb0oYeaWGdu2x5+d\nGbHnVZjhXsQgjnoeWtgVXncvyoQkGhEpe8gmppwRE4AmeRyLBP5L18MYxRxW\nQ7lU74+kA0fRZgv0+4VOwyvzsuj7NU4+4/A0eTQHLymaOZeQuTQUgxqYDyTr\nxHyWSdHcg1/8xePUJELDT0g2Z/xH3+SgMu5SvEvf/kl8d+YCxqdzBWkHpZrz\nkXlQjToS2esqmTgOgXQZJYRwtV/+QKQoSm9Rm16o6J3ASfNW09NC24CuOEfO\nW+0TqNOYWg50iC2JnLUSNUkVD609YnvLgBgPoN55I0uW6rFOh4QmxZokNR7e\nO2rIWy9xdclpd5Zl9h1ljt9118vofOHxq5rpfEC1r3SrTW/QtM5HoKErJmAb\no1SnnLzBM7addbTE64uMWFtYAGyNbqp0y4nM+pqd5k/k6zHQqa2huGF6+Amz\nfhNdtLFFM2ydYNIaWKzNiKw7md/Xaz15Cxi+gwTdxiWKdhfMT5chngwF/wLm\niMpbmD9B+oXQDiwtZB8yigHmC/OmxLwlL7qYRllXKocbQsfo6k1wVKERD1QB\nNwV/pXJ+sJwQhvoXWGFOmj5k6Bk5LMCSXQITTeBXzN46uK8LkE4jFWn2K8Ie\n6Cmr\r\n=3C1/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.1": {"name": "@expo/image-utils", "version": "0.3.1", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.17"}, "dist": {"shasum": "e222c3bc81df0ac79649287647ab3dc0806c69f3", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.1.tgz", "fileCount": 30, "integrity": "sha512-xrdNnyczAiOBydMz+VMnkGij3Y/AE9jfLgkot65EqqDNbFLqPElTvJaZ6rY+LZOL2C1PtLaDzcp0yzse9K5DuQ==", "signatures": [{"sig": "MEQCIB/Fk9md3UgvrcKsvjZxPOmkMi0bnuodUNB+s5CU/O5/AiAuTFl4+BbB8XgSZ9eC8MDAwwDzaj3IAj+ZoEUnFPy5Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzEDCRA9TVsSAnZWagAA9y8P/ibQQrA/d0k1NpW67sKn\ntRVyG/NTg3FBGqUahZajvJo6LN0HPEPjk2T7uIvkur4BmY1IoCE+cEyWU/70\n6WQa+GSiK4TBl54YJcXrfMRFsbmvyyg6fHmJivUcmt3Fv0hmKQIrvdGMKef5\nIwJGu/FgA9OLNbfZUtDIAMpBcGleJwSZhjdi6xWxFzeRwLG9kOSj/OqI7+Kj\ndnwLQhqNGfUlFpSmdnyC7/h2Td7W1G52nnvsOpYTwjhgoXh4fQxxM8Rl/zEf\nD4BoWi0Z42NQvIRFTZmETeJKLVJddr4wDwY97e2AWzsAJRvX5fC03SaXh7QQ\nPdeUpcQ4FqRTxben/nQBHCp/qQbHoboN+rrtED9YEeK/3YvkTNpOlWXxk+ML\nFsf9QEUfLyoTTeTL/zGbwHujRA0LFTNeNn1nWl1L7admJXcjKxJHKs/KnMyC\naMulr/lhlKUU0mC94aYcpwAYnDrHjWQcYvK4UESVpKEmu7MG8AjrIG2M9gg8\nfhPaWmrDNK0/Gw0+A2OuDYAeooHO5s/4t4SoJSynPqu6hI+ki5vddankxAZR\nF4NkoIWGGP5h7g61fAkIwpE8UhqEMCIzuyqLf1rv4dvfAgRaXMypoHVSptRj\n21bX/b83uZmCKFsepuca1FcdmweZYTnJ/MdEve6edbcd4Oe8JZN+2XrjoWbV\no4PN\r\n=Y9/s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.2": {"name": "@expo/image-utils", "version": "0.3.2", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "tempy": "0.3.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.17"}, "dist": {"shasum": "2cca2b7448cbca6daf67067fea90e1116d52b91e", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.2.tgz", "fileCount": 30, "integrity": "sha512-wxLwq17aQJEfWjk1H28f89cLV+2pNOkxqw4u6fdFZTw/cq8mRjI7huylwOnR1W2xJoTCnH6k8pCjRQ1fdL106g==", "signatures": [{"sig": "MEYCIQDbtCykunLIkIuA7/dt5N+iC+YU+HtQa3YNocTf/KPNHgIhAMLt7yDiO40jGAutnpto2CFqQLplx5r5D2ROXE7AnZbw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRjX6CRA9TVsSAnZWagAALp0QAJPtfMTgnx9hxQL1KpEN\nOA0WI7gNoQP5Qt2r8ZL0tqdDoz1/C/4nnqpDuH7+Kic865tk1F5d3WXNyOdV\ndpy7oDpoo/ftZp/mQCFOjnYRa6n5euL9bpKernlJvVQR9l6m6N+fMZ/KmH9d\nk+NMcigRXAmUe3NqCHYWqtj4sQoOidED/GliIsxAv+Z7HfhoYvqRGt5X+/bE\n9whthrSdsIPgmiCCIrZl3dJi91SVAKNmZxDjvwLyLu0Fyw4y8OmxJDT8HbyH\nKdgFAnvqDwCdsm8oOZJaESWbS+HlOTn1XXXYasqxzSsYVzPjzvqQZjOg+ZcX\np9dX9i44pb7HKc0anyeU+ak3G1ShA+Jxi079OWDMNHh4Z75qhAmYZWIsM/Dd\n8Lss6JnX+xdE0HGh+vxQ+dMyl43pUsnFyfvysRbxJcY3+6DwZQ3s3txw8mq3\naVD1OHnA3C2PwVHThzhP031gIMISpgZVh1Tp62ED6JipgnNyZJHMn5QuXcv/\n00C657u60n68x8wP9WjsuzZ9cZTnG3adlOPn1Aj/qJKZFKaDeCPsGx1lLN8f\nd7wwqTnJrZjsSaYETbICzSrgXbVBaBf8GAyR8QPRxo0j9hIO4bZTlymXGPxR\ngd/Blpov8k5dEbRYKew8gHF6Q10bkqIDGYtsgsZOYz9o8ZoMCHq80UYF+jfC\nh1mX\r\n=lCro\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.3": {"name": "@expo/image-utils", "version": "0.3.3", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.17"}, "dist": {"shasum": "c7f3377d534d9aad470d16997e73d3cdedd0f30d", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.3.tgz", "fileCount": 30, "integrity": "sha512-XSPbUYMUmBVfEMoY+JVJVtiCi3/oQl3NMVpoDWPW/HVa2jqK4pz9xjks4F8QcMep1/DsisclLWDeuFOMzJqwLw==", "signatures": [{"sig": "MEUCIQDP1AV9yFWNVJfbSSVYFWkfJDeDXuS1M79lXlCVUSF0+QIgB6GBM7OypdMVl2FjDATD3/m2YeTrv9sYlwMekabo+EI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfR+ykCRA9TVsSAnZWagAAFFoQAJceVdmW5C+NtT8/u7NF\nGIMl5CGmd1+ln31o8dixxhpbUIA0D/+IHRzBh7beDW8gVA6rED3o/8WR8pBB\nPxJAmqZ02ffjNEYtnNW9QYLnCGzWjEDtjEwrLUqtTpMOzd/MIsK3HSmIPPIH\nFXpwvHBwfspxFnO9MTbuqBjtrdrxnlrZj2aK7d+uF6Q+S2InDJgQ6h8r47iB\nb6iMc+zE5BM+TSX3B27DOa9T6TU79dEHqk52tnDEKBf41/vhuKGx3TuaD3oo\n/rkpEnjvwhXyp+YvQFjZClOBsZ2ee8WcX2HzTBvK2zCwDZ21ipE2jX2M7Axo\nGqthDfrK8aUw67fMLA8i6EOC37OYS0rs7r27RM7Tk0XajCO8jO3fTua5i7s7\nM8SF+Nyt2oFa62vHYWGAQ8geqzOLo15jznOROYQ+Kn0ByQGo3jMfNor6lFVD\nyU4Qvk81fdh9f4DIbNpKZGYesbsTaY51vRhKGHUO0KfT/NOwcmkQXdlyqdSb\nF+kfe2RXzowFguN5DW/pqW2hTb8wr39aga/8jfOL9WX4Q9zjtNRnJO/QYogO\nGPIygVbN+olbtYALKGknFdvRBPY/0tDDC1Ne4v+0s9BxoqK0DXl5a9xmfg+x\nC1sBA/Hx5Zev/hRtUOzNgbEV42F/BD+QG/h3pf1qIVMDgbY4VnbXxfIYUZtf\nLxpe\r\n=UF6E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.4": {"name": "@expo/image-utils", "version": "0.3.4", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.17"}, "dist": {"shasum": "ff042ff33dfb107f4bfa3421c2e6c9ecdc46aa64", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.4.tgz", "fileCount": 30, "integrity": "sha512-ifW7ozVv74GTydPHuUapYU2Gb5oVDJLOwEY1hwcVQVkSs2dT2PBMGw8lFtX1XinFDSVQ5xA4yiyJDP+ycYaC4Q==", "signatures": [{"sig": "MEQCIDUAWxHxMIkLiOfuPTyKpNFJIj2qPMVLuosvDu7STU5CAiADNfzAjyPy7gISbf4rfb4dOx9NBGXaUhDbhZXSO94OcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTt3MCRA9TVsSAnZWagAAJrUQAJq9dR2+BXPIfhLDcSlI\nN5MC9cH7C6HYK6lwmshA14qe5yoAb6n/s6lnwOJqzH8da82oLnsHZt5E4Z1/\nztpD0BVPa/SYY7eHl8Mv931+NBxt7ctU+QjmqzMelkMHWW1/pjxNr5SKuS/i\nZig2h6ujALiRB5TfRQ0nZtLBAgVLP4xh70Cp1ThlY6h4x6ihUyXKh0QpC2KH\nWxbLIIUeQnW5a7+4KKr2BSpmVmHJ4etktzA7QV4FLuyFa+7/t5tP5rpqaDBj\nNjoDhBhEFqW8HHm9nDqutnwWlpq4e3FnKXNl39ao3+RrTHtfX2/AUidf2JHW\noEpANBZe0A6YgnAgnJnfSi6j79Ukx9EkvGnYbZs9lWULg5OFkQZRKJgHH8wH\nYHfedDNUhKxpH8CvtGmT0l/pAKry06WRcz9gMUWgJpnkck4RPhNMjC8+/6ox\n5QrMNbg+2sJ3kOShoHjTx0ROWiJ95MV0iKQBoBEXvnqT4DiCv0zfsx+BB4gA\n63M0Nllt8OwvCZUjKztOw8cBMgw56ZL+gxWaeYITOIpJc325HeqZbVpQnBAd\nSYNB8LYQLhzbrMm8time2aT1m+OcA6y2cCMnMrRc6fdT80suQO1tTjXM9GK/\na1i7qbHokxsRCvXAVUHdlLi8G5YX/Ejc/HKynt+nicvfpK+rC09HAvvvOxV4\n8TOO\r\n=HxVJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.5": {"name": "@expo/image-utils", "version": "0.3.5", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.17"}, "dist": {"shasum": "5ad6ff3c4bebfcbc36fa0999b604a114121598de", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.5.tgz", "fileCount": 30, "integrity": "sha512-3hoq5oWzkmDpBq1Bsft1/QH6YdHYixqWKDpvWPFI9/AVr/6ACoQlMhEfZuXyss6/Srgzlnm7DHEVtmiLofxmHg==", "signatures": [{"sig": "MEUCIQCrhIOEZrmP2Uw3IlbJnyPSORerthMLmutosVCoyYhYmgIgAebhwNNPBTxMC8b5Mx5yntQrNqkqbczTCXots9VpUg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV3d4CRA9TVsSAnZWagAAGvsP/RneJWJil/NIY/Tg6AIc\nOXE42vp6qP+gHd8scD4WQ4kw1FGLOmpCTPhboUUfjz/YlOycoCyk+a+4R/lp\n8zjlNrhBarMQjtaFu1AGVfiXsjLaY0zxmxp9D3vSiGrKkdow5zwehb8BpL7D\nCSEWMoTXceHurzxB0e9LPtJKEy8WpfyxRliHQBrGlYtnMw++azaSsHYTP7pa\nB7tr/TeHHINW3KlN9vG5Dt2VSkMEQxWzOF2L8EbPGfjq659V1dZce97v1OBR\nasRYwUx0EMurhxCZ//if/GA2zKtoxW+SfBs61MzFi17LdHBAUvFyg8/t9dpo\nl3i6fa+wsxyyfipPa7F8ttO18a83Y87qU8fTBbk3VGtrl5jcjveZLwlyrcSQ\ny29eUfKDT7M5lkDfXjtvQVhygjqJ3QscF6TFmEnEXYtkU2iBa8DVVOfsHsno\nzI9+nCEl9VEV3ase1IYZqV/a+B6SBSLQUJQ03imxjsy97qPomIY0SoXYMvna\nGaXXVdv+VOhdz2kArD1UdsbIz5/+blMHXvtTJ6hS8ipC7ZnMS082nqj1S6Fl\neIQFPlABwUkqPxJn4lBDiFej5Ddr+GZKxLzooD4FBDoTzXpkohHBZv4UgKQE\nWTHsIfqK66Jk+ZppdA+2Qv9XjeBcwYDEH/Wqy6cLi9ynMOs7RjpbtKnkSsL6\ns/mm\r\n=ABl5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.6": {"name": "@expo/image-utils", "version": "0.3.6", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.18"}, "dist": {"shasum": "b51d8c51b6fc467696fdad0ee86b3663af482df5", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.6.tgz", "fileCount": 30, "integrity": "sha512-jHbyqw3s/EG4XkVMI3EVt79sJJ6HiBajU5RwAscUVcNX9CpyJIhyvm05kNx0Dk+yANuDLkbyjvXJcZVLIYWZxg==", "signatures": [{"sig": "MEUCIFouY+DUiUOJe6dbcsP9mC0+0di2p7wgFozHTaovI9MUAiEAy+NakiHTN1IuKbqyOSnhkFHpk8pI3+4wnKnLjea5Buw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfY8j3CRA9TVsSAnZWagAAXjoP/0WvU1gVZqoBC7NFmYrO\no7xnXNSXsLiI29ZLMNwAP2KOYE23j3MHIZ1vRizM6DNRGwWIeTiMHZ9ykSqC\n1zk1mbDb6a/Kdc93xYHj7CkyajU+SOB138ZMg2EyD5lDdqNN5JgKc1/g5ZdK\n4tp7i04oP5C/Krv7iR++33HdCycLUSCwRyhkgVkBtcr7xCoupQ0P0z4L40iq\nygFRBGXRcUewnTqgGNSiHR/DYAgPrY+rBKQD/qQlGysO6++oRw7IuqguKhDY\n42swvJ+UNkuOGAuZzLUnRdCw/PXOEplSthNeSPKKjFRCG6pXnP/yBToUC0Wi\n8uxACncZZJ3vJAm5qpCcFGds8yhOcHrq0gJZG/12ILnzUpmfzM76+mKv6l+c\nHwTfBM+A3ZRw7QmM0GQpCFW5Zed7d6uWTOH7ntGXcmmITTT/gSxx4z6L75BV\n6Z6cMawa7J3I+Je1h5zwiWg581FmeaNYK8vAGOFf5BR54/ts0nrnUvDe4+cW\nv1SWq2sf7Ul3Tz3yt6m8OEQva5yE7054OOCJN6WGQC6WIRfKbuEoB3fgVU4N\nPP2io8mrb1xUI9eQWd6Rk4iX74WezltwUT7WIuHiixsfEaRWxbzXv6ZnBQMQ\nllTUXKy6qGEWTcXJIJrAG+aKqIwhoW3bptd68u54HFPnpM+GJ5JvT38HEsUt\nAGbS\r\n=qmEN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.7": {"name": "@expo/image-utils", "version": "0.3.7", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.18"}, "dist": {"shasum": "48436a5f509818dc43a30f73dbfd4baed88a5f23", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.7.tgz", "fileCount": 30, "integrity": "sha512-Vo1p5uv1JlRacgVIiVa+83oRoHfC7grSU8cypAtgvOYpbmdCWR8+3F4v+vaabHe6ktvIKRE78jh6vHMGwv2aOA==", "signatures": [{"sig": "MEYCIQDtkp9rlk9zECrwp4lVL9XZC0kLHS9rHeRIObheU3AlFgIhANnnPeQblADUoNsLtNIOfTuNM65assTAYMc6o6oK8Frt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfd27dCRA9TVsSAnZWagAA8wEP/RnvcCPyk9gJ+QkA0OhA\nK2o876PvSAT5GQFTLFNuTIFaa1Z6UVKT16LpVSFXLxsqiYUMdX7vytmBAizA\ne0mJNTjQFWD70UtDXSPXXpd56snGs4BCe6wFXazJ8FpoGeWzsp6WgTy91Nxx\nSmC1zdAq0s9nMRmLSDkpumnZF6NfDDncal7vQ+cYWSEOeNwDuVyqx2MQGgFX\n9I6cU9JrSv/4iQTdGw5CT0GLL1bk0FunoHuQIwK07wpCMY7BoLTkDNViQILE\nOP9eNeHpN8Lxi2fhvEj1znHXtq+Z6vHrh8lxH3phNtHEpN0MtaUwGZKz2stQ\nIlYfsIRVOL7Izrd3bQMfnCQ+ujgeD0KHajUmprGOf2dxjqcSiRJGQR2J56R5\nqpXffr8jX6iU9poDHbRjWBu+hRs/IAegjAmJGMAtpoqJ6ruwmQOxyDPzC/oX\nkbEP6aW1LwuRwqYwQIEu2OvuFIKdMrNdmdLPX1pNxperCdN18502YT9YQcWS\nCdpkvR5MLPVbYaQ089hWCLf3ndcShUCVYHpP6IWCSxzs12dnAT02ydK0pGK7\nUogedTBgOuVUlfz/NEhXfoLesRyUp0+ke5L3A8hk/ndENlTWp6qwBEXE+nJ+\nF/Ff3CnmIu+i8Md+oTGyl+7kAs/RXWo0sygAaE6H/DOAN5k31uhV2MH6UTei\niO0t\r\n=wnnU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.8": {"name": "@expo/image-utils", "version": "0.3.8", "dependencies": {"jimp": "^0.9.6", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.18"}, "dist": {"shasum": "0552ace9d77d0c63a846938410fca94b4c1808cb", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.8.tgz", "fileCount": 30, "integrity": "sha512-x0befqdid6D18W7QjFwyy8F8vI+kADYsYyAMHWqsehSSc9WTZrsunXujVe5mbQM6reMniBxacIEX01jLLojhIA==", "signatures": [{"sig": "MEUCIAYQDlioP8CLFBVC+bhGBGNl9y+Zp2tQheFvyREZG/YtAiEAqyADX+VKYG6/dpNJbDWC9OpnPXdD3ZhO4uomqW9FqSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwA9ECRA9TVsSAnZWagAAyIgP/RK3+C+8ZENgBToLCtqe\nNFwrEg/4mQOFgoi9gcOsNy0/N6tr82IFw77pQOImBcRygmQbl6SG4DFt7RWg\nRxcHJj0K76lRdc8cfAbVIskyqHXwolKeC9ycq08nQY35wUG0mEKad+Prb3hm\nTW04lnwO0uoTlR9kQ1fEUTXlppLBBRE6U2oqz5F2baZTOs4eK4RjZbM6f9TO\nacN7AxA8Q/dSawnRHX1OT5rzwKuIi7ldt9Khdt4En6nuvUuQhF+0OIu/esO6\nOHhCXTD1hXxyUndpnymoaE2vX2K0qxMl/SroZ0sS+rvIXTm7ytAB5I+4ZTaD\n1B4eBoKmTQgUCfc1qp7JAxAKmpGJrS85a2T0+5c6BEX45Egn3t8b7dLI6uhn\ns76VHtmO9NXg5Vc8CGrL8bcqKl+4x832EDixCPhxFzlPMU8BDOF6C/EAsO/O\nDutA1byX8ki48igITLhXnTyhO8Km/mI/S8+gsj3XGycwHqmIxVLXL/6vw79e\nvMfh+18OFwH0tLyeHVYnFx4UT31L7vSFhicjLkpFNWTXeBpDpQ+giDhwmwdf\nKwSofdDebj3wTCEUFZv3LTrvT42ooXFHWGg1zX0/bGZ7EUKIX9i0hZbKsE1N\nWZ0IymF9EPOnfi3NyC2ir7l055KsTzDffOULeWk6Jrz6fGRT9FsDXm9jlomL\ntPrI\r\n=hf+G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.9": {"name": "@expo/image-utils", "version": "0.3.9", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "6.1.1", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.18"}, "dist": {"shasum": "10dc8044943c1bd34fe9658f2835a079e4041a0b", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.9.tgz", "fileCount": 30, "integrity": "sha512-VarvpeNXtvPexmJSEllDF1RRHrjznsgf2Y0bZ2IehmOZwxdqz/YssGxY2ztMx5pn3utuhc9Enx140BHDBOp8UQ==", "signatures": [{"sig": "MEUCIFoMMXnuI9Xno6iUbB1JxEaRuj/+gBiS04LWW/Dx2RTWAiEAv5YA2c0z7N/8641t4fdyw0wfZp1Ph02Jj6kb4u9zYzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwX7mCRA9TVsSAnZWagAAK0EP/ijBT35k0+ib6EHjAGoK\n/DGQgyqAyL/SMAhF6BBaWQoxtcPd6sYqAWGrx0w4i27vC3t5oXcFlg+5S0ks\ndyKrsBm0vT7veiVqWFDY6CgYOHAoYiILh2oi4jcM95udtWRjl/Rwv9LD8QFN\nINW1A2IsSn8cM1a6tKpgO49jEBkBUQKuLSwD18xMvgLV6j7uRaAWsQxaBi3L\n/YrrAwteUfY+IP8xqNKliAVR4bZTvtJ8m0M4BBZQnUUF0OVE6BsCvaAg5PiR\n/5iv0SRpE0A7X4/DG0Gi7r/p69cnGqxlNIMyKnrFGmUT7G5YMD39qY4eSkhj\nt5z7NmdkwBy0EK18Ogr9Ffb1FVMWvbAVsPdnD8J86ku+fPVu/beMcaPiECJd\nhUgcRQVyR1dp+iN6MTxSkWTuQ55J9r7Rycl7hS4eLI16k5ut2/Twgzdr7dLb\nXVfGTDr8Kc8k1i/Hk96hZBDDFwymM20akvTd8hJM0qlmV2aBIe7T/CWJvnxb\naXokQwSZu7Ne6OTRfJE8nSyISDoRfOOUm9oiLurUIZT4y6RBydCVAXAHLRiZ\nbqwDQVBLpeDmAWYfMR2NcFylgVxIVEWUR4v3W7SkpdU1gXKPzC2W+OZNWCkj\nwtef4+nEk2pk5G5saL4fPDq/RyJAxGRVrzzZYTnnqWcTkl2eAj/dWk7ZQ31c\n/RU7\r\n=L/e5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.10-alpha.0": {"name": "@expo/image-utils", "version": "0.3.10-alpha.0", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.18"}, "dist": {"shasum": "eca7778f27099716c80fbe5f56a5cc51a56d52bf", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.10-alpha.0.tgz", "fileCount": 30, "integrity": "sha512-SQKKZhoVxl8Jp1XGRyWQSeSr80jsxHVEJ9z1pjk859djASLulTcAq0lA+JSOChyflM1isrk6AkNe04j3tvlm/Q==", "signatures": [{"sig": "MEYCIQDEDu5Jy5z1MBh1BUX+RxPM+Si/TIuG18PY9PNKtSdyQgIhAOXOFyBL8urcVQSVwh7R7Akham/RYpDqCgWV9xCOijU/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/x3VCRA9TVsSAnZWagAA204P/RKQR0ftvqMfCIbyOaMJ\nCk/OfVsiepKzzJeJ+BexxmOgCqve5qSlXNjCPuoFR9XSaDdmNmtgk2NYU+Hq\nxAzen9uJliE7nr+RMSc+ldAsEbowiVl6kG4sFYjb3RfnYnQNCcci2ufh3Z5w\nX2uCQpd4QGoXitBYyV2RzyR6oHVBue2V4Wpuaq+KbwdDxRJzmUh+4/uUcUUi\nQe9RyfQddQPxSm++4RDEjwFK9dqbukwYbJ6CWfuQJjlr3/QBGc6IrRtCg32t\n7f1KxqWTU0DNSnwbarhH0Z7WeY6/E15Oo1N8tgLwpJEQqhXjFCuLAmBFW4fs\nysqDpS7zGy+LAJAj+2xqj9Msu/v/BZ7dc1pyvYN9p/5omOU7V0fHImW4lWcm\n0ujF0hbqr+/xeV4u545e+QYYrZdgb7xfxHq4dJXA78jhHScgOarppPmVY3/E\ndTHUsMCg816y7gpj8NWL1nwjPTeT5L/fWrlE6Df9ZECGQ7g2Rw1srFA3vaHR\nE+5ClQ4i5SYmgX1uzx/hQxv5Zu1yiRcsfmsR8C5qY7fV+ESDEx+OXEh1NUzB\n9kMFuPoTLft145ngtI7Dpr/1c4r33n+vHxhcn4RCHcApzRm3X6GcG61L5O/K\njql4g8DjsPomMIIriYq8058hr1TF+c987AroTn2zPxTKuIgMLT9RsgCD1RS6\nfogE\r\n=36Ji\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.10": {"name": "@expo/image-utils", "version": "0.3.10", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "0.7.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.18"}, "dist": {"shasum": "bdb9d6a2a7280680dd56d7f9dd283863b58d1e1e", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.10.tgz", "fileCount": 30, "integrity": "sha512-EebukeUnzyk4ts1E1vMQSb0p8otYqWKsZNDZEoqHtERhxMSO7WhQLqa7/z2kB/YMHRJjrhaa3Aa2X5zjYot1kA==", "signatures": [{"sig": "MEUCIQDaz8Xna9Q1s7hLKdGLx/8BDjBkTNIvnCx4CNbG9u1EJwIgSZb8LEqVCMOJSiDNBeTveEjSNYcdgFxKJ89zjesdPYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAEi2CRA9TVsSAnZWagAAi10QAJg6djleeJfVdbrz/4PZ\ntumYb/PdMODiFHJ+z18hHpyGdd/Mr289iTaQS3jhPrK1hpC80Iq+LhOU1z99\nKkcc/qXLbcQRojJxY92NjaInJvj2SYigVkXr4mZ1N+PbPVmoy3e1tp2nj/Bx\ng7yEf8wnrUZeeDdzFz6bbJ4ZeLeME8RVvjq8oMsuslHW/cm3H0w+LujfNifd\ndvOVH8cXqOELZk3qG3JTcpdCMxaQ13B1tAmbpxP05SGzvwXhu4NgR22vVq7j\nJPefk79+nvOx7iirs3dH4jcQOFZHMmdwu1TXu6Y686lP8cezNtr28sLgfy5/\nEAsoSlnL/pMmb5bw5OzeHkXrtiHjp9CKOhp24z4zGlqIuocjCdWmSXxrohvn\n8wB0vrKKwwFwI/Zp/cN0DnPUFD6FVZlsPY55pArBvw/iTgGIgsQDyBgtVNsI\nGnk0Ei6tefDztxE1/lurdLJm1hKyzTKry7+p1WvAnDFS4Bcs8CcvYA7W7PpZ\nJd2rawMYa3hR2tW2Z8VlvlcBPYL3W4cGD4BCvyMCgnG6S4B5WJhfz3N8YNua\nkRhQvNQa7xLfBA8lfR0xKgy7Aa+Vc2C5YZCHTLM/nZjyrO8LwUH/OK8/SY7A\n+VgPYkZ4KcCXxjcuvsZJgACBJONigLgOWUb4bicto1pfmwbwlK+D5/b1xMgL\nZwHb\r\n=lt1l\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.11": {"name": "@expo/image-utils", "version": "0.3.11", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.18"}, "dist": {"shasum": "2a2ddf48af14eb0faf5b1b296b16eb6518b22d79", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.11.tgz", "fileCount": 30, "integrity": "sha512-JH5/FExvEsz9H9QsgoOEN3QYqH9V50dKjpkwTkytXLutD5Z587xSRka57pbX+d4nkHqiQBT0W8buUkbma78NYg==", "signatures": [{"sig": "MEUCICE+zRw73VpEy7wphOkXRcYIpiKpN1GkZTrwGdrdSF9NAiEA9OkwVdnjlsquw8UCPJMio7DRPAJlb7gk19kbCTJ6M3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU6YdCRA9TVsSAnZWagAA81YQAIu+JvV2U+uxndUQnT82\n3lL1oV8IBsdOhYQ19xG/86Cj4vpubgafgW2BWvn21td+/1s+2Vw6xSigM1k0\nWfkM0AJt8XL/enC2QJ9LLMDc730Kv7NsWGXjZrNVNb5oROxkX15Fco2qzH8g\nCfo7dkDRt3mAJqvlxxS4qtg9RutKqDN9DpfoPFQ9uQOICms6j6yNjpRuIpG5\nZ5EhiBrrf6CP7/vkvYuVmTM83QqiQ3UHRRw7+zCnxwABUIu7etyvRzvfRvKr\nrhlpHVNkalPs20+eu36NcUVnS9+ve9D3mu5/auVllQNWoV4RsowVcoo7Y1v6\n76NLwOrpHASsCbEvInjaEGSBPoPuR/iUk1vsCWIdee4cxG3zFYfDFc3vViss\nshF3IN5xYuno0UsUZy7PG8xXnotyGu7NFfudh8QWOev4c7FIy/SfCu5MmKoa\nQs5rwt6LbYQm28xnHT8NL5FSY2NJ23CCDHeY7K/YdiYa6S0KJI9B+oAYWbPk\n9ox27Md/RoJ1ZxW5dHRl9qP8yNrfZAyrhuLNj6DDarpQyCpORvuXWtr1wc/9\ncNCBkfag5k7TpXeHFrPtxTFNFMIJGoa9wasd+TgsnLaZAxmQzU4fOCXOg8OK\nHH+C3NnIldfonDuq4NrfNgr3oM2JTJNP2yHskbqMovTzpHozLD+juD1YrulJ\nDgFK\r\n=FgmB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.12": {"name": "@expo/image-utils", "version": "0.3.12", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.19"}, "dist": {"shasum": "23a7dbf2cfe40fa9dd0c355fe7b4ca15ef83808e", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.12.tgz", "fileCount": 30, "integrity": "sha512-QMMAjyCJUFXI8b6AOrVC40gubkt++qYfRJdKgbegf9JHQNul/O4Q1ENWTgid+PH0EUqlMLbv6kJ6ai0ZjO08kw==", "signatures": [{"sig": "MEUCIQDqwq5v7srs7zZID981yzqvamPsy00OCgb7kNMYiCZbwQIgHhsqKHQXFlPpBx32lZfuXXu90ugp5mXb1o6c6u0Y6fM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZjrCCRA9TVsSAnZWagAAbFMQAJpam3TI+zHoLM3X2pL/\nJfs/PhnvlqPpDxU/AA62nWKevoDhqabBiDBvrGE/OeJtBbNamJD78G0fCt8x\ngHqXuYqbg19tUMZXN2T6rGmYOZcWpakygDEuqBACnmdZ7jPXvzZCdyt3xFKa\nlnDS5/3SsRcOYgZtUFOBm7JhIM/z9v11mt1NkbDI5AyWnd6HXLCo4h6kIlfB\nseb3OZ5eYhd+oU5HLxf6aM10x9snZABxZiObaGZkytCZPkDyMsmrAHn3O82U\nHl/FOVOYse/HWIAuPFtEMEpNRDMD4AeVwOJyhKaj1Mp3RTDFlz2/IMuCs+LZ\nN/xcfnIcazcyK4BipI0jxhCssWOyT/mcZkbYU+P5r23aMivztsXv0aAasTKi\nVk8QjK2wMUCGZuWwdXmtzftI+lBLFG9SZefiLaS6Jx6U6tn5KKYcXXAKHvX0\npnANZ7nhQpVpyrIRckZZn6FtduhtjArAewWAaPFnyTdsRyEAFYNf2CWTrByO\ngPwOg+1th/IrBBgieYCK3huiIZdoAXY1kretQe9vosDBca2EYlrGRtfkQX3Y\nSO/whxcT1ksbEduRu65euqoGtuECVX8bJWcC5VoMbRsbkcaEPZFfkl0A4aRO\nP0VTEINZ3HhtyWDhVsHlMVKbc+YqCFMU7dm/59NWx6iDsD6vp2aL1GLdmGu2\nLNbn\r\n=Tv3W\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.13": {"name": "@expo/image-utils", "version": "0.3.13", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.20"}, "dist": {"shasum": "cba070a61ce89e6c113b8e6afd5655cb83da6377", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.13.tgz", "fileCount": 30, "integrity": "sha512-BpKoFVJBjG9H5AU040Skrm3R2uDGpWXBU/4TivB5H10cyJphoJKp3GNJVPHYLOVc70OtAxjWDIhLMW6xsWfrgw==", "signatures": [{"sig": "MEQCIDmZNuV3rm9tRALpmBs3eax/o8cP4C8mln1iA6hW3tyAAiAxVq9MV3E5JIaOglSM58HYSAiEl6mmmugRa8Z4gi7HFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96609, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfho2CRA9TVsSAnZWagAA9SgP+wZ8rDsttB8pOCdW677X\n2HLzbEd6G9UOu7EnXoXpkzEOcdfG5uGkvUM7fznosT7IwhzK0VVKW4XRgC5A\ndgfzFlHjSs4SMtI8Ep3XPyFCJBeSWVmTnR5iNKth2IB8MBWcvdB/HAGIBIoo\nyKSj1wCJvWnN8YGgtonojfxq5MRRWd69XPu0GzQDlzNtqooC5slqD5nETYNP\nCeYBa1NjoSQZf6iLC01pp2EnlMtf7OUcqE1IZizd9lvAQujvaIVYaeUcmvpo\nv1HLGrKn2UatQdlZsbznLf+VJkY/rxnVwJzff/uCqETk2ICspSNVDt/dviVe\neGSeVXmvUA7U8FNZ0rDBTlbHAxBo7Gx/gShbKc58XIQ3sLR+pj1HOcQ1Luq6\nDV0Mi+R5UuJHdgJKA7n54u/J3gu1xWFvIYhINuCoXpZdOSFkiEL89rgPkgIa\n5nTAEgZLj/apiIwAR/c6HFcXq2X7E7Fq+UryKO4uiOqWWs6fUIxSvcaqJhiV\nZIP7drug4M9mTNye22u45mXdG3qdf9gvP70C9/x7zpMuZaenbdcIMqSFQHG0\nbzr6LyYMfH+6UZoBjJFSVuqCOtPYwYNtxLqeD/BfGHaPbu36Qvx0nf7HvLn5\nk1SLThC44ntfhAaRf+VWRePUKn9pccUpDom8+Fez8OwVV9Z35ieFvJdbdUgH\nfwqj\r\n=ORe3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.14": {"name": "@expo/image-utils", "version": "0.3.14", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.20"}, "dist": {"shasum": "eea0d59c5845e8b19504011c20afd837c5d044c5", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.14.tgz", "fileCount": 30, "integrity": "sha512-n+JkLZ71CWuNKLVVsPTzMGRwmbeKiVQw/2b99Ro7znCKzJy3tyE5T2C6WBvYh/5h/hjg8TqEODjXXWucRIzMXA==", "signatures": [{"sig": "MEUCID+OP+ilmYNVoQOY4P19dKg+gOxPJKyNWEl+pM1Vu7FRAiEAylNsFxIChaNaCSVzeL6nw83BaeL9SVvUGldKtuxAYX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnuj+CRA9TVsSAnZWagAAWa4QAJkpWyNoZDGwgVCL3FZu\n/RqJvVmClFc3qEFEFiNbunMcMHAltkrmN8AXAEiPisKfCx2WqiPEO16YhTI6\nD/SGxb7fu9DKq9ecypWQm0zFWD75DPLyIB2RgKnvBu1a1FfF4jeMiiwyts+h\nXJTR24+mEl2Acq1+xRv4pAvXvbFKPDow+mQhrgxvoFsKveqWKo2vtfw3uhyR\nbK3YAnifq413yYLsq5cBcaEAjVH9AeHo2apfOwBacpW4e2LFuAmCrKy7Ilii\nhghZLlUlgCyo4sgw/7xv5XgvmP/0ssO3XastHvTqPE3nVebf2GfqeJ/USY6T\nNjmMz0d72eOKMuso0Iu9iRajTIoXgtUeb0ESBsOTdWXuyRkBD3GeGhZVvoha\nWqgj4cYWvrN4uFOkPOA1ICwwZhaXZ6IqeiFJu5HfAQL3ZJBpNMsiTegVYWPt\n0Wx0zUdsZsr/MKptAcWK9qrQUaVKlHDd4zh0RJExOV1rC+t9MS7UxYzd31t8\n6vUbH+E370dFt9mtQn8fPcmhhcZxR5r7nLd6rZEbz8HjcPSFZuJvoFyy6wXI\nU4aouTOVXhNzS8hJmYXYMh85+a8CvFHoUpN9xU1T4pZUGh2xgTG2S39093tW\nXictdRDdc4Ugk9djDyHI5DymHbUTuA8GTdCZEturLSIeBbEOUanaU3m4wh8z\niuoi\r\n=L+kt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.15": {"name": "@expo/image-utils", "version": "0.3.15", "dependencies": {"jimp": "0.12.1", "mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"rimraf": "^3.0.2", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@expo/babel-preset-cli": "0.2.21"}, "dist": {"shasum": "d78f0ed10f69618096319c0343a9bad747f911ad", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.15.tgz", "fileCount": 30, "integrity": "sha512-uLJMNZ6ux5nBpLxxP1tUtFjpPG6stv0IGEBug6nVZfwQgR/BAsEao+RqZFqrcKhiXw2rIwhKhkspfWU5G6e1dw==", "signatures": [{"sig": "MEQCIGVkapjC3pEDZGIFcB021jWoSP8gbZmwflNNQaLfzFTdAiAMZGodrZTwQr8z/0zPrdgwaER/sjWakn3bf4o2g385oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8cxPCRA9TVsSAnZWagAAEloP/34lTbrnK6GUwVuTTS9P\nbvuvKhJBLANa0rK2U8bE/Xa4Hz/gjUJnlIfW6aDv1FsCo0j08ytcdCMCbWyD\nAHOSoK2jBQb+QAzvjxprGnD5WtVFLnorTox0Gu6TG6BYemrmZKARF1zLF7bT\nva4Eh2NsFIzLe0UIwm0Ov6gcID6k0VypLQfQy8gUCpW7xwk9srDKggwyMTHT\njw6f6H394onewlafru0U7LfZqal6//cJKs/L0pW3oDCR67xeX2jNaWaYLjhG\nxiI8KztKM74w0M+TvJgDj5AthMjeHCEnrfDaHz71BpZCOwb1NZH2Rhe1hlfC\nY9w9fT9+FnZmRaqxQEEDTq0zxkgzUWrjbTwf63sTQ33ylYQu5ol7AUGMfmm5\nYchsb4uxMqNnbVEeL20q/s/p8lVn/iJqrOwWGJDvo1nKdSQQlUsz4ecVo9cT\nringy3Bf3SlqSDkwTDXP/wu70OLtLDq5mqVtflNQ33IsGEdu1sCJtA/7FKFm\nE5HVi5DfHGUDLdoyn8oPTAM8DF3LoqJ5C7TRLxYX8X/fVr6eSVu4k4rFBxGS\nUnaCr1tS8QiwGYSVN8cxSCSqHu6t9MSQ0rMNmyNOiXazl/q2bC5Q4FcSHhAN\n1TdqLnQ+BwnJD+8ualC4AspTDc9nxWx+niKIL6VT3LmGqFINmC4iR8tOZORC\np8Lz\r\n=6zai\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.16": {"name": "@expo/image-utils", "version": "0.3.16", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "df4b38b728d29ae8bed686866dc264a2d64577e6", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.16.tgz", "fileCount": 30, "integrity": "sha512-ZggQK5w7awqYdA/TE0DT02nYxWirQm2r7NNy043zVtzBCtjhLpFpluk1v9W0pH4+nT1ChGk1c67j0mYRKcBkjg==", "signatures": [{"sig": "MEUCIBXDIhTu81QCmNX8FEMkm9WzQ67GtSlZcjx+zyLxNmWAAiEA+ipYqN/NZWrlNM3cpYwUS3vMHwLjDSY+GMS7r4tRABM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAcLmCRA9TVsSAnZWagAAX40P/39BzbnYn1JGjQAYj8Zu\n5kvWVh3NhpUUDeVICMbdRyI5QT4NyC9JykkBiloHhV2/eFsjpGDzognctSi6\n3E55VFfAxZvh5ZjJuYbm3BYiMrGTDLMxhDQ9dr+fDfpyHFKpFIPERstsNwNf\nplW4jrxFW4EuiHYkd0QzHo++LKwxEcWzf1qo1lAGBivqkwM921OxKD1A8kIM\nFKvMtKbV4IdFM/d7f1L5R573soIU+kDT8Xx99lzxyU4tmco6PVMPFO0MxvZW\nVN7Vq+V09iuXtIDG/w/iXGUk9Rg5mkvcpyGSk8kpJdgGdW9QfS0PQkxTBfhl\npEU6ogQcCxKSxDYpqh9AzUtiIeD4Aax8dlrCVgj3Fp8hwBnF7f9BWNB4tTEl\nnzUCLhp4ddLadRbBzhxtboxBkfOTbUnaabZI6zubt6JbtxZhf/KEY0uc9eRf\nVHJwnyQLgjzTD1jzpkkuCkdB5ixMuRHd94NeDuUYKW3C82woZiyEauP1zQF5\nEewJbEsy7gflLOgflOJVPJU3o+JHHbfsPWmH9MGQjnJJEruCpbU7NiOrwGmu\nra4H/HCaeX6LBz+8jr4QQdvr7y62Z0tC9o6N/uJw1fLXzyKFo+8tLO8EQj8M\nxvq27JGEhk1u2uWtm1OmAChshJuv717aMs6Sp0aCToQjJlKezHwsGRy7MDzb\n7uBj\r\n=Rsjp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.17": {"name": "@expo/image-utils", "version": "0.3.17", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "75e2606749ffa1284de570245f668503e4a06c3f", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.17.tgz", "fileCount": 30, "integrity": "sha512-zaOj24JK5F+pPGpULP8D9aAbApcR7ixV2GjT43YzZqjHuyxQ5knI+EsbmZ1TNnA1vNJJKMYrigB5+chcUGwkpw==", "signatures": [{"sig": "MEQCIG2xqk0/t/wIDhFdccE38bmb6ZM5v4mIyEOaSaWZUsUwAiAS0FeWZCGP5iK/oNJGJhMkKteJsJqw8IfNWgFyTT5nVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102638}}, "0.3.18": {"name": "@expo/image-utils", "version": "0.3.18", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "36a41ab2925974ac5707e02cb0d3694349140497", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.18.tgz", "fileCount": 30, "integrity": "sha512-77/ub2aGuf7SYfaFhvCHE54Hs/jRuU5j+pemS5seLfVHNwHbJSse91TMhsTLLNz3GwwqTxFVe3KMycSccJ73nA==", "signatures": [{"sig": "MEUCIQDyUpzhFHTmX2e2Zpm1CgJo4HAFfr1yOJmfIGXFUlRHiQIgPmk6vX962fL25KY4I9yPXw2+8rrZFXQbLfkyJu7IPX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnsJfCRA9TVsSAnZWagAAAcEP/R1wNn6PuYkm/Q//QpvV\n9vsQW5BFvJtLH/f7tcMiQLxYwIAgZflffdJAFer+kQJvVWU2THyKbQBykznu\nx8chkXOZHjC2i6TOpoSXGp5OhktZSR3XwefacTqQErcNTGhZ/5gwoBvqPufj\nv/f8JYHQLW4a3h8uxgOuUS81Mv6nMs728LoNyq9ACP6L9t/Oavy7OPZpjDNS\nmI1rgpodf4P0EsmWlYR9Bc62reD0BrGRBdyW00LATjf4VgwQdZXJe+l+qTUf\ni0ViZ07K4AkS+0QFDMLZpKlJ+XJ8dtdza29OzPuLNyvHB1BCHbulOlixTW6s\nIUe2z6b2zYWZUYWEB0zFGLKKyXlar7FYqW4ikueAOAp+9+n7T4za6kim1SEs\nz1efuz5f0RTjVCJOJcn5YpXs0lpswGEIeTmZUii2cyO4Z3gPTLWilEw2Z+T0\nJilKahduenhsw6yEeyqvot4rK49V9PhuQs0uuVhVNQX0mSqxZ3lK76jc0Cn3\nFgycYTZok50c4jCwhYrKz3gtTFvCS+qHsfqhbmyPg/HiMhd2LFJTpk/jh3KV\nmRuI0jsHBglmy/B4xA17nS8gOU8mhYfkAu7vB50UmD+4xB11ID6p6Yupwjb6\n4PqiVqs12iYfEeBsagqe7XjV2ZX8+S4gWVrhIw0uXEfDI5aOCjE9XeJnwLjN\nVgly\r\n=wD00\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.19": {"name": "@expo/image-utils", "version": "0.3.19", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "a8860314635988bc827d3ea6df712b073ad53ad8", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.19.tgz", "fileCount": 30, "integrity": "sha512-mBuWWltyQpl4WF0bIBitfJXOsjB2MOapP+SR3ZnOZ8hOf/a0lzpju94kplK+wa/f80S9owkVu7NZ3wwu+UxljA==", "signatures": [{"sig": "MEQCIFFXPIlxuRI1EW9VkpwclIH5iadUDjxh6TAP7uW5EAzGAiB4MYMFNtvvWX2Mxm5pUTKyPvRNqY6B8RqIyfBvUCsMJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVIw5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8cw//SORqP8rcfUkKMoHW4h53unIQeXhHyVR8JzxgNXl7+vkmrUlm\r\ntvTR9xQP/IGGYZnwr38FmIJTOgWyB1hi4UCCTmT8AmAdkbd8dudcrIrYrss+\r\n7JVCw/G9cQR2SUm47O3Lr78Ijmb8FLfYJPS6pOMFgxZOvBn0zzVVIUsraY2F\r\nYRgRf5M2ASMZzLxPpQAPTEe2/HMHO5pllS9uKGzzWnZBfITrwE87b3z9V9sV\r\ni/C23NmcUoL9VFMeEMC976MPyA++JtLzpQMJ9+yX+aPDq2Y3x5AQpbvtln2g\r\nxXXMUFtoVK1FU6gpH3yDNhae11644e1xWREJTny96HTQhcj+my93yvZeqgBA\r\nFDNYEhHVWALWP07gHbBeUDBDvhRTodaktwju79de+4aKDjgPDapEpnisyg6E\r\n2WW7mjV/zwdp6wiAjtpRkiYuX8Ig81LNCOhR1/dnOc5eSRVrQNa8HCH62gE/\r\nGTr2VOQxYdOivpVEwygfjVBxPXI6FUBxJa0Wmjhve6MTKpLLeEVjXZbR105j\r\n4iJNWdf2QpGQeRIiQXnIw5tvpt74KaSDIQ+B85gntSM3Bvv/uRhbMTIa4RIj\r\nEGxYI8EkJN5xbB6nIUYqDXKwFDFGO5+edRUlaYw/OC8Klz2cyrjo5wt5z9MY\r\n+rFD+AzkHuU9iNTijeaLIV0/HRVfwWc6h9E=\r\n=+YHm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.20": {"name": "@expo/image-utils", "version": "0.3.20", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "b8777a2ca18e331f084e62ee8e0f047a6fc52c16", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.20.tgz", "fileCount": 30, "integrity": "sha512-NgF/80XENyCS+amwC0P6uk1fauEtUq7gijD19jvl2xknJaADq8M2dMCRHwWMVOXosr2v46f3Z++G/NjmyOVS7A==", "signatures": [{"sig": "MEUCIQC3wJWCGPMywyUl9VJGNH9yRrobx3u+8mwvomnX8geFjgIgIaM9kd8EVduzDHGGUzVkEWaS18pLhVHE3GwF8NpwTPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZtAXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD/xAAl8KwpWCKhc6C00uVjEyeIbZJmjUf4t6VwXNovfi/LGDrEx9N\r\n6tYylbxa9eVCvlDJzDlfwEmHQVJuGVDFf5Vkvje3N3AHF4P7EALKD7iVI+Jv\r\nEGOUiL5TP8Bgb8pP/5sXzZuQn0OxUnMI1lx0bceCChqX0tZb7+YJuc/rr47Y\r\n8agCfGkp3IIXjrPwe1uGieJUVAh/CFOobeCmGEEDjdkSA/ViC2UubL4Ru4eH\r\nmiBp8UgTweky318CLHhDDiJXpEzjxvfwBmEp2IqxKMn15JjUoc8vRrCeQe3s\r\ntrM7hHhxCfbYBcBzUvNAl5h/lBlzRlTIcOKVPkDgMdvZS5eNitJY4QaRteFC\r\nczEvWr8OOw3scR40iSSZfAincnbvE+IvZ+LwOeTQ5Ik9FqJmz1iK7ootNeha\r\nepEQn4vXWIz6/+ZgJkgC9b8+60dMRsI+wGQjiiATerB9fIDgnZS+ZTf5nuv2\r\nXxTENlJPC73uJEyFd80Hr2/xZBJsfrgCVQr1TRZhip+KfQ45bY8jfqD3IppX\r\nSp0G6yZsgEdxebs0r/tGBRqhsVSC/9uLjTQ/6WxMM9yyAUh/4NhQ5mrTK8zw\r\nr7gudmW1aWL2YeMRmNxg+xBhl+ttoCQibT9PLTSQPgdF64jWyL/TSCfm4KJ/\r\neyNUZcDpVBhLtgdFOtcuIHQxR6urXzH84tE=\r\n=Vfgi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.21": {"name": "@expo/image-utils", "version": "0.3.21", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "dabe772135a66671939f87389e11f23e54341e1e", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.21.tgz", "fileCount": 30, "integrity": "sha512-Ha7pNcpl52RJIeYz3gR1ajOgPPl7WLZWiLqtLi94s9J0a7FvmNBMqd/VKrfHNj8QmtZxXcmXr7y7tPhZbVFg7w==", "signatures": [{"sig": "MEYCIQCmUKVRaNwRGsiTyrJS5n2FgOXth4wbvOzuIfZ0QzB/ywIhAMlMWjWn6hH8FbcKQ6cEXoHmgQKhr1T4vFv2iuVK1DqV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitliRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsTxAAlCsEqQghgblr6LAS6B5b0rfC0be30rKFEgmPqLFGNAppWwJU\r\nrWu/dZp7J+VOMOT2b20DPmW5yW7Dz1H7bx9LqLHBnhkTs4VuASlg5aGX6eOA\r\niX5vXKc+PcvCvZoqvx4oyLgi/mfW66hmX61Z9CGnyuVxscr5qT2jYiFN9ref\r\na5MobJlipVBtgMDo8vCb40MDj9pjqYvk8MRYXErQHY4JnWOLtw0BlMnxGF7U\r\nvE0drY6rOaNARab8P87QaAKhx5Sl+J13MnQjW7CD4zELerlUqhm37PF3W69j\r\nD9UgxYqjDLi60+jegX02JRTFYljYiPX9duS+qP3KobrMW8j9h/cD5j95Gr2Q\r\n7Lcm+b1qb20z2nqrIwOXnQXJIUP42ATVcrvV9gLz2/WB2C5gZLfZRY+C6ySk\r\nCrg6rUbBC6GR+/Jbq2BO8r4/1zcL2QsnWe1Lu/BkBbqkyQJyRBWGDp+az5h8\r\nK3LYoZkVjOh0okForjAB1nZ7ZT+qkRmT9rlpC+olxbEowQHaJwCfavYZBwaW\r\nJ37BWAx0oiq8piACNT88cAGtCvjQS5EFFu9j+KfzIDVvXHeS+bFTebLaIKVm\r\nBDBxAzuYQt/FuGoIYEd3QKPnLBMLIa4VxafYtI83HHhcpBMBszvEfr4sfnk+\r\nfkUi0Wjs7bOEsNzywJNJ1Ul4UypaFdAq40g=\r\n=KYuW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.22": {"name": "@expo/image-utils", "version": "0.3.22", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "3a45fb2e268d20fcc761c87bca3aca7fd8e24260", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.22.tgz", "fileCount": 30, "integrity": "sha512-uzq+RERAtkWypOFOLssFnXXqEqKjNj9eXN7e97d/EXUAojNcLDoXc0sL+F5B1I4qtlsnhX01kcpoIBBZD8wZNQ==", "signatures": [{"sig": "MEQCIBz7ph71cGkGrbNcK8RTTjmTYSJFPZMxNPEGsCxyr9lwAiBDgd75XO4jcMg0ahPuAgd0Z4Lqgbtwm6/ozMTh37YW5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2JMRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqN9g/+JaDvEzebqO7rCEZoDwX/z9C8KSQaqenegIyAq6XRNlvLu5Yw\r\nP+dt8pSXfp1r9sJ02EbR7UjcuI3vE6zFb7Ns99XOP+IGkDbh8YJWP2hgN8J+\r\nYyFLR/AjhViF6VsUFFLUueUY3mwepGvoaYO3EkGW3/pHed7W2w9mZ/09+z9I\r\nmZbTq/+eGAvZp3MK8+RyePqqf3yv2VATSsc9beWwt1KMl/tpcYdIUcCEPbPV\r\npm8Pv0ctf/nWufyqolMl8CAGjK45MDie7M1UCjYywn0LDRLWfOKQVgoH+86t\r\nXb5daMNvSodpxTN45SLEvCth3LFftGM4japXt6POK+gpSohCKdudY597Ia1A\r\nrL27j6hDdmlozYi/RAjXRYw093hHW3bU7b3IUQ5afPuMakDQD83q2UzkDPSj\r\n5Evmld0pq22FVkTyWpsUkhw9yc3Wk4WeD1Bnn4GtE6g02NfX+ctpoRg1jFlP\r\n9XQQvWb7icPmtnmkF+C3V4r3DXWgwL28BgqLilekEaTcS+dSM4yitX3b+OvZ\r\nKOgN7JBYTbLS3G9GwMdq4p99b29iGl8lzDdcKj6oSRbw6bYu1Pg7qBjzhIny\r\nHtzUNLLWKlVSj6g9lo1piTBxCQap15AkVUs+81biFJRr6XOaKJ+1GLTo8Vnd\r\nt2nzflnq8gB5H9jYVey4vaAdgOooDTUFuWM=\r\n=D3uq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.23": {"name": "@expo/image-utils", "version": "0.3.23", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1"}, "dist": {"shasum": "f14fd7e1f5ff6f8e4911a41e27dd274470665c3f", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.3.23.tgz", "fileCount": 33, "integrity": "sha512-nhUVvW0TrRE4jtWzHQl8TR4ox7kcmrc2I0itaeJGjxF5A54uk7avgA0wRt7jP1rdvqQo1Ke1lXyLYREdhN9tPw==", "signatures": [{"sig": "MEYCIQCoKZpj+yoamYheRwqCl/4vvyB/h9OKDZGyFdbGHhyJiwIhALxWJ7cRpYLMRzMgbk24RbGW4UvM4TnDaS9fsAqqBQxO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYECzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6qg/+MxBc+V8aWjuTvCT7Yvzrjet6cbuWT31LfnMePMFKoXhSZmky\r\nZPiZkLDvPMwxPWSguOy9IjAEcD+eYrkSa0BKq/Eu6/RLXdjA14mtyE62bWjH\r\nOJ+lvO6qIh08X4iyYsN4uI5JBQaB1CTtqHN16MV9r/ZyfUf9HOfVPAFX3ypI\r\nxNQJUFO+3jzBwLJ29E1O34+ZRYwz4dGtmTahGgyRUc5HEW02M30JKGv0o2Qa\r\niA/pVWzYxJhVO5Y7kt9oIR2gWsyK1xL1iOp274A4a1UFKnAbf71ugddr3Bv0\r\n9ftc4FpePdnS5thi/+HS0mOoXcywTjXkKbucESaoYpE2jWAPgyZHL26jBtuC\r\nUWhK87FMT1ysocZonp/nw+4TagR2yyUObtCzSjUVMZbSJobKKkmEHX0gY33j\r\n6W9RFI99hb/B1pt4B6HJpgh+i7Ne/zlZP9SFW5ApvAW+10b87QdybPDPe7cG\r\n7yoPnQ9mOMcb64CpjS9P+5mnzu6+p9O6fLigb6CipUpKiFOuuNZ4WCOFyr+Y\r\n5dwKFUOWkUAy8A1ne59jljmWGyR84yglUH/npfaR2OzgbL863DDSQ31Sbin5\r\nKNAz3rT7iVbOOh2sWB7POozUoUErG+DUxVoCJ2yPEaS0av6ogN2M5Ai1F4hF\r\nAY3H980SwZ2VVX5xmUsB2PDqS2hocTwkYVA=\r\n=gWMB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-canary-20231205-250b31f": {"name": "@expo/image-utils", "version": "0.0.1-canary-20231205-250b31f", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/mime": "^2.0.3", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20231205-250b31f"}, "dist": {"shasum": "2b72e7875a74b8701c47b0f19d4bd7b2d234b5c2", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20231205-250b31f.tgz", "fileCount": 33, "integrity": "sha512-u5HThsixPQxyNIGPJGnL3rhTxIqoDEtK21DNlLcp/crrk3N098Fg5mbxpyD73tNOElwN2vo1QilkuM77TvyKKQ==", "signatures": [{"sig": "MEYCIQD4enc8UMKz6ftRU4wa0oQwaQ6PVOrdLtCgXUSnZiTmHwIhANdQtSpzc66kxWHm6kxDb5L8Abi6PukHxjUI6sl7Y9Gr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74260}}, "0.4.0": {"name": "@expo/image-utils", "version": "0.4.0", "dependencies": {"mime": "^2.4.4", "chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/mime": "^2.0.3", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "^3.3.0"}, "dist": {"shasum": "5ddca7de3faaf1682d259ccc6c9cce5f96337578", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.4.0.tgz", "fileCount": 33, "integrity": "sha512-8j0zyNAv9+hGXTo/9N8bhp7qAkUOrXfeprTBfem5JPHZg5kEYok38xMSeiDZlTxn5wF88EqtrDrY350Fp+cuIQ==", "signatures": [{"sig": "MEUCIQDE1nBdWSJpmbkClbWCp9Li7+sqEEmbndeAGXHbSGIFRQIgdkY/FlWveYTHxpXyT3y0g7mlTLpZpkRt8c4FhVKPUKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74213}}, "0.4.1": {"name": "@expo/image-utils", "version": "0.4.1", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "^3.3.0"}, "dist": {"shasum": "78c54b8aaa974d0ac37fee5285683b54ff161b2c", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.4.1.tgz", "fileCount": 33, "integrity": "sha512-EZb+VHSmw+a5s2hS9qksTcWylY0FDaIAVufcxoaRS9tHIXLjW5zcKW7Rhj9dSEbZbRVy9yXXdHKa3GQdUQIOFw==", "signatures": [{"sig": "MEQCIBEwCI5rVSqNCF/gxRTtz9beXBsSs497WYLmo2PSKiynAiBnl9Bof56hC0PVfUntxagvvJrQyzmPzhHdb3yZFHygZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75090}}, "0.0.1-canary-20240109-93608d8": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240109-93608d8", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240109-93608d8"}, "dist": {"shasum": "143032b99568f2a374978b2a47d2b2f28ecad44c", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240109-93608d8.tgz", "fileCount": 33, "integrity": "sha512-i7TIuFnROXVMIk/Vnfoy6C3zl4sC55TtKG/RBYZG+QsUbfy/NRK1rZDG9xLeYN6y7YdkpQfY0g6u2OzL6O3y8g==", "signatures": [{"sig": "MEUCIB+IHEbh8zUzdHM0iMocdmYl0WPB9y2nu6ataH8aAbCdAiEAzAQ74FobycIX+2vuuIdVnEl0jd2dm3W+9RM2G/bgNaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75137}}, "0.0.1-canary-20240222-c729a13": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240222-c729a13", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240222-c729a13"}, "dist": {"shasum": "80f0ccf3ad8dfd3fb9494b9c924299ee45ae6565", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240222-c729a13.tgz", "fileCount": 33, "integrity": "sha512-SCd65dcDFhlYKn442SjwwKZWnvLh1SrCuzHFeB4chk9f01Cvg8sU/YKh1q408wS4O7dxBNecWvDiw3AvVcx9uA==", "signatures": [{"sig": "MEUCIQCJwueVv7ZLwASijvn51uNRA6rCPGjp78T2fmM9cjNW0gIgciRfZVwcEvnphQLupkAMhsauMfiWNMElnfj4EEThxbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240228-7cee619": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240228-7cee619", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240228-7cee619"}, "dist": {"shasum": "2ca677798b74eaf934748f8bba76ec918474ec02", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240228-7cee619.tgz", "fileCount": 33, "integrity": "sha512-IWQexFPoAfxusQatxZuvJeW/IWixuPrSXCy071qrDXixmsY8vm4OO85BmyrsRjCybm/F9k/LRwewZLKs3lOe9g==", "signatures": [{"sig": "MEYCIQD60h5V9BddygsvO1dDjf+hnSFTb7LMAqaJRMVyky/6BwIhAI6vvXioQM9w3jrZTxVl73ehpuIP2db74STdbx+6fFQN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240305-e60019e": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240305-e60019e", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240305-e60019e"}, "dist": {"shasum": "bd9ddc174c0f203fb1f8ba66864f369ac3ec9f43", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240305-e60019e.tgz", "fileCount": 33, "integrity": "sha512-Oyp9SDiQsu9YqH1QdELAlVLHPyxisfTyacNzmBwd4rJ6ZVlhhS3gzY/l4biNDI1ugokNTI4RgHFdMAs5+7nrAg==", "signatures": [{"sig": "MEYCIQC+sCDC7cPOWH6LcZdNLp2Ja9q1XQT7xlYoQqXWL/IBrQIhAJywH54R6skB447pWxGiwM7KwVFm+NDyQraPAXP2rJR6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240308-6715875": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240308-6715875", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240308-6715875"}, "dist": {"shasum": "242e823f0f78efeb13d88b8b74eb3f57816276ee", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240308-6715875.tgz", "fileCount": 33, "integrity": "sha512-gKSRWmd80jRIsS4m6+ojqpz5FcmDkgPE7eb/YzfWf/h3mfGpV8w4GyP7NBzq7i39Y7cXw4zJ8/oW9o1DuCGs9w==", "signatures": [{"sig": "MEYCIQCsiVXP7XOEP2YgjQoNxu13a391Dg0Q4Wtzc+6docW4GQIhAJj3I8Om6v0eY8ivTDa15v5UxGnM2QJ/YNPDrwCPzfI7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240315-ce71005": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240315-ce71005", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240315-ce71005"}, "dist": {"shasum": "38a21cf37a642e9299c51bce2aef3abb656c38b1", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240315-ce71005.tgz", "fileCount": 33, "integrity": "sha512-LceN//UjT6XSxWtzXWY8Jue7Ucv6CW0eRj0jdWfTCBujcvp9ZYIlNTFIITZQ2pEM8Syt0sb1Ktal5hiZ6uVR8g==", "signatures": [{"sig": "MEQCICKd0Np0hubZ02CkiNn5HHDZXnlcFD5UNxEoNBkJr/AlAiAMLcrxQ5xL8BkuKd2u/x3wgoVhANYlQXT6cRZLA11NlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240318-53194f5": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240318-53194f5", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240318-53194f5"}, "dist": {"shasum": "80812e5062f9ffa767787abd6026edcc413bb09a", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240318-53194f5.tgz", "fileCount": 33, "integrity": "sha512-dDAMrr8CxQ4I6Gi5HHaC+GYCNWWI1OzVPuEETrtLjtdsWqQYVnAwiWwQ9e1auWs1Nef1BgDhuS881+cjDpu4Yw==", "signatures": [{"sig": "MEUCICSA84qwDzqMMrbhwTsRT1llUM3Fg0LsQH6Y7F6k3LHqAiEApuEWXSU6+04ebYjHDiSXgge4N8WJhNht5cxoeVpJGmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240318-af634b1": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240318-af634b1", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240318-af634b1"}, "dist": {"shasum": "cf7e8c06ea279673392a09c09f9b58a8560086f3", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240318-af634b1.tgz", "fileCount": 33, "integrity": "sha512-b4ffz7KMLm/TI6LLIqBSKc18IbJeZSFvxZyjtZ5RDpIvSQNFQTk/4ZAzzFkLiRMnmbaxAATuOaXaaQKWVLC6zw==", "signatures": [{"sig": "MEUCIDoToZ4EvlTdURRIhRuQ1yuKZLvcfag7+j4fcU8MXUkkAiEA0CI5oTtrS52BL7gakqBRAkZXDeXz9HlHATRmwRCslSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240318-dd8f245": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240318-dd8f245", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240318-dd8f245"}, "dist": {"shasum": "fb0960f00155b801dcc57a7dd1a5995cd8aaf2b4", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240318-dd8f245.tgz", "fileCount": 33, "integrity": "sha512-fEWRgFUPl8Ia0zh1o4by2a7O1uhRM0jbioEUlZ2P9gHkqIlI+c9nzj3oNtc1oUc2DI5Z3TJAX9o877MTXx75RA==", "signatures": [{"sig": "MEQCIDoY5IS4k09fPJ2JeOt0qcYjqZx/DKXZTU1PJEX+emzxAiAO0IZcuJMKS5hNuLMPqQNHeUgGtiRZFaX+SfqreXLkPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240320-8a10e09": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240320-8a10e09", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.5.4", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240320-8a10e09"}, "dist": {"shasum": "f65323185c0217e08b341407848651d8999e1925", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240320-8a10e09.tgz", "fileCount": 33, "integrity": "sha512-3x022XlmX0t6pJ6Yg4aCm4OuzP07XkC+ESZUDEF30KPqzdCIj+fTz931TxtMxpfsxJ1hTI1EDQFijRF9fJc5VQ==", "signatures": [{"sig": "MEUCIQCzyASHE9CDvFsyX7dfUjKfTBQEpZWxzfapugaDoXTA8wIgZXX/wuTo6RxZJymbavlzMMOMOC62ke5BGBohBZ+r9Ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75138}}, "0.0.1-canary-20240327-a7302d9": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240327-a7302d9", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240327-a7302d9"}, "dist": {"shasum": "50352fd46795c9398acc567bba128d1aa2848760", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240327-a7302d9.tgz", "fileCount": 33, "integrity": "sha512-JlVNwTDbkEm01j1RtNlMrkn/57tPFqdvKPHT7v3O/EJOtYiVXsQAvavl7tnE5odO359gRjBTfhb8A0rtm4UGDg==", "signatures": [{"sig": "MEQCIE08qFjt1gGxYG7G2lBLiy0w4d/9umH8wppbZzgmGheaAiARHG9FUkf0qewbbksCjAzZycmTzefegnN6L7pd+VhpGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240328-24ecc5e": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240328-24ecc5e", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240328-24ecc5e"}, "dist": {"shasum": "7fabe92a2ecdf2b36e4d0e863e9c2ce925bbff63", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240328-24ecc5e.tgz", "fileCount": 33, "integrity": "sha512-pf/DwkFtTkB5ewA3GZLpqkIBcEhse1BeeqTvgCYu2Dq/aTA3G4pr2jjDjIUzGaOeNrvGTNTQ4wghwZghu/c0Gg==", "signatures": [{"sig": "MEUCIQDWA3OEJvur2uVFp42VasQzFcJiguJuwjX+1KX+u/FSUwIgRcwwF0j5YNkfBGJe8Lk+ZgfnW05cWRoMxtMIB48oLVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240404-e2b8743": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240404-e2b8743", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240404-e2b8743"}, "dist": {"shasum": "5a157b0b426f1212741bafaf610eb7aa66d8b98c", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240404-e2b8743.tgz", "fileCount": 33, "integrity": "sha512-g2yc8R3gI2VssP63TFPCNR0E4OYchET72F0AbzD8qfDxIVuuf6qml6eioFIjKCYQgm+bry79H/R5lyBbcAVLVg==", "signatures": [{"sig": "MEQCIEm/62uoI1ddK32yuJ5biUzAF4+a6HFGFVEv/dS3YhLFAiBtxFmDmE91r2eChtDz2ntIx5JN51VoEzCwvlJFGroj0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240405-cdfd9c1": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240405-cdfd9c1", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240405-cdfd9c1"}, "dist": {"shasum": "e239e9d32dafb8bba62b7eb48e2015bcbbbbec82", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240405-cdfd9c1.tgz", "fileCount": 33, "integrity": "sha512-lMxR72h1JnkGwcwjT0Gp8uTA/RqYlSTv11k8yo+MSgg16nUy98jmbg5Fc9GOjyHl1SAWXEBV8tvnQEO6Xo5A2Q==", "signatures": [{"sig": "MEYCIQCv2zrSt27zv6hYV3jlpFBDI9wckd7o3LheBspaKXj60gIhAK4Ld0C3p6cpVNBS0hAC1JIsDx4Wk2csLAyy1xAM7AP7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240406-a4950c7": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240406-a4950c7", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240406-a4950c7"}, "dist": {"shasum": "7b0c4caa3d9698ed0880ae6da61109f170fc57a3", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240406-a4950c7.tgz", "fileCount": 33, "integrity": "sha512-UjPpLQGW3fV2mt/3MmxwKMBH0eKW9nXTLmmnZ7fuTa7VaZe84SXL/K6k8J2O0pjSRH/LLwDyvrF4Yg+BBP1kHA==", "signatures": [{"sig": "MEQCIC/NVFyFLRIQ/Fqknc/zq7ZRrdjpi00aCQZPnzw0s1zrAiABdwfI2Li8H4ACoGIC9wmKjXaRyWNVHL/vHeaKmkyvog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240411-33a7dc1": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240411-33a7dc1", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240411-33a7dc1"}, "dist": {"shasum": "148cbf5eff82e99287d97ada464c5849f707c066", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240411-33a7dc1.tgz", "fileCount": 33, "integrity": "sha512-z<PERSON>zzlyl6tu7rRr0cN6KEX09KShMOxrUl+96YfCH7o4uffjHtRpAD4PY5suSKRb9AbQIR0GQO3gHbO2lteLFU7g==", "signatures": [{"sig": "MEYCIQD2mJCuZH8/LyovjnVVySVxn/Ng2A9ACgoJh32E6WwMfgIhANwUmkoM5vyPABh9Iul4eIZEamChqxRtK2fnb/dT/uPq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240411-55a0085": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240411-55a0085", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240411-55a0085"}, "dist": {"shasum": "87208264b2f1b26220d6eba5f9a88638071a7328", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240411-55a0085.tgz", "fileCount": 33, "integrity": "sha512-liyg9JN1r1mOzNvpXWSO3ow2gj1IdFqrZorDk+S+hYwdsz3UsX8/GkpAzIN0Hty5LzudYu8ucmkTl1AQmQuqOQ==", "signatures": [{"sig": "MEUCIHBEJFKKxppcY1mlKpGG2DuDRaTicu/P/520tcsYrksMAiEAid1p2RNblGZ2edOZGRujSuDJmazmGmYxz/fHrCAb+eE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.4.2": {"name": "@expo/image-utils", "version": "0.4.2", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "7.3.2", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "1.5.0"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "^3.3.0"}, "dist": {"shasum": "84beabbe50d7e1c2ad78f1b44be6ec49a9dbc0da", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.4.2.tgz", "fileCount": 33, "integrity": "sha512-CxP+1QXgRXsNnmv2FAUA2RWwK6kNBFg4QEmVXn2K9iLoEAI+i+1IQXcUgc+J7nTJl9pO7FIu2gIiEYGYffjLWQ==", "signatures": [{"sig": "MEYCIQCS2XW8koDjdUZ19pI9mxJ0IHLQD7Lmv9dR6E1tATGHjAIhAJTP27wj9WA4A1PBoh9yNl6ZY1hawch1fF+cKXvxz2qW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75090}}, "0.0.1-canary-20240415-0868fc4": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240415-0868fc4", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240415-0868fc4"}, "dist": {"shasum": "0d946e133ac476c09e0fc5aa89b8cbccfab3f3a6", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240415-0868fc4.tgz", "fileCount": 33, "integrity": "sha512-81coTHcz2RqwrCqYpxC2+KgEvzG3xeEsZvO1CRTkZkCZFGnXQtvYYXuHd6i/Ou99gv69pGSgTWcEVUJGsz8iqQ==", "signatures": [{"sig": "MEQCIA5+giWP1nN9V5ApR6COjwbPbPrMqejsX3p+IWVTs+8vAiA57F5HPK67mLZkXoSzaj5TIKRyZRzO1mS4zPfSoJEVTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240415-cca04d8": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240415-cca04d8", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240415-cca04d8"}, "dist": {"shasum": "7d56be69b02335cdf535bd302c0df9d412bf18fd", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240415-cca04d8.tgz", "fileCount": 33, "integrity": "sha512-pZk3WGeMB3eSlusnX/v/XGu2kwygoI0OzYh7uqLbgb1pHTBkc7kOK2E2ktYNFS3sxod16j/uLiXlA4XZHV/G4Q==", "signatures": [{"sig": "MEYCIQCjVeiNwNdv9aAG9YRSm13p8FSHvqO0VANuRqnJXG/SsgIhAIT8UIpIiQZk1LpnpeYqlTnxG/OVnfxuAZdRvQqK896V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.0.1-canary-20240418-8d74597": {"name": "@expo/image-utils", "version": "0.0.1-canary-20240418-8d74597", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "0.0.1-canary-20240418-8d74597"}, "dist": {"shasum": "19cbadf4c0e349c5a9f65fb8d39c8c1d3d0b98ef", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.0.1-canary-20240418-8d74597.tgz", "fileCount": 33, "integrity": "sha512-VfwtsZvA1MUOpbwQzJ356K5dADfzaNaxG+8BPdP50LZafVSHHJnmmzQgP9hzY86fLJ30N3tVDGA8U/mZcqYCuQ==", "signatures": [{"sig": "MEUCIQDUCucyeTcKjZpp6LedhH8XSKgVHXdfRSohun9dxMx6dAIgdBIOqEkPtpaW7yOf5qc7F3fwWK+BWk/IaKZmmD6dZNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77251}}, "0.5.0": {"name": "@expo/image-utils", "version": "0.5.0", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "^3.3.0"}, "dist": {"shasum": "0dcff83e4f32e5fb8e4660162d91eba51bcf215e", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.0.tgz", "fileCount": 33, "integrity": "sha512-/lzZrCwoDQtdfS6mqCcflFxgXwY0NraM4+kRFUYB4JENzjB9FgLu/D7FB+A4tKJW70H3hh+2kQjMi3VgMNLNDg==", "signatures": [{"sig": "MEUCIFmz5p74MTJgNB3DeZKcLg7OvAsNRvHEuu8dBqtdzuzjAiEAxqR6HBS5KgvvxwWu6dX0i3cCAi6yDpo9C3DxEEj0/eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76239}}, "0.5.1": {"name": "@expo/image-utils", "version": "0.5.1", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "^3.3.0"}, "dist": {"shasum": "06fade141facebcd8431355923d30f3839309942", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.1.tgz", "fileCount": 33, "integrity": "sha512-U/GsFfFox88lXULmFJ9Shfl2aQGcwoKPF7fawSCLixIKtMCpsI+1r0h+5i0nQnmt9tHuzXZDL8+Dg1z6OhkI9A==", "signatures": [{"sig": "MEUCIQDu/+IB9wYstOf+Jrw6zUMkCL60AL7GpOIcf+O7ZWSRqQIgVovBBWq6HZVBiqorZ6yZ/hVZ+WU3MmtcHDVxqiBtIHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76234}}, "0.5.2-canary-20240625-2333e70": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240625-2333e70", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "3.6.0-canary-20240625-2333e70"}, "dist": {"shasum": "8e499b25fc7b524c35f703991d8aa46edc860079", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240625-2333e70.tgz", "fileCount": 33, "integrity": "sha512-XEYvc+kTtljDeDgvWVoY7xpSt4xgz+pqSWW4Zihug9w8gfxNnxmt4po0fbhGdmB0IKGkPDo+TquERqIYKN/uvw==", "signatures": [{"sig": "MEYCIQCbKxFvrxPHTzeqYwSuBay0+WykOEh0R+WVbhbLdKO5YQIhAJKKEWwmWFPjWux/RwyJXx5M7EpiN8iyC+GD13jn1imV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76281}}, "0.5.2-canary-20240627-1402f4b": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240627-1402f4b", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "3.6.0-canary-20240627-1402f4b"}, "dist": {"shasum": "be199475e409b50728ca6e2ef9a70b00bdc00013", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240627-1402f4b.tgz", "fileCount": 33, "integrity": "sha512-8dH+v7fyHeZXVby0Zrr/bvRWjyT2jIT0bknSzKlxcMZR1v8nlDdteqCut1v2SzgRf6a04w9mN3CD19LDCwLelA==", "signatures": [{"sig": "MEUCIQCd0SmorDQG04s3hhIdlmTQVWiqZ3znehfJlacHUh2C9gIgXJXVKl+Gm5vAI290sqP9aNQSu0Z1wkHbRl3eWxI1W0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76281}}, "0.5.2-canary-20240628-1ba8152": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240628-1ba8152", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "3.6.0-canary-20240628-1ba8152"}, "dist": {"shasum": "bd06a45b88c4fd0994a9f633644ab9a24029e49b", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240628-1ba8152.tgz", "fileCount": 33, "integrity": "sha512-SDKtUW1JKhBjnJukxL9Ha6enoHDvYHtIpoAPo3L7cEmJ1mS6kOiWgFP6emKgy1ccwRmI2+QHXHaF61nqJAwYyw==", "signatures": [{"sig": "MEQCIE4VnIRG1IXDTOB83HIiGR/9ywcKFsHByC8S3zO9fH7AAiBNUdvWGiXpH120dZ/H1XjOuDx/m7IuLbq8uumoNwBlDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76281}}, "0.5.2-canary-20240719-83ee47b": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240719-83ee47b", "dependencies": {"chalk": "^4.0.0", "tempy": "0.3.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "parse-png": "^2.1.0", "node-fetch": "^2.6.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "@types/node-fetch": "^2.6.5", "expo-module-scripts": "3.6.0-canary-20240719-83ee47b"}, "dist": {"shasum": "cd9fef250ea88112803449ba191bf6b013c47846", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240719-83ee47b.tgz", "fileCount": 33, "integrity": "sha512-SuDj+Btbj06vFZt6H/QspForqiYPWQEcNJjkt2373BEbOMCsgzDlImYNYFtvrBoNbDXIqnx6M4he4wJkY8nEsA==", "signatures": [{"sig": "MEUCIQDgcDERWH3C2TFsHif0l9OVOR6iq9L4VES0Tkgch5GxBAIgWKmNcuflTVdqYn3/paWgAcWveQ3dClUZVzCI/JOGYQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76281}}, "0.5.2-canary-20240814-ce0f7d5": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240814-ce0f7d5", "dependencies": {"chalk": "^4.0.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "temp-dir": "~2.0.0", "parse-png": "^2.1.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "unique-string": "~2.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "expo-module-scripts": "3.6.0-canary-20240814-ce0f7d5"}, "dist": {"shasum": "e6daab622b20dbbe7266c7a01b13ae90f9586354", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240814-ce0f7d5.tgz", "fileCount": 33, "integrity": "sha512-NFschNZiTJ7pbuYYn/dQA8QX6mXwrkJQoYBNLGZ0HPBvvs1tbG9afw70kk3yub8OBUVOgUJ/GwK81vNX+Dis1g==", "signatures": [{"sig": "MEYCIQCl+o0+1sj/uDb+3MqgzdEFz7TS4dl6BVhbRU2uth67oAIhAOOpDn/9znukUBKDhxpcv2O9TVyZWV/88v+Wi6JiQU4K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77040}}, "0.5.2-canary-20240904-69100c1": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240904-69100c1", "dependencies": {"chalk": "^4.0.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "temp-dir": "~2.0.0", "parse-png": "^2.1.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "unique-string": "~2.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "expo-module-scripts": "3.6.0-canary-20240904-69100c1"}, "dist": {"shasum": "9905b18a516308e16158b9f1e0b0f5c300e825ac", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240904-69100c1.tgz", "fileCount": 33, "integrity": "sha512-axqvesL0jiHbfTFMth8j0a/13QwokzYUAhV0xWf20ghV2xv2hYsjZA64qQDMD15LLP5eNuamuVNj3EJWMl6aQg==", "signatures": [{"sig": "MEYCIQD127Ub5t0CMBn+8tEQkYXQpje6xgiqaM08nyYKI77EbgIhANV3tpLF6nQpKPX5WX9QlahswO7luj6nebMFQo72UEq3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77040}}, "0.5.2-canary-20240912-1059f85": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240912-1059f85", "dependencies": {"chalk": "^4.0.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "temp-dir": "~2.0.0", "parse-png": "^2.1.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "unique-string": "~2.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "expo-module-scripts": "3.6.0-canary-20240912-1059f85"}, "dist": {"shasum": "38b45b21b9c2d828f9dfc0735f06e4f4e89f6ba5", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240912-1059f85.tgz", "fileCount": 33, "integrity": "sha512-MoLXoOv40gEVp0iIDWo6IsvI/oxK8jzJCb4xmbaqJ9o0LRiFMaBsrpWA8eMqoY+zbObiVFvbjLKXOOx1BOIBaQ==", "signatures": [{"sig": "MEYCIQCfVDKS75JzaLHR39zZ0pJh7l3gLiPUUHcIqm/ZW8JecQIhAJdRf0OO70rEQB0zko/WvIX7AKTT+8Q+98k6PtD3KDhN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77040}}, "0.5.2-canary-20240927-ab8a962": {"name": "@expo/image-utils", "version": "0.5.2-canary-20240927-ab8a962", "dependencies": {"chalk": "^4.0.0", "getenv": "^1.0.0", "semver": "^7.6.0", "fs-extra": "9.0.0", "temp-dir": "~2.0.0", "parse-png": "^2.1.0", "jimp-compact": "0.16.1", "resolve-from": "^5.0.0", "unique-string": "~2.0.0", "@expo/spawn-async": "^1.7.2"}, "devDependencies": {"@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "@types/fs-extra": "^9.0.1", "expo-module-scripts": "3.6.0-canary-20240927-ab8a962"}, "dist": {"shasum": "fd657e5fa11bfe1ed10cbeb8c61d4a7ea547400c", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.5.2-canary-20240927-ab8a962.tgz", "fileCount": 33, "integrity": "sha512-iZyqW+uggwfe3tb9KxbnPnGKM2JUXSG22Ns1Poyfe5YtjXw0W9jWSlGH62a27zTduGZcYR1yeyYG1Bt0IwIahg==", "signatures": [{"sig": "MEYCIQCClpzqjxbbWvGEU5wa92W+1RQlEkknjPBsc895kathtQIhAOuCY4fXSmhaEdeyMBYIKee6JRQuFrc5ecFKXxTb1OUk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77040}}, "0.6.0-canary-20241008-90b13ad": {"name": "@expo/image-utils", "version": "0.6.0-canary-20241008-90b13ad", "dependencies": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "fs-extra": "9.0.0", "getenv": "^1.0.0", "jimp-compact": "0.16.1", "parse-png": "^2.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0", "temp-dir": "~2.0.0", "unique-string": "~2.0.0"}, "devDependencies": {"@types/fs-extra": "^9.0.1", "@types/getenv": "^0.7.0", "@types/semver": "^6.0.0", "expo-module-scripts": "3.6.0-canary-20241008-90b13ad"}, "dist": {"integrity": "sha512-6g3p4kFp/xz58mPf+1JQ5eS0up7aWGgsT7wljqnG/GJkS8+mfvKA6VtrdUCLq2cgwiwfhJf4KPUIexdEh99qwQ==", "shasum": "ea42d54ea84eb3fa190461186fcea506f7961558", "tarball": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.6.0-canary-20241008-90b13ad.tgz", "fileCount": 33, "unpackedSize": 77251, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID2FzESgGaHX0JBPCTPpR6iXO7SsY7+NiMbmurh39El4AiB2c6zMrjEmHHPVpcwZzG/xNBlAkwumvSb+ug3g2JLMLg=="}]}}}, "modified": "2024-10-08T10:09:16.385Z"}