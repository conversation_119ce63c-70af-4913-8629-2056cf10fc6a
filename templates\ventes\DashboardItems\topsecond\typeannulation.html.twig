<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Compact Sales Dashboard</title>
  
  <style>
    :root {
        --background-color: #1a1a1a; /* Dark background */
        --text-colorlabel: #cbd8e3; /* White text */
        --label-color: #888; /* Light grey for labels */
        --primary-bar-bg: linear-gradient(90deg, #005f93, #234a62); /* Original primary gradient */
        --secondary-bar-bg: linear-gradient(90deg, #47abe9, #59caf2); /* Original secondary gradient */
        --label-colorstart: #155274;
     --text-colors4: #1d4d6b;
    }

    [data-theme="dark"] {
        --text-colorlabel: #cbd8e3; /* White text */
        --color-background-light: #1e1e1e; /* Dark gradient start */
        --color-background-dark: #2a2a2a; /* Dark gradient end */
      --text-colors4: #ffffff;

    }

    .custom-progress-container {
        padding: 2px; /* Reduced padding */
        width: 100%;
    }

    .custom-progress-row {
        display: flex;
        flex-direction: column;
        gap: 5px; /* Reduced gap */
        max-width: 350px;
        width: 100%;
    }

    .progress-bar-section {
        display: flex;
        align-items: center;
        position: relative;
        width: 100%;
    }

    .progress-label-start,
    .progress-label-end {
        font-size: 12px; /* Smaller font size */
        color: var( --text-colors4);
        width: 60px; /* Reduced width */
        font-weight: bold;
        text-align: center;
        flex-shrink: 0;
    }

    .progress-bar-wrapper {
        flex: 1;
        height: 15px; /* Reduced bar height */
        background-color: #333;
        border-radius: 15px;
        position: relative;
        overflow: hidden;
    }

    .progress-bar-primary,
    .progress-bar-secondary {
        height: 100%;
        border-radius: 0 15px 15px 0;
    }

    .progress-bar-primary {
        background: var( --primary-bar-bg);
    }

    .progress-bar-secondary {
        background: var( --secondary-bar-bg);
    }

    /* Center label style */
    .progress-label-center {
        position: absolute;
        top: 50%;
        left: 13%;
        font-size: 11px; /* Smaller font size */
        font-weight: bold;
        color: #ffffff;
        transform: translate(-50%, -50%);
        white-space: nowrap;
    }

    .type-annulation-text {
        font-size: 14px; /* Reduced font size */
        font-weight: 500;
        text-align: left;
        color: var( --text-colors4);
    }

    /* Responsive adjustments */
    @media (max-width: 500px) {
        .progress-label-start,
        .progress-label-end {
            font-size: 10px;
            width: 50px;
        }

        .progress-label-center {
            font-size: 10px;
        }

        .type-annulation-text {
            font-size: 12px;
        }

        .progress-bar-wrapper {
            height: 12px;
        }
    }
  </style>
</head>
<body>

<div class="custom-progress-container">
    <div class="custom-progress-row">
        <div class="type-annulation-text">Production Intervention</div>

        <!-- Xap RDV Section -->
        <div class="progress-bar-section">
            <span class="progress-label-start">XapRdv</span>
            <div class="progress-bar-wrapper">
                <div class="progress-bar-primary" style="width: {% set xapPercentage = (productionsIntervention[0].totalXapRDV / (productionsIntervention[0].totalXapRDV + productionsIntervention[0].totalXavRDV)) * 100 %}{{ xapPercentage }}%;"></div>
                <span class="progress-label-center">{{ productionsIntervention[0].totalXapRDV }}</span>
            </div>
            <span class="progress-label-end">{{ xapPercentage|number_format(2) }}%</span>
        </div>

        <!-- Xav RDV Section -->
        <div class="progress-bar-section">
            <span class="progress-label-start">XavRdv</span>
            <div class="progress-bar-wrapper">
                <div class="progress-bar-secondary" style="width: {% set xavPercentage = (productionsIntervention[0].totalXavRDV / (productionsIntervention[0].totalXapRDV + productionsIntervention[0].totalXavRDV)) * 100 %}{{ xavPercentage }}%;"></div>
                <span class="progress-label-center">{{ productionsIntervention[0].totalXavRDV }}</span>
            </div>
            <span class="progress-label-end">{{ xavPercentage|number_format(2) }}%</span>
        </div>
    </div>
</div>


