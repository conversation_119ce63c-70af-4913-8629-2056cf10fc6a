:root
{
--background-color: #fff;
--text-color: #989898;
--border-color: rgba(225, 224, 225, 0.7);
--alert-success-bg: #b5afce;
--alert-pending-bg: #b7cb8b;
--alert-cancelled-bg: #dc3545;
--status-fulfilled-color: #c794ad;
--status-unfulfilled-color: #dc3545;
--status-cancelled-color: #ffc107;
--nav-active-bg: #e1e1e0;
--nav-active-text-color: #3a3b3b ;
--table-th-color: #989898;
--table-td-color: #989898;
--strong-text: #5b5a5b;
--bg-checkbox:#e4e4e4 ;
--background-table-erea:#ffffff ;
--bg-checkbox-border: none;
}
[data-theme="dark"] {
--background-color: #333;
--text-color: #9ea1a1;
--border-color: rgba(225, 224, 225, 0.1);
--alert-success-bg: #575e3f;
--alert-pending-bg: #584388;
--alert-cancelled-bg: #ff4c4c;
--status-fulfilled-color: #537d6d;
--status-unfulfilled-color: #fff;
--status-cancelled-color: #fff;
--nav-active-bg: #202020;
--nav-active-text-color:#abaaaa ;
--table-th-color: #989898;
--table-td-color: #989898;
--strong-text: #afb0b0;
--bg-checkbox:#1b1b1b ;
--background-table-erea: #0c0e0d ;
--bg-checkbox-border: #4a4a4a;
}

.full-tabledataclusters .table-striped tbody tr:nth-of-type(odd) {
background-color: transparent;
}
.full-tabledataclusters .table-hover tbody tr:hover {
background-color: transparent;
}
.full-tabledataclusters .table-container {
width: 100%;

margin: 0 auto;
}
.alert-payment-success,
.alert-payment-pending,
.alert-payment-cancelled {
color: var(--text-color);
padding: 0.25rem 0.5rem;
border-radius: 25px;
}
.alert-payment-success {
background-color: var(--alert-success-bg);
}
.alert-payment-pending {
background-color: var(--alert-pending-bg);
color: #1e1f22 !important;
}
[data-theme="dark"] .alert-payment-pending {
color: white !important;
}
.name {
background-color: var(--alert-pending-bg);
color: #1e1f22 !important;
}
[data-theme="dark"] .name {
color: white !important;
}
.alert-payment-cancelled {
background-color: var(--alert-cancelled-bg);
}
.status-fulfilled {
color: var(--status-fulfilled-color);
background-color: transparent;
padding: 0.1rem 0.4rem;
border-radius: 25px;
border: 0.1px solid var(--status-fulfilled-color);
display: inline-block;
}
.status-unfulfilled {
color: var(--status-unfulfilled-color);
background-color: transparent;
padding: 0.25rem 0.5rem;
border-radius: 25px;
}
.status-cancelled {
color: var(--status-cancelled-color);
background-color: transparent;
padding: 0.25rem 0.5rem;
border-radius: 25px;
}
.form-select,
.btn-outline-secondary {
border-radius: 25px;
font-size: 0.7875rem;
background-color: transparent;
margin-right: -50px !important;
}
.full-tabledataclusters th i {
margin-right: 0.25rem;
}
.full-tabledataclusters th {
white-space: nowrap;
font-weight: 400;
border-top: 0.2px var(--border-color);
border-bottom: 0.2px var(--border-color);
color: var(--table-th-color);
}
.full-tabledataclusters th {
color: var(--table-th-color) !important;
}
.full-tabledataclusters td {
color: var(--table-td-color) !important;
}


.full-tabledataclusters strong {
color: var(--strong-text);
}

.full-tabledataclusters table {
background-color: transparent;
}
 .full-tabledataclusters th,
 .full-tabledataclusters td {
background-color: transparent;
}
.table-striped tbody tr:nth-of-type(odd),
.table-hover tbody tr:hover {
background-color: transparent !important;
}
.full-tabledataclusters table {
border-collapse: collapse;
margin-bottom: 1rem;
}
.full-tabledataclusters th,
.full-tabledataclusters td {
padding: 0.75rem;
text-align: left;
vertical-align: middle;
border-top: 0.1px solid var(--border-color);
border-bottom: 0.1px solid var(--border-color);
box-shadow: 0 0 0.1px var(--border-color);
position: relative;
}
.full-tabledataclusters th {
font-weight: 600;
}
@media(max-width: 767.98px) {
    .full-tabledataclusters .table-responsive {
width: 100%;
}
.full-tabledataclusters .table-responsive thead {
display: none;
}
.full-tabledataclusters .table-responsive tbody,
.full-tabledataclusters .table-responsive tr,
.full-tabledataclusters .table-responsive td {
display: block;
width: 100%;
}
.full-tabledataclusters .table-responsive tr {
margin-bottom: 1rem;
border: 0.2px solid var(--border-color);
}
.full-tabledataclusters .table-responsive td {
text-align: right;
padding-left: 50%;
position: relative;
}
.full-tabledataclusters .table-responsive td::before {
content: attr(data-label);
position: absolute;
left: 0;
width: 50%;
padding-left: 1rem;
font-weight: 600;
text-align: left;
}
}
.navbar {
flex-wrap: nowrap;
}
.navbar-nav {
flex-direction: row;
}
.navbar-nav .nav-link {
padding-left: 0.5rem;
padding-right: 0.5rem;
}
@media(max-width: 767.98px) {
.navbar-nav {
flex-wrap: wrap;
justify-content: center;
}
.navbar-nav .nav-item {
width: 100%;
text-align: center;
}
}
.form-check-input {
position: relative;
appearance: none;
width: 15px;
height: 15px;
background-color: var(--bg-checkbox);
border: 0.1px solid var(--bg-checkbox-border);
border-radius: 5px;
cursor: pointer;
}
.form-check-input:checked {
background-color: transparent;
}
.form-check-input:checked::after {
content: "";
position: absolute;
left: 2px;
top: 2px;
width: 9px;
height: 9px;
background-color: #f2eff3;
}
.form-check-input:focus {
outline: none;
}
.table-container {
background-color: var(--background-table-erea);
padding: 20px;
}
.table-container .navbar, .table-container .container {
background-color: transparent;
}
.table-container .custom-select-container .form-select {
background-color: var(--background-table-erea);
}
.table-container .table {
background-color: var(--background-table-erea);
}
.table-container .full-tabledataclusters thead {
background-color: var(--background-table-erea);
}