<style>
.bonustechno-header {
    font-size: 0.8em;
    margin: 0;
}



.bonustechno-legend {
    list-style: none;
    padding: 0;
    margin: 0;
}

.bonustechno-legend li {
    display: flex;
    align-items: center;
     color: var( --4text-colors);
    font-size: 0.8em;
    margin-bottom: 5px;
}

.bonustechno-legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 30%;
}
</style>
<div class="bonustechno-container">
    <h5 class="text-header4s">Par Techno</h5>
    <div class="bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="bonustechnoPieChart"></canvas>
        </div>
        <ul class="bonustechno-legend">
            {% for item in productionsGroupData.Tech %}
                {% set gradient %}
                    {% if item.tech == 'ADSL' %}
                        linear-gradient(to right, #49b9ee, #a8e2f5)
                    {% elseif item.tech == 'FTTB' %}
                        linear-gradient(to right, #027fc0, #4bb3e5)
                    {% elseif item.tech == 'FTTH' %}
                        linear-gradient(to right, #145277, #3a7d9c)
                    {% else %}
                        linear-gradient(to right, #888, #ccc) {# Default gradient if tech doesn't match #}
                    {% endif %}
                {% endset %}
                <li><span class="bonustechno-legend-color" style="background: {{ gradient }};"></span>{{ item.tech }}</li>
            {% endfor %}
        </ul>
    </div>
</div>

<script>
    var chartData = {{ productionsGroupData.Tech | json_encode() | raw }}; // Pass data from PHP to JS

    var bonustechnoCtx = document.getElementById('bonustechnoPieChart').getContext('2d');

    // Create gradients for the segments
    var bonustechnoGradientA = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientA.addColorStop(0, '#49b9ee');
    bonustechnoGradientA.addColorStop(1, '#a8e2f5');

    var bonustechnoGradientB = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientB.addColorStop(0, '#027fc0');
    bonustechnoGradientB.addColorStop(1, '#4bb3e5');

    var bonustechnoGradientC = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientC.addColorStop(0, '#145277');
    bonustechnoGradientC.addColorStop(1, '#3a7d9c');

    // Process chartData for the chart's labels and data
    var labels = [];
    var data = [];
    var backgroundColors = []; // Array to hold background colors for each segment

    chartData.forEach(function(item) {
        labels.push(item.tech); // Add technology name as label (e.g., 'FTTB', 'FTTH')
        data.push(item.totalVentes); // Add totalVentes as data
        
        // Assign gradient based on technology type
        if (item.tech == 'ADSL') {
            backgroundColors.push(bonustechnoGradientA);
        } else if (item.tech == 'FTTB') {
            backgroundColors.push(bonustechnoGradientB); // FTTB gets the correct gradient
        } else if (item.tech == 'FTTH') {
            backgroundColors.push(bonustechnoGradientC);
        } else {
            backgroundColors.push('#888'); // Default color for other technologies
        }
    });

    // Create pie chart with the processed data
    var bonustechnoPieChart = new Chart(bonustechnoCtx, {
        type: 'pie',
        data: {
            labels: labels, // Technology names as labels
            datasets: [{
                data: data, // Sales data for each technology
                backgroundColor: backgroundColors, // Assign the background colors array dynamically
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false // Hide the default legend (you've created a custom one)
                }
            }
        }
    });
</script>

