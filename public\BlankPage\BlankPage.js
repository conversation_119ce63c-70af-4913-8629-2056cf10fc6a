document.getElementById('headerTreeSection').addEventListener('click', function (event) {
    event.stopPropagation();
    const initialDropdown = document.getElementById('initialDropdown');
    const headerTreeSection = document.getElementById('headerTreeSection');
    const rect = headerTreeSection.getBoundingClientRect();
    initialDropdown.style.position = 'absolute';
    initialDropdown.style.top = `${rect.bottom + window.scrollY }px`;
    initialDropdown.style.left = `${rect.left + window.scrollX -10}px`;
    initialDropdown.style.display = 'block';
});



var currentDate = new Date();
// Format the current date as 'dd-mm-yyyy'
var day = String(currentDate.getDate()).padStart(2, '0');
var month = String(currentDate.getMonth() + 1).padStart(2, '0');

var year = currentDate.getFullYear();
let Type='V';
const formattedDate = `${day}-${month}-${year}`;
function formatDateRange(month, year) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    const formatStartDate = `${String(startDate.getDate()).padStart(2, '0')}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;
    const formatEndDate = `${String(endDate.getDate()).padStart(2, '0')}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${endDate.getFullYear()}`;
    return `debut=${formatStartDate}&fin=${formatEndDate}`;
}
document.addEventListener('DOMContentLoaded', function() {
    setCurrentMonthName();

    var dropdownButton = document.getElementById('MonthSelector');
    var dropdownMenu = document.querySelector('.MonthSelectorDropdown');
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
    dropdownButton.addEventListener('click', function() {
        var isExpanded = this.getAttribute('aria-expanded') === 'true';
        this.setAttribute('aria-expanded', !isExpanded);
        dropdownMenu.style.display = isExpanded ? 'none' : 'flex';
        if (isExpanded) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });
});
function setCurrentMonthName() {
    const date = new Date();
    const monthIndex = date.getMonth();
    const monthNames = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
        "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"];

    const currentMonthName = monthNames[monthIndex];
    document.querySelector('.CurrentlyMonthApplyed').textContent = currentMonthName;
}
function updateMonth(selectedMonth,monthName) {
    document.querySelector('.CurrentlyMonthApplyed').textContent = monthName;
    month = selectedMonth;

    document.querySelector('.MonthSelectorDropdown').style.display = 'none';
    recallFunctionUpdatedParam();
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
    dropdownIcon.classList.remove("bi-chevron-down");
    dropdownIcon.classList.add("bi-x-lg");
}
function updateTypeOption(selectedElement) {
    var selectors = document.querySelectorAll('.TypeOptionSelector');

    selectors.forEach(function(selector) {
        selector.classList.remove('clickedType');
    });

    selectedElement.classList.add('clickedType');
    Type = selectedElement.getAttribute('dataType');
    localStorage.setItem('clusterForOnedays', '');
    localStorage.setItem('codeinseeForOnedays', '');

    recallFunctionUpdatedParam();
}

document.addEventListener('DOMContentLoaded', function () {
    const resizers = document.querySelectorAll('.resizer');

    resizers.forEach((resizer) => {
        let startY, startHeightPrev, startHeightNext;

        resizer.addEventListener('mousedown', function (e) {
            const prevDiv = resizer.previousElementSibling;
            const nextDiv = resizer.nextElementSibling;

            // Store the initial mouse position and heights
            startY = e.pageY;
            startHeightPrev = prevDiv.offsetHeight;
            startHeightNext = nextDiv.offsetHeight;

            // Add mousemove and mouseup event listeners
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);

            function mouseMoveHandler(e) {
                const dy = e.pageY - startY;

                // Calculate new heights
                const newHeightPrev = Math.max(50, startHeightPrev + dy);
                const newHeightNext = Math.max(50, startHeightNext - dy);

                // Apply new heights to the sections
                prevDiv.style.height = `${newHeightPrev}px`;
                nextDiv.style.height = `${newHeightNext}px`;
            }

            function mouseUpHandler() {
                // Remove the event listeners when mouse is released
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            }
        });
    });
    document.getElementById('HandleRightPanel').addEventListener('click', function() {
        var sidebar = document.querySelector('.right-sidebar-concepteur');
        var sidebarStyle = window.getComputedStyle(sidebar);
        var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
        if (sidebarStyle.right === '0px') {
            sidebar.style.right = '-319px';
            IconRightPanelToggle.style.transform = 'rotate(-90deg)';
        } else {
            sidebar.style.right = '0px';
            IconRightPanelToggle.style.transform = 'rotate(90deg)';
        }
    });
});
document.addEventListener('DOMContentLoaded', () => {
    const currentThemeDisplay = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const currentThemeIcon = currentThemeDisplay.querySelector('img');
    const currentThemeText = currentThemeDisplay.childNodes[1];

    let currentTheme = localStorage.getItem('theme') || 'dark'||'darkblue'||'lightsand'||'darklight'||"darkpurple";
    document.body.setAttribute('data-theme', currentTheme);

    const currentThemeElement = document.querySelector(`li[data-theme="${currentTheme}"]`);
    if (currentThemeElement) {
      updateThemeDisplay(currentThemeElement);
    }

    currentThemeDisplay.addEventListener('click', () => {
      themeOptions.style.display = themeOptions.style.display === 'block' ? 'none' : 'block';
    });

    themeOptions.addEventListener('click', event => {
      const themeChoice = event.target.closest('li');
      if (themeChoice) {
        const selectedTheme = themeChoice.getAttribute('data-theme');
        const imgSrc = themeChoice.querySelector('img').src;
        const themeName = themeChoice.textContent.trim();

        currentThemeIcon.src = imgSrc;
        currentThemeText.nodeValue = " " + themeName + " ";

        document.body.setAttribute('data-theme', selectedTheme);
        localStorage.setItem('theme', selectedTheme);

        themeOptions.style.display = 'none';

        document.querySelectorAll('.theme-options li').forEach(li => li.classList.remove('active'));
        themeChoice.classList.add('active');
      }
    });
});
function updateThemeDisplay(themeElement) {
    const iconSrc = themeElement.querySelector('img').src;
    const iconName = themeElement.textContent.trim();
    document.querySelector('.current-theme img').src = iconSrc;
    document.querySelector('.current-theme').childNodes[1].nodeValue = " " + iconName + " ";
}
const toggleButtonTable = document.getElementById('toggle-Chart');
const TablePanel = document.getElementById('displayChartPanel');
let isTableVisible = false;
TablePanel.style.bottom = '-330px';

toggleButtonTable.addEventListener('click', function () {
    TablePanel.style.transition='bottom 0.3s ease-in-out';
    TablePanel.style.height = '350px';
    var IcontoggleChart = document.querySelector('.IcontoggleChart');
    if (isTableVisible) {
        TablePanel.style.bottom = '-330px';
        setTimeout(() => IcontoggleChart.style.transform = 'rotate(0deg)', 300);
    } else {
        setTimeout(() => TablePanel.style.bottom = '0', 10);
        IcontoggleChart.style.transform = 'rotate(180deg)';
    }

    isTableVisible = !isTableVisible;
    document.querySelectorAll('.ChartsByType').forEach(element => {
        if (TablePanel.style.height === '350px' || TablePanel.style.height === '') { 
            element.style.height = '100%';
        } else {
            element.style.height = '157px';
        }
    });
    //drawAudienceChart();
    //changeCanvaschartHeight(TablePanel);
});


const openPanelButton = document.getElementById('openPanel');
const closePanelButton = document.getElementById('closePanel');
const productionPanel = document.getElementById('displayproductionPanel');

openPanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '0';
});

closePanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '-100%';
    closePanelButton.style.top='39px';
});
function replaceParams(query) {
    return query.replace("debut=", "dateDebut=").replace("fin=", "dateFin=");
}

const input = document.getElementById('SearchInputTopBar');
const inputGroup = input.closest('.input-group');

input.addEventListener('input', () => {
  inputGroup.classList.toggle('active-border', input.value.trim() !== '');
});

async function recallFunctionUpdatedParam(){
}

document.addEventListener("DOMContentLoaded", async function () {
    let data = HierarchyData;
    const htmlContent = generateHierarchyHtml(data);
    const treeRoot = document.getElementById('tree-root');
    treeRoot.innerHTML = htmlContent;
    setupCaretListeners('tree-root');
    setupSearchFunctionality();
    setupDropdownListeners();
    productionsByDay();
});
function generateHierarchyHtml(data, categoryFilter = null,FilterByEtatCluster = false) {
    allHiearchyData=data;
    let htmlContent = '';
    const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    
    data.forEach(cluster => {
        let clusterTotal;
        if(categoryFilter!==null){
            clusterTotal=cluster.categories[categoryFilter];
        } else{
            clusterTotal = Object.values(cluster.categories).reduce((sum, current) => sum + current, 0);
        }
        let placesContent = '';

        cluster.villes.forEach(place => {

            let totalVenteVille;
            if(categoryFilter!==null){
                totalVenteVille=place.categories[categoryFilter];
            } else{
                totalVenteVille =  Object.values(place.categories).reduce((sum, current) => sum + current, 0);
            }
            if (!categoryFilter || (place.categories[categoryFilter] && place.categories[categoryFilter] > 0)) {
                placesContent += `
                    <li class="field-list-item">
                        <div class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
                            <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                                <div class="fieldLiSpan" style="flex-grow: 1; display: flex; align-items: center;"
                                    data-cluster-code="${cluster.clusterCode}" data-insee-code="${place.code_insee}">
                                    <div style="display: flex; align-items: center;" >
                                        <svg style="height: 12px; width: 12px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d=M12.7,1.1c0-0.6-0.5-1.1-1.1-1.1H1.1C0.5,0,0,0.5,0,1.1v4.7h12.7V1.1z /><path d=M12.7,11.6V6.9H0v4.7c0,0.6,0.5,1.1,1.1,1.1h10.4C12.2,12.7,12.7,12.2,12.7,11.6z /></g></svg>
                                    </div>
                                    ${place.ville}
                                </div>
                                <div class="total-place" style="margin-right: 15px; color: #326E78;">
                                    ${totalVenteVille || 0}
                                </div>
                            </div>
                        </div>
                    </li>
                `;
            }
        });

        if (placesContent !== '') {
            htmlContent += `
                <li class="formListItem">
                    <div class="caret formSpan ClusterSpan" data-CLusterCode="${cluster.clusterCode}"style="display: flex; justify-content: space-between; align-items: center; width: 100%;" > 
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;">
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color); opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                                </div>
                                ${cluster.libelleCluster}
                            </div>
                            <div class="total-cluster" style="display: flex; align-items: center; margin-right: 5px; font-size: 16px;"
                                data-CLusterID="${cluster.cluster_id}" data-CLusterCode="${cluster.clusterCode}" data-CLusterName="${cluster.libelleCluster}">
                                ${clusterTotal}
                            </div>
                        </div>
                    </div>
                    <ul class="nested">${placesContent}</ul>
                </li>
            `;
        }
    });
    return htmlContent;
}
function setupCaretListeners(rootElementId) {
    const rootElement = document.getElementById(rootElementId);
    const togglers = rootElement.querySelectorAll(".caret, .formSpan");
    
    togglers.forEach(caret => {
        caret.addEventListener("click", function(event) {
            event.stopPropagation();
    
            let nestedUl = this.closest('li').querySelector('.nested');
            if (nestedUl) {
                nestedUl.classList.toggle("active");
            }
    
            document.querySelectorAll('.active-field').forEach(el => {
                el.classList.remove('active-field');
            });
            this.classList.add('active-field');
    
            this.classList.toggle("caret-down");
        });
    });
}
function setupSearchFunctionality() {
    const searchInput = document.getElementById('TreeSearch');
    searchInput.addEventListener('input', function() {
        const searchText = this.value.toLowerCase().trim();
        filterFormListItems(searchText);
    });
}
function setupDropdownListeners() {
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    var ClusterFilter = document.querySelector('.ClusterFilter');
    const itemChoosed = document.querySelector('.itemChoosed');
    dropdownItems.forEach(item => {
        item.addEventListener('click', function () {
            var categoryFilter = this.classList[1];
            if (categoryFilter==='TousCluster'){categoryFilter=null;}
            data = HierarchyData[0][Number(year)][Number(month)];
            const htmlContent = generateHierarchyHtml(data, categoryFilter);
            itemChoosed.textContent = this.textContent;
            itemChoosed.setAttribute('categoryFilter', categoryFilter);
            ClusterFilter.style.display = 'none';
            const treeRoot = document.getElementById('tree-root');
            treeRoot.innerHTML = htmlContent;
            setupCaretListeners('tree-root');
            setupSearchFunctionality();
            setupDropdownListeners();
        });
    });
}

function generateCalendarProduction(month, year, currentMonthData) {
    const daysForActualMonth = new Date(year, month, 0).getDate(); // Get the number of days in the month
    let daysProduction = '';

    const firstDay = new Date(year, month - 1, 1); // Get the first day of the month
    let offset = (firstDay.getDay() + 6) % 7; // Calculate the day of the week offset for the first day

    // Add empty divs for the offset to align the start of the month correctly
    for (let i = 0; i < offset; i++) {
        daysProduction += `<div class="DaysProductionTreeView"></div>`;
    }

    // Loop through all the days of the month
    for (let day = 1; day <= daysForActualMonth; day++) {
        const date = new Date(year, month - 1, day); // Create a date object for each day
        let dayValue = currentMonthData[day] || 0; // Get the production data for each day, default to 0 if not available
        let displayValue = dayValue === 0 ? '' : dayValue; // Determine the display value, empty if zero

        // Create a div for each day with onclick event to fetch productions
        daysProduction += `<div class="DaysProductionTreeView OpenContactPanel" onclick="fetchProductionsForOnedays('${day}','${month}', '${year}')">
            <span>${day}</span>
            <div class="DaysDetails" style="text-align: center; height: 22px;">${displayValue}</div>
        </div>`;
    }

    // Set the inner HTML of the calendar container with the days' production
    const calendarContainer = document.querySelector('.ActualMonthProductionsDays');
    calendarContainer.innerHTML = daysProduction;
}

async function productionsByDay(clusterCode= null, codeInsee = null) {
    let data=AgendaData;
    var currentMonthData = data.ventes_par_jour;
    var PrevMonthData = data["ventes_par_jour_mois-1"];
    const dayIndex = String(day-1);
    generateCalendarProduction(parseInt(month), parseInt(year),currentMonthData);

}
/**** */

document.querySelectorAll('#fileUploadForm input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', updateTotales);
});

const colorMap = new Map();

function getColorForCodeIris(code_iris) {
    if (!colorMap.has(code_iris)) {
        // Alterne entre rouge et bleu de manière cohérente
        const color = colorMap.size % 2 === 0 ? '#094044' : '#094044';
        colorMap.set(code_iris, color);
    }
    return colorMap.get(code_iris);
}


const treeViewIcon = document.getElementById('TreeViewIcon');
const fleche = '/discord/treeview/Fleche.svg';
const flecheFields = treeViewIcon ? treeViewIcon.getAttribute('flecheFields') : '';





let map;

document.addEventListener("DOMContentLoaded", function () {
    let theme = localStorage.getItem("theme") || "darkpurple";
    mapboxgl.accessToken = 'pk.eyJ1IjoicmdvdW50aXRpIiwiYSI6ImNtMnA1bHJ5NDBuczcycnNieGsyamVjOTMifQ.FjXmzR2E_Di8YWn8nfTPog';

    function initializeMap(theme) {
        let MapTheme;
        if (theme === "darkpurple") {
            MapTheme = 'mapbox://styles/rgountiti/cm70iodpa01iu01sa8ihf5wch';
        } else if (theme === "darkblue") {
            MapTheme = 'mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw';
        } else {
            MapTheme = 'mapbox://styles/mapbox/light-v10';
        }

        const mapContainer = document.getElementById('map');
        mapContainer.innerHTML = '';

        map = new mapboxgl.Map({
            container: 'map',
            style: MapTheme,
            center: [2.3522, 48.8566],
            zoom: 6
        });
        map.addControl(new mapboxgl.NavigationControl());
    }


    initializeMap(theme);

    setInterval(() => {
        let newTheme = localStorage.getItem("theme");
        if (newTheme !== theme) {
            theme = newTheme;
            if (theme === "darkpurple") {
                location.reload();
            } else if (theme === "light") {
                location.reload();
            }
            else if (theme === "darkblue") {
                location.reload();
            }
            else {
                map.setStyle('mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw'); // Update the map style
            }
        }
    }, 500);
});


document.querySelector('.close-btnInitialDropdown').addEventListener('click', function (event) {
    event.stopPropagation();
    document.getElementById('initialDropdown').style.display = 'none';
});