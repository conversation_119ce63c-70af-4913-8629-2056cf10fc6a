


document.addEventListener('DOMContentLoaded', () => {
    const currentThemeDisplay = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const currentThemeIcon = currentThemeDisplay.querySelector('img');
    const currentThemeText = currentThemeDisplay.childNodes[1];

    let currentTheme = localStorage.getItem('theme') || 'dark'||'darkblue'||'lightsand'||'darklight'||"darkpurple";
    document.body.setAttribute('data-theme', currentTheme);

    const currentThemeElement = document.querySelector(`li[data-theme="${currentTheme}"]`);
    if (currentThemeElement) {
      updateThemeDisplay(currentThemeElement);
    }

    currentThemeDisplay.addEventListener('click', () => {
      themeOptions.style.display = themeOptions.style.display === 'block' ? 'none' : 'block';
    });

    themeOptions.addEventListener('click', event => {
      const themeChoice = event.target.closest('li');
      if (themeChoice) {
        const selectedTheme = themeChoice.getAttribute('data-theme');
        const imgSrc = themeChoice.querySelector('img').src;
        const themeName = themeChoice.textContent.trim();

        currentThemeIcon.src = imgSrc;
        currentThemeText.nodeValue = " " + themeName + " ";

        document.body.setAttribute('data-theme', selectedTheme);
        localStorage.setItem('theme', selectedTheme);

        themeOptions.style.display = 'none';

        document.querySelectorAll('.theme-options li').forEach(li => li.classList.remove('active'));
        themeChoice.classList.add('active');
      }
    });
  });
  
  function updateThemeDisplay(themeElement) {
    const iconSrc = themeElement.querySelector('img').src;
    const iconName = themeElement.textContent.trim();
    document.querySelector('.current-theme img').src = iconSrc;
    document.querySelector('.current-theme').childNodes[1].nodeValue = " " + iconName + " ";
  }
  
  const toggleButtonTable = document.getElementById('toggle-Chart');
  const TablePanel = document.getElementById('displayChartPanel');
  let isTableVisible = false;

  toggleButtonTable.addEventListener('click', function () {
    var IcontoggleChart= document.querySelector('.IcontoggleChart');
      if (isTableVisible) {
          TablePanel.style.bottom = '-218px';
          setTimeout(() => IcontoggleChart.style.transform = 'rotate(0deg)', 300);
          //setTimeout(() => TablePanel.style.display = 'none', 300);
      } else {
          //TablePanel.style.display = 'block';
          setTimeout(() => TablePanel.style.bottom = '0', 10);
          IcontoggleChart.style.transform = 'rotate(180deg)';
      }
      isTableVisible = !isTableVisible;
  });
function changeTab(tabName) {
    const tabs = document.querySelectorAll('.tab');
    const contents = document.querySelectorAll('.contentTab');

    contents.forEach(content => {
        content.style.display = 'none';
        content.classList.remove('activeTab');
    });

    tabs.forEach(tab => {
        tab.classList.remove('activeTab');
    });

    document.getElementById(`content-${tabName}`).style.display = 'block';
    document.getElementById(`content-${tabName}`).classList.add('activeTab');
    document.querySelector(`.tab[onclick="changeTab('${tabName}')"]`).classList.add('activeTab');
}

document.getElementById('HandleRightPanel').addEventListener('click', function() {
    var sidebar = document.querySelector('.right-sidebar-concepteur');
    var sidebarStyle = window.getComputedStyle(sidebar);
    var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');

    if (sidebarStyle.right === '0px') {
        sidebar.style.right = '-319px';
        IconRightPanelToggle.style.transform = 'rotate(-90deg)';
    } else {
        sidebar.style.right = '0px';
        
        IconRightPanelToggle.style.transform = 'rotate(90deg)';
    }
});

// document.addEventListener('DOMContentLoaded', function() {
//     fetchBoards();
// });

function fetchBoards() {
    fetch('/api/boards')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (!data['hydra:member']) {
                throw new Error('No boards data found');
            }
            displayBoards(data['hydra:member']);
        })
        .catch(error => {
            console.error('Error fetching boards:', error);
        });
}
let currentlyBoardId =null;


async function getFormsByBoardId(boardId) {
    const subscriberId = 1;
    try {
        const params = new URLSearchParams({ subscriberId: subscriberId });
        if (boardId) params.append('boardId', boardId);
        const apiUrl = `/api/forms-filter?${params.toString()}`;

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            throw new Error(`Error fetching forms: ${response.statusText}`);
        }

        const data = await response.json();

        const forms = data.resultat;

        return forms;g
    } catch (error) {
        console.error('Error fetching forms by board and subscriber ID:', error);
        return null;
    }
}

async function displayFormsByBoardId(boardId) {
    const Contentarea =document.querySelector('.concepteur-area');
    try {
        const forms = await getFormsByBoardId(boardId);
        globalForms = forms;
        console.log('forms',forms);
        currentlyBoardId=boardId;

        const treeRoot = document.getElementById('formulaireContainer');
        if (forms && forms.length) {
            // fetchFormFileData();
            fetchRoleData();
            processForms();
        } else {
            console.error('No forms to display');
        }
    } catch (error) {
        console.error('Error fetching forms:', error);
    }
}

async function fetchFormFileData(callback) {
    try {
        const response = await fetch('/api/form-import-files/1');
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        displayExampleTreeData(data);
        if (callback) {
            callback();
        }
    } catch (error) {
        console.error('Failed to fetch form data:', error);
    }
}


function displayExampleTreeData(forms) {
    const treeRoot = document.getElementById('tree-root-data');
    const databaseIcon = document.getElementById('TreeViewIcon').getAttribute('databaseIcon');
    const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
    treeRoot.innerHTML = '';

    let htmlContent = '';
    forms.forEach(form => {
        localStorage.setItem(`form_${form.id}`, form.id +' ' + form.nom);
        htmlContent += `
            <li class="formListItem" data-file-id="${form.id}">
                <span class="caret formSpan" onclick="displayFieldsForForm(${form.id});displayGroupFieldsAdapted('${escapeHTML(form.nom)}','${form.id}')" data-group-name="${escapeHTML(form.nom)}" data-fields='${escapeHTML(JSON.stringify(form.wwkAppImportFileStructures))}'>
                    <img src="${fleche}" class="flecheimg"></img>
                    <img src="${databaseIcon}" style="width:17px; margin-right:8px;"></img>${escapeHTML(form.nom)}
                </span>
                <ul class="nested" id="fieldsForForm${form.id}"></ul>
            </li>`
        ;
    });

    treeRoot.innerHTML = htmlContent;
    setupCaretListeners('tree-root-data');
    setupFieldListenersDAta();
}
async function fetchAndDisplayFormFields(formId) {
    console.log('formId:',formId);
    try {
        const response = await fetch(`/api/form-import-file-data/${formId}`);
        if (!response.ok) {
            throw new Error('Failed to fetch fields for form');
        }
        const responseData = await response.json();
        console.log("Received data for formId:", formId, responseData);

        if (!Array.isArray(responseData.data)) {
            console.error("Expected an array of fields, received:", responseData.data);
            return [];
        }
        return responseData.data;
    } catch (error) {
        console.error('Error fetching fields:', error);
        return [];
    }
}

async function displayFieldsForForm(formId) {
    const fieldsContainer = document.getElementById(`fieldsForForm${formId}`);
    if (fieldsContainer.innerHTML !== '') {
        fieldsContainer.style.display = fieldsContainer.style.display === 'none' ? 'block' : 'none';
        return;
    }

    const fieldsData = await fetchAndDisplayFormFields(formId);
    await loadDataForForm({ data: fieldsData }, formId);
    if (!fieldsData || fieldsData.length === 0) {
        fieldsContainer.innerHTML = '<p>No fields available.</p>';
        fieldsContainer.style.display = 'block';
        return;
    }

    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    let fieldsContent = fieldsData.map(field => `
        <li class="field-list-item" data-field-id="${field.id}">
            <span class="fieldLiSpan" style="padding: 4px 0px 1px 34px;">
                <img src="${flecheFields}" style="width: 12px; margin-right: 6px;"></img>${escapeHTML(field.internal_name)}
            </span>
        </li>
    `).join('');

    fieldsContainer.innerHTML = fieldsContent;
    fieldsContainer.style.display = 'block';
}



function setClassNewDataUploaded(formId) {
    const formElements = document.querySelectorAll('.formListItem[data-file-id="' + formId + '"]');
    formElements.forEach(element => {
        element.classList.add('newFormAppended');
    });
}

function escapeHTML(htmlStr) {
    const div = document.createElement('div');
    div.textContent = htmlStr;
    return div.innerHTML;
}


function setupFieldListenersDAta() {
    document.querySelectorAll('.field-name, .field-list-item, .formSpan').forEach(element => {
        element.addEventListener('click', function(event) {
            event.stopPropagation();

            document.querySelectorAll('.field-name, .field-list-item, .formSpan').forEach(el => {
                el.classList.remove('active-field');
            });

            this.classList.add('active-field');

            if (this.classList.contains('formSpan')) {
                console.log("Form span clicked: ", this.textContent);
                localStorage.setItem('clickedTextContent', this.textContent);
            }
        });
    });
}

let isSaveButtonListenerAdded = false;
function toggleStyleChecked(element) {
    const isChecked = element.querySelector('span').style.backgroundColor === "rgb(45, 140, 235)";
    if (isChecked) {
        element.querySelector('span').style.backgroundColor = "";
    } else {
        element.querySelector('span').style.backgroundColor = "rgb(45, 140, 235)";
    }
    updateSaveButtonVisibility();
}

function updateSaveButtonVisibility() {
    const anyChecked = Array.from(document.querySelectorAll('.checkedFieldLine span')).some(
        span => span.style.backgroundColor === "rgb(45, 140, 235)"
    );
    const saveButton = document.getElementById('savefileToForm');
    saveButton.style.display = anyChecked ? 'block' : 'none';
}

async function displayGroupFieldsAdapted(titre,formId) {

    const Contentarea = document.querySelector('.concepteur-area');
    Contentarea.style.display = 'block';
    const container = document.getElementById('fieldsDisplayDetailsContainer');
    container.innerHTML = '';
    const fieldsData = await fetchAndDisplayFormFields(formId);
    console.log('fieldsData', fieldsData);
    setFormTitle(titre);
    displayListFormInForContent();
    toggleFormContent();
    let htmlContent = `
        <div class="group-section">
            <span class="sectionDefault">${titre}</span>
            <div class="groups-section-details">
                <div style="display: flex;flex-direction: column;">
                    <span class="fieldSpan" style="width:25px;height: 26px;"></span>
                    <span class="fieldSpan" style="width:25px;height: 26px;"></span>
                    <span class="fieldSpan" style="width:25px;height: 26px;"></span>
                </div>
                <div class="groups-details" style="width: 200px;">
                    <span class="groupDetailsSpan colorFieldDetails">Name</span>
                    <span class="groupDetailsSpan colorFieldDetails">Description</span>
                    <span class="groupDetailsSpan colorFieldDetails">Nombre de colonne</span>
                </div>
                <div class="groups-details">
                    <span class="groupDetailsSpan spanEditable" onclick="makeEditableGroup(this,'groupName');"></span>
                    <span class="groupDetailsSpan spanEditable" onclick="makeEditableGroup(this,'description');"></span>
                    <span class="groupDetailsSpan spanEditable" onclick="makeEditableGroup(this,'nbrCol');"></span>
                </div>
            </div>
        </div>
        <div class="fieldSectionDetails">
            <span style="width: 200px;margin-left: 25px">Nom Du Champs</span>
            <span style="width: 50%;">Type de données</span>
        </div>
    `;

    fieldsData.forEach(field => {
        htmlContent += `
            <div class="groups-section-details field-name" data-id-field="${field.id}" data-field-name="${field.internal_name}">
                <div class="fieldSpan checkedFieldLine" onclick="toggleStyleChecked(this);">
                    <span style="background-color: var(--nav-link-active-bg);border: 4px solid var(--bg-table-section);"></span>
                </div>
                <span class="fieldSpan colorFieldDetails spanEditable ClickedField" onclick="makeEditable(this);" data-old="${field.internal_name}">${field.internal_name}</span>
                <span class="fieldSpan spanEditable ClickedField" onclick="makeEditable(this);">${field.type}</span>
            </div>
        `;
    });

    container.innerHTML = htmlContent;
    setupFieldClickListeners();
    setupSaveButtonListener(formId, titre);
}


function setupSaveButtonListener(formId, titre) {
    console.log('Current formId and titre:', formId, titre);
    const saveButton = document.getElementById('savefileToForm');

    if (saveButton) {
        const newSaveButton = saveButton.cloneNode(true);
        saveButton.parentNode.replaceChild(newSaveButton, saveButton);

        newSaveButton.addEventListener('click', async () => {
            console.log('Save button clicked');
            const selectedFieldIds = Array.from(
                document.querySelectorAll('.checkedFieldLine span[style*="rgb(45, 140, 235)"]')
            ).map(span => parseInt(span.closest('.field-name').getAttribute('data-id-field'), 10));

            if (selectedFieldIds.length === 0) {
                alert('No fields selected!');
                return;
            }

            const payload = {
                titre: titre,
                idFile: parseInt(formId, 10),
                idSubscriber: 1,
                idBoard: currentlyBoardId,
                idFields: selectedFieldIds,
            };

            console.log('Payload:', payload);
            fetchAndProcessForm(payload);
        });

        console.log('Save button listener added or updated');
    }
}

function setupFieldClickListeners() {
    document.querySelectorAll('.ClickedField').forEach(span => {
        span.addEventListener('click', function() {
            document.querySelectorAll('.ClickedField').forEach(s => {
                s.style.backgroundColor = "";
                s.style.border = "";
                s.style.borderRadius = "";
                s.style.color = "";
            });
            this.style.backgroundColor = "#04395e";
            this.style.border = "1px solid #2D8ceb";
            this.style.borderRadius = "3px";
            this.style.color = "#fff";
        });
    });
}


async function fetchAndProcessForm(payload) {
    const queryParams = new URLSearchParams(payload).toString();
    const url = `/api/form-from-file?${queryParams}`;

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
            },
        });

        if (response.ok) {
            const newForm = await response.json();
            console.log('Received form:', newForm);
            if (newForm && newForm.result && newForm.result.structureJson) {
                if (typeof newForm.result.structureJson === 'string') {
                    newForm.result.structureJson = JSON.parse(newForm.result.structureJson);
                }

                globalForms.push(newForm.result);
                const treeRoot = document.getElementById('tree-root');
                treeRoot.innerHTML += generateFormHtml(newForm.result, true);
                setupCaretListeners('tree-root');
                setupFieldListeners();
                alert('Form saved successfully!');
            } else {
                alert('Failed to process form data.');
            }
        } else {
            const errorResponse = await response.text();
            alert(`Failed to save the form: ${errorResponse}`);
        }
    } catch (error) {
        console.error('Error saving the form:', error);
        alert('An error occurred while saving the form.');
    }
}





async function handleSaveButtonClick() {
    console.log('Save button clicked');
    const element = document.getElementById('savefileToForm');
    if (!element) {
        console.error('Element not found!');
        return;
    }
    console.log('Element inner text:', element.innerText);
    const selectedFieldIds = Array.from(
        document.querySelectorAll('.checkedFieldLine span[style*="rgb(45, 140, 235)"]')
    ).map(span => parseInt(span.closest('.field-name').getAttribute('data-id-field'), 10));

    if (selectedFieldIds.length === 0) {
        alert('No fields selected!');
        return;
    }

    const titre = document.getElementById('currentFormTitle').innerText;
    const formId = document.getElementById('currentFormId').value;
    const payload = {
        titre: titre,
        idFile: formId,
        idSubscriber: 1,
        idBoard: currentlyBoardId,
        idFields: selectedFieldIds,
    };
    await fetchAndProcessForm(payload);
}



function toggleStyleChecked(element) {
    const span = element.querySelector('span');
    const button = document.getElementById('savefileToForm');

    if (span.style.backgroundColor === 'rgb(45, 140, 235)') {
        span.style.backgroundColor = 'var(--checkedFieldLine-span)';
        span.style.border = '4px solid var(--bg-table-section)';
    } else {
        span.style.backgroundColor = '#2D8ceb';
        span.style.border = '4px solid var(--bg-table-section)';
    }

    const checkedFields = document.querySelectorAll('.checkedFieldLine span[style*="rgb(45, 140, 235)"]');
    if (checkedFields.length > 0) {
        button.style.display = 'block';
    } else {
        button.style.display = 'none';
    }
}


let AllRolesData =null;
async function fetchRoleData() {
    try {
        const response = await fetch(`/api/subscribers/1/roles-forms`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const rolesData = await response.json();
        displayRolesTree(rolesData);
        AllRolesData = rolesData;
    } catch (error) {
        console.error('Failed to fetch roles and forms data:', error);
    }
}

function displayRolesTree(rolesData) {
    const treeRoot = document.getElementById('tree-root-roles');
    const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
    treeRoot.innerHTML = '';

    let htmlContent = '';
    console.log(rolesData); 

    Object.entries(rolesData).forEach(([department, roles]) => {
        htmlContent += `<li>
            <span class="caret formSpan field-name" data-department-id="${department}" ondragover="event.preventDefault();" ondrop="dropHandler(event, this)">
                <img src="${fleche}" class="flecheimg"></img>
                <i class="bi bi-people-fill" style="color:#a074c4;font-size:17px;margin-right:2px;margin-left: 0px;width: 25px;" class="dropdown-icon"></i>
                ${department}
            </span>
            <ul class="nested">${generateRolesHtml(roles,1)}</ul>
        </li>`;
    });

    treeRoot.innerHTML = htmlContent;
    setupCaretListeners('tree-root-roles');
    setupFieldListenersRoles();
}

function generateRolesHtml(roles, depth = 0) {
    let content = '';
    roles.forEach(role => {
        const isLeafNode = !role.childs || role.childs.length === 0;
        const paddingLeft = 20 * depth;
        const iconSrc = isLeafNode
            ? document.getElementById('TreeViewIcon').getAttribute('flecheFields') 
            : document.getElementById('TreeViewIcon').getAttribute('fleche');
        const iconStyle = isLeafNode ? 'width: 12px; margin-right: 6px;' : '';
        const backgroundColor = depth % 2 !== 0 ? '' : 'background-color: rgb(1, 57, 94);';

        content += `
        <li style="${backgroundColor}">
            <span class="caret groupSpan roleSpan field-name" style="padding-left: ${paddingLeft}px;" data-role-id="${role.roleId}"
                draggable="true" ondragover="event.preventDefault();" ondrop="dropRoleHandler(event, this)">
                <img src="${iconSrc}" class="${isLeafNode ? '' : 'flecheimg'}" style="${iconStyle}">${role.role}
            </span>
            <ul class="nested">`;

        if (role.forms && role.forms.length > 0) {
            role.forms.forEach(form => {
                content += `
                    <li>
                        <span  onclick="displaySection(${form.formId},${role.roleId})"class="formSpan field-name" data-form-id="${form.formId}" style="padding-left: ${paddingLeft + 20}px; ">
                            <i class="bi bi-list-task" style="padding-left: 5px; color:#ea7362; font-size:17px; margin-right:5px;"></i>
                            ${form.formTitre}
                        </span>
                    </li>`;
            });
        }

        if (role.childs && role.childs.length > 0) {
            content += `${generateRolesHtml(role.childs, depth + 1)}`;
        }

        content += `</ul></li>`;
    });
    return content;
}
let RoleIdbyForm=null;
let currentFormIndex = 0;

function getFormById(formId) {
    const formIndex = globalForms.findIndex(f => f.id === formId);

    if (formIndex !== -1) {
        currentFormIndex = formIndex;
        return globalForms[formIndex];
    } else {
        console.error("Form not found for ID:", formId);
        currentFormIndex = -1;
        return null;
    }
}
function setGroupName(groupName) {
    console.log('setGroupName', groupName);
    currentGroupName=groupName;
    const navTabContentFieldAttribut = document.getElementById('nav-tabContent');
    navTabContentFieldAttribut.style.display = 'none';
}

let formIdForDataTable =null;
let specificRoles =null;
let allRolesData =null;
async function displaySection(formId,roleId) {

    const Contentarea =document.querySelector('.concepteur-area');
    Contentarea.style.display = 'block';
    const form = getFormById(formId);
    formIdForDataTable= formId;
    formDataCurrent=form;


    RoleIdbyForm = roleId;
    console.log('Role ID set for form:', RoleIdbyForm);

    specificRoles = await fetchAndFilterData(formId);
    console.log('Fetched Roles:', specificRoles);
    allRolesData = findAllRolesByFormId(formId, AllRolesData);
    console.log('All Roles:', allRolesData);
    displayViewListForm(formDataCurrent.id);


    if (!form) {
        console.error("Form not found!", formId);
        return;
    }

    const container = document.getElementById('fieldsDisplayDetailsContainer');
    if (!container) {
        console.error("Container not found!");
        return;
    }
    const roleAuthorized = specificRoles;


    setFormTitle(form.titre);
   fetchFormulaire();
    currentGroupName=form.structureJson.groups[0].groupName;
    console.log("currentGroupName",currentGroupName);
    displayListFormInForContent();
    toggleFormContent();
    let htmlContent = `
        <div class="group-section">
            <span class="sectionDefault">${form.titre || 'Form Title'}</span>
            <div class="table-section" >`;

    form.structureJson.groups.forEach((group, index) => {
        htmlContent += `
            <div class="groups-section-header" style="display:flex; cursor: pointer;" 
                onclick="displayFields(${index}, ${formId});setGroupName( '${group.groupName.replace(/'/g, "\\'")}');">
                <span class="fieldSpan" style="width:25px;height: 26px;"></span>
                <div class="groups-details" style="width: 200px;">
                    <span class="groupDetailsSpan colorFieldDetails spanEditable ClickedGroup" onclick="makeEditableGroup(this,'groupName');">${group.groupName}</span>
                </div>
                <div class="groups-details">
                    <span class="groupDetailsSpan spanEditable ClickedGroup" onclick="makeEditableGroup(this,'description');">${group.description || ''}</span>
                </div>
            </div>`;
    });
    htmlContent += getAddNewGroupLineHtml();

    htmlContent += `
        <div class="fieldSectionDetails">
            <span style="width: 200px; margin-left: 25px">Nom Du Champs</span>
            <span style="width: 50%;">Type de données</span>
            <span class="" style="width: 88px;">Display</span>
            <span class="" style="">Enable</span>
        </div>
        <div class="fieldbyGroup" id="fieldbyGroup"></div>
        </div></div>`;
    htmlContent += getAddNewFieldLineHtml();

    htmlContent += `
        <div class="nav-tabs-container">
            <nav>
                <div class="nav nav-tabs Tab-details-fields" id="nav-tab" role="tablist">
                    <button class="nav-link active" id="nav-tab1" data-bs-toggle="tab" data-bs-target="#nav-tab1-pane" type="button" role="tab" aria-controls="nav-tab1-pane" aria-selected="true">Tab1</button>
                    <button class="nav-link" id="nav-tab2" data-bs-toggle="tab" data-bs-target="#nav-tab2-pane" type="button" role="tab" aria-controls="nav-tab2-pane" aria-selected="false">Tab2</button>
                    <button class="nav-link" id="nav-tab3" data-bs-toggle="tab" data-bs-target="#nav-tab3-pane" type="button" role="tab" aria-controls="nav-tab3-pane" aria-selected="false">Tab3</button>
                </div>
            </nav>
            <div class="tab-content" id="nav-tabContent">
                <div class="tab-pane fade show active" id="nav-tab1-pane" role="tabpanel" aria-labelledby="nav-tab1"></div>
                <div class="tab-pane fade" id="nav-tab2-pane" role="tabpanel" aria-labelledby="nav-tab2"></div>
                <div class="tab-pane fade" id="nav-tab3-pane" role="tabpanel" aria-labelledby="nav-tab3"></div>
            </div>
        </div>`;

    container.innerHTML = htmlContent;
    document.querySelectorAll('.ClickedGroup').forEach(span => {
        span.addEventListener('click', function() {
            document.querySelectorAll('.ClickedGroup').forEach(s => {
                s.style.backgroundColor = "";
                s.style.border = "";
                s.style.borderRadius = "";
                s.style.color = "";
            });

            this.style.backgroundColor = "#04395e";
            this.style.border = "1px solid #2D8ceb";
            this.style.borderRadius = "3px";
            this.style.color = "#fff";
        });
    });


    if (form.structureJson.groups.length > 0) {
        displayFields(0, formId);
    }
}

let LastFieldOrdre=null;

function displayFields(groupIndex, formId) {
    const form = getFormById(formId);
    const fieldsContainer = document.getElementById('fieldbyGroup');
    var newFieldInserted = document.getElementById('newFieldInserted');
    if (newFieldInserted) {newFieldInserted.innerHTML = "";}
    fieldsContainer.innerHTML = '';
    console.log('groupIndex:', groupIndex);
    if (!form) {
        console.error('Form not found with ID:${formId}');
        return;
    }

    if (!form.structureJson.groups[groupIndex]) {
        console.error('Group ${groupIndex} not found in form ${formId}');
        return;
    }

    const fields = form.structureJson.groups[groupIndex].fields;

    if (!fields || fields.length === 0) {
        console.error('No fields found in group ${groupIndex} of form ${formId}');
        return;
    }

    const sortedFields = fields.sort((a, b) => parseInt(a.ordre) - parseInt(b.ordre));

    let lastFieldOrdre = null;

    sortedFields.forEach((field, index) => {
        const fieldHtml = `
            <div class="groups-section-details field-name" draggable="true" data-idunique="${field.idUnique}" data-ordre="${field.ordre}" data-field-name="${field.name}" data-index="${index}">
                <div class="fieldSpan checkedFieldLine" style="padding: 0; width:25px; display: flex;" onclick="toggleStyle(this);">
                    <span style="background-color: var(--nav-link-active-bg);border: 4px solid var(--bg-table-section);"></span>
                </div>
                <span class="fieldSpan colorFieldDetails spanEditable ClickedField fieldNameDisplay" data-idunique="${field.idUnique}"
                    onclick="makeEditablefieldDetails(this, 'name', '${field.idUnique}');updateTabsContent('${field.idUnique}');" 
                    data-old="${field.name}">
                    ${field.name}</span>
                <span class="fieldSpan spanEditable ClickedField typeField" data-idunique="${field.idUnique}" style="width: 50%;" data-idunique="${field.idUnique}"
                    onclick="showDropdown(this, 'type', '${field.idUnique}');">
                    ${field.type}</span>
                <div class="fieldSpan" style="display: flex; padding: 0;">
                    <div class="fieldSpan checkedFieldLine DisplayCase" style="margin-right: 68px; padding: 0; width:25px; display: flex;" onclick="showRolesDropdown(this, 'displayed', '${field.idUnique}');">
                        <span style="background-color: var(--nav-link-active-bg); border: 4px solid var(--bg-table-section);"></span>
                    </div>
                    <div class="fieldSpan checkedFieldLine EnableCase" style="flex-grow: unset; padding: 0; width:25px; display: flex;" onclick="showRolesDropdown(this, 'enabled', '${field.idUnique}');">
                        <span style="background-color: var(--nav-link-active-bg); border: 4px solid var(--bg-table-section);"></span>
                    </div>
                </div>
            </div>`
        ;
        fieldsContainer.insertAdjacentHTML('beforeend', fieldHtml);

        lastFieldOrdre = field.ordre;
    });

    LastFieldOrdre = lastFieldOrdre;
    console.log("Last field ordre:", LastFieldOrdre);

    addDraggableHandlers(fieldsContainer, formId, form.structureJson.groups[groupIndex].groupName);
}

let isDroppedInNewSection = false;

function addDraggableHandlers() {
    let dragElements = document.querySelectorAll('.field-name');
    let sectionHeaders = document.querySelectorAll('.groups-section-header');

    dragElements.forEach(element => {
        element.addEventListener('dragstart', handleDragStart);
        element.addEventListener('dragover', handleDragOver);
        element.addEventListener('drop', handleDrop);
        element.addEventListener('dragend', handleDragEnd);
    });

    sectionHeaders.forEach(header => {
        header.addEventListener('dragover', handleSectionDragOver);
        header.addEventListener('drop', handleSectionDrop);
    });
}

function handleDragStart(e) {
    // this.style.opacity = '0.5';
    e.dataTransfer.setData('text/plain', this.dataset.idunique);
    this.classList.add('draggingField');
    isDroppedInNewSection = false;
}

function handleDragOver(e) {
    e.preventDefault();
    const activeElement = document.querySelector('.draggingField');
    const currentElement = e.target.closest('.field-name');

    if (activeElement !== currentElement && currentElement) {
        const generatedRect = currentElement.getBoundingClientRect();
        const offset = e.clientY - generatedRect.top - generatedRect.height / 2;
        const next = offset > 0 ? currentElement.nextSibling : currentElement;
        currentElement.parentNode.insertBefore(activeElement, next);
    }
}

function handleDragEnd(e) {
    if (isDroppedInNewSection) {
        return;
    }

    const draggingElement = document.querySelector('.draggingField');
    if (draggingElement) {
        draggingElement.style.opacity = '1';
        draggingElement.classList.remove('draggingField');

        const container = draggingElement.closest('.fieldbyGroup');
        if (!container) {
            console.error('Unable to find the container for the fields.');
            return;
        }

        const updatedFields = Array.from(container.getElementsByClassName('field-name')).map((element, index) => ({
            idUnique: element.getAttribute('data-idunique'),
            ordre: index + 1
        }));

        updateFieldOrderInBackend(updatedFields);
        console.log('Fields reordered within the same section:', updatedFields);
    }
}

function handleSectionDragOver(e) {
    e.preventDefault();
}

function handleSectionDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    const fieldId = e.dataTransfer.getData('text/plain');
    const targetSection = e.target.closest('.groups-section-header');
    const targetGroupName = targetSection.getAttribute('onclick').match(/'([^']+)'/)[1];

    moveFieldToNewSection(fieldId, targetGroupName);
}

function handleDrop(e) {
    e.preventDefault();
    const draggingElement = document.querySelector('.draggingField');
    const targetElement = e.target.closest('.field-name');
    const targetSection = e.target.closest('.groups-section-header');

    if (draggingElement) {
        draggingElement.classList.remove('draggingField');

        if (targetElement) {
            targetElement.parentNode.insertBefore(draggingElement, targetElement.nextSibling);

            const container = targetElement.closest('.fieldbyGroup');
            const updatedFields = Array.from(container.getElementsByClassName('field-name')).map((element, index) => ({
                idUnique: element.getAttribute('data-idunique'),
                ordre: index + 1
            }));
            updateFieldOrderInBackend(updatedFields);
            console.log('Fields reordered within the same section:', updatedFields);
        } else if (targetSection) {
            const newGroupName = targetSection.getAttribute('onclick').match(/'([^']+)'/)[1];
            moveFieldToNewSection(draggingElement.dataset.idunique, newGroupName);
            isDroppedInNewSection = true;
        }
    }
}

function moveFieldToNewSection(fieldId, newGroupName) {
    const fieldElement = document.querySelector(`[data-idunique="${fieldId}"]`);
    console.log('Element before move:', fieldElement);

    const newSection = document.querySelector(`[onclick*="${newGroupName}"]`)?.nextElementSibling;

    if (fieldElement && newSection) {
        newSection.appendChild(fieldElement);
        console.log('Element after move:', fieldElement);

        if (newSection.contains(fieldElement)) {
            console.log(`Field ${fieldId} is now in the new section`);

            moveField(fieldId, currentGroupName, newGroupName);
           
        } else {
            console.error(`Field ${fieldId} failed to move into the new section`);
        }
    } else {
        console.error("Failed to move the field: Element or target section not found.");
    }
}

function deleteDivByIdUnique(idUnique) {
    const element = document.querySelector(`div[data-idunique="${idUnique}"]`);

    if (element) {
        console.log(`Element with data-idunique="${idUnique}" has been hidden.`);
        element.remove();
    } else {
        console.error(`No div found with data-idunique="${idUnique}".`);
    }
}

function moveField(fieldId, currentGroupName, targetGroupName) {
    console.log(fieldId, currentGroupName, targetGroupName);
    
    if (currentGroupName === targetGroupName) {
        console.error("Cannot move the field to the same section.");
        return;
    }

    const currentGroup = formDataCurrent.structureJson.groups.find(
        group => group.groupName === currentGroupName
    );

    const targetGroup = formDataCurrent.structureJson.groups.find(
        group => group.groupName === targetGroupName
    );

    if (!currentGroup || !targetGroup) {
        console.error("Invalid group names. Please check your inputs.");
        return;
    }

    const fieldIndex = currentGroup.fields.findIndex(
        field => field.idUnique === fieldId
    );

    if (fieldIndex === -1) {
        console.error("Field not found in the current group.");
        return;
    }

    const fieldToMove = currentGroup.fields[fieldIndex];
    currentGroup.fields.splice(fieldIndex, 1);

    targetGroup.fields.push(fieldToMove);

    console.log(
        `Field ${fieldId} has been moved from "${currentGroupName}" to "${targetGroupName}".`
    );

    console.log(formDataCurrent);
    deleteDivByIdUnique(fieldId);
    updateFieldInBackend(fieldId, currentGroupName, targetGroupName);
}

function updateFieldInBackend(fieldId, currentGroupName, targetGroupName) {
    const formId = formDataCurrent.id;
    const url = `/api/forms/${formId}/move-field`;

    const payload = {
        currentGroupName: currentGroupName,
        newGroupName: targetGroupName,
        idUnique: fieldId,
    };

    fetch(url, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Failed to update field in backend. Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Field update successful:', data);
        })
        .catch(error => {
            console.error('Error updating field in backend:', error);
        });
}






// function addDraggableHandlers(container, formId, groupName) {
//     let dragElements = container.getElementsByClassName('field-name');
//     Array.from(dragElements).forEach(element => {
//         element.addEventListener('dragstart', handleDragStart);
//         element.addEventListener('dragover', handleDragOver);
//         element.addEventListener('drop', handleDrop);
//         element.addEventListener('dragend', e => handleDragEnd(e, formId, groupName));
//     });

//     function handleDragStart(e) {
//         this.style.opacity = '0.5';
//         e.dataTransfer.setData('text/html', this.outerHTML);
//         this.classList.add('draggingField');
//     }

//     function handleDragOver(e) {
//         e.preventDefault();
//         const activeElement = container.querySelector('.draggingField');
//         const currentElement = e.target.closest('.field-name');
//         if (activeElement !== currentElement) {
//             const generatedRect = currentElement.getBoundingClientRect();
//             const offset = e.clientY - generatedRect.top - generatedRect.height / 2;
//             const next = (offset > 0) ? currentElement.nextSibling : currentElement;
//             container.insertBefore(activeElement, next);
//         }
//     }

//     function handleDrop(e) {
//         e.stopPropagation();
//     }

//     function handleDragEnd(e, formId, groupName) {
//         const draggingElement = container.querySelector('.draggingField');
//         if (draggingElement) {
//             draggingElement.style.opacity = '1';
//             draggingElement.classList.remove('draggingField');

//             const updatedFields = Array.from(container.getElementsByClassName('field-name')).map((element, index) => ({
//                 idUnique: element.getAttribute('data-idunique'),
//                 ordre: index + 1
//             }));

//             updateFieldOrderInBackend(updatedFields, formId, groupName);
//         }
//     }

// }

function updateFieldOrderInBackend(fields) {
    const formId = formDataCurrent.id;
    console.log('formId:', formId);
    const url = `/api/forms/${formId}/groups/${currentGroupName}/update-fields-order`;

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fields: fields })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.json();
    })
    .then(data => {
        console.log('Field order updated successfully:', data);
        updateLocalFieldOrder(fields, currentGroupName); // Update local state to reflect new order
    })
    .catch(error => {
        console.error('Error updating field order:', error);
    });
}

function updateLocalFieldOrder(fields, currentGroupName) {
    if (formDataCurrent && formDataCurrent.structureJson && formDataCurrent.structureJson.groups) {
        const group = formDataCurrent.structureJson.groups.find(g => g.groupName === currentGroupName);
        if (!group) {
            console.error('Group not found for local update:', currentGroupName);
            return;
        }

        const ordreMap = fields.reduce((map, field) => {
            map[field.idUnique] = field.ordre;
            return map;
        }, {});

        group.fields.forEach(field => {
            if (ordreMap.hasOwnProperty(field.idUnique)) {
                field.ordre = ordreMap[field.idUnique];
            }
        });

        console.log('Local field orders updated:', group.fields);
    }
}


let isDropdownOpen = false;
let lastToggledElement = null;

async function showRolesDropdown(element, actionType, identifierField) {
    const formId = formDataCurrent.id;
    const dropdown = document.getElementById('dropdownRoles');
    if (!dropdown) {
        console.error('Dropdown element not found!');
        return;
    }

    if (isDropdownOpen && lastToggledElement === element) {
        closeDropdown();
        return;
    }

    isDropdownOpen = true;
    lastToggledElement = element;
    dropdown.innerHTML = '';
    dropdown.style.display = 'block';

    const rolesByFieldId = await compileRoleList(actionType, identifierField);

    allRolesData.forEach(role => {
        const matchingRole = rolesByFieldId.find(r => r.roleId === role.roleId);
        createDropdownItem(dropdown, formId, role, matchingRole, identifierField, actionType);
    });

    dropdown.style.position = 'absolute';
    dropdown.style.left = `${element.getBoundingClientRect().left}px`;
    dropdown.style.top = `${element.getBoundingClientRect().bottom}px`;
    dropdown.style.zIndex = '1000';
}

function createDropdownItem(dropdown, formId, role, matchingRole, identifierField, actionType) {
    console.log(dropdown, formId, role, matchingRole, identifierField, actionType);
    const li = document.createElement('li');
    li.className = 'ListInputMessage';
    li.style.display = 'flex';
    li.style.alignItems = 'center';

    const span = document.createElement('span');
    span.textContent = role.role;
    span.setAttribute('idrole', role.roleId);

    const div = document.createElement('div');
    div.className = 'fieldSpan checkedFieldLine';
    div.style.cssText = 'flex-grow: unset; padding: 0; height: 25px; width: 25px; border: 1px solid #323232; display: flex;';

    const innerSpan = document.createElement('span');
    innerSpan.style.cssText = 'background-color: rgb(49, 49, 49); border: 4px solid rgb(22, 22, 22);';

    if (matchingRole.matchesCriteria===true && matchingRole.actionType===actionType) {
        innerSpan.style.backgroundColor = 'rgb(45, 140, 235)';
    }

    div.appendChild(innerSpan);
    li.appendChild(div);
    li.appendChild(span);
    dropdown.appendChild(li);

    div.addEventListener('click', () => toggleRoleSelection(formId, role.roleId, identifierField, actionType, div, matchingRole));
}

function toggleRoleSelection(formId, roleId, identifierField, actionType, div, matchingRole) {
    const newRoleValue = !(matchingRole.matchesCriteria===true && matchingRole.actionType===actionType);
    div.firstChild.style.backgroundColor = newRoleValue ? 'rgb(45, 140, 235)' : 'rgb(49, 49, 49)';
    updateOrCreateRoleSetting(formId, roleId, identifierField, actionType, newRoleValue);
}

function closeDropdown() {
    const dropdown = document.getElementById('dropdownRoles');
    dropdown.style.display = 'none';
    isDropdownOpen = false;
    lastToggledElement = null;
}

document.addEventListener('click', function (event) {
    if (!lastToggledElement) return;
    const dropdown = document.getElementById('dropdownRoles');
    const isClickInsideElement = dropdown.contains(event.target) || lastToggledElement.contains(event.target);
    if (!isClickInsideElement && isDropdownOpen) {
        closeDropdown();
    }
});





function updateOrCreateRoleSetting( formId, roleId, identifierField, actionType, newRoleValue) {
    console.log('updateOrCreateRoleSetting',formId, roleId, identifierField, actionType, newRoleValue)
    let roleExists = false;

    specificRoles.forEach(role => {
        if (role.form.id === formId && role.roleApplicatif.id === roleId && role.identifierField === identifierField) {
            roleExists = true;
            role[actionType] = newRoleValue;
        }
    });
    console.log('Updated role', specificRoles);
    if (!roleExists) {
        const newRole = {
            form: { id: formId },
            roleApplicatif: { id: roleId },
            identifierField: identifierField,
            [actionType]: newRoleValue,
        };
        specificRoles.push(newRole);
        console.log(`Created new role: ${JSON.stringify(newRole)}`);
    }
    postRoleChange(formId, roleId, identifierField, actionType, newRoleValue);
}


async function postRoleChange(formId, roleId, identifierField, actionType, newRoleValue) {
    actionType = actionType === 'display' ? 'displayed' : actionType === 'enable' ? 'enabled' : actionType;
    const postData = {
        subscriberId: 1,
        formId: formId,
        roleId: roleId,
        identifierField: identifierField,
        [actionType]: newRoleValue
    };
    try {
        const response = await fetch('/api/forms-authorization-roles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(postData)
        });
        if (!response.ok) throw new Error('Failed to update role status');
        console.log('Role update successful:', await response.json());
    } catch (error) {
        console.log('Error updating role:', error);
    }
}

async function compileRoleList(actionType, identifierField) {

    //console.log('Compiling role list:', actionType, identifierField);
    if (!Array.isArray(allRolesData)) {
        console.error('Failed to fetch all roles or data format is incorrect');
        return [];
    }

    let resultList = allRolesData.map(role => ({
        roleId: role?.roleId,
        roleTitre: role.role,
        actionType:actionType,
        matchesCriteria: false
    }));

    specificRoles.forEach(filteredRole => {
        if (filteredRole.identifierField === identifierField) {
            const matchingRole = resultList.find(r => r.roleId === filteredRole.roleApplicatif?.id);
            if (matchingRole) {
                matchingRole.matchesCriteria = !!filteredRole[actionType];
            }
        }
    });

    return resultList;
}





async function fetchAndFilterData(formId) {
    const url = `/api/forms-authorization-roles/1/${formId}`;
    try {
        const response = await fetch(url);
        const data = await response.json();

        console.log('Data received:', data);

        if (data.message && data.message === "Aucune ligne trouvée pour cet abonné") {
            console.log("Aucune ligne trouvée pour cet abonné");
            return [];
        }

        if (!response.ok) {
            throw new Error(`Network response was not ok (${response.status})`);
        }

        return data;
    } catch (error) {
        console.error('Failed to fetch and filter data:', error);
        return [];
    }
}



function findAllRolesByFormId(formId, structure, results = []) {
    if (Array.isArray(structure)) {
        structure.forEach(item => findAllRolesByFormId(formId, item, results));
    } else if (typeof structure === 'object' && structure !== null) {
        if (structure.forms && structure.forms.some(form => form.formId === formId)) {
            results.push({ role: structure.role, roleId: structure.roleId });
        }
        Object.values(structure).forEach(value => findAllRolesByFormId(formId, value, results));
    }
    return results;
}






function showDropdown(element, fieldType, fieldId) {
    const dropdown = document.getElementById('dropdownTypes');
    const fieldSpan = document.querySelector(`[data-idunique="${fieldId}"] .typeField`);


    dropdown.style.display = 'block';
    dropdown.style.position = 'absolute';
    dropdown.style.left = `${element.getBoundingClientRect().left}px`;
    dropdown.style.top = `${element.getBoundingClientRect().bottom}px`;
    dropdown.style.zIndex = '1000';
    dropdown.style.bottom = 'auto';

    dropdown.querySelectorAll('.ListInputMessage').forEach(item => {
        item.onclick = async () => {
            const newType = item.textContent.trim();
            fieldSpan.textContent = newType;
            dropdown.style.display = 'none';
            updateTypeFieldTabs(fieldId, newType);
            await updateFieldType(fieldId, newType);
        };
    });

    const outsideClickListener = (event) => {
        if (!dropdown.contains(event.target) && event.target !== element) {
            dropdown.style.display = 'none';
            document.removeEventListener('click', outsideClickListener);
        }
    };
    document.addEventListener('click', outsideClickListener, { once: true });
}
function updateTypeFieldTabs(idUnique, newType) {
    const typeFields = document.querySelectorAll(`.fieldTypeTabs[data-idunique="${idUnique}"]`);

    if (typeFields.length) {
        typeFields.forEach(field => {
            field.textContent = newType;
        });
    } else {
        console.log(`No typeField elements found with idUnique ${idUnique}.`);
    }
}


async function updateFieldType(fieldId, newType) {
    const fieldSpan = document.querySelector(`[data-idunique="${fieldId}"] .typeField`);

    if (fieldSpan) {
        console.log('Field type updated to:', newType);

        await finishEditingField(newType, 'type', fieldId);
    } else {
        console.error('No type field found for:', fieldId);
    }
}


function setupFieldClicks() {
    document.querySelectorAll('.ClickedField').forEach(span => {
        span.addEventListener('click', function() {
            document.querySelectorAll('.ClickedField').forEach(s => {
                s.classList.remove('active-field');
            });
            this.classList.add('active-field');
        });
    });
}

document.addEventListener('click', function(e) {
    if (!e.target.classList.contains('ClickedField')) {
        document.getElementById('dropdownTypes').style.display = 'none';
    }
});










document.addEventListener('DOMContentLoaded', () => {
    const draggables = document.querySelectorAll('.groups-section-details .draggable-row');
    const dropZone = document.querySelector('.groups-section');
    let draggingItem = null;
    let dragPreview = null;

    draggables.forEach(draggable => {
        // Make items draggable
        draggable.setAttribute('draggable', 'true');

        // Start dragging
        draggable.addEventListener('dragstart', (e) => {
            draggingItem = draggable;

            // Create a preview element (clone)
            dragPreview = draggable.cloneNode(true);
            dragPreview.style.opacity = '0.5';
            dragPreview.style.pointerEvents = 'none';
            dragPreview.style.position = 'absolute';
            dragPreview.style.zIndex = '1000';

            document.body.appendChild(dragPreview);

            // Hide original dragging element visually
            setTimeout(() => {
                draggingItem.style.visibility = 'hidden';
            }, 0);

            movePreview(e);
        });

        draggable.addEventListener('drag', (e) => {
            if (dragPreview) {
                movePreview(e);
            }
        });

        draggable.addEventListener('dragend', () => {
            if (draggingItem) {
                draggingItem.style.visibility = 'visible';
            }
            if (dragPreview) {
                document.body.removeChild(dragPreview);
                dragPreview = null;
            }
            draggingItem = null;
        });
    });

    function movePreview(e) {
        if (e.clientY !== 0 && dragPreview) {
            dragPreview.style.top = `${e.clientY + 10}px`;
            dragPreview.style.left = `${e.clientX + 10}px`;
        }
    }
});



/***************************** */

function generateFormHtml(form, isNewForm = false) {
    if (!form || typeof form.structureJson === 'string') {
        try {
            form.structureJson = JSON.parse(form.structureJson);
        } catch (error) {
            console.error('Failed to parse structureJson:', error);
            return '';
        }
    }

    if (!form.structureJson.groups) {
        console.error('Invalid form data: groups are missing', form);
        return '';
    }

    const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    let groupsContent = '';

    form.structureJson.groups.forEach(group => {
        let fieldsContent = '';
        group.fields.forEach(field => {
            fieldsContent += `
            <li class="field-list-item" data-field-name="${field.name}">
                <span class="fieldLiSpan" onclick="updateTabsContent('${escapeJSON(JSON.stringify(field))}');" data-idunique="${field.idUnique}">
                    <img src="${flecheFields}" style="width: 12px; margin-right: 6px;">
                    <span class="fieldNameText">${field.name}</span>
                </span>
            </li>`;
        });

        const groupIcon = group.pictoGroup ? `<i class="material-icons" style="font-size: 14px; vertical-align: middle;">${group.pictoGroup}</i>` : `<i class="material-icons">folder</i>`;

        groupsContent += `
            <li>
                <span class="caret groupSpan" onclick="event.stopPropagation(); formDataCurrent = ${JSON.stringify(form).replace(/"/g, '&quot;')}; displayGroupFields(${JSON.stringify(group).replace(/"/g, '&quot;')},'${escapeHTML(form.titre)}');" style="display: flex; align-items: center; gap: 12px;">
                    ${groupIcon}
                    <span>${group.groupName}</span>
                </span>
                <ul class="nested">${fieldsContent}</ul>
            </li>`;
    });

    const newClass = isNewForm ? " newFormAppended" : "";
    return `
        <li class="formListItem">
            <span onclick="displaySection(${form.id})" class="caret formSpan ${newClass}" data-form-id="${form.id}" data-form-title="${form.titre}" draggable="true" 
                ondragstart="dragStartHandler(event, '${escapeHTML(form.titre)}', '${form.id}');">
                <img src="${fleche}" class="flecheimg"></img>
                <i class="bi bi-list-task"
                    style="cursor: grabbing;color: #ea7362;font-size:17px;margin-right:8px;"></i>${form.titre}
            </span>
            <ul class="nested">${groupsContent}</ul>
        </li>`;
}


function processForms() {
    const forms = globalForms;
    //console.log('last update forms',forms);
    let htmlContent = '';
    forms.forEach(form => {
        htmlContent += generateFormHtml(form);
    });
    requestAnimationFrame(() => {
        const treeRoot = document.getElementById('tree-root');
        treeRoot.innerHTML = htmlContent;
        setupCaretListeners('tree-root');
        setupFieldListeners();
    });
}



function dragStartHandler(event, title, id) {
    const dragData = JSON.stringify({ title, id });
    event.dataTransfer.setData("application/json", dragData);
    event.dataTransfer.effectAllowed = "move";

    const targetElement = event.target.closest('.formSpan');
    document.querySelectorAll('.active-field').forEach(el => {
        el.classList.remove('active-field');
    });

    if (targetElement) {
        targetElement.classList.add('active-field');
    }
}

async function dropRoleHandler(event, element) {
    event.preventDefault();

    const data = event.dataTransfer.getData("application/json");
    const { title, id: formId } = JSON.parse(data);

    const roleId = element.getAttribute('data-role-id');

    try {
        const response = await fetch(`/api/link-role-form/subscribers/1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ formId: formId, roleId: roleId })
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Link created successfully:', result);
        } else {
            console.error('Failed to create link:', response.statusText);
        }
    } catch (error) {
        console.error('Error making API call:', error);
    }

    let parentUl = element.nextElementSibling;
    if (!parentUl || parentUl.tagName !== 'UL') {
        parentUl = document.createElement('ul');
        parentUl.className = 'nested';
        element.parentNode.appendChild(parentUl);
    }

    const newLi = document.createElement('li');
    newLi.style.backgroundColor = 'rgba(0,0,0,0.1)';
    const newPadding = parseInt(element.style.paddingLeft, 10) + 20;

    newLi.innerHTML = `
        <span class="caret groupSpan roleSpan field-name" style="padding-left: ${newPadding}px;" data-role-id="${roleId}-child">
            <i class="bi bi-list-task" draggable="true" style="cursor: grabbing;color: #ea7362;font-size:17px;margin-right:8px;"></i>
            ${title}
        </span>
    `;

    parentUl.appendChild(newLi);

    setupDraggableItems();
}

function setupDraggableItems() {
    document.querySelectorAll('.formSpan').forEach(item => {
        item.draggable = true;
        item.addEventListener('dragstart', (event) => {
            event.dataTransfer.setData("text", item.getAttribute('data-form-title'));
            event.dataTransfer.effectAllowed = "move";
        });
    });
}



/************************************************************** */



function setupFieldListenersRoles() {
    const treeRoot = document.getElementById('tree-root-roles');

    treeRoot.addEventListener('click', function(event) {
        let target = event.target;
        while (target !== treeRoot && !target.classList.contains('roleSpan') && !target.classList.contains('field-name') && !target.classList.contains('field-list-item')) {
            target = target.parentNode;
        }

        if (target === treeRoot) return;

        document.querySelectorAll('.roleSpan, .field-name, .field-list-item').forEach(el => {
            el.classList.remove('active-field');
        });

        target.classList.add('active-field');
    });
}




function escapeJSON(jsonString) {
    return jsonString.replace(/"/g, '&quot;');
}
let formDataCurrent = {};



function escapeHTML(input) {
    if (typeof input !== 'string') {
        console.warn('Input to escapeHTML is not a string:', input);
        return ''; // Default value or handle appropriately
    }
    return input.replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');
}

function setupFieldListeners() {
    document.querySelectorAll('.field-list-item, .groupSpan, .formSpan').forEach(element => {
        element.addEventListener('click', function(event) {
            event.stopPropagation();

            document.querySelectorAll('.active-field').forEach(el => {
                el.classList.remove('active-field');
            });

            this.classList.add('active-field');

            if (this.classList.contains('field-list-item')) {
                const fieldName = this.getAttribute('data-field-name');
                const fields = Array.from(this.closest('ul').querySelectorAll('.field-list-item'));
                const field = fields.find(f => f.getAttribute('data-field-name') === fieldName);

                if (field) {
                    const fieldData = JSON.parse(field.getAttribute('data-field'));
                    updateTabsContent(fieldData);
                }
            }

            else if (this.classList.contains('groupSpan')) {
                const groupData = JSON.parse(this.getAttribute('data-group'));
                //displayGroupFields(groupData);
            }

            else if (this.classList.contains('formSpan')) {
                const formTitle = this.getAttribute('data-form-title');
               // setFormTitle(formTitle);
            }
        });
    });
}

function setupCaretListeners(rootElementId) {
    const rootElement = document.getElementById(rootElementId);
    const togglers = rootElement.querySelectorAll(".caret");
    Array.from(togglers).forEach(function(caret) {
        caret.addEventListener("click", function() {
            this.parentElement.querySelector(".nested").classList.toggle("active");
            this.classList.toggle("caret-down");
        });
    });
}


function setFormTitle(titre) {
    document.getElementById('formTitre').innerHTML = '<i class="bi bi-list-task form-title-icon" style="margin-right: 7px;color: #ea7362;"></i>' + titre;
}



function displayGroupFields(group, titre) {
    const container = document.getElementById('fieldsDisplayDetailsContainer');
    if (!container) {
        console.error('Container with ID "fieldsDisplayDetailsContainer" not found.');
        return;
    }

    setFormTitle(titre);
    let htmlContent = `
        <div class="group-section">
            <span class="sectionDefault"> ${group.groupName}</span>
            <div class="groups-section-details">
                <div style="display: flex;flex-direction: column;">
                    <span class="fieldSpan" style="width:25px;height: 26px;"></span>
                    <span class="fieldSpan" style="width:25px;height: 26px;"></span>
                    <span class="fieldSpan" style="width:25px;height: 26px;"></span>
                </div>
                <div class="groups-details" style="width: 200px;">
                    <span class="groupDetailsSpan colorFieldDetails">Name</span>
                    <span class="groupDetailsSpan colorFieldDetails">Description</span>
                    <span class="groupDetailsSpan colorFieldDetails">Nombre de colonne</span>
                </div>
                <div class="groups-details">
                    <span class="groupDetailsSpan spanEditable" onclick="makeEditableGroup(this,'groupName');">${group.groupName || 'N/A'}</span>
                    <span class="groupDetailsSpan spanEditable" onclick="makeEditableGroup(this,'description');">${group.description || ''}</span>
                    <span class="groupDetailsSpan spanEditable" onclick="makeEditableGroup(this,'nbrCol');">${group.nbrCol || 'N/A'}</span>
                </div>
            </div>
        </div>
        <div class="fieldSectionDetails">
            <span style="width: 200px;margin-left: 25px">Nom Du Champs</span>
            <span style="width: 50%;">Type de données</span>
        </div>
    `;

    group.fields.forEach(field => {
        htmlContent += `
            <div class="groups-section-details field-name" data-idunique="${field.idUnique}" data-field-name="${field.name}">
                <div class="fieldSpan checkedFieldLine" style="padding: 0; width:25px; border:1px solid #323232; display: flex;" onclick="toggleStyle(this);">
                    <span style=""></span>
                </div>
                <span class="fieldSpan colorFieldDetails spanEditable ClickedField"
                      onclick="makeEditablefieldDetails(this, 'name', '${field.idUnique}');updateTabsContent('${escapeJSON(JSON.stringify(field))}');" 
                      data-idunique="${field.idUnique}" data-old="${field.name}">
                      ${field.name}</span>
                <span class="fieldSpan spanEditable ClickedField typeField" 
                      onclick="showDropdown(this, 'type', '${field.idUnique}');">
                      ${field.type}</span>
            </div>
        `;
    });

    setGroupName(group.groupName);
    htmlContent += getAddNewFieldLineHtml();

    let tabsHtml = `
        <nav>
            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                <button class="nav-link active fieldTabs" id="nav-tab1" data-bs-toggle="tab" data-bs-target="#nav-tab1-pane" type="button" role="tab" aria-controls="nav-tab1-pane" aria-selected="true">Tab1</button>
                <button class="nav-link" id="nav-tab2" data-bs-toggle="tab" data-bs-target="#nav-tab2-pane" type="button" role="tab" aria-controls="nav-tab2-pane" aria-selected="false">Tab2</button>
                <button class="nav-link" id="nav-tab3" data-bs-toggle="tab" data-bs-target="#nav-tab3-pane" type="button" role="tab" aria-controls="nav-tab3-pane" aria-selected="false">Tab3</button>
            </div>
        </nav>
        <div class="tab-content" id="nav-tabContent">
            <div class="tab-pane fade show active ListViewForm" id="nav-tab1-pane" role="tabpanel" aria-labelledby="nav-tab1"></div>
            <div class="tab-pane fade ListViewForm" id="nav-tab2-pane" role="tabpanel" aria-labelledby="nav-tab2"></div>
            <div class="tab-pane fade ListViewForm" id="nav-tab3-pane" role="tabpanel" aria-labelledby="nav-tab3"></div>
        </div>
    `;

    htmlContent += tabsHtml;
    container.innerHTML = htmlContent;

    container.addEventListener('click', function(event) {
        let targetElement = event.target.closest('.field-name');
        if (targetElement) {
            const fieldIdUnique = targetElement.getAttribute('data-idunique');
            const field = group.fields.find(f => f.idUnique === fieldIdUnique);
            if (field) {
                updateTabsContent(field);
            } else {
                console.error(`Field with idUnique ${fieldIdUnique} not found.`);
            }
        }
    });

    document.querySelectorAll('.ClickedField').forEach(span => {
        span.addEventListener('click', function() {
            document.querySelectorAll('.ClickedField').forEach(s => {
                s.style.backgroundColor = "";
                s.style.border = "";
                s.style.borderRadius = "";
                s.style.color = "";
            });
            this.style.backgroundColor = "#04395e";
            this.style.border = "1px solid #2D8ceb";
            this.style.borderRadius = "3px";
            this.style.color = "#fff";
        });
    });

    if (group.fields.length > 0) {
        updateTabsContent(group.fields[0]);
    }
}

let currentGroupName=null;
function getAddNewFieldLineHtml() {
    return `
        <div class="groups-section-details field-name add-field-line">
            <span class="fieldSpan colorgroupspan" style="background-color: var(--nav-link-active-bg);border: 4px solid var(--bg-table-section);"></span>
            <span class="fieldSpan colorFieldDetails spanEditable ClickedField" tabindex="0" onclick="makeEditableAndAddNewLineField(this);" style="">
                <i class="bi bi-plus" style="color: #fff;"></i>
            </span>
            <span class="fieldSpan"></span>
        </div>
    `;
}

function makeEditableAndAddNewLineField(element,groupName) {
    const originalHtml = '<i class="bi bi-plus" style="color: #fff;"></i>';
    element.innerHTML = '';
    element.contentEditable = true;
    element.focus();

    element.onkeypress = function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const fieldName = element.textContent.trim();
            if (!fieldName) {
                alert("Field name cannot be empty.");
                resetEditableField(element, originalHtml);
                return;
            }
            addNewfieldLine(fieldName,groupName);
            resetEditableField(element, originalHtml);
        }
    };

    element.onblur = function() {
    };
}





function addNewGroupLine(groupName) {
    const formId = formDataCurrent.id||formIdForDataTable;
    console.log(formId);
    const newFieldData = {
        groupName: groupName,
    };

    const url = `/api/forms/${formId}/groups`;

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(newFieldData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(`Failed to add field: ${data.error}`);
            return;
        }

    })
    .catch(error => {
        console.error('Error adding field:', error);
        alert('Error adding field: ' + error.message);
    });
}

function addNewfieldLine(fieldName) {
    const formId = formDataCurrent.id||formIdForDataTable;
    console.log(formId);
    const newFieldData = {
        name: fieldName,
        type: 'text',
        ordre:LastFieldOrdre+1
    };

    const url = `/api/forms/${formId}/groups/${currentGroupName}/fields`;

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(newFieldData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(`Failed to add field: ${data.error}`);
            return;
        }
        updateUIWithNewField(fieldName,data.fieldIdUnique,data.ordre);
        AddNewField(data.fieldIdUnique, data.fieldName,data.ordre);
    })
    .catch(error => {
        console.error('Error adding field:', error);
        alert('Error adding field: ' + error.message);
    });
}

function updateUIWithNewField(fieldName, fieldIdUnique,ordre) {
    let field = { name: fieldName, idUnique: fieldIdUnique, type: 'text',ordre:ordre };
    LastFieldOrdre=ordre;
    const newFieldDiv = document.createElement('div');
    newFieldDiv.className = 'groups-section-details field-name';
    newFieldDiv.id='newFieldInserted';
    newFieldDiv.setAttribute('data-idunique', fieldIdUnique);
    newFieldDiv.setAttribute('data-field-name', fieldName);
    newFieldDiv.setAttribute('data-ordre', ordre);


    newFieldDiv.innerHTML = `
        <div class="fieldSpan checkedFieldLine" style="padding: 0; width:25px;  display: flex;" onclick="toggleStyle(this);">
            <span style="background-color: var(--nav-link-active-bg);border: 4px solid var(--bg-table-section);"></span>
        </div>
        <span class="fieldSpan colorFieldDetails spanEditable ClickedField" 
            onclick="makeEditablefieldDetails(this, 'name', '${fieldIdUnique}'); updateTabsContent('${fieldIdUnique}');" 
            data-old="${fieldName}">
            ${fieldName}
        </span>
        <span class="fieldSpan spanEditable ClickedField typeField" style="width: 50%;"
            onclick="showDropdown(this, 'type', '${fieldIdUnique}');">
            text
        </span>
        <div class="fieldSpan"style="display: flex;padding: 0;">
            <div class="fieldSpan checkedFieldLine DisplayCase" style="margin-right: 68px; padding: 0; width:25px; display: flex;" onclick="showRolesDropdown(this, 'displayed', '${field.idUnique}');">
                <span style="background-color: var(--nav-link-active-bg); border: 4px solid var(--bg-table-section);"></span>
            </div>
            <div class="fieldSpan checkedFieldLine EnableCase" style="flex-grow: unset; padding: 0; width:25px;  display: flex;" onclick="showRolesDropdown(this, 'enabled', '${field.idUnique}');">
                <span style="background-color: var(--nav-link-active-bg); border: 4px solid var(--bg-table-section);"></span>
            </div>
        </div>
    `;

    const addFieldLine = document.querySelector('.add-field-line');

    addFieldLine.parentNode.insertBefore(newFieldDiv, addFieldLine);

    makeEditableAndAddNewLineField(addFieldLine.childNodes[3]);
}







function toggleStyle(element) {
    const span = element.querySelector('span');
    if (span.style.backgroundColor === 'rgb(45, 140, 235)') {
        span.style.backgroundColor = 'var(--nav-link-active-bg)';
        span.style.border = '4px solid var(--bg-table-section)';
    } else {
        span.style.backgroundColor = '#2D8ceb';
        span.style.border = '4px solid var(--bg-table-section)';
    }
}

function makeEditable(element) {
    element.setAttribute('contenteditable', 'true');
    element.focus();

    element.onblur = function() {
    };
    element.onkeypress = function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            element.blur();
            finishEditing(element);
        }
    };
}

function makeEditableGroup(element, attribute) {
    element.setAttribute('contenteditable', 'true');
    element.focus();
    //element.style.background="transparent";
    element.setAttribute('data-old', element.textContent);

    element.onblur = function() {
    };
    element.onkeypress = function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            element.blur();
            finishEditingGroup(element, attribute);
        }
    };
}

function finishEditingGroup(element, attribute) {
    const newValue = element.textContent.trim();
    console.log(attribute, newValue);

    element.removeAttribute('contenteditable');
    element.onblur = null;
    element.onkeypress = null;

    if (formDataCurrent && formDataCurrent.structureJson && formDataCurrent.structureJson.groups) {
        let groupUpdated = false;
        formDataCurrent.structureJson.groups.forEach(group => {
            if (group.groupName === currentGroupName) {
                group[attribute] = newValue;
                groupUpdated = true;
            }
        });
        if (groupUpdated) {
            const formIndex = globalForms.findIndex(f => f.id === formDataCurrent.id);
            if (formIndex !== -1) {
                globalForms[formIndex] = {...globalForms[formIndex], ...formDataCurrent};
                console.log(`globalForms updated for form ${formDataCurrent.id}`);
            }
            processForms();
            updateGroupDetailsInBackend(currentGroupName, attribute, newValue);
            displayListFormInForContent();
        } else {
            console.log("No matching group found or no updates made.");
        }
    } else {
        console.error("Invalid form data or structure missing");
    }
}

function updateGroupDetailsInBackend(attribute, newValue) {
    if(attribute ==='groupName') {currentGroupName=newValue;}
    const formId = formDataCurrent.id;

    const url = `/api/forms/${formId}/UpdateGroup`;
    const data = {
        OldgroupName: currentGroupName,
        [attribute]: newValue
    };

    console.log(`Sending update to backend for group ${currentGroupName} with data:`, data);
    fetch(url, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            console.error(`Failed to update group: ${data.error}`);
            alert(`Failed to update group: ${data.error}`);
        } else {
            console.log('Group updated successfully:', data.message);
            alert('Group updated successfully');
        }
    })
    .catch(error => {
        console.error('Error updating group:', error);
        alert('Error updating group: ' + error.message);
    });
}



function makeEditablefieldDetails(element, attribute, fieldIdUnique) {
    const dropdown = document.getElementById('dropdownTypes');
    dropdown.style.display = 'none';
    element.setAttribute('contenteditable', 'true');
    element.focus();

    element.onblur = function() {
    };

    element.onkeypress = async function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            element.blur();
            await finishEditingField(element, attribute, fieldIdUnique);
        }
    };
}



async function finishEditingField(elementOrValue, attribute, fieldIdUnique) {
    let newValue;
    let oldValue;

    console.log('elementOrValue',elementOrValue);

    if (typeof elementOrValue === 'string') {
        newValue = elementOrValue.trim();
        oldValue = '';
    } else {
        newValue = elementOrValue.textContent.trim();
        oldValue = elementOrValue.getAttribute('data-old-value');
    }

    if (newValue !== oldValue) {
        console.log(`Updating field ${attribute} to ${newValue}`);

        if (formDataCurrent && formDataCurrent.structureJson && formDataCurrent.structureJson.groups) {
            formDataCurrent.structureJson.groups.forEach(group => {
                group.fields.forEach(field => {
                    if (field.idUnique === fieldIdUnique) {
                        console.log(`Found field to update:`, field);
                        field[attribute] = newValue;
                    }
                });
            });

            const formIndex = globalForms.findIndex(f => f.id === formDataCurrent.id);
            if (formIndex !== -1) {
                globalForms[formIndex] = { ...globalForms[formIndex], ...formDataCurrent };
                console.log(`globalForms updated for form ${formDataCurrent.id}`);
            }

            try {
             updateFieldValueInBackend(fieldIdUnique, attribute, newValue);
            } catch (error) {
                console.error('Failed to update field value in backend:', error);
            }

            processForms();
            displayListFormInForContent();
        }
    }

    if (typeof elementOrValue !== 'string') {
        elementOrValue.removeAttribute('contenteditable');
        elementOrValue.onblur = null;
        elementOrValue.onkeypress = null;
    }
}

async function updateFieldValueInBackend(fieldIdUnique, attribute, value) {
    console.log(`Updating field ${attribute} to ${value}`);

    if (value === "true") {
        value = true;
    } else if (value === "false") {
        value = false;
    }

    const url = `/api/forms/${formDataCurrent.id}/fields/${fieldIdUnique}`;
    const data = { [attribute]: value };

    if (attribute === 'name') {
        updateFieldNameDisplayAndTabs(fieldIdUnique, value);
    }

    try {
        const response = await fetch(url, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok) {
            console.log(`Field updated successfully:`, result);
        } else {
            console.error('Failed to update field:', result.error || response.statusText);
        }
    } catch (error) {
        console.error('Error updating field:', error);
    }
}




function UpdateForm() {
    const url = `/api/forms/${formDataCurrent.id}`;
    const data = { [attribute]: value };

    fetch(url, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formDataCurrent)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            console.error('Failed to update form:', data.error);
        } else {
            console.log(`form updated successfully:`, data);
        }
    })
    .catch(error => {
        console.error('Error updating field:', error);
    });
}

function makeEditablefield(element) {
    element.setAttribute('contenteditable', 'true');
    element.focus();

    element.onblur = function() {
    };
    element.onkeypress = function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            element.blur();
            finishEditingfield(element);
        }
    };
}
function finishEditing(element) {
    element.removeAttribute('contenteditable');

    if (element.textContent.trim() !== element.getAttribute('data-old')) {
        console.log('New value:', element.textContent);
    }
}

function finishEditingfield(element) {
    console.log('Finished editing', element);
    element.removeAttribute('contenteditable');
    let newValue = element.textContent.trim();
    let oldVal = element.getAttribute('data-old');
    let fieldIdUnique = element.getAttribute('data-field-idUnique');
    let fieldName = element.getAttribute('data-field-name');
    if (newValue !== oldVal) {
        console.log('New value:', newValue);
        updateFieldValue(fieldIdUnique, fieldName, newValue);
    }
}

function updateFieldValue(fieldIdUnique, fieldName, newValue) {
   const dataTableId=formIdForDataTable;
    const url = `/api/forms/${dataTableId}/fields/${fieldIdUnique}`;
    const data = { name: newValue };
    console.log('data', data);

    fetch(url, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log(`Field ${fieldName} updated to ${newValue}:`, data);
        updateFieldDisplayName(fieldIdUnique, newValue);
    })
    .catch(error => console.error('Error updating field:', error));
}
function updateFieldDisplayName(fieldIdUnique, newName) {

    if (formDataCurrent && formDataCurrent.structureJson && formDataCurrent.structureJson.groups) {
        formDataCurrent.structureJson.groups.forEach(group => {
            group.fields.forEach(field => {
                if (field.idUnique === fieldIdUnique) {
                    field.name = newName;
                    console.log(`formDataCurrent updated for field ${fieldIdUnique}`);
                }
            });
        });

        const formIndex = globalForms.findIndex(f => f.id === formDataCurrent.id);
        if (formIndex !== -1) {
            globalForms[formIndex] = {...globalForms[formIndex], ...formDataCurrent};
            console.log(`globalForms updated for form ${formDataCurrent.id}`);
        }

        processForms();
        displayListFormInForContent();
    }
}

function AddNewField(fieldIdUnique, newName, ordre) {
    if (!formDataCurrent || !formDataCurrent.structureJson || !formDataCurrent.structureJson.groups) {
        console.error("Invalid form data or structure not loaded.");
        return;
    }

    let groupFound = false;

    formDataCurrent.structureJson.groups.forEach(group => {
        if (group.groupName === currentGroupName) {
            groupFound = true;
            if (!group.fields) {
                group.fields = [];
            }
            group.fields.push({
                idUnique: fieldIdUnique,
                name: newName,
                type: 'text',
                ordre: ordre
            });
            console.log(`Added new field to group '${group.groupName}' with idUnique '${fieldIdUnique}' and name '${newName}'`);
        }
    });

    if (!groupFound) {
        console.error(`Group '${groupName}' not found in the form structure.`);
        return;
    }

    const formIndex = globalForms.findIndex(f => f.id === formDataCurrent.id);
    if (formIndex !== -1) {
        globalForms[formIndex] = {...globalForms[formIndex], ...formDataCurrent};
        console.log(`globalForms updated for form ${formDataCurrent.id}`);
    } else {
        console.error(`Form with ID ${formDataCurrent.id} not found in globalForms.`);
    }

    processForms();
    displayListFormInForContent();
}

function updateTabsContent(fieldIdUnique) {
    const navTabContentFieldAttribut = document.getElementById('nav-tabContent');
    navTabContentFieldAttribut.style.display = 'block';
    if (typeof fieldIdUnique !== 'string') {fieldData=fieldIdUnique;}
    else{fieldData=findFieldByIdUnique(fieldIdUnique);}
    
    let field;
    if (typeof fieldData === 'string') {
        try {
            field = JSON.parse(fieldData);
        } catch (error) {
            console.error('Failed to parse fieldData:', error);
            return;
        }
    } else {
        field = fieldData;
    }

    console.log('Field:',fieldIdUnique, field );

    const {
                name = 'N/A',
                description = 'N/A',
                placeholder = 'N/A',
                required = false,
                type = 'N/A',
                options = [],
                filtre = 'N/A',
                afficherDansTable = false,
                position = 'N/A, N/A',
                colored = 'none'
            } = field;

    const positionParts = position.split(',');

    const tab1HTML = buildTab1Content(field);
    document.getElementById('nav-tab1-pane').innerHTML = tab1HTML;

    const tab2HTML = buildTab2Content(field);
    document.getElementById('nav-tab2-pane').innerHTML = tab2HTML;

    const tab3HTML = buildTab3Content(field, positionParts);
    document.getElementById('nav-tab3-pane').innerHTML = tab3HTML;

}

function buildTab1Content(field) {
    return `
       <div class="groups-section-details">
            <div class="fieldSpan checkedFieldLine"></div>
            <span class="fieldSpan colorFieldDetails ">Title</span>
            <span class="fieldSpan spanEditable fieldNameTabs"  data-idunique="${field.idUnique}"data-field-attribute="name" onclick="makeEditablefieldDetails(this, 'name','${field.idUnique}');">${field.name}</span>
        </div>
        <div class="groups-section-details">
            <div class="fieldSpan checkedFieldLine"></div>
            <span class="fieldSpan colorFieldDetails">Description</span>
            <span class="fieldSpan spanEditable" data-field-attribute="description" onclick="makeEditablefieldDetails(this, 'description','${field.idUnique}');">${field.description}</span>
        </div>
        <div class="groups-section-details">
            <div class="fieldSpan checkedFieldLine"></div>
            <span class="fieldSpan colorFieldDetails">Placeholder</span>
            <span class="fieldSpan spanEditable" data-field-attribute="placeholder" onclick="makeEditablefieldDetails(this, 'placeholder','${field.idUnique}');">${field.placeholder}</span>
        </div>
        <div class="groups-section-details">
            <div class="fieldSpan checkedFieldLine"></div>
            <span class="fieldSpan colorFieldDetails">Required</span>
            <div class="fieldSpan spanEditable">
            <label class="switch">
<input type="checkbox" class="toggle-required" data-idunique="${field.idUnique}" ${field.required ? 'checked' : ''} onchange="updateRequiredField(this, 'required', '${field.idUnique}')">
                <span class="slider"></span>
            </label>
            </div>
        </div>

       <div class="groups-section-details">
            <div class="fieldSpan checkedFieldLine"></div>
            <span class="fieldSpan colorFieldDetails">Type</span>
            <span class="fieldSpan spanEditable fieldTypeTabs" data-idunique="${field.idUnique}" data-field-attribute="type" onclick="toggleDropdownULInputType(this, '${field.idUnique}');" style="flex: 1;">${field.type}</span>
            <ul id="dropdown-${field.idUnique}" class="ULInputType" style="display:none;position: absolute; z-index: 1000;bottom: auto;">
                <li class="ListInputMessage"><span>Text</span></li>
                <li class="ListInputMessage"><span>Long Texte</span></li>
                <li class="ListInputMessage"><span>Email</span></li>
                <li class="ListInputMessage"><span>Date</span></li>
                <li class="ListInputMessage"><span>Date RDV</span></li>
                <li class="ListInputMessage"><span>Addresse</span></li>
            </ul>
        </div>

        ${field.type === 'options' && options.length ? `
        <div class="groups-section-details">
            <span class="fieldSpan colorFieldDetails">Options</span>
            <span class="fieldSpan spanEditable" data-field-attribute="options" onclick="makeEditablefieldDetails(this, 'options','${field.idUnique}');">${options.join(', ')}</span>
        </div>` : ''}
    `;
}
function buildTab2Content(field) {
return `<div class="groups-section-details">
            <span class="fieldSpan checkedFieldLine" style="width:25px;"></span>
            <span class="fieldSpan colorFieldDetails">Filter</span>
            <div class="fieldSpan spanEditable">
            <label class="switch ">
<input type="checkbox" class="toggle-required" data-idunique="${field.idUnique}" ${field.filtre ? 'checked' : ''} onchange="updateRequiredField(this, 'filtre', '${field.idUnique}')">
                <span class="slider "></span>
            </label>
            </div>
        </div>
        <div class="groups-section-details">
            <span class="fieldSpan checkedFieldLine" style="width:25px;"></span>
            <span class="fieldSpan colorFieldDetails">Show in List</span>
            <div class="fieldSpan spanEditable">
            <label class="switch">
<input type="checkbox" class="toggle-required" data-idunique="${field.idUnique}" ${field.afficherDansTable ? 'checked' : ''} onchange="updateRequiredField(this, 'afficherDansTable', '${field.idUnique}')">
                <span class="slider"></span>
            </label>
            </div>
        </div>
`
}
function buildTab3Content(field,positionParts) {
    return `
        <div class="groups-section-details">
            <div class="fieldSpan checkedFieldLine"></div>
            <span class="fieldSpan colorFieldDetails"style="width: 173px;">Colored</span>
            <div class="fieldSpan checkedFieldLine" id="ColoredPicker" style="cursor:pointer;" onclick="setColoredPickerStyle(this);"></div>
            <span class="fieldSpan spanEditable" data-field-attribute="colored" onclick="toggleDropdownULInputColored(this, '${field.idUnique}');" style="flex: 1;">
                ${field.Colored || 'None'}
            </span>
            <ul id="dropdown-colored-${field.idUnique}" class="ULInputType" style="display:none;position: absolute; z-index: 1000;bottom: auto;">
                <li class="ListInputMessage"><span>None</span></li>
                <li class="ListInputMessage"><span>Border</span></li>
                <li class="ListInputMessage"><span>Background</span></li>
            </ul>
        </div>

        <div class="groups-section-details">
            <span the="fieldSpan" style="width:25px;"></span>
            <span class="fieldSpan colorFieldDetails">Line</span>
            <span class="fieldSpan spanEditable" data-field-attribute="line" onclick="makeEditablefieldDetails(this, 'line','${field.idUnique}');">${positionParts[0]}</span>
        </div>
        <div class="groups-section-details">
            <span class="fieldSpan" style="width:25px;"></span>
            <span class="fieldSpan colorFieldDetails">Column</span>
            <span class="fieldSpan spanEditable" data-field-attribute="column" onclick="makeEditablefieldDetails(this, 'column','${field.idUnique}');">${positionParts[1]}</span>
        </div>
    `;
}


function findFieldByIdUnique(idUnique, structure = formDataCurrent.structureJson) {
    if (!structure || !structure.groups) return null;

    for (let group of structure.groups) {
        if (group.fields) {
            for (let field of group.fields) {
                if (field.idUnique === idUnique) {
                    return field;
                }
            }
        }
        if (group.groups) {
            const result = findFieldByIdUnique(idUnique, group);
            if (result) return result;
        }
    }
    return null;
}


async function updateRequiredField(checkboxElement, attribute, idUnique) {
    const isChecked = checkboxElement.checked;

    const slider = checkboxElement.nextElementSibling;
    if (slider) {
        slider.classList.add('loading');
    }

    console.log(`Attempting to update ${attribute} for field ID: ${idUnique} to ${isChecked}`);

    try {
        console.log(checkboxElement, attribute, idUnique);
        await finishEditingField(String(isChecked), attribute, idUnique);
        console.log(`Field updated successfully: ${idUnique}`);
        if (slider) {
            slider.classList.remove('loading');
        }
    } catch (error) {
        console.error(`Operation failed: ${error}`);
        checkboxElement.checked = !isChecked;
        if (slider) {
            slider.classList.remove('loading');
        }
    }
}




function setColoredPickerStyle(element) {
    element.style.border = '2px solid rgb(82, 162, 232)';
    element.style.borderRadius = '0';
    element.style.zIndex = '2';
    if (window.activePicker && window.activePicker !== element) {
        resetPickerStyle(window.activePicker);
    }
    window.activePicker = element;
}


function resetPickerStyle(element) {
    element.style.border = '';
    element.style.borderRadius = '';
    element.style.zIndex = '';
    element.style.backgroundColor = '';
}

document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('#color-palette div').forEach(color => {
        color.addEventListener('click', function() {
            if (window.activePicker) {
                const bgColor = window.getComputedStyle(this).backgroundColor;
                window.activePicker.style.backgroundColor = bgColor;
            }
        });
    });
});


function toggleDropdownULInputType(element, idUnique) {
    const dropdown = element.nextElementSibling;

    dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';

    if (dropdown.style.display === 'block') {
        const rect = element.getBoundingClientRect();
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        const scrollLeft = window.scrollX || document.documentElement.scrollLeft;

        const leftB = rect.left + scrollLeft - 310;
        const topB = rect.bottom + scrollTop - 43;

        dropdown.style.position = 'absolute';
        dropdown.style.top = `30px`;

        setupDropdownItemClickHandlers(dropdown, element, idUnique);

        const outsideClickListener = (event) => {
            if (!dropdown.contains(event.target) && event.target !== element) {
                dropdown.style.display = 'none';
                document.removeEventListener('click', outsideClickListener);
            }
        };

        document.addEventListener('click', outsideClickListener);
    }
}
function updateFieldNameDisplayAndTabs(idUnique, newType) {
    const nameFieldTabs = document.querySelectorAll(`.fieldNameTabs[data-idunique="${idUnique}"]`);
    const nameFieldDisplaySection = document.querySelectorAll(`.fieldNameDisplay[data-idunique="${idUnique}"]`);

    const updateTextContent = (elements, newValue) => {
        elements.forEach(element => {
            element.textContent = newValue;
            console.log(`Updated to: ${newValue}`);
        });
    };

    if (nameFieldTabs.length) {
        updateTextContent(nameFieldTabs, newType);
    } else {
        console.log(`No fieldNameTabs elements found with idUnique ${idUnique}.`);
    }

    if (nameFieldDisplaySection.length) {
        updateTextContent(nameFieldDisplaySection, newType);
    } else {
        console.log(`No fieldNameDisplay elements found with idUnique ${idUnique}.`);
    }
}






function closeDropdownOnOutsideClick(event, dropdown, triggerElement) {
    if (!dropdown.contains(event.target) && event.target !== triggerElement) {
        dropdown.style.display = 'none';
    }
}

function setupDropdownItemClickHandlers(dropdown, relatedSpan, idUnique) {
    dropdown.querySelectorAll('.ListInputMessage span').forEach((item) => {
        item.onclick = (event) => {
            updateTypeFieldDisplay(idUnique, item.textContent.trim())
            updateFieldTypeForTabs(idUnique, item.textContent.trim(), relatedSpan);

            dropdown.style.display = 'none';

        };
    });
}

async function updateFieldTypeForTabs(fieldId, newType, typeSpan) {
    console.log('Updating field type for:', typeSpan);
    if (typeSpan) {
        typeSpan.textContent = newType;
        await finishEditingField(newType, 'type', fieldId);
    } else {
        console.error('Type field not found for:', fieldId);
    }
}
function updateTypeFieldDisplay(idUnique, newType) {
    const typeFields = document.querySelectorAll(`.typeField[data-idunique="${idUnique}"]`);

    if (typeFields.length) {
        typeFields.forEach(field => {
            field.textContent = newType;
        });
    } else {
        console.log(`No typeField elements found with idUnique ${idUnique}.`);
    }
}






function toggleDropdownULInputColored(element, idUnique) {
    const dropdown = element.nextElementSibling;

    dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';

    if (dropdown.style.display === 'block') {
        const rect = element.getBoundingClientRect();
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        const scrollLeft = window.scrollX || document.documentElement.scrollLeft;

        const leftB = rect.left + scrollLeft - 310;
        const topB = rect.bottom + scrollTop - 43;

        dropdown.style.position = 'absolute';
        dropdown.style.left = `${leftB}px`;
        dropdown.style.top = `${topB}px`;
        dropdown.style.zIndex = '1000';

        setupDropdownItemClickHandlersColored(dropdown, element, idUnique);

        const outsideClickListener = (event) => {
            if (!dropdown.contains(event.target) && event.target !== element) {
                dropdown.style.display = 'none';
                document.removeEventListener('click', outsideClickListener);
            }
        };

        document.addEventListener('click', outsideClickListener);
    }
}
function setupDropdownItemClickHandlersColored(dropdown, relatedSpan, idUnique) {
    dropdown.querySelectorAll('.ListInputMessage span').forEach((item) => {
        item.onclick = async (event) => {
            const newValue = item.textContent.trim();

            relatedSpan.textContent = newValue;

            dropdown.style.display = 'none';

            await updateFieldColoredForTabs(idUnique, newValue, relatedSpan);
        };
    });
}
async function updateFieldColoredForTabs(fieldId, newValue, coloredSpan) {
    console.log('Updating colored field for:', coloredSpan);

    if (coloredSpan) {
        coloredSpan.textContent = newValue;

        await finishEditingField(newValue, 'Colored', fieldId);
    } else {
        console.error('Colored field not found for:', fieldId);
    }
}





/******  Create new Section */

function getAddNewGroupLineHtml() {
    return `
        <div class="group-add-section" style="display:flex;">
            <span class="groupSpan colorgroupspan" style=""></span>
            <span class="groupDetailsSpan colorGroupDetails spanEditable" tabindex="0" onclick="makeEditableAndAddNewGroup(this);" style="width: 249px;">
                <i class="bi bi-plus-lg" style="color: #fff;"></i>
            </span>
            <div class="groups-details">
                <span class="groupDetailsSpan spanEditable spanEditable"></span>
            </div>
        </div>
    `;
}

function makeEditableAndAddNewGroup(element) {
    const originalHtml = '<i class="bi bi-plus-lg" style="color: #fff;"></i>';
    element.innerHTML = '';
    element.contentEditable = true;
    element.focus();

    element.onkeypress = function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const groupName = element.textContent.trim();
            if (!groupName) {
                alert("Group name cannot be empty.");
                resetEditableGroup(element, originalHtml);
                return;
            }
            //addNewGroup(groupName);
            //const groupIndex = integrateNewGroup(groupName, formDataCurrent);
            updateUIWithNewGroup(groupName);
            resetEditableGroup(element, originalHtml);
        }
    };

    element.onblur = function() {
        //resetEditableGroup(element, originalHtml);
    };
}

function resetEditableField(element, html) {
    element.innerHTML = html;
    element.contentEditable = false;
    element.onclick = function() { makeEditableAndAddNewLineField(element); };
}

function resetEditableGroup(element, html) {
    element.innerHTML = html;
    element.contentEditable = false;
    element.onclick = function() { makeEditableAndAddNewGroup(element); };
}
// function addNewGroup(groupName) {
//     const formId = formDataCurrent.id || formIdForDataTable;
//     const newGroupData = { groupName, description: "" };
//     const url = `/api/forms/${formId}/groups`;

//     // Fire-and-forget the fetch call
//     fetch(url, {
//         method: 'POST',
//         headers: {'Content-Type': 'application/json'},
//         body: JSON.stringify(newGroupData)
//     })
//     .then(response => response.json())
//     .then(data => {
//         if (data.error) {
//             console.error(`Failed to add group: ${data.error}`);
//             // Optionally handle minimal error reporting to UI here
//         }
//     })
//     .catch(error => {
//         console.error('Error adding group:', error.message);
//         // Optionally handle minimal error reporting to UI here
//     });
// }




// function integrateNewGroup(groupName, formDataCurrent) {
//     if (!formDataCurrent.structureJson) {
//         formDataCurrent.structureJson = { groups: [] };
//     }

//     formDataCurrent.structureJson.groups.push({
//         groupName: groupName,
//         fields: [],
//         description: "New group description"
//     });

//     const newGroupIndex = formDataCurrent.structureJson.groups.length - 1;

//     const formIndex = globalForms.findIndex(f => f.id === formDataCurrent.id);
//     if (formIndex !== -1) {
//         globalForms[formIndex] = {...globalForms[formIndex], ...formDataCurrent};
//         console.log(`globalForms updated for form ${formDataCurrent.id}`);
//     } else {
//         console.error(`Form with ID ${formDataCurrent.id} not found in globalForms.`);
//     }

//     processForms();

//     return newGroupIndex;
// }



function updateUIWithNewGroup(groupName) {
    
    requestAnimationFrame(() => {
        const formId = formDataCurrent.id || formIdForDataTable;
        const newGroupIndex = formDataCurrent.structureJson.groups.length ;

        const newGroupDiv = document.createElement('div');
        newGroupDiv.className = 'groups-section-header';
        newGroupDiv.style.display = 'flex';
        newGroupDiv.setAttribute('data-group-name', groupName);
        newGroupDiv.innerHTML = `
            <div class="groupSpan checkedGroupLine" style="padding: 0; width:30px; border:1px solid #323232; display: flex;">
                <span style="width: 100%; border: 4px solid #161616; background-color: #313131;"></span>
            </div>
            <span class="groupDetailsSpan spanEditable colorFieldDetails" onclick="displayFields(${newGroupIndex}, ${formId});makeEditableGroup(this,'groupName');" data-old="${groupName}"  style="width:270px;">${groupName}</span>
            <div class="groups-details">
                <span class="groupDetailsSpan spanEditable" onclick="makeEditableGroup(this,'description');"></span>
            </div>
        `;

        const addGroupLine = document.querySelector('.group-add-section');
        addGroupLine.parentNode.insertBefore(newGroupDiv, addGroupLine);
        makeEditableAndAddNewGroup(addGroupLine.childNodes[3]);
        addNewGroup(groupName);
    });
}

let worker;
function addNewGroup(groupName) {

    const formId = formDataCurrent.id || formIdForDataTable;
    initializeWorker(groupName,formId);
    if (worker) {
        worker.postMessage({
            groupName: groupName,
            formId: formId,
        });
    } else {
        console.error("Worker not initialized or failed to load.");
    }
}

function initializeWorker() {
    if (!worker) {
        worker = new Worker('js/CreateSection.js');
        worker.onmessage = function(e) {
            const { success, data, error } = e.data;
            if (success) {
                console.log('Group added successfully:', data);
                updateFormDataCurrent(data.groupName);
            } else {
                console.error('Error adding group:', error);
            }
        };
        worker.onerror = function(error) {
            console.error('Worker error:', error.message);
        };
    }
}



function updateFormDataCurrent(groupName) {

    formDataCurrent.structureJson.groups.push({
        groupName: groupName,
        fields: [],
        description: ""
    });
    if (currentFormIndex !== -1) {
        globalForms[currentFormIndex] = {...globalForms[currentFormIndex], ...formDataCurrent};
    }
    processForms();
    displayListFormInForContent();
}








/******  End Create new Section */




async function fetchFormResponses(formId) {
    const apiUrl = `/api/forms/${formId}/response`;
    try {
        const response = await fetch(apiUrl);
        if (!response.ok) {
            throw new Error(`Error: ${response.statusText}`);
        }
        const data = await response.json();
        return data['La liste des Responses du Formulaire'];
    } catch (error) {
        console.error('Failed to fetch form responses:', error);
        return [];
    }
}






function addClassToRoleById(roleId) {
    const roleElement = document.querySelector(`[data-role-id="${roleId}"]`);

    if (roleElement) {
        roleElement.classList.add('newFormAppended');
        console.log(`Class newFormAppended added to role with ID ${roleId}`);
    } else {
        console.error(`No role element found with ID ${roleId}`);
    }
}
function openCustomTab(evt, tabName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("customTabContent");
    for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("customTabLinks");
    for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    document.getElementById(tabName).style.display = "block";
    evt.currentTarget.className += " active";
  }
  
//   document.addEventListener("DOMContentLoaded", function() {
//     document.querySelector('.customTabLinks').click();
//   });



document.addEventListener('DOMContentLoaded', function () {
    const resizers = document.querySelectorAll('.resizer');

    resizers.forEach((resizer) => {
        let startY, startHeightPrev, startHeightNext;

        resizer.addEventListener('mousedown', function (e) {
            const prevDiv = resizer.previousElementSibling;
            const nextDiv = resizer.nextElementSibling;

            // Store the initial mouse position and heights
            startY = e.pageY;
            startHeightPrev = prevDiv.offsetHeight;
            startHeightNext = nextDiv.offsetHeight;

            // Add mousemove and mouseup event listeners
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);

            function mouseMoveHandler(e) {
                const dy = e.pageY - startY;

                // Calculate new heights
                const newHeightPrev = Math.max(50, startHeightPrev + dy);
                const newHeightNext = Math.max(50, startHeightNext - dy);

                // Apply new heights to the sections
                prevDiv.style.height = `${newHeightPrev}px`;
                nextDiv.style.height = `${newHeightNext}px`;
            }

            function mouseUpHandler() {
                // Remove the event listeners when mouse is released
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            }
        });
    });
});








  document.addEventListener('DOMContentLoaded', function() {
    const panel = document.querySelector('.panel');

    if (panel) {
        panel.addEventListener('click', function(event) {
            const target = event.target.closest('.icon');
            if (target) {
                document.querySelectorAll('.panel .icon').forEach(icon => {
                    icon.classList.remove('active-field');
					icon.classList.remove('brighticon');
                });

                target.classList.add('active-field');
				target.classList.add('brighticon');
            }
        });
    }
});


  // Select all buttons with the class "day"
  const dayButtons = document.querySelectorAll('.day');

  // Loop through each button and add a click event listener
  dayButtons.forEach(button => {
      button.addEventListener('click', function() {
          // Toggle the "activeday" class when clicked
          this.classList.toggle('activeday');
      });
  });



       // Récupérer tous les boutons et sections
       const spans = document.querySelectorAll('.cardCan .headers span');
       const sections = document.querySelectorAll('.cardCan > div[class^="content-"]');
   
       // Ajouter un événement de clic à chaque bouton
       spans.forEach(span => {
           span.addEventListener('click', () => { // Retirer la classe active de tous les boutons
               spans.forEach(s => s.classList.remove('activejourne'));
               // Ajouter la classe active au bouton cliqué
               span.classList.add('activejourne');
   
               // Masquer toutes les sections
               sections.forEach(section => section.classList.remove('active-section'));
               // Afficher la section correspondante
               const targetId = span.id.replace('-palettes', '-contents');
               document.getElementById(targetId).classList.add('active-section');
           });
       }); 



       document.addEventListener("DOMContentLoaded", function () {
        const treeRoot = document.getElementById('tree-root');
        const menuItems = document.querySelectorAll('.dropdown-content a');
        var itemChoosed = document.querySelector('.itemChoosed');
        menuItems.forEach(item => {
            item.addEventListener('click', function (event) {
                event.preventDefault();
    
                const selectedMenu = this.textContent.trim();
                let dataToUse;
                let isMigrable = false;
                let isArretCuivre = false;
                let htmlContent = '';
                itemChoosed.textContent = selectedMenu;
    
                if (selectedMenu === "Parc") {
                    dataToUse = HierarchyData;
                    htmlContent = generateHierarchyHtml(dataToUse, isMigrable);
                } else if (selectedMenu === "Migrables") {
                    dataToUse = migrableData;
                    isMigrable = true;
                    htmlContent = generateHierarchyHtml(dataToUse, isMigrable);
                    // No need to set innerHTML here, it's done after field listeners setup
                } else if (selectedMenu === "Arret cuivre") {  // New condition
                    dataToUse = arretcuivre;  // Assuming you have a data source for "Arret cuivre"
                    isArretCuivre = true;
                    htmlContent = generateHierarchyHtmlCuivre(dataToUse);
                } else {
                    console.warn("Menu non pris en charge :", selectedMenu);
                    return;
                }
    
                // Update treeRoot and setup listeners only once
                treeRoot.innerHTML = htmlContent;
                setupCaretListeners('tree-root');
                setupFieldListeners(isMigrable, isArretCuivre);  // Pass both flags
    
            });
        });
    
        // Chargement par défaut
        const htmlContent = generateHierarchyHtml(HierarchyData);
        treeRoot.innerHTML = htmlContent;
        setupCaretListeners('tree-root');
        setupFieldListeners(false, false); // Default: neither migrable nor cuivre
        setupSearchFunctionality();
    });
    
    
 


    function generateHierarchyHtml(data, isMigrable = false) { 
        //console.log('Data:', data);
        let htmlContent = '';
        const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
        const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
        
        data.forEach(cluster => {
            let placesContent = '';
    
            cluster.villes.forEach(place => {
                placesContent += `
                    <li class="field-list-item">
                        <span class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
                            <span class="fieldLiSpan" style="flex-grow: 1; display: flex; align-items: center;"
                                data-cluster-code="${cluster.clusterCode}" data-insee-code="${place.cod_insee}"
                                onclick="saveClusterInsee('${cluster.clusterCode}', '${place.cod_insee}')"
                            >
                                <img src="${flecheFields}" style="width: 12px; margin-right: 6px;">
                                <div style="height: 13px; width: 13px; background: #015D92; border-radius: 2px; margin-right: 5px; display: flex; align-items: center;">
                                    <div style="height: 2px; width: 13px; background: #000;"></div>
                                </div>
                                ${place.ville}
                            </span>
                            <span class="total-place" style="margin-right: 15px; color: #326E78;"
                                data-nb-fyr-adsl="${place.nb_fyr_adsl}"
                                data-nb-fyr-thd="${place.nb_fyr_thd}"
                                data-nb-fyr-mob-mono="${place.nb_fyr_mob_mono}"
                                data-nb-fyr-multi-thd="${place.nb_fyr_mob_multi_thd}"
                                data-nb-fyr-multi-adsl="${place.nb_fyr_mob_multi_adsl}"
                                data-total-prises="${place.total_prises}">
                                ${ 
                                    (
                                        (document.getElementById('nb_fyr_adsl').checked ? place.nb_fyr_adsl : 0) +
                                        (document.getElementById('nb_fyr_thd').checked ? place.nb_fyr_thd : 0) +
                                        (document.getElementById('nb_fyr_mob_mono').checked ? place.nb_fyr_mob_mono : 0) +
                                        (document.getElementById('nb_fyr_mob_multi_thd').checked ? place.nb_fyr_mob_multi_thd : 0) +
                                        (document.getElementById('nb_fyr_mob_multi_adsl').checked ? place.nb_fyr_mob_multi_adsl : 0)
                                    ) || place.total_prises || 0
                                }
                            </span>
                        </span>
                    </li>
                `;
            });
    
            const total = isMigrable 
                ? (
                    (document.getElementById('nb_fyr_adsl').checked ? cluster.nb_fyr_adsl : 0) +
                    (document.getElementById('nb_fyr_thd').checked ? cluster.nb_fyr_thd : 0) +
                    (document.getElementById('nb_fyr_mob_mono').checked ? cluster.nb_fyr_mob_mono : 0) +
                    (document.getElementById('nb_fyr_mob_multi_thd').checked ? cluster.nb_fyr_mob_multi_thd : 0) +
                    (document.getElementById('nb_fyr_mob_multi_adsl').checked ? cluster.nb_fyr_mob_multi_adsl : 0)
                  )
                : cluster.total_prises;
    
            htmlContent += `
                <li class="formListItem">
                    <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;" 
                    onclick="ProductionKPIByPointOfSaleId('${cluster.clusterCode}');findClusterData('${cluster.clusterCode}');fetchdataCluster('${cluster.clusterCode}');"> 
                        <span style="display: flex; align-items: center;"  >
                            <img src="${fleche}" class="flecheimg" style="margin-right: 10px;">
                            <div style="height: 12px; width: 12px; background: #54C5d0; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                            ${cluster.libelle_cluster}
                        </span>
                        <div class="total-cluster" style="display: flex; align-items: center; margin-right: 5px; font-size: 16px;"
                            data-nb-fyr-adsl="${cluster.nb_fyr_adsl}"
                            data-nb-fyr-thd="${cluster.nb_fyr_thd}"
                            data-nb-fyr-mob-mono="${cluster.nb_fyr_mob_mono}"
                            data-nb-fyr-mob-multi-thd="${cluster.nb_fyr_mob_multi_thd}"
                            data-nb-fyr-mob-multi-adsl="${cluster.nb_fyr_mob_multi_adsl}"
                            data-total-prises="${cluster.total_prises}"
                            data-is-migrable="${isMigrable}">
                            ${total}
                            <i class="bi bi-plugin" style="color: #54C5d0; margin-left: 6px; border-radius: 2px;"></i>
                        </div>
                    </span>
                    <ul class="nested">${placesContent}</ul>
                </li>
            `;
        });
    
        return htmlContent;
    }
    function saveClusterInsee(clusterCode, codInsee) {
        // Retrieve existing data from localStorage or create an empty array
        let savedData = JSON.parse(localStorage.getItem("clusterInseeData")) || [];
    
        // Push the new cluster and place into the saved data
        savedData.push({ clusterCode, codInsee });
    
        // Save the updated data back to localStorage
        localStorage.setItem("clusterInseeData", JSON.stringify(savedData));
    
        console.log("Saved data:", savedData);
    }

    // Fonction pour mettre à jour les totaux
    function updateTotals() {
        // Mise à jour des totaux des lieux
        document.querySelectorAll('.total-place').forEach(element => {
            const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
            const thd = parseInt(element.dataset.nbFyrThd) || 0;
            const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
            const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
            const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
            const totalPrises = parseInt(element.dataset.totalPrises) || 0;
    
            let sum = 0;
            if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
            if (document.getElementById('nb_fyr_thd').checked) sum += thd;
            if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
            if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
            if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;
    
            element.textContent = sum || totalPrises || 0;
        });
    
        // Mise à jour des totaux des clusters
        document.querySelectorAll('.total-cluster').forEach(element => {
            const isMigrable = element.dataset.isMigrable === 'true';
            const totalPrises = parseInt(element.dataset.totalPrises) || 0;
    
            if (isMigrable) {
                const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
                const thd = parseInt(element.dataset.nbFyrThd) || 0;
                const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
                const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
                const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
    
                let sum = 0;
                if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
                if (document.getElementById('nb_fyr_thd').checked) sum += thd;
                if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
                if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
                if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;
    
                const textNode = element.childNodes[0];
                textNode.nodeValue = sum;
            } else {
                const textNode = element.childNodes[0];
                textNode.nodeValue = totalPrises;
            }
        });
    }

    document.querySelectorAll('#fileUploadForm input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateTotals);
    });

    
    function generateHierarchyHtmlCuivre(data) {
        const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
        const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
        let htmlContent = '';
    
        // Process years in descending order
        const years = Object.keys(data).sort((a, b) => b - a);
    
        years.forEach(year => {
            const yearData = data[year][1];
            const clusters = yearData.clusters;
    
            // Generate clusters for this year
            let clustersHtml = '';
            clusters.forEach(cluster => {
                // Generate cities for this cluster
                let villesHtml = '';
                if (cluster.villes) {
                    cluster.villes.forEach(ville => {
                        villesHtml += `
                            <li class="field-list-item">
                                <span class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
                                    <span   class="fieldLiSpan"  style="flex-grow: 1; display: flex; align-items: center;" 
                                    data-cluster-code="${cluster.code_cluster}" 
                                    data-insee-code="${ville.cod_insee}"
                                                                    
                                     >
                                        <img src="${flecheFields}" style="width: 12px; margin-right: 6px;">
                                        <div style="height: 13px; width: 13px; background: #015D92; border-radius: 2px; margin-right: 5px; display: flex; align-items: center;">
                                            <div style="height: 2px; width: 13px; background: #000;"></div>
                                        </div>
                                        ${ville.ville}
                                    </span>
                                    <span style="margin-right: 15px; color: #326E78;">${ville.totalPrises}</span>
                                </span>
                            </li>
                        `;
                    });
                }
    
                // Cluster HTML
                clustersHtml += `
                    <li class="formListItem">
                        <span class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                            <span style="display: flex; align-items: center;">
                                <img src="${fleche}" class="flecheimg" style="margin-right: 10px;">
                                <div style="height: 12px; width: 12px; background: #54C5d0; margin-right: 5px; border-radius: 2px;"></div>
                                ${cluster.libelle_cluster}
                            </span>
                            <div style="display: flex; align-items: center;margin-right: 5px;font-size: 16px;">
                            ${cluster.totalPrises}
                              <i class="bi bi-plugin" style="color: #54C5d0; margin-left: 6px;border-radius: 2px;"></i>
                               </div>
                        </span>
                        <ul class="nested">${villesHtml}</ul>
                    </li>
                `;
            });
    
      
            htmlContent += clustersHtml;
        });
    
        return htmlContent;
    }
    

    
    function setupCaretListeners(rootElementId) {
        const rootElement = document.getElementById(rootElementId);
        const togglers = rootElement.querySelectorAll(".caret, .formSpan");
        
        togglers.forEach(caret => {
            caret.addEventListener("click", function(event) {
                event.stopPropagation();
        
                let nestedUl = this.closest('li').querySelector('.nested');
                if (nestedUl) {
                    nestedUl.classList.toggle("active");
                }
        
                document.querySelectorAll('.active-field').forEach(el => {
                    el.classList.remove('active-field');
                });
                this.classList.add('active-field');
        
                this.classList.toggle("caret-down");
            });
        });
    }
    
    function setupSearchFunctionality() {
        const searchInput = document.getElementById('TreeSearch');
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase().trim();
            filterFormListItems(searchText);
        });
    }
    
    function filterFormListItems(searchText) {
        const formListItems = document.querySelectorAll('.formListItem');
    
        if (searchText.length < 3) {
            // Show all items if the search text length is less than 3
            formListItems.forEach(item => {
                item.style.display = '';
                item.querySelectorAll('.field-list-item').forEach(subItem => subItem.style.display = '');
            });
        } else {
            formListItems.forEach(item => {
                let hasMatch = false;
                const placeItems = item.querySelectorAll('.field-list-item');
    
                // Check each place within the cluster for a match
                placeItems.forEach(subItem => {
                    const placeName = subItem.querySelector('.fieldLiSpan').textContent.toLowerCase();
                    if (placeName.includes(searchText)) {
                        subItem.style.display = '';
                        hasMatch = true; // Mark as match if any place matches
                    } else {
                        subItem.style.display = 'none';
                    }
                });
    
                // Check the cluster name itself
                const clusterName = item.querySelector('.formSpan').textContent.toLowerCase();
                if (clusterName.includes(searchText) || hasMatch) {
                    item.style.display = ''; // Show the cluster if it or any place matches
                } else {
                    item.style.display = 'none'; // Hide the cluster if no places match
                }
            });
        }
    }

// Fonction de réattachement des écouteurs de clic pour les Rues
function setupFieldListeners(isMigrable = false, isArretCuivre = false) {
    document.querySelectorAll('.fieldLiSpan').forEach(element => {
        element.addEventListener('click', function () {
            const clusterCode = this.getAttribute('data-cluster-code');
            const inseeCode = this.getAttribute('data-insee-code');
          
            if (!clusterCode || !inseeCode) {
                console.warn('Cluster Code or INSEE Code is null. Skipping fetch.');
                return;
            }

            // Définir l'URL en fonction de la sélection
            const url = isArretCuivre 
                ? `/GeoMapp/arretcuivre-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}` 
                : isMigrable 
                ? `/GeoMapp/migrable-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}` 
                : `/GeoMapp/street-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    const treeRoot = document.getElementById('tree-root-roles');
                    treeRoot.innerHTML = '';
                    const htmlContent = isArretCuivre ? generateHierarchyHtmlscuivre(data) : generateHierarchyHtmls(data);
                    treeRoot.innerHTML = htmlContent;
                    setupCaretListeners('tree-root-roles');
                    setupSearchFunctionalityRues();
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                });
        });
    });
}

// Stocker la couleur de chaque rue
const colorMaps = new Map();

function getColorForStreets(streetName) {
    if (!colorMaps.has(streetName)) {
        
        const color = colorMaps.size % 2 === 0 ? '#A074C4' : '#55C5D0';
        colorMaps.set(streetName, color);
    }
    return colorMaps.get(streetName);
}

function generateHierarchyHtmls(data) {
    let htmlContent = '';
    const groupedData = {};

    // Trier et regrouper les rues par code_iris
    data.forEach(street => {
        const codeIris = street.code_iris;
        if (!groupedData[codeIris]) {
            groupedData[codeIris] = [];
        }
        groupedData[codeIris].push(street);
    });

    // Générer l'affichage HTML pour chaque code_iris
    for (const codeIris in groupedData) {
        // Obtenir une couleur unique pour le code_iris
        const backgroundColor = getColorForStreets(codeIris);

        htmlContent += `<div id="${codeIris}" class="code-iris-container"  style="transition: border-left 0.3s ease;" >
           <style>
            #${codeIris}:hover {
                border-left: 2px solid ${backgroundColor}; /* Change la couleur et la taille du border-left selon tes besoins */
            }
        </style>`
                         ; 

        groupedData[codeIris].forEach(street => {
            const total =
                (document.getElementById('nb_fyr_adsl').checked ? street.nb_fyr_adsl : 0) +
                (document.getElementById('nb_fyr_thd').checked ? street.nb_fyr_thd : 0) +
                (document.getElementById('nb_fyr_mob_mono').checked ? street.nb_fyr_mob_mono : 0) +
                (document.getElementById('nb_fyr_mob_multi_thd').checked ? street.nb_fyr_mob_multi_thd : 0) +
                (document.getElementById('nb_fyr_mob_multi_adsl').checked ? street.nb_fyr_mob_multi_adsl : 0) ||
                street.total_prises || 0;

            if (total > 0) {
                htmlContent += `
                        <li class="formListItem" style="background-color: var(--nested-bg, #f9f9f9); border-right: 2px solid ${backgroundColor}; ">
                    <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span class="formSpanvoie" id="formSpanvoiedata" style="display: flex; align-items: center;font-size: 13px;" 
                            onclick="saveToLocalStorages('${street.nom_voie || 'N/A'}', '${street.complement || 'N/A'}', '${backgroundColor}', '${total}', this)">
                            <div style="margin-left: 15px;">
                                <input class="checkbox-voie" data-nomvoie="${street.nom_voie}" onclick="handleClick('${total}', this)" type="checkbox">
                            </div>
                            <div style="height: 12px; width: 12px; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                            ${truncateText(street.nom_voie || 'N/A', 25)}
                        </span>
                        <div id="totalePrises" class="total-street" 
                            style="display: flex; color:${backgroundColor}; align-items: center; margin-right: 15px; font-size: 14px;"
                            data-nb-fyr-adsl="${street.nb_fyr_adsl}"
                            data-nb-fyr-thd="${street.nb_fyr_thd}"
                            data-nb-fyr-mob-mono="${street.nb_fyr_mob_mono}"
                            data-nb-fyr-mob-multi-thd="${street.nb_fyr_mob_multi_thd}"
                            data-nb-fyr-mob-multi-adsl="${street.nb_fyr_mob_multi_adsl}"
                            data-total-prises="${street.total_prises}">
                            ${total}
                        </div>
                    </span>
                </li>

                
                `;
            }
        });

        htmlContent += `</ul></div>`; // Fermeture du <ul> et du conteneur <div>
    }

    return htmlContent;
}

function truncateText(text, maxLength) {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
}


let totalGeneral = 0;
let totalDistanceSum = 0;
let somme = 0;
let decouche =false ;
function handleClick(totalprises, checkbox) {
    // Vérifie si la checkbox est cochée
    const isChecked = checkbox.checked;
    const totalDistance = parseFloat(localStorage.getItem("totalDistance"));

    // Ajoute ou soustrait la valeur de la checkbox au total général
    if (isChecked) {
        totalGeneral += parseInt(totalprises); // Ajoute si la checkbox est cochée
        totalDistanceSum += totalDistance;
        somme++;
        decouche = true;
    } else {
        totalGeneral -= parseInt(totalprises); // Soustrait si la checkbox est décochée
        totalDistanceSum -= totalDistance;
        somme--;
        decouche = false
    }

    // Sauvegarde les valeurs dans le localStorage
    localStorage.setItem('isCheckedsomme', somme);
    localStorage.setItem('totalGeneral', totalGeneral);
    localStorage.setItem('totalDistanceSum', totalDistanceSum.toFixed(2));
    
    // Sauvegarde isChecked dans localStorage
    localStorage.setItem('isChecked', isChecked ); // Si isChecked est vrai, enregistre "true", sinon "false"
    localStorage.setItem('decouche', decouche );
}


function saveToLocalStorages(nomVoie, complement, backgroundColor, totalprises, spanElement) { 
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;

    // Sauvegarde des informations dans localStorage
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises);
    localStorage.setItem('isChecked', isChecked);
    // Récupération des voies sauvegardées
    let savedVoies = JSON.parse(localStorage.getItem('savedVoies')) || [];

    // Vérifier si la voie est déjà enregistrée pour éviter les doublons
    if (!savedVoies.includes(nomVoie)) {
        savedVoies.push(nomVoie);
        localStorage.setItem('savedVoies', JSON.stringify(savedVoies));
    }
}





function updateTotales() {
      // Mise à jour des totaux des lieux
      document.querySelectorAll('.total-place').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;

        element.textContent = sum || totalPrises || 0;
    });
    // Mise à jour des totaux des rues
    document.querySelectorAll('.total-street').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;

        element.textContent = sum || totalPrises || 0;
    });
}

    // Ajout des écouteurs d'événements sur les checkboxes
    document.querySelectorAll('#fileUploadForm input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateTotales);
    });
// Fonction de génération du contenu HTML pour "Arret Cuivre"

const colorMap = new Map();

function getColorForCodeIris(code_iris) {
    if (!colorMap.has(code_iris)) {
        // Alterne entre rouge et bleu de manière cohérente
        const color = colorMap.size % 2 === 0 ? '#094044' : '#094044';
        colorMap.set(code_iris, color);
    }
    return colorMap.get(code_iris);
}

function generateHierarchyHtmlscuivre(data) {
    let htmlContent = '';
    const years = Object.keys(data).sort((a, b) => b - a); // Tri des années en ordre décroissant

    years.forEach(year => {
        const yearData = data[year][1]; // Accès aux données de l'année
        const voies = yearData.voies; // Liste des 'voies' pour cette année

        voies.forEach(voie => {
            // Obtenir une couleur unique pour chaque code_iris
            const backgroundColor = getColorForCodeIris(voie.code_iris);

            // Construire le HTML pour chaque 'voie'
            htmlContent += `
                <li class="formListItem" style="background-color:  var(--nested-bg, #f9f9f9); border-right: 2px ${backgroundColor}; ">
                    <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span class="formSpanvoie" data-nom-voie="${voie.nom_voie}" style="display: flex; align-items: center;"
                            onclick="selectVoieAndSearchStreet('${voie.nom_voie || 'N/A'},${voie.complement}, ${backgroundColor}, ${voie.total_prises || 0},this')">
                             <div class="" style="margin-left: 15px;">
                            <div style="margin-left: 15px;">
                            <input class="checkbox-voie" data-nomvoie= "${street.nom_voie }"onclick="handleClick( '${total}',this)"  id="checkbox8" type="checkbox">
                            </div>
                            </div>
                            <div style="height: 12px; width: 12px; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                            ${voie.nom_voie || 'N/A'}
                        </span>
                        <div style="display: flex; color: ${backgroundColor}; align-items: center; margin-right: 15px; font-size: 14px;">
                            ${voie.total_prises || 0} <!-- Afficher totalPrises ou 0 si non défini -->
                        </div>
                    </span>
                </li>
            `;
        });
    });

    return htmlContent; // Retourne le HTML généré
}


function selectVoieAndSearchStreet(nomVoie,complement,backgroundColor,totalprises,spanElement) {
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises); 
    localStorage.setItem('isChecked', isChecked);
}
function toggleStyle(element, isChecked) {
    const span = element.querySelector('span');

    // Toggle background color and border
    if (span.style.backgroundColor === 'rgb(45, 140, 235)') {
        span.style.backgroundColor = 'var(--nav-link-active-bg)';
        span.style.border = '4px solid var(--bg-table-section)';

    } else {
        span.style.backgroundColor = '#2D8ceb';
        span.style.border = '4px solid var(--bg-table-section)';
  
    }
}


    
    function setupCaretListeners(rootElementId) {
        const rootElement = document.getElementById(rootElementId);
        const togglers = rootElement.querySelectorAll(".caret, .formSpan");
        
        togglers.forEach(caret => {
            caret.addEventListener("click", function(event) {
                event.stopPropagation();
        
                let nestedUl = this.closest('li').querySelector('.nested');
                if (nestedUl) {
                    nestedUl.classList.toggle("active");
                }
        
                document.querySelectorAll('.active-field').forEach(el => {
                    el.classList.remove('active-field');
                });
                this.classList.add('active-field');
        
                this.classList.toggle("caret-down");
            });
        });
    }
    
    function setupSearchFunctionalityRues() {
        const searchInput = document.getElementById('TreeSearchRues');
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase().trim();
            filterFormListItemsRues(searchText);
        });
    }
    
    function filterFormListItemsRues(searchText) {
        const formListItems = document.querySelectorAll('.formListItem');
    
        if (searchText.length < 3) {
            // Afficher tous les éléments si la recherche contient moins de 3 caractères
            formListItems.forEach(item => {
                item.style.display = '';
            });
        } else {
            formListItems.forEach(item => {
                const streetNameElement = item.querySelector('.formSpanvoie');
                if (streetNameElement) {
                    const streetName = streetNameElement.textContent.toLowerCase();
                    if (streetName.includes(searchText)) {
                        item.style.display = ''; // Afficher l'élément si le nom correspond
                    } else {
                        item.style.display = 'none'; // Cacher sinon
                    }
                }
            });
        }
    }
    

    
    
    document.addEventListener('DOMContentLoaded', function () {
        const container = document.querySelector('.interface .cards');
        if (!container) return;
    
        container.innerHTML = '';
    
        // Ajouter le style CSS pour la classe .active
        const style = document.createElement('style');
        style.innerHTML = `
            .active {
                padding: 0;
                display: block;
                padding-left:10px;
                
            }
         
        `;
        document.head.appendChild(style);
    
        // Création des en-têtes
        const headers = document.createElement('div');
        headers.className = 'headers';
        headers.innerHTML = `
            <!-- Optionally you can add some header content here -->
        `;
        container.appendChild(headers);
    
        // Vérification des icônes TreeView
        const treeViewIcon = document.getElementById('TreeViewIcon');
        const fleche = '/discord/treeview/Fleche.svg';
        const flecheFields = treeViewIcon ? treeViewIcon.getAttribute('flecheFields') : '';
    
        function generateTree(user, level = 0) {
            if (!user) return '';
    
            let childrenContent = '';
            if (user.children && user.children.length > 0) {
                childrenContent = `
                    <div class="nested" style="display: none;">
                        ${user.children.map(child => generateTree(child, level + 1)).join('')}
                    </div>
                `;
            }
    
            const colors = ['#54C5d0', '#015D92', '#015D92'];
            const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    
            // Separate HTML structure for each level
            const baseStyle = {
                level0: `
                    <span class="caret formSpan caret-down" 
                          style="display: flex; justify-content: space-between; align-items: center; width: 91%; font-size: 14px;">
                        <span style="display: flex; align-items: center;">
                            <img src="${fleche}" style="margin-right:10px;" class="flecheimg">
                            <div style="height:12px;width:12px;background:${colors[level]};margin-right:5px;border-radius:2px;"></div>
                            ${user.nom}
                        </span>
                        <div style="display: flex; align-items: center; margin-right: 40px;">
                            <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                            <i class="bi bi-plugin" style="color: #54C5d0; margin-left: 6px; border-radius: 2px;"></i>
                        </div>
                    </span>
                `,
                level1: `
                    <span class="caret formSpan caret-down" 
                          style="display: flex; justify-content: space-between; align-items: center; width: 91%; font-size: 14px;">
                        <span style="display: flex; align-items: center;">
                            <img src="${flecheFields}" style="width:12px;margin-right:6px;" class="flecheimg child-img">
                            <div style="height:12px;width:12px;background:${colors[level]};margin-right:5px;border-radius:2px;"></div>
                            ${user.nom}
                        </span>
                        <div style="display: flex; align-items: center; margin-right: 40px;">
                            <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                        </div>
                    </span>
                `,
                level2: `
                <div class="formListItem">
                    <span class="caret formSpan formSpanvoie" data-userId="${user.id}" 
                     onclick="handleSelectNomPrenom('${user.nom}', '${user.prenom}', ${level}, ${user.id}); toggleContent('${user.id}');"
                          style="display: flex; justify-content: space-between; align-items: center; width:  91%; font-size: 14px;">
                        <span style="display: flex; align-items: center;">
                            <img src="${flecheFields}" style="width:12px;margin-right:6px;" class=" child-img">
                            <div style="background:;margin-right:5px;border-radius:2px;"> <i class="bi bi-person-fill " style="color:${colors[level]}"></i></div>
                            ${user.nom}
                        </span>
                        <div style="display: flex; align-items: center; margin-right: 40px;">
                            <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                        </div>
                    </span>
                </div>
                `
            };
    
            // Return the appropriate structure based on the level
            let nodeContent = '';
            if (level === 0) {
                nodeContent = baseStyle.level0;
            } else if (level === 1) {
                nodeContent = baseStyle.level1;
            } else if (level === 2) {
                nodeContent = baseStyle.level2;
            }
    
            return `
                <div class="tree-node" style="color:#b0b0b0;">
                    ${nodeContent}
                    ${childrenContent}
                </div>
            `;
        }
    
                // Ajouter un délai pour améliorer les performances
            let searchTimeout;
            const searchInput = document.getElementById('TreeSearchinterface');
            searchInput.addEventListener('input', function () {
                clearTimeout(searchTimeout); // Annuler le délai précédent
                searchTimeout = setTimeout(() => {
                    const searchText = this.value.toLowerCase().trim();
                    filterSearcher(searchText);
                }, 300); // Délai de 300 ms
            });

            function filterSearcher(query) {
                const nodes = document.querySelectorAll('.tree-node');

                nodes.forEach(node => {
                    // Vérifier si ce nœud ou ses enfants correspondent à la recherche
                    const isMatch = searchNode(node, query);

                    if (isMatch) {
                        node.style.display = 'block'; // Afficher ce nœud
                        openParentNodes(node); // Ouvrir les parents
                        openAllChildrenNodes(node); // Ouvrir tous les enfants
                    } else {
                        node.style.display = 'none'; // Masquer ce nœud
                    }
                });
            }

            /**
             * 🔹 Recherche récursive pour vérifier si un nœud ou ses enfants correspondent à la recherche.
             */
            function searchNode(node, query) {
                // Vérifier le texte du nœud dans les différents niveaux
                const level0 = node.querySelector('.caret:not(.child-img)');
                const level1 = node.querySelector('.caret.child-img');
                const level2 = node.querySelector('.formSpanvoie');

                const level0Text = level0 ? level0.textContent.toLowerCase().trim() : '';
                const level1Text = level1 ? level1.textContent.toLowerCase().trim() : '';
                const level2Text = level2 ? level2.textContent.toLowerCase().trim() : '';

                // Vérifier si le nœud correspond à la recherche
                if (level0Text.includes(query) || level1Text.includes(query) || level2Text.includes(query)) {
                    return true;
                }

                // Rechercher récursivement dans les enfants
                const nestedNodes = node.querySelectorAll('.nested .tree-node');
                for (let nestedNode of nestedNodes) {
                    if (searchNode(nestedNode, query)) {
                        return true; // Retourner true dès qu'une correspondance est trouvée
                    }
                }

                return false; // Aucune correspondance trouvée
            }

            /**
             * 🔹 Ouvre tous les parents du nœud pour qu'ils soient visibles.
             */
            function openParentNodes(node) {
                let parent = node.closest('.nested');

                while (parent) {
                    const parentNode = parent.closest('.tree-node');
                    if (!parentNode) break;

                    parent.style.display = 'block'; // Afficher le parent

                    const caret = parentNode.querySelector('.caret');
                    if (caret) {
                        caret.classList.add('caret-down'); // Ouvrir le parent
                    }

                    parent = parentNode.closest('.nested'); // Remonter au parent suivant
                }
            }

            /**
             * 🔹 Ouvre tous les enfants du nœud (récursivement).
             */
            function openAllChildrenNodes(node) {
                const nested = node.querySelector('.nested');
                if (nested) {
                    nested.style.display = 'block'; // Afficher les enfants
                    const childrenNodes = nested.querySelectorAll('.tree-node');
                    childrenNodes.forEach(child => openAllChildrenNodes(child)); // Ouvrir les enfants récursivement
                }
            }
        
        // Rendering the effectifs
        if (typeof effectifs !== 'undefined' && Array.isArray(effectifs)) {
            const mainList = document.createElement('div');
            mainList.innerHTML = effectifs.map(effectif => generateTree(effectif)).join('');
            container.appendChild(mainList);
        } else {
            console.error("Erreur: `effectifs` n'est pas défini ou n'est pas un tableau.");
        }
    
        // Gestion des événements
        container.addEventListener('click', function (event) {
            const target = event.target.closest('.caret');
            if (target) {
                target.classList.toggle('caret-down');
                const nested = target.parentElement.querySelector('.nested');
                if (nested) {
                    nested.classList.toggle('active');
                    nested.style.display = (nested.style.display === 'none') ? 'block' : 'none';
    
                    // Mise à jour des enfants
                    const childImgs = nested.querySelectorAll('.child-img');
                    childImgs.forEach(img => {
                        img.classList.toggle('caret-down', target.classList.contains('caret-down'));
                    });
                }
            }
        });
    });
    
    async function toggleContent(userId) {
        try {
            // Show structureCard
            document.getElementById('structureCard').style.display = 'block';
    
            // Fetch data for the user
            const data = await fetchtotalforUser(userId);
            console.log(data);
    
            // Check if mainList already exists
            let mainList = document.querySelector('.main-list-back');
            
            // If it doesn't exist, create a new one
            if (!mainList) {
                mainList = document.createElement('div');
                mainList.classList.add('main-list-back'); // Add the class
                document.getElementById('structureCard').appendChild(mainList);
            }
    
            // Update the innerHTML of mainList
            mainList.innerHTML = generateClusterNodes(data);
    
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    }
    // Fonction fetch avec paramètre dynamique
async function fetchtotalforUser(cpv,codecluster) {
    try {
        // Construct the URL with the correct cpv and userId
        const url = `http://api.nomadcloud.fr/api/interventions-places-total-by-user/{$cpv}/{$codecluster}?page=1`;
        console.log('Fetching data from:', url);  // Debugging URL
    
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,  // Ensure jwtToken is defined
                'Content-Type': 'application/json'
            }
        });
    
        // Check if the response is OK
        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
    
        const data = await response.json();
        console.log('Response data:', data);  // Log the fetched data
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}

// Click handler for expanding/collapsing nodes
document.addEventListener('DOMContentLoaded', function () {
    const container = document.querySelector('.interface .cardsSturct');
    if (!container) return;

    container.innerHTML = '';

    // Styles for different levels
    const style = document.createElement('style');
    style.innerHTML = `
        .active { display: block !important; }
        .nested { display: none; }
      
    `;
    document.head.appendChild(style);

    // Event listener for the search input
    const searchInput = document.getElementById('TreeSearchinterface');
    searchInput.addEventListener('input', function () {
        filterSearcher(searchInput.value);
    });

    // Search filter function
    function filterSearcher(query) {
        const users = document.querySelectorAll('.tree-node');
        users.forEach(userNode => {
            const userName = userNode.querySelector('.caret').textContent.toLowerCase();
            if (userName.includes(query.toLowerCase())) {
                userNode.style.display = ''; // Show matching node
            } else {
                userNode.style.display = 'none'; // Hide non-matching node
            }
        });
    }

    // Click handler for expanding/collapsing nodes
    container.addEventListener('click', async function (event) {
        const target = event.target.closest('.caret');
        if (!target) return;

        // Toggle caret icon
        target.classList.toggle('caret-down');
        const nested = target.parentElement.querySelector('.nested');
        if (nested) nested.classList.toggle('active');

        // If it's a user node and it's not already loaded
        if (target.classList.contains('user-node') && !target.dataset.loaded) {
            const userId = target.dataset.userId;
            target.dataset.loaded = 'true';

            try {
                const data = await fetchtotalforUser(userId);
                const nestedContainer = target.parentElement.querySelector('.nested') || createNestedContainer(target.parentElement);
                nestedContainer.innerHTML = generateClusterNodes(data);
            } catch (error) {
                console.error('Error:', error);
            }
        }
    });
});

// Function to generate HTML for the clusters
function generateClusterNodes(clusters) {
    const level = 0;
    const fleche = '/discord/treeview/Fleche.svg';
    const colors = ['#54C5d0', '#015D92', '#015D92'];
    const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    
    return clusters.map(cluster => `
        <div class="tree-node">
            <span  class="caret formSpan cluster-node user-node caret-down" 
                style="display: flex; justify-content: space-between; align-items: center; width: 93%; font-size: 14px; ">
                <span style="display: flex; align-items: center;color:#b0b0b0;">
                 <img src="${fleche}" style="margin-right:10px;" class="flecheimg">
                    <div style="height:12px;width:12px;background:${colors[level]};margin-right:5px;border-radius:2px;"></div>
                    ${cluster.libelle_cluster}
                </span>
                <div style="display: flex; align-items: center; margin-right: 40px;">
                    <span style="color: ${prenomColors[level]}">${cluster.total_prises}</span>
                    <i class="bi bi-plugin" style="color: #54C5d0; margin-left: 6px; border-radius: 2px;"></i>
                </div>
            </span>

            <div class="nested">
                ${generateVilleNodes(Object.values(cluster.villes))}
            </div>
        </div>
    `).join('');
}

// Function to generate ville nodes
// function generateVilleNodes(villes) {
//     return villes.map(ville => `
//         <div class="tree-node">
//             <span  class="caret formSpan ville-node" 
//                 style="display: flex; justify-content: space-between; align-items: center; width: 88%; font-size: 12px;">
//                 <span style="display: flex; align-items: center;">
//                     ${ville.ville} - ${ville.cod_insee}
//                 </span>
//                 <div style="display: flex; align-items: center;">
//                     <span style="color: #b0b0b0">${ville.total_prises}</span>
//                 </div>
//             </span>
//         </div>
//     `).join('');
// }


// Génération des villes
function generateVilleNodes(villes) {
    const level = 1;
    const fleche = '/discord/treeview/Fleche.svg';
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    const colors = ['#54C5d0', '#015D92', '#015D92'];
    const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    return villes.map(ville => `
        <div class="tree-node">
            <span  class="caret formSpan  ville-node cluster-node user-node caret-down" 
                style="display: flex; justify-content: space-between; align-items: center; width: 88%; font-size: 14px;">
                <span style="display: flex; align-items: center;color:#b0b0b0;">
                    <img src="${flecheFields}" style="margin-right:10px;" class="flecheimg">
                      <div style="height: 13px; width: 13px; background: #015D92; border-radius: 2px; margin-right: 5px; display: flex; align-items: center;">
                                            <div style="height: 2px; width: 13px; background: #000;"></div>
                                        </div>
                    ${ville.ville}
                </span>
                <div style="display: flex; align-items: center; margin-right: 40px;">
                    <span style="color: ${prenomColors[level]}">${ville.total_prises}</span>
                </div>
            </span>
            <div class="nested">
                ${generateVoieNodes(Object.values(ville.voies))}
            </div>
        </div>
    `).join('');
}

// Génération des voies
function generateVoieNodes(voies) {
    const level = 2;
    const fleche = '/discord/treeview/Fleche.svg';
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    const colors = ['#54C5d0', '#015D92', '#015D92'];
    const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    return voies.map(voie => `
        <div class="tree-node">
            <span  class="caret formSpan  ville-node cluster-node user-node caret-down" 
                style="display: flex; justify-content: space-between; align-items: center; width: 88%; font-size: 14px;">
                <span style="display: flex; align-items: center;color:#b0b0b0;">
                    <img src="" style="margin-right:10px;" class="flecheimg">
                    <div style="height:12px;width:12px;background:${colors[level]};margin-right:5px;border-radius:2px;"></div>
                    ${ truncateTexts(voie.nom_voie,20)} 
                </span>
                <div style="display: flex; align-items: center; margin-right: 40px;">
                    <span style="color: ${prenomColors[level]}">${voie.total_prises}</span>
                </div>
            </span>
        </div>
    `).join('');
}

// Helper pour créer un conteneur nested
function createNestedContainer(parent) {
    const nested = document.createElement('div');
    nested.className = 'nested';
    parent.appendChild(nested);
    return nested;
}

// Fonction fetch avec paramètre dynamique
async function fetchtotalforUser(userId) {
    try {
        // Construct the URL with the correct cpv and userId
        const url = `https://api.nomadcloud.fr/api/interventions-places-total-for-a-user/${cpv}/${userId}?page=1`;
        console.log('Fetching data from:', url);  // Debugging URL
    
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,  // Ensure jwtToken is defined
                'Content-Type': 'application/json'
            }
        });
    
        // Check if the response is OK
        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
    
        const data = await response.json();
        console.log('Response data:', data);  // Log the fetched data
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}


    function generateUserNode(user) {
        const level = 0;
        const fleche = '/discord/treeview/Fleche.svg';
        const colors = ['#54C5d0', '#015D92', '#015D92'];
        const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
        return `
            <div class="tree-node">
                <span data-user-id="${user.id}" class="caret formSpan user-node caret-down" 
                      style="display: flex; justify-content: space-between; align-items: center; width: 88%; font-size: 12px;">
                    <span style="display: flex; align-items: center;">
                              <div style="margin-left: 15px;">
                                <input class="checkbox-voie"  type="checkbox">
                            </div>
                        <div style="margin-right:5px;border-radius:2px;"></div>
                        ${truncateTexts(user.nom,20)}   
                    </span>
                    <div style="display: flex; align-items: center; margin-right: 40px;">
                        <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                    </div>
                </span>
                <div class="nested"></div>
            </div>
        `;
    }
    function truncateTexts(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
    }
        // Handle selection only for sub-child (level 2)
        function handleSelectNomPrenom(nom, prenom, level, id) {
            if (level === 2) {  // Only save for sous-enfant (sub-child)
                localStorage.setItem('selectednom', nom);
                localStorage.setItem('selectedprenom', prenom);
                localStorage.setItem('selecteduserid', id);
            }
            
        }
    
        document.addEventListener("DOMContentLoaded", () => {
            localStorage.removeItem('clusterInseeData');
            localStorage.removeItem('savedVoies');
            localStorage.removeItem('selectedUserId');
            localStorage.removeItem('selectednom');
            localStorage.removeItem('selectedprenom');
            
        });
     
 
        
        document.addEventListener('DOMContentLoaded', function () {
            // Initialiser l'affichage
            // Initialiser l'affichage
            document.getElementById('MiseEnpageCard').style.display = "block";
            document.getElementById('structureCard').style.display = "none";

        

         

     

        });
    // document.addEventListener("DOMContentLoaded", () => {
    
    //     const dropdownIcon = document.querySelector(".dropdown-icon");
    //     const dropdownContent = document.querySelector(".dropdown-content");
    //     const dropdownLinks = dropdownContent.querySelectorAll("a"); // All menu links
    //     const topbarText = document.querySelector("#topbarText"); // The element containing "Topbar"
    //     const treeRoot = document.getElementById('tree-root-roles'); // Element to clear and update
    
    //     // Show/Hide the menu on click of the icon
    //     dropdownIcon.addEventListener("click", () => {
    //         const isDisplayed = dropdownContent.style.display === "block";
    //         dropdownContent.style.display = isDisplayed ? "none" : "block";
    //     });
    
    //     // Handle click on menu items
    //     dropdownLinks.forEach(link => {
    //         link.addEventListener("click", () => {
    //             const selectedText = link.querySelector("span").innerText; // Get the selected option's text
    //             topbarText.innerText = selectedText; // Replace "Topbar" text
    //             dropdownContent.style.display = "none"; // Hide the menu
    //             treeRoot.innerHTML = ''; // Clear treeRoot content
    //         });
    //     });
    
    //     // Close the menu when clicking outside
    //     document.addEventListener("click", (event) => {
    //         if (!dropdownIcon.contains(event.target) && !dropdownContent.contains(event.target)) {
    //             dropdownContent.style.display = "none";
    //         }
    //     });
    // });


