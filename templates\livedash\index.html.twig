{% extends 'base.html.twig' %}

{% block body %}
	<style>
:root {
  

    --sidebar-bg-colors: #f5f5f5;

}

[data-theme="dark"] {
  
  
    --sidebar-bg-colors: #000;

}
		.container-fluid,
		.main-content,
		.chat-area,
		.table-area {
			background-color: transparent;
		}
		.container-fluid {
			display: flex;
			height: 100vh;
			width: 100vw;
			margin: 0;
			padding: 0;
		}

	</style>
	<body data-theme="light">
		<div class="container-fluid d-flex" style="margin: 0; padding: 0; min-height: 100vh;">

<div style="padding: 5px;" class="sidebar">
				{% include 'livedash/LeftSideBar/content.html.twig' %}
			</div>
			<div id="leftSidebar" style="max-width: 240px;" class="left-block col-auto col-md-3 col-xl-2 px-sm-2 px-0">
				<div class="content">
					{% include 'livedash/LeftSideBar2/content.html.twig' %}

					<div style="bottom: 0px; position: absolute;" class="bottombar footer">
						{% include 'livedash/bottombar/content.html.twig' %}
					</div>
				</div>
			</div>
			<div class="main-content">
				<div class="right-block ">
					<div style="background-color: var(--sidebar-bg-colors); position: fixed; width: 95%;" class="topbar topbar-right-blcok">
						{% include 'livedash/Navbar/content.html.twig' %}
					</div>
					<div class="chat-areablock">
						<div class="table-area" id="dynamic-content">
						
							{% include 'livedash/Mainlivedash/content.html.twig' %}
								
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	<script>
		const checkbox = document.getElementById('chk');
function applyTheme(theme) {
if (theme === 'dark') {
document.body.setAttribute('data-theme', 'dark');
localStorage.setItem('theme', 'dark');
} else {
document.body.setAttribute('data-theme', 'light');
localStorage.setItem('theme', 'light');
}
}
checkbox.addEventListener('change', () => {
applyTheme(checkbox.checked ? 'dark' : 'light');
});

const currentTheme = localStorage.getItem('theme') || 'light';
checkbox.checked = currentTheme === 'dark';

applyTheme(currentTheme);

document.getElementById('load-cluster-dashboard').addEventListener('click', function (e) {
e.preventDefault();

fetch('{{ path('clusters_dashboard') }}').then(response => {
if (!response.ok) {
throw new Error('Network response was not ok');
}
return response.text();
}).then(html => {
document.getElementById('dynamic-content').innerHTML = html;
}).catch(error => {
console.error('There was a problem with the fetch operation:', error);
});
});

document.getElementById('load-ventes-dashboard').addEventListener('click', function (e) {
e.preventDefault();

fetch('{{ path('ventes_dashboard') }}').then(response => {
if (!response.ok) {
throw new Error('Network response was not ok');
}
return response.text();
}).then(html => {
document.getElementById('dynamic-content').innerHTML = html;
}).catch(error => {
console.error('There was a problem with the fetch operation:', error);
});
});
	</script>
{% endblock %}
