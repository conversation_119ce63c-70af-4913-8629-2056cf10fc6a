<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css">
		<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
		<link rel="stylesheet" href="{{ asset('styles/distribution.css') }}">

		<style>
		:root
		{
			--background-color: #ffffff;
			--text-color: #000000;
			--footer-color: #333333;
			--col-bg-color: transparent;
			--map-bg-color: #e0e0e0;
		}

		/* Dark Theme Colors */
		[data-theme="dark"] {
			--background-color: #121212;
			--text-color: #e0e0e0;
			--footer-color: #1f1f1f;
			--col-bg-color: #313338;
			--map-bg-color: #4d254d;
		}

		body {
			background-color: var(--background-color);
			color: var(--text-color);
		}

		footer {
			background-color: var(--footer-color);
			color: var(--text-color);
			width: 100%;
			bottom: 0;
			position: absolute;
		}

		@media(min-width: 1367px) {
			footer {
				width: 79%;
			}
		}

		.col {
			background-color: var(--col-bg-color);
			padding-left: 30px;
			padding-right: 30px;
		}

		.dark-mode .clusters-info,
		.dark-mode .bonus-cluster {
			border-color: transparent;
			background-color: transparent;
		}

		.map-analytics {
			background-color: var(--map-bg-color);
			border-color: transparent;
			background-image: linear-gradient(to top, transparent, var(--map-bg-color));
			background-blend-mode: overlay;
			border-radius: 20px 20px 0 0;
		}

		@media(min-width: 1367px) {
			footer {
				color: white;
				width: 100%;
				bottom: 0;
				position: absolute;

			}
		}

		.top-flop-cluster {
			width: 242px;
		}
		.clusters-info .bonus-cluster {
			width: 370px;
		}
		.col {
			padding-left: 30px;
			padding-right: 30px;
		}

		@media(min-width: 1367px) {
			.top-flop-cluster {
				width: 282px;
			}
			.clusters-info .bonus-cluster {
				width: 370px;
			}
			.dashboard-container {
				position: static;
			}
            .full-tabledata{
                    width: 100%;!important
            }
		}
	</style>
</head>
<body>

	<div class="dashboard-container">


		<div class="row">

			<div class="col bonus-cluster" style="padding: 0;!important">
				{% include 'distributions/DashboardItems/chartographieprise.html.twig' %}
			</div>

			<div class="col clusters-info" style="padding-left: 0; padding-right: 0;">
				{% include 'distributions/DashboardItems/filtres.html.twig' %}
			</div>

			<div class=" top-flop-cluster" style="padding-left: 0; padding-right: 0;">
				{% include 'distributions/DashboardItems/volumetrieprise.html.twig' %}
			</div>
		</div>
	</div>
	<div class="row">
		<div class="full-tabledata">
			{% include 'distributions/DashboardItems/table.html.twig' %}
		</div>

	</div>
	<footer >
		<div class="">
			{% include 'distributions/DashboardItems/footer.html.twig' %}
		</div>
	</footer>
</body></html></div><script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script><script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script><script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/js/bootstrap.min.js"></script><script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script><script>const checkbox = document.getElementById('chk');
function applyTheme(theme) {
if (theme === 'dark') {
document.body.setAttribute('data-theme', 'dark');
localStorage.setItem('theme', 'dark');
} else {
document.body.setAttribute('data-theme', 'light');
localStorage.setItem('theme', 'light');
}
}
checkbox.addEventListener('change', () => {
applyTheme(checkbox.checked ? 'dark' : 'light');
});

const currentTheme = localStorage.getItem('theme') || 'light';
checkbox.checked = currentTheme === 'dark';

applyTheme(currentTheme);

document.getElementById('load-cluster-dashboard').addEventListener('click', function (e) {
e.preventDefault();

fetch('{{ path('clusters_dashboard') }}').then(response => {
if (!response.ok) {
throw new Error('Network response was not ok');
}
return response.text();
}).then(html => {
document.getElementById('dynamic-content').innerHTML = html;
}).catch(error => {
console.error('There was a problem with the fetch operation:', error);
});
});

document.getElementById('load-ventes-dashboard').addEventListener('click', function (e) {
e.preventDefault();

fetch('{{ path('ventes_dashboard') }}').then(response => {
if (!response.ok) {
throw new Error('Network response was not ok');
}
return response.text();
}).then(html => {
document.getElementById('dynamic-content').innerHTML = html;
}).catch(error => {
console.error('There was a problem with the fetch operation:', error);
});
});</script></body></html></body></html>
