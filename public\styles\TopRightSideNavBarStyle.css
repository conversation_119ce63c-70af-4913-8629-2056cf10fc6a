.checkbox {
    opacity: 0;
    position: absolute;
}

.label {
    background-color: #111;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    position: relative;
    height: 15px;
    width: 35px;
    transform: scale(1.5);
}

.label .ball {
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: -10px;
    height: 12px;
    width: 12px;
    transform: translateX(10px);
    transition: transform 0.2s linear;
}

.checkbox:checked + .label .ball {
    transform: translateX(30px);
}

.fa-moon {
    color: #f1c40f;
}

        .collapsed {
            width: 0 !important;
            overflow: hidden;
            transition: width 0.3s;
        }