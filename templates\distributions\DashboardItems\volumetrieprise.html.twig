<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
		<style>

			.container {
				display: flex;
				justify-content: space-between; /* Align items horizontally */
				flex-wrap: wrap; /* Allow wrapping on smaller screens */
				margin-bottom: 10px;
				margin-top: -30px;
				width: 100%;
				padding-right: 0;
				padding-left: 0px;
			}
			.chart-container {
				flex: 1 1 40%; /* Grow to fill space, with a base width of 40% */
				height: 100px;
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;

				width: 100px; /* Minimum width for responsiveness */
			}
			.tables-container {
				flex: 1 1 30%; /* Grow to fill space, with a base width of 30% */
				display: flex;
				flex-direction: column;


			}
			.sales-table,
			.sales-table th,
			.sales-table td {
				border: none !important;
				background-color: transparent;
				padding: 5px 0; /* Add some padding for better spacing */
			}
			.sales-table th {
				color: #000;
				font-size: 0.8em;
				text-align: left;
			}
			.sales-table td {
				color: #72ac9d;
				font-size: 0.8em;
				text-align: left;
			}
			.sales-table thead {
				font-weight: bold;
				border-bottom: 2px solid transparent;
			}
			.sales-table tbody tr:nth-child(even) {
				background-color: transparent;
			}
			.sales-table .negative-sales {
				color: red;
			}
			.sales-table-bad {
				color: black;
				border-radius: 15px;
				border-collapse: collapse;
				margin-top: 10px;
				text-align: left;
				border: none;
			}
			.sales-table-bad td {
				padding: 1px 11px;
				font-size: 0.8em;
				color: red;
				text-align: left;
				border: none;
			}
			.text-overlay {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				text-align: center;
				color: #000;
				z-index: 10;
			}
			#global-text {
				font-size: 1.1em;
				color: #333;
				text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
				margin: 0;
				line-height: 1.2;
			}
			#total-sales {
				font-size: 2em;
				font-weight: bold;
				color: #785EF0;
				margin: 0;
				line-height: 1.2;
			}
			.sales-info {
				font-size: 15px;
				color: #333;
				margin: 0;
				line-height: 1;
			}
			#sales-bad {
				font-size: 0.6em;
				color: red;
				margin: 0;
				font-weight: bold;
				line-height: 1.2;
			}
			.icons {
				display: flex;
				align-items: center;
				padding: 10px 0; /* Adjusted padding for icons */
			}
			.icons i {
				margin: 0 -13px;
			}
			.col-5 {
				-ms-flex: 0 0 41.666667%;
				flex: 0 0 41.666667%;
				max-width: 42.666667%;
			}
			.cardes {
				border: 1px solid #ccc;
				border-radius: 15px;
				background-color: #fff;
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
				width: 100px;
				height: 65px;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 5px;
				text-align: center;
				font-size: 23px;
				margin-bottom: 8px;
				font-weight: bold;
				transition: background-color 0.3s; /* Smooth transition on hover */
			}
			.cardes:hover {
				background-color: #f0f0f0; /* Light gray on hover */
			}
			.circule {
				border: 1px solid #ccc;
				border-radius: 50%;
				background-color: transparent;
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
				width: 60px;
				height: 60px;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #000;
				border-color: #000;
				z-index: auto;
			}

			/* Dark Mode Styles */
			body.dark-mode .top-flop-cluster {
				border-color: #222; /* Dark border */
				background-color: #222;
			}
			body.dark-mode .sales-table th,
			body.dark-mode .sales-table td {
				color: #fff; /* Light text for table */
			}
			body.dark-mode .sales-table {
				background-color: #444; /* Dark background for tables */
			}
			.dark-mode #total-sales {
				color: #785EF0; /* Light color for total sales */
			}
			.dark-mode .cardes {
				background-color: #111; /* Dark background for cards */

				border-color: transparent;
			}
			.dark-mode .circule {
				border-color: white; /* Light border for circle */
			}
			.dark-mode .sales-info {
				color: white;  !important
			}
			.dark-mode .title {
				color: white;  !important
			}
            .title{
                color: #333;
            }
		</style>
	</head>
	<body>

		<h6 class="title" style="padding: 10px;">Volumetrie</h6>

		<div class="container">
			<div class="chart-container">
				<canvas id="globalSalesChart"></canvas>
				<div style="margin-top: 6px;" class="circule">
					<div class="text-overlay">
						<div id="total-sales">146</div>
						<div class="sales-info">Prise</div>
					</div>
				</div>
			</div>

			<div
				class="tables-container" style="margin-top: 10px; margin-left: 18px;">
				<!-- Added class for proper flex handling -->
				<div
					class="row">
					<!-- First Column -->
					<div class="col-5">
						<div class="cardes" style="color: #785EF0;">
							<img src="{{ asset('image/Icon-Adsl.svg') }}" alt="Router Icon" width="60" height="60">
							<div>14</div>
						</div>
						<div class="cardes" style="color: #785EF0;">
							<img src="{{ asset('image/Icon-Mob.svg') }}" alt="Router Icon" width="60" height="60">
							<div>11</div>
						</div>
						<div class="cardes" style="color: #785EF0; width: 210px;">
							<div class="icons">
								<img src="{{ asset('image/Icon-Mob-adsl.svg') }}" alt="Router Icon" width="60" height="60">
							</div>
							<div>6</div>
						</div>
					</div>

					<!-- Second Column -->
					<div class="col-5">
						<div class="cardes" style="color: #785EF0;">
							<div class="icon">
								<img src="{{ asset('image/Icon-Fibre.svg') }}" alt="Router Icon" width="60" height="60">
							</div>
							<div>14</div>
						</div>
						<div class="cardes" style="color: #785EF0;">
							<div class="icons">
								<img src="{{ asset('image/Icon-Mob-Fibre.svg') }}" alt="Router Icon" width="60" height="60">
							</div>
							<div>3</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>
		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>
		<script>
			feather.replace();
// Initialize feather icons

// Initial colors for light and dark mode
const lightModeColors = ['#3b2089', '#4AC1E6'];
const darkModeColors = ['#205c89', '#2c79d1']; // Adjusted for dark mode

const ctx = document.getElementById('globalSalesChart').getContext('2d');
const globalSalesChart = new Chart(ctx, {
type: 'doughnut',
data: {
datasets: [
{
data: [
50, 50
],
backgroundColor: lightModeColors, // Default colors
borderWidth: 0
}
]
},
options: {
cutout: '80%',
responsive: true,
maintainAspectRatio: false,
plugins: {
tooltip: {
enabled: false
},
hover: {
mode: null
}
}
}
});

// Dark mode toggle functionality
		</script>
		<script>
			const darkModeToggle = document.getElementById('chk');
darkModeToggle.addEventListener('change', () => {
document.body.classList.toggle('dark-mode');

// Update chart colors based on the dark mode state
if (document.body.classList.contains('dark-mode')) {
globalSalesChart.data.datasets[0].backgroundColor = darkModeColors;
} else {
globalSalesChart.data.datasets[0].backgroundColor = lightModeColors;
} globalSalesChart.update(); // Refresh the chart
});
		</script>
	</body>
</html>
