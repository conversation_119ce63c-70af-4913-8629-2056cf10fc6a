<?php

namespace App\Controller;

use App\Entity\Competitions;
use App\Entity\PlayersClub;
use App\Service\ClubService;
use App\Service\PlayersService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpFoundation\Request;

class CompetitionController extends AbstractController
{

    private $clubService;
    private $playersService;
    private $entityManager;
    private $client;
    public function __construct(ClubService $clubService, PlayersService $playersService, EntityManagerInterface $entityManager, HttpClientInterface $client)
    {
        $this->clubService = $clubService;
        $this->playersService = $playersService;
        $this->entityManager = $entityManager;
        $this->client = $client;
    }
}