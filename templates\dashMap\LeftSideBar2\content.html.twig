<div class="sidebar-header" id="eventTrigger">

    <i class="bi bi-calendar-event" style="color: transparent"></i>
</div>

<div class="sectionDivider"></div>
	<div class="contentent  " style="">
							<!-- Section Formulaires -->
							<div class="Tree-Formulaires">
								<div class="headerTreeSection">
									<div style="display: flex; align-items: center;">
										<i class="bi bi-list-task" style="color: #ea7362; font-size: 17px; margin-right: 4px; margin-left: 6px;"></i>
										<p class="sidebar-title">Formulaires</p>
									</div>
									<i class="bi bi-plus-circle icon-color"></i>
								</div>
								<div class="sectionDivider skeleton" style="margin-top: 5px; margin-bottom: 0px;"></div>
								<div class="Tree-Froms scrollbar-custom skeleton" style="background-color: var(--tree-view-bg);color: var(--tree-view-color);">
									<div class="tree-view skeleton">
										<ul id="tree-root" style="font-size: 11px;height: 45vh;">
										</ul>
									</div>
								</div>
							</div>
							<div class=" resizer resizer2"><spans>. . .</spans></div>
							<!-- Section Roles -->
							<div class="Tree-Roles">
								<div class="headerTreeSection" id="headerTreeSectionRoles">
									<div style="display: flex; align-items: center;">
										<i class="bi bi-people-fill" style="color:#a074c4; font-size: 17px; margin-right: 4px; margin-left: 5px;"></i>
										<p class="sidebar-title">Roles</p>
									</div>
									<i class="bi bi-plus-circle icon-color"></i>
								</div>
								<div class="sectionDivider skeleton" style="margin-top: 5px; margin-bottom: 0px;"></div>
								<div class="Tree-Roles-content scrollbar-custom skeleton">
									<div class="tree-view skeleton">
										<ul id="tree-root-roles">
										
										</ul>
									</div>
								</div>
							</div>

					</div>
                    <div id="TreeViewIcon" folder="{{asset('discord/treeview/dossier.svg')}}" fleche="{{asset('discord/treeview/Fleche.svg')}}" flecheFields="{{asset('discord/treeview/Fleche-contour.svg')}}" databaseIcon="{{asset('discord/treeview/databaseIcon.svg')}}"></div>



<script>
    var HierarchyData = JSON.parse({{ HierarchyData|raw }});
    console.log(HierarchyData);
</script>