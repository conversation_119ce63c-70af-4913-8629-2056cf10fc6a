
<style>
.heart {
	position: relative;
	width: 15px;
	height: 15px;
	background-color: var(--coloredTextPrimary);
	transform: rotate(45deg);
	animation: heartbeat 1.4s linear infinite
}

.heart:before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: var(--coloredTextPrimary);
	transform: translateY(-50%);
	border-radius: 50%;
}

.heart:after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: var(--coloredTextPrimary);
	transform: translateX(-50%);
	border-radius: 50%;
}


@keyframes heartbeat {
	0% { transform: rotate(45deg) scale(1); }
	25% { transform: rotate(45deg) scale(1); }
	30% { transform: rotate(45deg) scale(1.4); }
	50% { transform: rotate(45deg) scale(1.2); }
	70% { transform: rotate(45deg) scale(1.4); }
	100% { transform: rotate(45deg) scale(1); }
}

.Beat {
    font-family: Consolas, monospace !important;
    height: 67px;
    width: 100%;
    padding: 3px;
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 10px;
    justify-content: space-around;
}
.cardContent{
display: flex;
    justify-content: space-around;
    align-items: center;
    flex-direction: column;
    gap: 3px;
}
.cardTitle{font-size:14px;color: var(--tree-view-color, #000) !important;}

</style>
<div class="Beat" style="">
    <div class="heart"></div>
    <div class="cardContent">
        <div class="cardTitle">PROD</div>
        <span class="productionsByDayProd"style="color: var(--coloredTextPrimary);font-size: 20px;">-</span>
        <span style="color: #B0B0B0;font-size: 16px;">23/H</span>
    </div>
<div class="cardContent">
    <div class="cardTitle">PANIERS</div>
    <span id="nombreLignesSpan" style="color: var(--coloredTextPrimary);font-size: 20px;">223</span>
    <div>
        <span id="nombreHourActuel" style="color: #B0B0B0;font-size: 16px;">17</span> <!-- Nombre de lignes de l'heure actuelle -->
    </div>
</div>


</div>
<script>


</script>