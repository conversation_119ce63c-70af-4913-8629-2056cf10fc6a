const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
document.getElementById('headerTreeSection').addEventListener('click', function (event) {
    event.stopPropagation();
    const initialDropdown = document.getElementById('initialDropdown');
    const headerTreeSection = document.getElementById('headerTreeSection');
    const rect = headerTreeSection.getBoundingClientRect();
    initialDropdown.style.position = 'absolute';
    initialDropdown.style.top = `${rect.bottom + window.scrollY }px`;
    initialDropdown.style.left = `${rect.left + window.scrollX -10}px`;
    initialDropdown.style.display = 'block';
});
document.addEventListener('DOMContentLoaded', function () {
    const resizers = document.querySelectorAll('.resizer');

    resizers.forEach((resizer) => {
        let startY, startHeightPrev, startHeightNext;

        resizer.addEventListener('mousedown', function (e) {
            const prevDiv = resizer.previousElementSibling;
            const nextDiv = resizer.nextElementSibling;

            // Store the initial mouse position and heights
            startY = e.pageY;
            startHeightPrev = prevDiv.offsetHeight;
            startHeightNext = nextDiv.offsetHeight;

            // Add mousemove and mouseup event listeners
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);

            function mouseMoveHandler(e) {
                const dy = e.pageY - startY;

                // Calculate new heights
                const newHeightPrev = Math.max(50, startHeightPrev + dy);
                const newHeightNext = Math.max(50, startHeightNext - dy);

                // Apply new heights to the sections
                prevDiv.style.height = `${newHeightPrev}px`;
                nextDiv.style.height = `${newHeightNext}px`;
            }

            function mouseUpHandler() {
                // Remove the event listeners when mouse is released
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            }
        });
    });
    document.getElementById('HandleRightPanel').addEventListener('click', function() {
        var sidebar = document.querySelector('.right-sidebar-concepteur');
        var sidebarStyle = window.getComputedStyle(sidebar);
        var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
        if (sidebarStyle.right === '0px') {
            sidebar.style.right = '-319px';
            IconRightPanelToggle.style.transform = 'rotate(-90deg)';
        } else {
            sidebar.style.right = '0px';
            IconRightPanelToggle.style.transform = 'rotate(90deg)';
        }
    });
});
document.addEventListener('DOMContentLoaded', () => {
    const currentThemeDisplay = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const currentThemeIcon = currentThemeDisplay.querySelector('img');
    const currentThemeText = currentThemeDisplay.childNodes[1];

    let currentTheme = localStorage.getItem('theme') || 'dark'||'darkblue'||'lightsand'||'darklight'||"darkpurple";
    document.body.setAttribute('data-theme', currentTheme);

    const currentThemeElement = document.querySelector(`li[data-theme="${currentTheme}"]`);
    if (currentThemeElement) {
      updateThemeDisplay(currentThemeElement);
    }

    currentThemeDisplay.addEventListener('click', () => {
      themeOptions.style.display = themeOptions.style.display === 'block' ? 'none' : 'block';
    });

    themeOptions.addEventListener('click', event => {
      const themeChoice = event.target.closest('li');
      if (themeChoice) {
        const selectedTheme = themeChoice.getAttribute('data-theme');
        const imgSrc = themeChoice.querySelector('img').src;
        const themeName = themeChoice.textContent.trim();

        currentThemeIcon.src = imgSrc;
        currentThemeText.nodeValue = " " + themeName + " ";

        document.body.setAttribute('data-theme', selectedTheme);
        localStorage.setItem('theme', selectedTheme);

        themeOptions.style.display = 'none';

        document.querySelectorAll('.theme-options li').forEach(li => li.classList.remove('active'));
        themeChoice.classList.add('active');
      }
    });
});


function updateThemeDisplay(themeElement) {
    const iconSrc = themeElement.querySelector('img').src;
    const iconName = themeElement.textContent.trim();
    document.querySelector('.current-theme img').src = iconSrc;
    document.querySelector('.current-theme').childNodes[1].nodeValue = " " + iconName + " ";
}
const toggleButtonTable = document.getElementById('toggle-Chart');
const TablePanel = document.getElementById('displayChartPanel');
let isTableVisible = false;
TablePanel.style.bottom = '-330px';

toggleButtonTable.addEventListener('click', function () {
    TablePanel.style.transition='bottom 0.3s ease-in-out';
    TablePanel.style.height = '350px';
    var IcontoggleChart = document.querySelector('.IcontoggleChart');
    if (isTableVisible) {
        TablePanel.style.bottom = '-330px';
        setTimeout(() => IcontoggleChart.style.transform = 'rotate(0deg)', 300);
    } else {
        setTimeout(() => TablePanel.style.bottom = '0', 10);
        IcontoggleChart.style.transform = 'rotate(180deg)';
    }

    isTableVisible = !isTableVisible;
    document.querySelectorAll('.ChartsByType').forEach(element => {
        if (TablePanel.style.height === '350px' || TablePanel.style.height === '') { 
            element.style.height = '100%';
        } else {
            element.style.height = '157px';
        }
    });
    //drawAudienceChart();
    //changeCanvaschartHeight(TablePanel);
});


const openPanelButton = document.getElementById('openPanel');
const closePanelButton = document.getElementById('closePanel');
const productionPanel = document.getElementById('displayproductionPanel');

openPanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '0';
});

closePanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '-100%';
    closePanelButton.style.top='39px';
});
var currentDate = new Date();
// Format the current date as 'dd-mm-yyyy'
var day = String(currentDate.getDate()).padStart(2, '0');
var month = String(currentDate.getMonth() + 1).padStart(2, '0');

var year = currentDate.getFullYear();
let Type='V';
const formattedDate = `${day}-${month}-${year}`;
function formatDateRange(month, year) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    const formatStartDate = `${String(startDate.getDate()).padStart(2, '0')}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;
    const formatEndDate = `${String(endDate.getDate()).padStart(2, '0')}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${endDate.getFullYear()}`;
    return `debut=${formatStartDate}&fin=${formatEndDate}`;
}
document.addEventListener('DOMContentLoaded', function() {
    setCurrentMonthName();

    var dropdownButton = document.getElementById('MonthSelector');
    var dropdownMenu = document.querySelector('.MonthSelectorDropdown');
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
    dropdownButton.addEventListener('click', function() {
        var isExpanded = this.getAttribute('aria-expanded') === 'true';
        this.setAttribute('aria-expanded', !isExpanded);
        dropdownMenu.style.display = isExpanded ? 'none' : 'flex';
        if (isExpanded) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });
});

function setCurrentMonthName() {
    const date = new Date();
    const monthIndex = date.getMonth();
    const monthNames = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
        "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"];

    const currentMonthName = monthNames[monthIndex];
    document.querySelector('.CurrentlyMonthApplyed').textContent = currentMonthName;
}
// import CompetitionData from './CompetitionData.json';

document.addEventListener("DOMContentLoaded", async function () {
    let data = HierarchyData;
    const htmlContent = generateHierarchyHtml(data);
    const treeRoot = document.getElementById('tree-root');
    treeRoot.innerHTML = htmlContent;
    setupCompetitionListeners();


});
function generateHierarchyHtml(data) {
  let htmlContent = '';

  data.forEach(competition => {
    htmlContent += `
      <li class="formListItem">
        <div class="caret formSpan ClusterSpan" data-competition-id="${competition.id}" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
          <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;">
            <div style="display: flex; align-items: center;">
              <div class="flecheimg caretImg" style="display: flex; align-items: center;">
                <svg style="height: 12.7px; width: 12.7px; fill: var(--tree-view-color); opacity: 0.5;" version="1.1" viewBox="0 0 12.7 12.7" xmlns="http://www.w3.org/2000/svg"><g><polygon points="1,1.7 1,11.1 11.7,6.4"/></g></svg>
              </div>
              <div style="display: flex; align-items: center;">
                <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" viewBox="0 0 12.7 12.7" xmlns="http://www.w3.org/2000/svg"><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
              </div>
              <strong>${competition.name}</strong> 
            </div>
            <div class="total-cluster" style="margin-right: 10px;">
               (${competition.country})
            </div>
          </div>
        </div>
        <ul class="nested">
          
        </ul>
      </li>
    `;
  });

  return htmlContent;
}


function setupCompetitionListeners() {
  const competitionElements = document.querySelectorAll('.ClusterSpan[data-competition-id]');

  competitionElements.forEach(elem => {
    elem.addEventListener('click', async function () {
      const competitionId = this.getAttribute('data-competition-id');
      const nestedUl = this.nextElementSibling; // <ul class="nested">

      // Toggle visibility (open/close)
      nestedUl.classList.toggle('active');

      // If already loaded, just toggle visibility without fetching again
      if (this.classList.contains('loaded')) return;

      this.classList.add('loaded');

      try {
        const res = await fetch(`https://transfermarkt-api.fly.dev/competitions/${competitionId}/clubs`);
        const data = await res.json();

        if (data.clubs && data.clubs.length > 0) {
          const clubsHtml = data.clubs.map(club => `
            <li class="field-list-item">
              <div class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
                <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;">
                  <div class="fieldLiSpan" style="flex-grow: 1; display: flex; align-items: center;">
                    <div style="display: flex; align-items: center;">
                      <svg style="height: 12px; width: 12px; fill: var(--coloredTextPrimary); margin-right: 5px;" viewBox="0 0 12.7 12.7" xmlns="http://www.w3.org/2000/svg"><path d="M12.7,1.1c0-0.6-0.5-1.1-1.1-1.1H1.1C0.5,0,0,0.5,0,1.1v4.7h12.7V1.1z"/><path d="M12.7,11.6V6.9H0v4.7c0,0.6,0.5,1.1,1.1,1.1h10.4C12.2,12.7,12.7,12.2,12.7,11.6z"/></svg>
                    </div>
                    ${club.name}
                  </div>
                </div>
              </div>
            </li>
          `).join('');

          nestedUl.innerHTML = clubsHtml;
        } else {
          nestedUl.innerHTML = `<li class="field-list-item"><em>No clubs found</em></li>`;
        }
      } catch (err) {
        console.error('Failed to fetch clubs:', err);
        nestedUl.innerHTML = `<li class="field-list-item"><em>Error loading clubs</em></li>`;
      }
    });
  });
}



document.addEventListener("DOMContentLoaded", function () {
  const searchInput = document.getElementById('SearchInputTopBar');
  const resultsContainer = document.getElementById('playerSearchResults');
  const profileContainer = document.getElementById('PlayerProfile');

  let timeout = null;

  searchInput.addEventListener('input', function () {
    const query = this.value.trim();

    clearTimeout(timeout);

    if (query.length < 2) {
      resultsContainer.innerHTML = '';
      return;
    }

    timeout = setTimeout(() => {
      fetch(`https://transfermarkt-api.fly.dev/players/search/${encodeURIComponent(query)}?page_number=1`)
        .then(response => response.json())
        .then(data => {
          if (data.results && data.results.length > 0) {
            resultsContainer.style.display = 'flex';
            profileContainer.style.display = 'none';

            resultsContainer.innerHTML = data.results.map(player => {
              const marketValue = player.marketValue
                ? `€${(player.marketValue / 1_000_000).toFixed(2)}M`
                : 'N/A';

              const nationality = player.nationalities?.join(', ') ?? 'Unknown';

              return `
                <div class="playersCard player-result" data-player-id="${player.id}" style="cursor: pointer;">
                  <strong>${player.name} (${player.position})</strong>
                  <span><i>Age:</i> ${player.age}, <i>Nationality:</i> ${nationality}</span>
                  <span><i>Club:</i> ${player.club?.name || 'Unknown'}, <i>Market Value:</i> ${marketValue}</span>
                </div>
              `;
            }).join('');

            // Add click listeners for each card
            document.querySelectorAll('.player-result').forEach(card => {
              card.addEventListener('click', () => {
                const playerId = card.getAttribute('data-player-id');
                loadPlayerProfile(playerId);
              });
            });

          } else {
            resultsContainer.innerHTML = `<div>No results found for <strong>${query}</strong>.</div>`;
          }
        })
        .catch(err => {
          console.error(err);
          resultsContainer.style.display = 'flex';
          profileContainer.style.display = 'none';

          const dummyCards = Array.from({ length: 6 }).map((_, index) => `
 
  <div class="card green CardFootballPalyer" style="min-width: 30%;" onclick="loadPlayerProfile()" >
    <div class="additional">
      <div class="user-card">
        <div class="level center">
          Level 13
        </div>
        <div class="points center">
          5,312 Points
        </div>
        <svg width="110" height="110" viewBox="0 0 250 250" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="title desc" class="center">
          <title id="title">Teacher</title>
          <desc id="desc">Cartoon of a Caucasian woman smiling, and wearing black glasses and a purple shirt with white collar drawn by Alvaro Montoro.</desc>
          <style>
            .skin { fill: #eab38f; }
            .eyes { fill: #1f1f1f; }
            .hair { fill: #2f1b0d; }
            .line { fill: none; stroke: #2f1b0d; stroke-width:2px; }
          </style>
          <defs>
            <clipPath id="scene">
              <circle cx="125" cy="125" r="115"/>
            </clipPath>
            <clipPath id="lips">
              <path d="M 106,132 C 113,127 125,128 125,132 125,128 137,127 144,132 141,142  134,146  125,146  116,146 109,142 106,132 Z" />
            </clipPath>
          </defs>
          <circle cx="125" cy="125" r="120" fill="rgba(0,0,0,0.15)" />
          <g stroke="none" stroke-width="0" clip-path="url(#scene)">
            <rect x="0" y="0" width="250" height="250" fill="#b0d2e5" />
            <g id="head">
              <path fill="none" stroke="#111111" stroke-width="2" d="M 68,103 83,103.5" />
              <path class="hair" d="M 67,90 67,169 78,164 89,169 100,164 112,169 125,164 138,169 150,164 161,169 172,164 183,169 183,90 Z" />
              <circle cx="125" cy="100" r="55" class="skin" />
              <ellipse cx="102" cy="107" rx="5" ry="5" class="eyes" id="eye-left" />
              <ellipse cx="148" cy="107" rx="5" ry="5" class="eyes" id="eye-right" />
              <rect x="119" y="140" width="12" height="40" class="skin" />
              <path class="line eyebrow" d="M 90,98 C 93,90 103,89 110,94" id="eyebrow-left" />
              <path class="line eyebrow" d="M 160,98 C 157,90 147,89 140,94" id="eyebrow-right"/>
              <path stroke="#111111" stroke-width="4" d="M 68,103 83,102.5" />
              <path stroke="#111111" stroke-width="4" d="M 182,103 167,102.5" />
              <path stroke="#050505" stroke-width="3" fill="none" d="M 119,102 C 123,99 127,99 131,102" />
              <path fill="#050505" d="M 92,97 C 85,97 79,98 80,101 81,104 84,104 85,102" />
              <path fill="#050505" d="M 158,97 C 165,97 171,98 170,101 169,104 166,104 165,102" />
              <path stroke="#050505" stroke-width="3" fill="rgba(240,240,255,0.4)" d="M 119,102 C 118,111 115,119 98,117 85,115 84,108 84,104 84,97 94,96 105,97 112,98 117,98 119,102 Z" />
              <path stroke="#050505" stroke-width="3" fill="rgba(240,240,255,0.4)" d="M 131,102 C 132,111 135,119 152,117 165,115 166,108 166,104 166,97 156,96 145,97 138,98 133,98 131,102 Z" />
              <path class="hair" d="M 60,109 C 59,39 118,40 129,40 139,40 187,43 189,99 135,98 115,67 115,67 115,67 108,90 80,109 86,101 91,92 92,87 85,99 65,108 60,109" />
              <path id="mouth" fill="#d73e3e" d="M 106,132 C 113,127 125,128 125,132 125,128 137,127 144,132 141,142  134,146  125,146  116,146 109,142 106,132 Z" /> 
              <path id="smile" fill="white" d="M125,141 C 140,141 143,132 143,132 143,132 125,133 125,133 125,133 106.5,132 106.5,132 106.5,132 110,141 125,141 Z" clip-path="url(#lips)" />
            </g>
            <g id="shirt">
              <path fill="#8665c2" d="M 132,170 C 146,170 154,200 154,200 154,200 158,250 158,250 158,250 92,250 92,250 92,250 96,200 96,200 96,200 104,170 118,170 118,170 125,172 125,172 125,172 132,170 132,170 Z"/>
              <path id="arm-left" class="arm" stroke="#8665c2" fill="none" stroke-width="14" d="M 118,178 C 94,179 66,220 65,254" />
              <path id="arm-right" class="arm" stroke="#8665c2" fill="none" stroke-width="14" d="M 132,178 C 156,179 184,220 185,254" />
              <path fill="white" d="M 117,166 C 117,166 125,172 125,172 125,182 115,182 109,170 Z" />
              <path fill="white" d="M 133,166 C 133,166 125,172 125,172 125,182 135,182 141,170 Z" />
              <circle cx="125" cy="188" r="4" fill="#5a487b" />
              <circle cx="125" cy="202" r="4" fill="#5a487b" />
              <circle cx="125" cy="216" r="4" fill="#5a487b" />
              <circle cx="125" cy="230" r="4" fill="#5a487b" />
              <circle cx="125" cy="244" r="4" fill="#5a487b" />
              <path stroke="#daa37f" stroke-width="1" class="skin hand" id="hand-left" d="M 51,270 C 46,263 60,243 63,246 65,247 66,248 61,255 72,243 76,238 79,240 82,243 72,254 69,257 72,254 82,241 86,244 89,247 75,261 73,263 77,258 84,251 86,253 89,256 70,287 59,278" /> 
              <path stroke="#daa37f" stroke-width="1" class="skin hand" id="hand-right" d="M 199,270 C 204,263 190,243 187,246 185,247 184,248 189,255 178,243 174,238 171,240 168,243 178,254 181,257 178,254 168,241 164,244 161,247 175,261 177,263 173,258 166,251 164,253 161,256 180,287 191,278"/> 
            </g>
          </g>
        </svg>
      </div>
      <div class="more-info">
        <h1>Player Test</h1>
        <div class="coords">
          <span>Group Name</span>
          <span>Joined January 2019</span>
        </div>
        <div class="coords">
          <span>Position/Role</span>
          <span>City, Country</span>
        </div>
        <div class="stats">
          <div>
            <div class="title">Awards</div>
            <i class="fa fa-trophy"></i>
            <div class="value">2</div>
          </div>
          <div>
            <div class="title">Matches</div>
            <i class="fa fa-gamepad"></i>
            <div class="value">27</div>
          </div>
          <div>
            <div class="title">Pals</div>
            <i class="fa fa-group"></i>
            <div class="value">123</div>
          </div>
          <div>
            <div class="title">Coffee</div>
            <i class="fa fa-coffee"></i>
            <div class="value infinity">∞</div>
          </div>
        </div>
      </div>
    </div>
    <div class="general">
      <h1>Jane Doe</h1>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce a volutpat mauris, at molestie lacus. Nam vestibulum sodales odio ut pulvinar.</p>
      <span class="more">Mouse over the card for more info</span>
    </div>
  </div>
`).join('');


          resultsContainer.innerHTML = dummyCards;

          // Optional: Add click listeners if needed
          document.querySelectorAll('.player-result').forEach(card => {
            card.addEventListener('click', () => {
              const playerId = card.getAttribute('data-player-id');
              loadPlayerProfile(playerId); // if you want the dummy cards to trigger something
            });
          });
        });

    }, 400); // debounce
  });



  // Back to search
  window.backToSearch = function () {
    profileContainer.style.display = 'none';
    resultsContainer.style.display = 'flex';
  };
});

function loadPlayerProfile(playerId) {
  const resultsContainer = document.getElementById('playerSearchResults');
  const profileContainer = document.getElementById('PlayerProfile');

  resultsContainer.style.display = 'none';
  profileContainer.style.display = 'block';
  profileContainer.innerHTML = `<p>Loading profile...</p>`;

//   fetch(`https://transfermarkt-api.fly.dev/players/${playerId}/profile`)
//     .then(response => {
//       if (!response.ok) {
//         throw new Error(`API error: ${response.status}`);
//       }
//       return response.json();
//     })
//     .then(player => renderPlayerProfile(player, profileContainer))
//     .catch(err => {
//       console.error('Error loading profile:', err);

      
//     });
    const fallbackPlayer = {
        name: "Unavailable Player",
        nameInHomeCountry: "N/A",
        imageUrl: "https://via.placeholder.com/120x150?text=No+Image",
        age: "N/A",
        citizenship: ["Unknown"],
        position: {
          main: "Unknown",
          other: []
        },
        club: {
          name: "N/A",
          joined: "N/A"
        },
        shirtNumber: "N/A",
        foot: "N/A",
        placeOfBirth: {
          city: "N/A",
          country: "N/A"
        },
        outfitter: "N/A",
        relatives: [],
        socialMedia: [],
        url: "#"
      };

      renderPlayerProfile(fallbackPlayer, profileContainer);
}



function renderPlayerProfile(player, container) {
  const nationality = player.citizenship?.join(', ') ?? '';
  const positions = [player.position?.main, ...(player.position?.other || [])].filter(Boolean).join(', ');
  //const image = player.imageUrl || '';
  const marketValue = player.marketValue ? `€${(player.marketValue / 1_000_000).toFixed(2)}M` : 'N/A';
  const relatives = player.relatives?.map(r => r.name).join(', ') || 'None';
  const socialLinks = player.socialMedia?.map(url => `<a href="${url}" target="_blank">${url}</a>`).join('<br/>') || 'N/A';

  let template = document.getElementById('playerProfileTemplate').innerHTML;

  template = template
    //.replaceAll('__imageUrl__', image)
    .replaceAll('__name__', player.name)
    .replaceAll('__nameInHomeCountry__', player.nameInHomeCountry)
    .replaceAll('__position__', positions)
    .replaceAll('__marketValue__', marketValue)
    .replaceAll('__age__', player.age)
    .replaceAll('__nationality__', nationality)
    .replaceAll('__clubName__', player.club?.name || 'N/A')
    .replaceAll('__joined__', player.club?.joined || 'N/A')
    .replaceAll('__shirtNumber__', player.shirtNumber || 'N/A')
    .replaceAll('__foot__', player.foot || 'N/A')
    .replaceAll('__city__', player.placeOfBirth?.city || 'N/A')
    .replaceAll('__country__', player.placeOfBirth?.country || 'N/A')
    .replaceAll('__outfitter__', player.outfitter || 'N/A')
    .replaceAll('__relatives__', relatives)
    .replaceAll('__socialLinks__', socialLinks)
    .replaceAll('__profileUrl__', player.url);

  container.innerHTML = template;
}


const buttons = document.querySelectorAll(".card-buttons button");
const card = document.querySelector(".card-football-player");

const handleButtonClick = (e) => {
  const targetSection = e.target.getAttribute("data-section");
  card.setAttribute("data-state", targetSection);
};

buttons.forEach((btn) => {
  btn.addEventListener("click", handleButtonClick);
});
