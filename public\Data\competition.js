const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
document.getElementById('headerTreeSection').addEventListener('click', function (event) {
    event.stopPropagation();
    const initialDropdown = document.getElementById('initialDropdown');
    const headerTreeSection = document.getElementById('headerTreeSection');
    const rect = headerTreeSection.getBoundingClientRect();
    initialDropdown.style.position = 'absolute';
    initialDropdown.style.top = `${rect.bottom + window.scrollY }px`;
    initialDropdown.style.left = `${rect.left + window.scrollX -10}px`;
    initialDropdown.style.display = 'block';
});
document.addEventListener('DOMContentLoaded', function () {
    const resizers = document.querySelectorAll('.resizer');

    resizers.forEach((resizer) => {
        let startY, startHeightPrev, startHeightNext;

        resizer.addEventListener('mousedown', function (e) {
            const prevDiv = resizer.previousElementSibling;
            const nextDiv = resizer.nextElementSibling;

            // Store the initial mouse position and heights
            startY = e.pageY;
            startHeightPrev = prevDiv.offsetHeight;
            startHeightNext = nextDiv.offsetHeight;

            // Add mousemove and mouseup event listeners
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);

            function mouseMoveHandler(e) {
                const dy = e.pageY - startY;

                // Calculate new heights
                const newHeightPrev = Math.max(50, startHeightPrev + dy);
                const newHeightNext = Math.max(50, startHeightNext - dy);

                // Apply new heights to the sections
                prevDiv.style.height = `${newHeightPrev}px`;
                nextDiv.style.height = `${newHeightNext}px`;
            }

            function mouseUpHandler() {
                // Remove the event listeners when mouse is released
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            }
        });
    });
    document.getElementById('HandleRightPanel').addEventListener('click', function() {
        var sidebar = document.querySelector('.right-sidebar-concepteur');
        var sidebarStyle = window.getComputedStyle(sidebar);
        var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
        if (sidebarStyle.right === '0px') {
            sidebar.style.right = '-319px';
            IconRightPanelToggle.style.transform = 'rotate(-90deg)';
        } else {
            sidebar.style.right = '0px';
            IconRightPanelToggle.style.transform = 'rotate(90deg)';
        }
    });
});
document.addEventListener('DOMContentLoaded', () => {
    const currentThemeDisplay = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const currentThemeIcon = currentThemeDisplay.querySelector('img');
    const currentThemeText = currentThemeDisplay.childNodes[1];

    let currentTheme = localStorage.getItem('theme') || 'dark'||'darkblue'||'lightsand'||'darklight'||"darkpurple";
    document.body.setAttribute('data-theme', currentTheme);

    const currentThemeElement = document.querySelector(`li[data-theme="${currentTheme}"]`);
    if (currentThemeElement) {
      updateThemeDisplay(currentThemeElement);
    }

    currentThemeDisplay.addEventListener('click', () => {
      themeOptions.style.display = themeOptions.style.display === 'block' ? 'none' : 'block';
    });

    themeOptions.addEventListener('click', event => {
      const themeChoice = event.target.closest('li');
      if (themeChoice) {
        const selectedTheme = themeChoice.getAttribute('data-theme');
        const imgSrc = themeChoice.querySelector('img').src;
        const themeName = themeChoice.textContent.trim();

        currentThemeIcon.src = imgSrc;
        currentThemeText.nodeValue = " " + themeName + " ";

        document.body.setAttribute('data-theme', selectedTheme);
        localStorage.setItem('theme', selectedTheme);

        themeOptions.style.display = 'none';

        document.querySelectorAll('.theme-options li').forEach(li => li.classList.remove('active'));
        themeChoice.classList.add('active');
      }
    });
});


function updateThemeDisplay(themeElement) {
    const iconSrc = themeElement.querySelector('img').src;
    const iconName = themeElement.textContent.trim();
    document.querySelector('.current-theme img').src = iconSrc;
    document.querySelector('.current-theme').childNodes[1].nodeValue = " " + iconName + " ";
}
const toggleButtonTable = document.getElementById('toggle-Chart');
const TablePanel = document.getElementById('displayChartPanel');
let isTableVisible = false;
TablePanel.style.bottom = '-330px';

toggleButtonTable.addEventListener('click', function () {
    TablePanel.style.transition='bottom 0.3s ease-in-out';
    TablePanel.style.height = '350px';
    var IcontoggleChart = document.querySelector('.IcontoggleChart');
    if (isTableVisible) {
        TablePanel.style.bottom = '-330px';
        setTimeout(() => IcontoggleChart.style.transform = 'rotate(0deg)', 300);
    } else {
        setTimeout(() => TablePanel.style.bottom = '0', 10);
        IcontoggleChart.style.transform = 'rotate(180deg)';
    }

    isTableVisible = !isTableVisible;
    document.querySelectorAll('.ChartsByType').forEach(element => {
        if (TablePanel.style.height === '350px' || TablePanel.style.height === '') { 
            element.style.height = '100%';
        } else {
            element.style.height = '157px';
        }
    });
    //drawAudienceChart();
    //changeCanvaschartHeight(TablePanel);
});


const openPanelButton = document.getElementById('openPanel');
const closePanelButton = document.getElementById('closePanel');
const productionPanel = document.getElementById('displayproductionPanel');

openPanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '0';
});

closePanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '-100%';
    closePanelButton.style.top='39px';
});
var currentDate = new Date();
// Format the current date as 'dd-mm-yyyy'
var day = String(currentDate.getDate()).padStart(2, '0');
var month = String(currentDate.getMonth() + 1).padStart(2, '0');

var year = currentDate.getFullYear();
let Type='V';
const formattedDate = `${day}-${month}-${year}`;
function formatDateRange(month, year) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    const formatStartDate = `${String(startDate.getDate()).padStart(2, '0')}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;
    const formatEndDate = `${String(endDate.getDate()).padStart(2, '0')}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${endDate.getFullYear()}`;
    return `debut=${formatStartDate}&fin=${formatEndDate}`;
}
document.addEventListener('DOMContentLoaded', function() {
    setCurrentMonthName();

    var dropdownButton = document.getElementById('MonthSelector');
    var dropdownMenu = document.querySelector('.MonthSelectorDropdown');
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
    dropdownButton.addEventListener('click', function() {
        var isExpanded = this.getAttribute('aria-expanded') === 'true';
        this.setAttribute('aria-expanded', !isExpanded);
        dropdownMenu.style.display = isExpanded ? 'none' : 'flex';
        if (isExpanded) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });
});

function setCurrentMonthName() {
    const date = new Date();
    const monthIndex = date.getMonth();
    const monthNames = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
        "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"];

    const currentMonthName = monthNames[monthIndex];
    document.querySelector('.CurrentlyMonthApplyed').textContent = currentMonthName;
}
// import CompetitionData from './CompetitionData.json';

// Variables globales pour la recherche
let allCompetitionsData = [];
let allClubsData = {};
let searchTimeout = null;

// Classe pour la recherche vocale
class VoiceSearch {
    constructor() {
        this.recognition = null;
        this.isListening = false;
        this.micIcon = null;
        this.searchInput = null;
        this.statusIndicator = null;
        this.initializeVoiceRecognition();
    }

    initializeVoiceRecognition() {
        // Vérifier la compatibilité du navigateur
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn(' Reconnaissance vocale non supportée par ce navigateur');
            return;
        }

        // Initialiser l'API de reconnaissance vocale
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();

        // Configuration de la reconnaissance
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.lang = 'fr-FR'; // Français par défaut
        this.recognition.maxAlternatives = 3; // Plusieurs alternatives pour améliorer la précision

        // Détection automatique de fin de parole
        this.silenceTimer = null;
        this.maxRecordingTime = 10000; // 10 secondes max

        // Événements de reconnaissance
        this.setupRecognitionEvents();

    }

    setupRecognitionEvents() {
        if (!this.recognition) return;

        // Début de reconnaissance
        this.recognition.onstart = () => {
            this.isListening = true;
            this.updateMicrophoneState('listening');
            this.showStatus('Parlez maintenant...');

            // Timeout de sécurité
            this.silenceTimer = setTimeout(() => {
                if (this.isListening) {
                    this.stopListening();
                    this.showStatus('Timeout - Essayez à nouveau');
                    setTimeout(() => this.resetMicrophone(), 2000);
                }
            }, this.maxRecordingTime);
        };

        // Résultat de reconnaissance
        this.recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            const speechConfidence = event.results[0][0].confidence;


            // Traitement intelligent de la requête vocale
            this.processIntelligentVoiceResult(transcript, speechConfidence);
        };

        // Erreur de reconnaissance
        this.recognition.onerror = (event) => {
            this.updateMicrophoneState('error');

            let errorMessage = 'Erreur de reconnaissance';
            switch (event.error) {
                case 'no-speech':
                    errorMessage = 'Aucune parole détectée';
                    break;
                case 'audio-capture':
                    errorMessage = 'Microphone non accessible';
                    break;
                case 'not-allowed':
                    errorMessage = 'Permission microphone refusée';
                    break;
                case 'network':
                    errorMessage = 'Erreur réseau';
                    break;
            }

            this.showStatus(errorMessage);

            // Réinitialiser après 3 secondes
            setTimeout(() => {
                this.resetMicrophone();
            }, 3000);
        };

        // Fin de reconnaissance
        this.recognition.onend = () => {
            
            this.isListening = false;

            // Nettoyer le timer
            if (this.silenceTimer) {
                clearTimeout(this.silenceTimer);
                this.silenceTimer = null;
            }

            if (this.micIcon && !this.micIcon.classList.contains('success') && !this.micIcon.classList.contains('error')) {
                this.updateMicrophoneState('inactive');
                this.hideStatus();
            }
        };
    }

    startListening() {
        if (!this.recognition) {
            alert('Reconnaissance vocale non supportée par votre navigateur.\nEssayez Chrome, Edge ou Safari.');
            return;
        }

        if (this.isListening) {
            this.stopListening();
            return;
        }

        try {
            this.recognition.start();
        } catch (error) {
            console.error('Erreur démarrage reconnaissance:', error);
            this.updateMicrophoneState('error');
            this.showStatus('Erreur de démarrage');
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    updateMicrophoneState(state) {
        if (!this.micIcon) return;

        // Supprimer toutes les classes d'état
        this.micIcon.classList.remove('inactive', 'listening', 'processing', 'success', 'error');

        // Ajouter la nouvelle classe d'état
        this.micIcon.classList.add(state);
    }

    showStatus(message, type = 'default') {
        if (!this.statusIndicator) {
            // Créer l'indicateur de statut s'il n'existe pas
            this.statusIndicator = document.createElement('div');
            this.statusIndicator.className = 'voice-status';

            const searchContainer = document.querySelector('.search-container');
            if (searchContainer) {
                searchContainer.appendChild(this.statusIndicator);
            }
        }

        // Supprimer les classes de type précédentes
        this.statusIndicator.classList.remove('competition', 'club', 'command', 'error', 'suggestion');

        // Ajouter la nouvelle classe de type
        if (type !== 'default') {
            this.statusIndicator.classList.add(type);
        }

        this.statusIndicator.textContent = message;
        this.statusIndicator.classList.add('show');
    }

    hideStatus() {
        if (this.statusIndicator) {
            this.statusIndicator.classList.remove('show');
        }
    }

    resetMicrophone() {
        this.updateMicrophoneState('inactive');
        this.hideStatus();
    }

    // Traitement intelligent du résultat vocal
    processIntelligentVoiceResult(transcript, speechConfidence) {
        this.updateMicrophoneState('processing');
        this.showStatus('Analyse en cours...');

        // Analyser la requête avec l'IA
        const analysis = processVoiceQuery(transcript);

        console.log('🧠 Analyse intelligente:', analysis);

        // Déterminer l'action à effectuer
        this.executeVoiceAction(analysis, speechConfidence);
    }

    // Exécuter l'action basée sur l'analyse vocale
    executeVoiceAction(analysis, speechConfidence) {
        const totalConfidence = (analysis.confidence + speechConfidence) / 2;

        // Actions basées sur le type détecté
        switch (analysis.action) {
            case 'ouvrir':
                this.handleOpenCommand(analysis);
                break;
            case 'fermer':
                this.handleCloseCommand(analysis);
                break;
            case 'effacer':
                this.handleClearCommand();
                break;
            case 'chercher':
            default:
                this.handleSearchCommand(analysis, totalConfidence);
                break;
        }
    }

    // Gérer la commande d'ouverture
    handleOpenCommand(analysis) {
        this.updateMicrophoneState('success');

        if (analysis.type === 'competition') {
            this.showStatus(`Ouverture: ${analysis.target}`, 'command');

            // Trouver et cliquer sur la compétition
            const competitionElement = this.findCompetitionElement(analysis.target);
            if (competitionElement) {
                competitionElement.click();
                this.showStatus(`✅ ${analysis.target} ouvert`, 'competition');
            } else {
                this.showStatus(`❌ ${analysis.target} non trouvé`, 'error');
            }
        } else {
            this.showStatus('Spécifiez une compétition à ouvrir', 'command');
        }

        setTimeout(() => this.resetMicrophone(), 3000);
    }

    // Gérer la commande de fermeture
    handleCloseCommand(analysis) {
        this.updateMicrophoneState('success');
        this.showStatus('Fermeture des compétitions ouvertes');

        // Fermer toutes les compétitions ouvertes
        const openCompetitions = document.querySelectorAll('.formListItem .caret.caret-down');
        openCompetitions.forEach(comp => comp.click());

        this.showStatus('✅ Compétitions fermées');
        setTimeout(() => this.resetMicrophone(), 2000);
    }

    // Gérer la commande d'effacement
    handleClearCommand() {
        this.updateMicrophoneState('success');
        this.showStatus('Effacement de la recherche');

        if (this.searchInput) {
            this.searchInput.value = '';
            showAllElements();
        }

        this.showStatus('✅ Recherche effacée');
        setTimeout(() => this.resetMicrophone(), 2000);
    }

    // Gérer la commande de recherche
    handleSearchCommand(analysis, confidence) {
        if (confidence > 0.6) {
            this.updateMicrophoneState('success');

            // Message intelligent basé sur le type
            let statusMessage = '';
            let statusType = 'default';

            switch (analysis.type) {
                case 'competition':
                    statusMessage = `🏆 Recherche compétition: "${analysis.target}"`;
                    statusType = 'competition';
                    break;
                case 'club':
                    statusMessage = `⚽ Recherche club: "${analysis.target}"`;
                    statusType = 'club';
                    break;
                case 'country':
                    statusMessage = `🌍 Recherche pays: "${analysis.target}"`;
                    statusType = 'competition';
                    break;
                default:
                    statusMessage = `🔍 Recherche: "${analysis.searchTerm}"`;
                    statusType = 'default';
            }

            this.showStatus(statusMessage, statusType);

            // Insérer dans le champ de recherche
            if (this.searchInput) {
                this.searchInput.value = analysis.searchTerm;
                performSearch(analysis.searchTerm, true);
            }

            // Afficher suggestions si disponibles
            if (analysis.suggestions.length > 0) {
                setTimeout(() => {
                    this.showSuggestions(analysis.suggestions);
                }, 1500);
            }

        } else {
            // Confiance faible - demander confirmation
            this.updateMicrophoneState('error');
            this.showStatus(`❓ Avez-vous dit "${analysis.originalText}" ?`);

            // Recherche quand même avec le texte original
            if (this.searchInput) {
                this.searchInput.value = analysis.originalText;
                performSearch(analysis.originalText, true);
            }
        }

        setTimeout(() => this.resetMicrophone(), 3000);
    }

    // Trouver un élément de compétition dans le DOM
    findCompetitionElement(competitionName) {
        const competitions = document.querySelectorAll('[data-competition-id]');

        for (const comp of competitions) {
            const compId = comp.getAttribute('data-competition-id');
            const compText = comp.textContent.toLowerCase();

            if (normalizeText(compId).includes(normalizeText(competitionName)) ||
                normalizeText(compText).includes(normalizeText(competitionName))) {
                return comp;
            }
        }

        return null;
    }

    // Afficher des suggestions
    showSuggestions(suggestions) {
        if (suggestions.length === 0) return;

        const suggestionText = `💡 Suggestions: ${suggestions.slice(0, 3).join(', ')}`;
        this.showStatus(suggestionText);

        setTimeout(() => this.hideStatus(), 4000);
    }

    initialize() {
        // Récupérer les éléments DOM
        this.micIcon = document.getElementById('micIcon');
        this.searchInput = document.getElementById('TreeSearch');

        if (!this.micIcon) {
            console.warn('⚠️ Icône microphone non trouvée');
            return;
        }

        // État initial
        this.updateMicrophoneState('inactive');

        // Ajouter l'événement de clic
        this.micIcon.addEventListener('click', () => {
            this.startListening();
        });

        console.log('✅ Recherche vocale configurée');
    }
}

// Fonction de normalisation du texte pour la recherche
function normalizeText(text) {
    if (!text) return '';
    return text.toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Supprime les accents
        .trim();
}

// Fonction pour vérifier si un élément correspond à la requête
function matchesQuery(item, query) {
    const normalizedQuery = normalizeText(query);
    if (!normalizedQuery) return true;

    const searchFields = [
        item.name,
        item.country,
        item.short_name,
        ...(item.keywords || [])
    ];

    return searchFields.some(field =>
        normalizeText(field).includes(normalizedQuery)
    );
}

// Base de données intelligente des mots-clés
const VOICE_KEYWORDS_DATABASE = {
    // Compétitions UEFA
    competitions: {
        'champions league': ['champions', 'ligue des champions', 'ucl', 'c1'],
        'europa league': ['europa', 'ligue europa', 'uel', 'c3'],
        'conference league': ['conference', 'ligue conference', 'uecl', 'c4'],
        'premier league': ['premier', 'angleterre', 'england', 'anglais'],
        'laliga': ['liga', 'espagne', 'spain', 'espagnol'],
        'serie a': ['serie', 'italie', 'italy', 'italien'],
        'bundesliga': ['bundesliga', 'allemagne', 'germany', 'allemand'],
        'ligue 1': ['ligue un', 'ligue 1', 'france', 'français'],
        'primeira liga': ['portugal', 'portugais', 'primeira'],
        'super lig': ['turquie', 'turkey', 'turc'],
        'russian premier league': ['russie', 'russia', 'russe'],
        'ligue 2': ['ligue deux', 'ligue 2', 'france 2'],
        'brasileirao': ['bresil', 'brazil', 'brésilien'],
        'liga argentina': ['argentine', 'argentina'],
        'j1 league': ['japon', 'japan', 'japonais']
    },

    // Clubs populaires avec variantes
    clubs: {
        'real madrid': ['real', 'madrid', 'réal', 'merengues'],
        'barcelona': ['barca', 'barcelone', 'barça', 'blaugrana'],
        'manchester city': ['city', 'man city', 'citizens'],
        'manchester united': ['united', 'man united', 'red devils'],
        'liverpool': ['liverpool', 'reds', 'lfc'],
        'arsenal': ['arsenal', 'gunners'],
        'chelsea': ['chelsea', 'blues'],
        'tottenham': ['spurs', 'tottenham'],
        'paris saint germain': ['psg', 'paris', 'saint germain'],
        'bayern munich': ['bayern', 'munich', 'bavière'],
        'borussia dortmund': ['dortmund', 'bvb', 'borussia'],
        'juventus': ['juve', 'juventus', 'bianconeri'],
        'ac milan': ['milan', 'rossoneri'],
        'inter milan': ['inter', 'nerazzurri'],
        'atletico madrid': ['atletico', 'atlético', 'colchoneros'],
        'napoli': ['naples', 'napoli'],
        'benfica': ['benfica', 'eagles'],
        'porto': ['porto', 'dragons'],
        'ajax': ['ajax', 'amsterdam'],
        'galatasaray': ['galatasaray', 'gala'],
        'fenerbahce': ['fenerbahçe', 'fener']
    },

    // Commandes vocales
    commands: {
        'ouvrir': ['ouvre', 'ouvrir', 'afficher', 'montrer', 'voir'],
        'fermer': ['ferme', 'fermer', 'cacher', 'masquer'],
        'chercher': ['cherche', 'chercher', 'recherche', 'trouve', 'trouver'],
        'effacer': ['efface', 'effacer', 'vider', 'supprimer', 'reset']
    },

    // Pays et régions
    countries: {
        'espagne': ['spain', 'espagnol', 'espagne'],
        'france': ['français', 'france'],
        'angleterre': ['england', 'anglais', 'angleterre'],
        'allemagne': ['germany', 'allemand', 'allemagne'],
        'italie': ['italy', 'italien', 'italie'],
        'portugal': ['portugais', 'portugal'],
        'bresil': ['brazil', 'brésilien', 'bresil'],
        'argentine': ['argentina', 'argentin', 'argentine'],
        'japon': ['japan', 'japonais', 'japon']
    }
};

// Système intelligent de détection de mots-clés
class VoiceKeywordDetector {
    constructor() {
        this.lastDetectedType = null;
        this.confidence = 0;
        this.suggestions = [];
    }

    // Analyser la phrase vocale et extraire les intentions
    analyzeVoiceInput(transcript) {
        const normalizedInput = normalizeText(transcript);
        const words = normalizedInput.split(/\s+/);

        const analysis = {
            type: null,           // 'competition', 'club', 'command', 'country'
            target: null,         // L'élément trouvé
            confidence: 0,        // Niveau de confiance (0-1)
            action: 'search',     // Action à effectuer
            suggestions: [],      // Suggestions alternatives
            originalText: transcript
        };

        // 1. Détecter les commandes d'action
        const detectedCommand = this.detectCommands(normalizedInput);
        if (detectedCommand) {
            analysis.action = detectedCommand.action;
            analysis.confidence += 0.3;
        }

        // 2. Détecter les compétitions
        const detectedCompetition = this.detectCompetitions(normalizedInput, words);
        if (detectedCompetition) {
            analysis.type = 'competition';
            analysis.target = detectedCompetition.name;
            analysis.confidence += detectedCompetition.confidence;
            analysis.suggestions.push(...detectedCompetition.suggestions);
        }

        // 3. Détecter les clubs
        const detectedClub = this.detectClubs(normalizedInput, words);
        if (detectedClub) {
            analysis.type = 'club';
            analysis.target = detectedClub.name;
            analysis.confidence += detectedClub.confidence;
            analysis.suggestions.push(...detectedClub.suggestions);
        }

        // 4. Détecter les pays
        const detectedCountry = this.detectCountries(normalizedInput, words);
        if (detectedCountry) {
            analysis.type = 'country';
            analysis.target = detectedCountry.name;
            analysis.confidence += detectedCountry.confidence;
        }

        // 5. Analyse contextuelle
        this.enhanceWithContext(analysis, normalizedInput);

        return analysis;
    }

    // Détecter les commandes vocales
    detectCommands(input) {
        for (const [command, variants] of Object.entries(VOICE_KEYWORDS_DATABASE.commands)) {
            for (const variant of variants) {
                if (input.includes(variant)) {
                    return {
                        action: command,
                        confidence: 0.8
                    };
                }
            }
        }
        return null;
    }

    // Détecter les compétitions
    detectCompetitions(input, words) {
        let bestMatch = null;
        let maxConfidence = 0;
        const suggestions = [];

        for (const [competition, keywords] of Object.entries(VOICE_KEYWORDS_DATABASE.competitions)) {
            let confidence = 0;
            let matchCount = 0;

            // Recherche exacte du nom
            if (input.includes(normalizeText(competition))) {
                confidence = 0.9;
                matchCount = 1;
            } else {
                // Recherche par mots-clés
                for (const keyword of keywords) {
                    if (input.includes(normalizeText(keyword))) {
                        confidence += 0.6 / keywords.length;
                        matchCount++;
                    }
                }
            }

            if (confidence > maxConfidence) {
                maxConfidence = confidence;
                bestMatch = {
                    name: competition,
                    confidence: confidence,
                    matchCount: matchCount,
                    suggestions: []
                };
            } else if (confidence > 0.3) {
                suggestions.push(competition);
            }
        }

        if (bestMatch) {
            bestMatch.suggestions = suggestions;
        }

        return bestMatch;
    }

    // Détecter les clubs
    detectClubs(input, words) {
        let bestMatch = null;
        let maxConfidence = 0;
        const suggestions = [];

        for (const [club, keywords] of Object.entries(VOICE_KEYWORDS_DATABASE.clubs)) {
            let confidence = 0;
            let matchCount = 0;

            // Recherche exacte du nom
            if (input.includes(normalizeText(club))) {
                confidence = 0.9;
                matchCount = 1;
            } else {
                // Recherche par mots-clés et surnoms
                for (const keyword of keywords) {
                    if (input.includes(normalizeText(keyword))) {
                        confidence += 0.7 / keywords.length;
                        matchCount++;
                    }
                }
            }

            if (confidence > maxConfidence) {
                maxConfidence = confidence;
                bestMatch = {
                    name: club,
                    confidence: confidence,
                    matchCount: matchCount,
                    suggestions: []
                };
            } else if (confidence > 0.3) {
                suggestions.push(club);
            }
        }

        if (bestMatch) {
            bestMatch.suggestions = suggestions;
        }

        return bestMatch;
    }

    // Détecter les pays
    detectCountries(input, words) {
        for (const [country, keywords] of Object.entries(VOICE_KEYWORDS_DATABASE.countries)) {
            for (const keyword of keywords) {
                if (input.includes(normalizeText(keyword))) {
                    return {
                        name: country,
                        confidence: 0.7
                    };
                }
            }
        }
        return null;
    }

    // Améliorer l'analyse avec le contexte
    enhanceWithContext(analysis, input) {
        // Bonus de confiance pour certains patterns
        if (input.includes('équipe') || input.includes('club')) {
            if (analysis.type === 'club') analysis.confidence += 0.1;
        }

        if (input.includes('championnat') || input.includes('ligue') || input.includes('compétition')) {
            if (analysis.type === 'competition') analysis.confidence += 0.1;
        }

        // Normaliser la confiance (max 1.0)
        analysis.confidence = Math.min(analysis.confidence, 1.0);
    }
}

// Fonction améliorée pour nettoyer et analyser les requêtes vocales
function processVoiceQuery(transcript) {
    const detector = new VoiceKeywordDetector();
    const analysis = detector.analyzeVoiceInput(transcript);

    console.log('🧠 Analyse vocale:', analysis);

    // Retourner le terme de recherche optimisé
    if (analysis.confidence > 0.5) {
        return {
            searchTerm: analysis.target,
            type: analysis.type,
            action: analysis.action,
            confidence: analysis.confidence,
            suggestions: analysis.suggestions,
            originalText: transcript
        };
    }

    // Si confiance faible, utiliser le texte original
    return {
        searchTerm: transcript,
        type: 'unknown',
        action: 'search',
        confidence: 0.3,
        suggestions: [],
        originalText: transcript
    };
}

// Fonction pour filtrer les compétitions et clubs
function performSearch(query, isVoiceSearch = false) {
    // Nettoyer la requête si elle vient de la recherche vocale
    if (isVoiceSearch) {
        query = cleanVoiceQuery(query);
    }

    console.log('🔍 Recherche pour:', query);

    if (!query || query.length < 1) {
        // Si pas de requête, afficher tout
        showAllElements();
        return;
    }

    const results = {
        competitions: [],
        clubs: {}
    };

    // Recherche dans les compétitions
    allCompetitionsData.forEach(competition => {
        const competitionMatches = matchesQuery(competition, query);
        let hasMatchingClubs = false;

        // Recherche dans les clubs de cette compétition
        const competitionClubs = allClubsData[competition.id] || [];
        const matchingClubs = competitionClubs.filter(club => matchesQuery(club, query));

        if (matchingClubs.length > 0) {
            hasMatchingClubs = true;
            results.clubs[competition.id] = matchingClubs;
        }

        // Inclure la compétition si elle correspond ou si elle a des clubs correspondants
        if (competitionMatches || hasMatchingClubs) {
            results.competitions.push(competition);
            if (!results.clubs[competition.id]) {
                results.clubs[competition.id] = competitionClubs; // Tous les clubs si la compétition correspond
            }
        }
    });

    console.log('📊 Résultats de recherche:', results);
    renderSearchResults(results, query);
}

// Fonction pour afficher les résultats de recherche
function renderSearchResults(results, query) {
    const treeRoot = document.getElementById('tree-root');
    const allCompetitionItems = treeRoot.querySelectorAll('.formListItem');

    // Masquer toutes les compétitions d'abord
    allCompetitionItems.forEach(item => {
        item.style.display = 'none';
    });

    // Afficher les compétitions correspondantes
    results.competitions.forEach(competition => {
        const competitionElement = treeRoot.querySelector(`[data-competition-id="${competition.id}"]`);
        if (competitionElement) {
            const listItem = competitionElement.closest('.formListItem');
            if (listItem) {
                listItem.style.display = 'block';

                // Surligner le texte correspondant dans le nom de la compétition
                highlightText(competitionElement, competition.name, query);

                // Filtrer les clubs dans cette compétition
                const clubsList = listItem.querySelector('.nested');
                if (clubsList && results.clubs[competition.id]) {
                    filterClubsInCompetition(clubsList, results.clubs[competition.id], query);
                }
            }
        }
    });

    // Afficher un message si aucun résultat
    if (results.competitions.length === 0) {
        showNoResultsMessage(query);
    }
}

// Fonction pour filtrer les clubs dans une compétition
function filterClubsInCompetition(clubsList, matchingClubs, query) {
    const allClubItems = clubsList.querySelectorAll('.field-list-item');

    // Masquer tous les clubs d'abord
    allClubItems.forEach(item => {
        item.style.display = 'none';
    });

    // Afficher seulement les clubs correspondants
    matchingClubs.forEach(club => {
        const clubElement = Array.from(allClubItems).find(item => {
            const clubName = item.querySelector('.fieldLiSpan');
            return clubName && normalizeText(clubName.textContent).includes(normalizeText(club.name));
        });

        if (clubElement) {
            clubElement.style.display = 'block';
            // Surligner le texte correspondant dans le nom du club
            const clubNameElement = clubElement.querySelector('.fieldLiSpan');
            if (clubNameElement) {
                highlightText(clubNameElement, club.name, query);
            }
        }
    });
}

// Fonction pour surligner le texte correspondant
function highlightText(element, originalText, query) {
    if (!query || query.length < 1) return;

    const normalizedQuery = normalizeText(query);
    const normalizedText = normalizeText(originalText);

    if (normalizedText.includes(normalizedQuery)) {
        // Trouver la position du match dans le texte original
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        const highlightedText = originalText.replace(regex, '<mark style="background-color: yellow; padding: 1px 2px; border-radius: 2px;">$1</mark>');

        // Mettre à jour seulement le texte, pas toute la structure HTML
        const textNode = element.querySelector('strong') || element;
        if (textNode.innerHTML !== highlightedText) {
            textNode.innerHTML = highlightedText;
        }
    }
}

// Fonction pour afficher tous les éléments (réinitialiser la recherche)
function showAllElements() {
    const treeRoot = document.getElementById('tree-root');
    const allItems = treeRoot.querySelectorAll('.formListItem, .field-list-item');

    allItems.forEach(item => {
        item.style.display = 'block';
    });

    // Supprimer le surlignage
    const highlightedElements = treeRoot.querySelectorAll('mark');
    highlightedElements.forEach(mark => {
        const parent = mark.parentNode;
        parent.replaceChild(document.createTextNode(mark.textContent), mark);
        parent.normalize();
    });

    // Masquer le message "aucun résultat"
    const noResultsMsg = document.getElementById('no-results-message');
    if (noResultsMsg) {
        noResultsMsg.remove();
    }
}

// Fonction pour afficher un message "aucun résultat"
function showNoResultsMessage(query) {
    const treeRoot = document.getElementById('tree-root');

    // Supprimer le message précédent s'il existe
    const existingMsg = document.getElementById('no-results-message');
    if (existingMsg) {
        existingMsg.remove();
    }

    // Créer le nouveau message
    const noResultsDiv = document.createElement('div');
    noResultsDiv.id = 'no-results-message';
    noResultsDiv.style.cssText = 'padding: 20px; text-align: center; color: var(--tree-view-color); font-style: italic;';
    noResultsDiv.innerHTML = `
        <div>🔍 Aucun résultat pour "${query}"</div>
        <div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">
            💡 Essayez: "Champions", "Premier", "Real"...
        </div>
    `;

    treeRoot.appendChild(noResultsDiv);
}

// Fonction pour initialiser la recherche
function initializeSearch() {
    const searchInput = document.getElementById('TreeSearch');
    if (!searchInput) {
        console.warn('⚠️ Élément de recherche TreeSearch non trouvé');
        return;
    }

    console.log('🔍 Initialisation de la recherche...');

    // Stocker les données des compétitions
    allCompetitionsData = HierarchyData || [];

    // Écouter les changements dans le champ de recherche
    searchInput.addEventListener('input', function(e) {
        const query = e.target.value.trim();

        // Debounce la recherche pour éviter trop d'appels
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    // Écouter la touche Escape pour vider la recherche
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            e.target.value = '';
            showAllElements();
        }
    });

    console.log('✅ Recherche initialisée avec succès');
}

document.addEventListener("DOMContentLoaded", async function () {


    // Afficher les compétitions supportées localement
    const localCompetitions = [
      'UEFA Champions League', 'UEFA Europa League', 'UEFA Europa Conference League',
      'GB1', 'ES1', 'IT1', 'L1', 'FR1', 'PO1', 'TR1', 'RU1', 'FR2', 'LUX1',
      'FRR1', 'FRR2', 'LUXR1',
      'TUN1', 'ALG1', 'BR1', 'AR1', 'JP1'
    ];


    let data = HierarchyData;
    const htmlContent = generateHierarchyHtml(data);
    const treeRoot = document.getElementById('tree-root');
    treeRoot.innerHTML = htmlContent;
    setupCompetitionListeners();

    // Initialiser la recherche
    initializeSearch();

    // Initialiser la recherche vocale
    const voiceSearch = new VoiceSearch();
    voiceSearch.initialize();

});
function generateHierarchyHtml(data) {
  let htmlContent = '';

  data.forEach(competition => {
    htmlContent += `
      <li class="formListItem">
        <div class="caret formSpan ClusterSpan" data-competition-id="${competition.id}" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
          <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;">
            <div style="display: flex; align-items: center;">
              <div class="flecheimg caretImg" style="display: flex; align-items: center;">
                <svg style="height: 12.7px; width: 12.7px; fill: var(--tree-view-color); opacity: 0.5;" version="1.1" viewBox="0 0 12.7 12.7" xmlns="http://www.w3.org/2000/svg"><g><polygon points="1,1.7 1,11.1 11.7,6.4"/></g></svg>
              </div>
              <div style="display: flex; align-items: center;">
                <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" viewBox="0 0 12.7 12.7" xmlns="http://www.w3.org/2000/svg"><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
              </div>
              <strong>${competition.name}</strong> 
            </div>
            <div class="total-cluster" style="margin-right: 10px;">
               (${competition.country})
            </div>
          </div>
        </div>
        <ul class="nested">
          
        </ul>
      </li>
    `;
  });

  return htmlContent;
}


function setupCompetitionListeners() {
  const competitionElements = document.querySelectorAll('.ClusterSpan[data-competition-id]');

  competitionElements.forEach(elem => {
    elem.addEventListener('click', async function () {
      const competitionId = this.getAttribute('data-competition-id');
      const nestedUl = this.nextElementSibling; // <ul class="nested">

      // Toggle visibility (open/close)
      nestedUl.classList.toggle('active');

      // If already loaded, just toggle visibility without fetching again
      if (this.classList.contains('loaded')) return;

      this.classList.add('loaded');

      try {
        // Définir les compétitions supportées localement
        const localCompetitions = [
          // UEFA - Compétitions Internationales
          'UEFA Champions League', 'UEFA Europa League', 'UEFA Europa Conference League',
          // UEFA - Championnats Nationaux
          'GB1', 'ES1', 'IT1', 'L1', 'FR1', 'PO1', 'TR1', 'RU1', 'FR2', 'LUX1',
          // UEFA - Playoffs et Barrages
          'FRR1', 'FRR2', 'LUXR1',
          // CAF - Afrique
          'TUN1', 'ALG1',
          // CONMEBOL - Amérique du Sud
          'BR1', 'AR1',
          // AFC - Asie
          'JP1'
        ];

        let res, data;

        if (localCompetitions.includes(competitionId)) {
          res = await fetch(`/api/competitions/${competitionId}/clubs`);
          data = await res.json();

          // Vérifier si la requête a échoué (fichier manquant)
          if (!res.ok) {
            console.log('🌐 Chargement depuis l\'API externe pour:', competitionId);
            res = await fetch(`https://transfermarkt-api.fly.dev/competitions/${competitionId}/clubs`);
            data = await res.json();
          }
        } else {
          // Autres compétitions : utiliser l'API externe
          console.log('🌐 Chargement depuis l\'API externe pour:', competitionId);
          res = await fetch(`https://transfermarkt-api.fly.dev/competitions/${competitionId}/clubs`);
          data = await res.json();
        }

        if (data.clubs && data.clubs.length > 0) {
          // Stocker les clubs pour la recherche
          allClubsData[competitionId] = data.clubs;

          const clubsHtml = data.clubs.map(club => `
            <li class="field-list-item">
              <div class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
                <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;">
                  <div class="fieldLiSpan" style="flex-grow: 1; display: flex; align-items: center;">
                    <div style="display: flex; align-items: center;">
                      <svg style="height: 12px; width: 12px; fill: var(--coloredTextPrimary); margin-right: 5px;" viewBox="0 0 12.7 12.7" xmlns="http://www.w3.org/2000/svg"><path d="M12.7,1.1c0-0.6-0.5-1.1-1.1-1.1H1.1C0.5,0,0,0.5,0,1.1v4.7h12.7V1.1z"/><path d="M12.7,11.6V6.9H0v4.7c0,0.6,0.5,1.1,1.1,1.1h10.4C12.2,12.7,12.7,12.2,12.7,11.6z"/></svg>
                    </div>
                    ${club.name}
                  </div>
                </div>
              </div>
            </li>
          `).join('');

          nestedUl.innerHTML = clubsHtml;
        } else {
          nestedUl.innerHTML = `<li class="field-list-item"><em>No clubs found</em></li>`;
        }
      } catch (err) {
        console.error('Failed to fetch clubs:', err);
        nestedUl.innerHTML = `<li class="field-list-item"><em>Error loading clubs</em></li>`;
      }
    });
  });
}



document.addEventListener("DOMContentLoaded", function () {
  const searchInput = document.getElementById('SearchInputTopBar');
  const resultsContainer = document.getElementById('playerSearchResults');
  const profileContainer = document.getElementById('PlayerProfile');

  let timeout = null;

  searchInput.addEventListener('input', function () {
    const query = this.value.trim();

    clearTimeout(timeout);

    if (query.length < 2) {
      resultsContainer.innerHTML = '';
      return;
    }

    timeout = setTimeout(() => {
      fetch(`https://transfermarkt-api.fly.dev/players/search/${encodeURIComponent(query)}?page_number=1`)
        .then(response => response.json())
        .then(data => {
          if (data.results && data.results.length > 0) {
            resultsContainer.style.display = 'flex';
            profileContainer.style.display = 'none';

            resultsContainer.innerHTML = data.results.map(player => {
              const marketValue = player.marketValue
                ? `€${(player.marketValue / 1_000_000).toFixed(2)}M`
                : 'N/A';

              const nationality = player.nationalities?.join(', ') ?? 'Unknown';

              return `
                <div class="playersCard player-result" data-player-id="${player.id}">
                  <div class="player-age">${player.age} ans</div>
                  <div class="player-name">${player.name}</div>
                  <div class="player-position">${player.position}</div>
                  <div class="player-club">${player.club?.name || 'Club inconnu'}</div>
                  <div class="player-value">${marketValue}</div>
                </div>
              `;
            }).join('');

            // Add click listeners for each card
            document.querySelectorAll('.player-result').forEach(card => {
              card.addEventListener('click', () => {
                const playerId = card.getAttribute('data-player-id');
                loadPlayerProfile(playerId);
              });
            });

          } else {
            resultsContainer.innerHTML = `<div>No results found for <strong>${query}</strong>.</div>`;
          }
        })
        .catch(err => {
          console.error(err);
          resultsContainer.style.display = 'flex';
          profileContainer.style.display = 'none';

          const dummyCards = Array.from({ length: 6 }).map((_, index) => `
 
  <div class="card green CardFootballPalyer" style="min-width: 30%;" onclick="loadPlayerProfile()" >
    <div class="additional">
      <div class="user-card">
        <div class="level center">
          Level 13
        </div>
        <div class="points center">
          5,312 Points
        </div>
        <svg width="110" height="110" viewBox="0 0 250 250" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="title desc" class="center">
          <title id="title">Teacher</title>
          <desc id="desc">Cartoon of a Caucasian woman smiling, and wearing black glasses and a purple shirt with white collar drawn by Alvaro Montoro.</desc>
          <style>
            .skin { fill: #eab38f; }
            .eyes { fill: #1f1f1f; }
            .hair { fill: #2f1b0d; }
            .line { fill: none; stroke: #2f1b0d; stroke-width:2px; }
          </style>
          <defs>
            <clipPath id="scene">
              <circle cx="125" cy="125" r="115"/>
            </clipPath>
            <clipPath id="lips">
              <path d="M 106,132 C 113,127 125,128 125,132 125,128 137,127 144,132 141,142  134,146  125,146  116,146 109,142 106,132 Z" />
            </clipPath>
          </defs>
          <circle cx="125" cy="125" r="120" fill="rgba(0,0,0,0.15)" />
          <g stroke="none" stroke-width="0" clip-path="url(#scene)">
            <rect x="0" y="0" width="250" height="250" fill="#b0d2e5" />
            <g id="head">
              <path fill="none" stroke="#111111" stroke-width="2" d="M 68,103 83,103.5" />
              <path class="hair" d="M 67,90 67,169 78,164 89,169 100,164 112,169 125,164 138,169 150,164 161,169 172,164 183,169 183,90 Z" />
              <circle cx="125" cy="100" r="55" class="skin" />
              <ellipse cx="102" cy="107" rx="5" ry="5" class="eyes" id="eye-left" />
              <ellipse cx="148" cy="107" rx="5" ry="5" class="eyes" id="eye-right" />
              <rect x="119" y="140" width="12" height="40" class="skin" />
              <path class="line eyebrow" d="M 90,98 C 93,90 103,89 110,94" id="eyebrow-left" />
              <path class="line eyebrow" d="M 160,98 C 157,90 147,89 140,94" id="eyebrow-right"/>
              <path stroke="#111111" stroke-width="4" d="M 68,103 83,102.5" />
              <path stroke="#111111" stroke-width="4" d="M 182,103 167,102.5" />
              <path stroke="#050505" stroke-width="3" fill="none" d="M 119,102 C 123,99 127,99 131,102" />
              <path fill="#050505" d="M 92,97 C 85,97 79,98 80,101 81,104 84,104 85,102" />
              <path fill="#050505" d="M 158,97 C 165,97 171,98 170,101 169,104 166,104 165,102" />
              <path stroke="#050505" stroke-width="3" fill="rgba(240,240,255,0.4)" d="M 119,102 C 118,111 115,119 98,117 85,115 84,108 84,104 84,97 94,96 105,97 112,98 117,98 119,102 Z" />
              <path stroke="#050505" stroke-width="3" fill="rgba(240,240,255,0.4)" d="M 131,102 C 132,111 135,119 152,117 165,115 166,108 166,104 166,97 156,96 145,97 138,98 133,98 131,102 Z" />
              <path class="hair" d="M 60,109 C 59,39 118,40 129,40 139,40 187,43 189,99 135,98 115,67 115,67 115,67 108,90 80,109 86,101 91,92 92,87 85,99 65,108 60,109" />
              <path id="mouth" fill="#d73e3e" d="M 106,132 C 113,127 125,128 125,132 125,128 137,127 144,132 141,142  134,146  125,146  116,146 109,142 106,132 Z" /> 
              <path id="smile" fill="white" d="M125,141 C 140,141 143,132 143,132 143,132 125,133 125,133 125,133 106.5,132 106.5,132 106.5,132 110,141 125,141 Z" clip-path="url(#lips)" />
            </g>
            <g id="shirt">
              <path fill="#8665c2" d="M 132,170 C 146,170 154,200 154,200 154,200 158,250 158,250 158,250 92,250 92,250 92,250 96,200 96,200 96,200 104,170 118,170 118,170 125,172 125,172 125,172 132,170 132,170 Z"/>
              <path id="arm-left" class="arm" stroke="#8665c2" fill="none" stroke-width="14" d="M 118,178 C 94,179 66,220 65,254" />
              <path id="arm-right" class="arm" stroke="#8665c2" fill="none" stroke-width="14" d="M 132,178 C 156,179 184,220 185,254" />
              <path fill="white" d="M 117,166 C 117,166 125,172 125,172 125,182 115,182 109,170 Z" />
              <path fill="white" d="M 133,166 C 133,166 125,172 125,172 125,182 135,182 141,170 Z" />
              <circle cx="125" cy="188" r="4" fill="#5a487b" />
              <circle cx="125" cy="202" r="4" fill="#5a487b" />
              <circle cx="125" cy="216" r="4" fill="#5a487b" />
              <circle cx="125" cy="230" r="4" fill="#5a487b" />
              <circle cx="125" cy="244" r="4" fill="#5a487b" />
              <path stroke="#daa37f" stroke-width="1" class="skin hand" id="hand-left" d="M 51,270 C 46,263 60,243 63,246 65,247 66,248 61,255 72,243 76,238 79,240 82,243 72,254 69,257 72,254 82,241 86,244 89,247 75,261 73,263 77,258 84,251 86,253 89,256 70,287 59,278" /> 
              <path stroke="#daa37f" stroke-width="1" class="skin hand" id="hand-right" d="M 199,270 C 204,263 190,243 187,246 185,247 184,248 189,255 178,243 174,238 171,240 168,243 178,254 181,257 178,254 168,241 164,244 161,247 175,261 177,263 173,258 166,251 164,253 161,256 180,287 191,278"/> 
            </g>
          </g>
        </svg>
      </div>
      <div class="more-info">
        <h1>Player Test</h1>
        <div class="coords">
          <span>Group Name</span>
          <span>Joined January 2019</span>
        </div>
        <div class="coords">
          <span>Position/Role</span>
          <span>City, Country</span>
        </div>
        <div class="stats">
          <div>
            <div class="title">Awards</div>
            <i class="fa fa-trophy"></i>
            <div class="value">2</div>
          </div>
          <div>
            <div class="title">Matches</div>
            <i class="fa fa-gamepad"></i>
            <div class="value">27</div>
          </div>
          <div>
            <div class="title">Pals</div>
            <i class="fa fa-group"></i>
            <div class="value">123</div>
          </div>
          <div>
            <div class="title">Coffee</div>
            <i class="fa fa-coffee"></i>
            <div class="value infinity">∞</div>
          </div>
        </div>
      </div>
    </div>
    <div class="general">
      <h1>Jane Doe</h1>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce a volutpat mauris, at molestie lacus. Nam vestibulum sodales odio ut pulvinar.</p>
      <span class="more">Mouse over the card for more info</span>
    </div>
  </div>
`).join('');


          resultsContainer.innerHTML = dummyCards;

          // Optional: Add click listeners if needed
          document.querySelectorAll('.player-result').forEach(card => {
            card.addEventListener('click', () => {
              const playerId = card.getAttribute('data-player-id');
              loadPlayerProfile(playerId); // if you want the dummy cards to trigger something
            });
          });
        });

    }, 400); // debounce
  });



  // Back to search
  window.backToSearch = function () {
    profileContainer.style.display = 'none';
    resultsContainer.style.display = 'flex';
  };
});

function loadPlayerProfile(playerId) {
  const resultsContainer = document.getElementById('playerSearchResults');
  const profileContainer = document.getElementById('PlayerProfile');

  resultsContainer.style.display = 'none';
  profileContainer.style.display = 'block';
  profileContainer.innerHTML = `<p>Loading profile...</p>`;

//   fetch(`https://transfermarkt-api.fly.dev/players/${playerId}/profile`)
//     .then(response => {
//       if (!response.ok) {
//         throw new Error(`API error: ${response.status}`);
//       }
//       return response.json();
//     })
//     .then(player => renderPlayerProfile(player, profileContainer))
//     .catch(err => {
//       console.error('Error loading profile:', err);

      
//     });
    const fallbackPlayer = {
        name: "Unavailable Player",
        nameInHomeCountry: "N/A",
        imageUrl: "https://via.placeholder.com/120x150?text=No+Image",
        age: "N/A",
        citizenship: ["Unknown"],
        position: {
          main: "Unknown",
          other: []
        },
        club: {
          name: "N/A",
          joined: "N/A"
        },
        shirtNumber: "N/A",
        foot: "N/A",
        placeOfBirth: {
          city: "N/A",
          country: "N/A"
        },
        outfitter: "N/A",
        relatives: [],
        socialMedia: [],
        url: "#"
      };

      renderPlayerProfile(fallbackPlayer, profileContainer);
}



function renderPlayerProfile(player, container) {
  const nationality = player.citizenship?.join(', ') ?? '';
  const positions = [player.position?.main, ...(player.position?.other || [])].filter(Boolean).join(', ');
  //const image = player.imageUrl || '';
  const marketValue = player.marketValue ? `€${(player.marketValue / 1_000_000).toFixed(2)}M` : 'N/A';
  const relatives = player.relatives?.map(r => r.name).join(', ') || 'None';
  const socialLinks = player.socialMedia?.map(url => `<a href="${url}" target="_blank">${url}</a>`).join('<br/>') || 'N/A';

  let template = document.getElementById('playerProfileTemplate').innerHTML;

  template = template
    //.replaceAll('__imageUrl__', image)
    .replaceAll('__name__', player.name)
    .replaceAll('__nameInHomeCountry__', player.nameInHomeCountry)
    .replaceAll('__position__', positions)
    .replaceAll('__marketValue__', marketValue)
    .replaceAll('__age__', player.age)
    .replaceAll('__nationality__', nationality)
    .replaceAll('__clubName__', player.club?.name || 'N/A')
    .replaceAll('__joined__', player.club?.joined || 'N/A')
    .replaceAll('__shirtNumber__', player.shirtNumber || 'N/A')
    .replaceAll('__foot__', player.foot || 'N/A')
    .replaceAll('__city__', player.placeOfBirth?.city || 'N/A')
    .replaceAll('__country__', player.placeOfBirth?.country || 'N/A')
    .replaceAll('__outfitter__', player.outfitter || 'N/A')
    .replaceAll('__relatives__', relatives)
    .replaceAll('__socialLinks__', socialLinks)
    .replaceAll('__profileUrl__', player.url);

  container.innerHTML = template;
}


const buttons = document.querySelectorAll(".card-buttons button");
const card = document.querySelector(".card-football-player");

const handleButtonClick = (e) => {
  const targetSection = e.target.getAttribute("data-section");
  card.setAttribute("data-state", targetSection);
};

buttons.forEach((btn) => {
  btn.addEventListener("click", handleButtonClick);
});
