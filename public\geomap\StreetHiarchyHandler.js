async function createRueHiarchy(clickedCluster,ByClickingCluster,data) {
    let clusterInseeDatag = JSON.parse(localStorage.getItem("clusterInseeDatag")) || [];
    if (!Array.isArray(clusterInseeDatag)) clusterInseeDatag = clusterInseeDatag ? [clusterInseeDatag] : [];
    const savedVoies = JSON.parse(localStorage.getItem("savedVoiestotal")) || JSON.parse(localStorage.getItem("RueHiarchyvoie")) || [];
    const savedVilles = JSON.parse(localStorage.getItem("savedVilleslibelle")) || [];
    const clusterInseeData = JSON.parse(localStorage.getItem("clusterInseeData")) || [];

    const lastItem = savedVilles[savedVilles.length - 1];

    let currentCluster = clickedCluster ?? (clusterInseeData.length > 0 ? clusterInseeData[clusterInseeData.length - 1] : null);
    if (!currentCluster) {
        console.error('No current cluster found');
        return;
    }

    if (typeof currentCluster === "string") {
        currentCluster = { clusterCode: currentCluster };
    }
    if (ByClickingCluster===true){
         const existingClusterIndex = clusterInseeDatag.findIndex(cluster => 
                cluster.clusterCode === (typeof clickedCluster === 'string' ? clickedCluster : clickedCluster.clusterCode)
            );
            if (existingClusterIndex !== -1) {
                clusterInseeDatag.splice(existingClusterIndex, 1);
            }
            if (typeof clickedCluster === "string") {
                clickedCluster = { clusterCode: clickedCluster };
            }
            findOrCreateCluster(clusterInseeDatag, clickedCluster);
            CallupdateHiarchyRue(clusterInseeDatag);
            return;
    }

    if (!currentCluster || !currentCluster.clusterCode) {
        console.error('No valid current cluster found');
        return;
    }
    const existingCluster = findOrCreateCluster(clusterInseeDatag, currentCluster);
    const existingVille = findOrCreateVille(
        existingCluster, 
        localStorage.getItem("ville"), 
        lastItem.codeInsee
    );
    savedVoies.forEach(voie => {
        addVoieToVille(existingVille, voie);
    });
    CallupdateHiarchyRue(clusterInseeDatag);
}

function findOrCreateCluster(clusterInseeDatag, currentCluster) {
    if (!currentCluster || !currentCluster.clusterCode) {
        console.error('Invalid cluster data');
        return null;
    }

    let existingCluster = clusterInseeDatag.find(c => c.codeCluster === currentCluster.clusterCode);
    if (!existingCluster) {
        existingCluster = {
            codeCluster: currentCluster.clusterCode,
            libelleCluster: localStorage.getItem("libelleCluster"),
            totalprises: localStorage.getItem('totalGeneralCluster'),
            villes: []
        };
        clusterInseeDatag.push(existingCluster);
    }
    
    return existingCluster;
}

function findOrCreateVille(cluster, ville, codeInsee) {
    if (!cluster) {
        console.error('No cluster provided');
        return null;
    }

    let existingVille = cluster.villes.find(v => v.codInsee === codeInsee);
    if (!existingVille) {
        existingVille = {
            codInsee: codeInsee,
            libelleVille: ville,
            totalbyville: localStorage.getItem("totalGeneralVille"),
            voies: []
        };
        cluster.villes.push(existingVille);
    }
    return existingVille;
}

function addVoieToVille(ville, voie) {
    if (!ville) {
        console.error('No ville provided');
        return;
    }

    const existingVoieIndex = ville.voies.findIndex(v => v.nomVoie === voie.nomVoie);
    
    if (existingVoieIndex === -1) {
        ville.voies.push({
            nomVoie: String(voie.nomVoie),
            total: Number(voie.total) || 0
        });
    }
    localStorage.setItem("lenghthsavedVoies", savedVoies.length);

}

async function updateTotals(data) {
    const clusters = Array.isArray(data) ? data : [data];

    return clusters.map(cluster => {
        if (!cluster || typeof cluster !== 'object') return cluster;

        // Ensure villes array exists and is an array
        if (!Array.isArray(cluster.villes)) cluster.villes = [];
        
        // If no villes, keep the original totalprises
        if (cluster.villes.length === 0) {
            return cluster;
        }

        // Process each ville
        cluster.villes.forEach(ville => {
            if (!ville || typeof ville !== 'object') return;
            
            // Ensure voies array exists and is an array
            if (!Array.isArray(ville.voies)) ville.voies = [];
            
            // If no voies, keep the original totalbyville
            if (ville.voies.length === 0) {
                return;
            }

            // Calculate total for ville if voies exist
            ville.totalbyville = ville.voies.reduce((sum, voie) => sum + Number(voie.total || 0), 0);
        });

        // Calculate total prises for the cluster
        cluster.totalprises = cluster.villes.reduce((sum, ville) => 
            sum + Number(ville.totalbyville || 0), 0
        );

        return cluster;
    });
}

async function CallupdateHiarchyRue(clusterInseeDatag) {
    const updatedhiarchydata = await updateTotals(clusterInseeDatag);
    localStorage.setItem("clusterInseeDatag", JSON.stringify(updatedhiarchydata));
    let totalPrises = await calculateTotalPrises({ cluster: updatedhiarchydata });
    generateHierarchyRue({ cluster: updatedhiarchydata }, totalPrises);
    setupSearchFunctionalityes();
}

let lastGenratedHiarchyRue;

function setupSearchFunctionalityes() {
    const searchInput = document.getElementById('TreeSearchinterfacePrises');
    if (!searchInput) return;

    // Suppression des anciens écouteurs
    searchInput.removeEventListener('input', handleSearchInput);
    searchInput.addEventListener('input', handleSearchInput);
    
}

function handleSearchInput(event) {
    const searchText = event.target.value.toLowerCase().trim();
    if (searchText){
        filterSearchertest(searchText);
    }else{
        resetTreeStructures() ;
    }
  
    // filterFormListItems(searchText);
}
function resetTreeStructures() {
    const nodes = document.querySelectorAll('.tree-node');
    
    nodes.forEach(node => {
        node.style.display = 'block'; // Réafficher tous les nœuds
    });

    const nestedElements = document.querySelectorAll('.resetTreeStructure');
    nestedElements.forEach(nested => {
        nested.style.display = 'none'; // Réinitialiser les sous-niveaux
    });

    const carets = document.querySelectorAll('.caret');
    carets.forEach(caret => {
        caret.classList.remove('caret-down'); // Réinitialiser les flèches
    });
}
// function filterFormListItems(searchText) {
//     const formListItems = document.querySelectorAll('.tree-node');
//     openRightPanel();

//     if (searchText.length < 3) {
//         formListItems.forEach(item => {
//             item.style.display = '';
//             item.querySelectorAll('.field-list-item').forEach(subItem => subItem.style.display = '');
//         });
//         return;
//     }

//     formListItems.forEach(item => {
//         let hasMatch = false;
//         const placeItems = item.querySelectorAll('.field-list-item');

//         placeItems.forEach(subItem => {
//             let matchFound = false;

//             const clusterElement = subItem.querySelector('.formSpan');
//             if (clusterElement && clusterElement.textContent.toLowerCase().includes(searchText)) {
//                 matchFound = true;
//             }

//             const villeElements = subItem.querySelectorAll('.villeNodeSpan');
//             villeElements.forEach(villeElement => {
//                 if (villeElement.textContent.toLowerCase().includes(searchText)) {
//                     matchFound = true;
//                 }
//             });

//             const voieElement = subItem.querySelector('.SpanStreetHiarchy');
//             if (voieElement && voieElement.textContent.toLowerCase().includes(searchText)) {
//                 matchFound = true;
//             }

//             if (matchFound) {
//                 subItem.style.display = '';
//                 hasMatch = true;
//                 hasAnyMatch = true; // Une correspondance trouvée
//             } else {
//                 subItem.style.display = 'none';
//             }
//         });

//         item.style.display = hasMatch ? '' : 'none';
//     });

//     // Ouvrir le panneau si une correspondance est trouvée
//     if (hasAnyMatch) {
       
//     }
// }
function filterSearchertest(query) {
    const nodes = document.querySelectorAll('.tree-node');
    openRightPanel();
    nodes.forEach(node => {
        // Vérifier si ce nœud ou ses enfants correspondent à la recherche
        const isMatch = searchNode(node, query);

        if (isMatch) {
            node.style.display = 'block'; // Afficher ce nœud
            openParentNodes(node); // Ouvrir les parents
            openAllChildrenNodes(node); // Ouvrir tous les enfants
        } else {
            node.style.display = 'none'; // Masquer ce nœud
        }
    });
}

/**
 * 🔹 Recherche récursive pour vérifier si un nœud ou ses enfants correspondent à la recherche.
 */
function searchNode(node, query) {
    // Vérifier le texte du nœud dans les différents niveaux
    const level0 = node.querySelector('.caret:not(.child-img)');
    const level1 = node.querySelector('.caret.child-img');
    const level2 = node.querySelector('.formSpanvoie');

    const level0Text = level0 ? level0.textContent.toLowerCase().trim() : '';
    const level1Text = level1 ? level1.textContent.toLowerCase().trim() : '';
    const level2Text = level2 ? level2.textContent.toLowerCase().trim() : '';

    // Vérifier si le nœud correspond à la recherche
    if (level0Text.includes(query) || level1Text.includes(query) || level2Text.includes(query)) {
        return true;
    }

    // Rechercher récursivement dans les enfants
    const nestedNodes = node.querySelectorAll('.nested .tree-node');
    for (let nestedNode of nestedNodes) {
        if (searchNode(nestedNode, query)) {
            return true; // Retourner true dès qu'une correspondance est trouvée
        }
    }

    return false; // Aucune correspondance trouvée
}

/**
 * 🔹 Ouvre tous les parents du nœud pour qu'ils soient visibles.
 */
function openParentNodes(node) {
    let parent = node.closest('.nested');

    while (parent) {
        const parentNode = parent.closest('.tree-node');
        if (!parentNode) break;

        parent.style.display = 'block'; // Afficher le parent

        const caret = parentNode.querySelector('.caret');
        if (caret) {
            caret.classList.add('caret-down'); // Ouvrir le parent
        }

        parent = parentNode.closest('.nested'); // Remonter au parent suivant
    }
}

/**
 * 🔹 Ouvre tous les enfants du nœud (récursivement).
 */
function openAllChildrenNodes(node) {
    const nested = node.querySelector('.nested');
    if (nested) {
        nested.style.display = 'block'; // Afficher les enfants
        const childrenNodes = nested.querySelectorAll('.tree-node');
        childrenNodes.forEach(child => openAllChildrenNodes(child)); // Ouvrir les enfants récursivement
    }
}
if (typeof effectifs !== 'undefined' && Array.isArray(effectifs)) {
    const mainList = document.createElement('div');
    mainList.innerHTML = effectifs.map(effectif => generateTree(effectif)).join('');
    // container.appendChild(mainList);
} else {
    console.error("Erreur: `effectifs` n'est pas défini ou n'est pas un tableau.");
}

// Gestion des événements
// container.addEventListener('click', function (event) {
//     const target = event.target.closest('.caret');
//     if (target) {
//         target.classList.toggle('caret-down');
//         const nested = target.parentElement.querySelector('.nested');
//         if (nested) {
//             nested.classList.toggle('active');
//             nested.style.display = (nested.style.display === 'none') ? 'block' : 'none';

//             // Mise à jour des enfants
//             const childImgs = nested.querySelectorAll('.child-img');
//             childImgs.forEach(img => {
//                 img.classList.toggle('caret-down', target.classList.contains('caret-down'));
//             });
//         }
//     }
// });
function openRightPanel() {
    console.log("Tentative d'ouverture du panneau...");
    var sidebar = document.querySelector('.right-sidebar-concepteur');
    var sidebarStyle = window.getComputedStyle(sidebar);
    var IconRightPanelToggle = document.querySelector('.IconRightPanelToggle');

    console.log("Position actuelle du panneau:", sidebarStyle.right);
    
    if (sidebarStyle.right !== '0px') {
        sidebar.style.right = '0px';
        IconRightPanelToggle.style.transform = 'rotate(90deg)';
        console.log("Panneau ouvert !");
    }
}




function generateHierarchyRue(data, totalPrises) {
    const successSpan = document.getElementById('RueHiarchyDataSucess');
    successSpan.style.display = 'none';
    const card = document.getElementById("RueHiarchyData");
    document.getElementById("RueHiarchy").style.display = 'block';

    card.innerHTML = '';
    card.style.display = 'block';
    lastGenratedHiarchyRue=data;
    const pictoprises = '/image/picto-prises.svg';
    const pictokm = '/image/picto-kilometres.svg';
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    const totalDistanceSum = parseFloat(localStorage.getItem("totalDistanceSum") || "0");
    setTimeout(() => {
        const nom = localStorage.getItem("selectednom");
        const prenom = localStorage.getItem("selectedprenom");
    
        const input = document.getElementById("TreeSearchinterfacePrises");
    
        if (input && (nom || prenom)) {
          input.placeholder = `${prenom || ''} ${nom || ''}`.trim();
        }
      }, 500);
    const headerHTML = `
    <div class="headerTreeSection" style="margin-top: 0px;border-radius: 10px 10px 0px 0px;">
        <div style="display: flex; align-items: center;">
            <span style="align-items: center;display: flex;color:#88969F"> 
                <img class="icon" src="${pictokm}"> Km 
            </span> 
            <span style="align-items: center;display: flex; color:#88969F;gap:2px;padding-left: 22px;">
                <span class="isCheckedsomme" id="totalDistanceSum">${totalDistanceSum}</span>
            </span>
            <p class="sidebar-title"></p>
        </div>
        <span style="align-items: center;display: flex;color:#88969F">
            <img class="icon" src="${pictoprises}"> Prises
        </span> 
        <span style="align-items: center;display: flex; color:#88969F;gap:2px">
            <span class="isCheckedsomme" id="totalGeneral">${totalPrises}</span>
        </span>
    </div>
    <div class="search-container">
        <svg xmlns="http://www.w3.org/2000/svg" class="icon_effbe2 visible_effbe2" aria-hidden="true" role="img" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path fill="var(--icon-topbar)" fill-rule="evenodd" d="M15.62 17.03a9 9 0 1 1 1.41-1.41l4.68 4.67a1 1 0 0 1-1.42 1.42l-4.67-4.68ZM17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" clip-rule="evenodd"></path>
        </svg>
        <input type="text" id="TreeSearchinterfacePrises" placeholder="Rechercher" style=" outline: none; width: 100%; border-radius: 2px;">
        <i class="bi bi-x"></i>
    </div>
    <div class="TreeRueClicked scrollbar-custom">
    `;

    const clustersHTML = data.cluster.map(cluster => `
        <div class="field-list-item" style="margin-top: 5px;">
            <div class="toggle-node" data-target="cluster-${cluster.codeCluster}" style="cursor: pointer; display: flex; justify-content: space-between;">
                <div class="caret caretNode formSpan cluster-node user-node" style="display: flex; justify-content: space-between; align-items: center; width: 100%; font-size: 14px;" data-loaded="true"
                     data-type="cluster" data-cluster-id="${cluster.codeCluster}">
                    <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;">
                        <div style="display: flex; align-items: center;color: var(--tree-view-color);">
                            <div class="flecheimg caretImg" style="display: flex; align-items: center;">
                                <svg style="height: 12.7px; width: 12.7px; fill: var(--tree-view-color);opacity: 0.5;" version="1.1" viewBox="0 0 12.7 12.7" x="0px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" y="0px"><g><polygon points="1,1.7 1,11.1 11.7,6.4"/></g></svg>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; border-radius: 3em;" version="1.1" viewBox="0 0 12.7 12.7" x="0px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" y="0px"><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                            </div>
                            ${cluster.libelleCluster}
                        </div>
                        <div style="display: flex; align-items: center; margin-right: 5px;">
                            <span style="color: var(--tree-view-color);">${cluster.totalprises || 0}</span>
                            <i class="bi bi-x IconDeleteHiarchyCard" onclick="deleteHiarchyCard(this);"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div id="cluster-${cluster.codeCluster}" class="nested-node" style="display: block;">
                ${cluster.villes.map(ville => `
                    <div class="toggle-node" data-target="ville-${ville.codInsee}" style="cursor: pointer; display: flex; justify-content: space-between;">
                        <span class="caret caretNode formSpan ville-node cluster-node user-node" style="padding:4px 4px 4px 20px; display: flex; justify-content: space-between; align-items: center; width: 100%; font-size: 14px;" data-loaded="true"
                              data-type="ville" data-cluster-id="${cluster.codeCluster}" data-ville-id="${ville.codInsee}">
                            <span class="villeNodeSpan" style="display: flex; align-items: center;color: var(--tree-view-color);">
                                <img src="${flecheFields}" style="margin-right:10px;" class="flecheimg">
                                <div style="height: 13px; width: 13px; background: #015D92; border-radius: 2px; margin-right: 5px; display: flex; align-items: center;">
                                    <div style="height: 2px; width: 13px; background: #000;"></div>
                                </div>
                                ${ville.libelleVille}
                            </span>
                            <div style="display: flex; align-items: center; margin-right: 5px;">
                                <span style="color: var(--tree-view-color);">${ville.totalbyville || 0}</span>
                                <i class="bi bi-x IconDeleteHiarchyCard" onclick="deleteHiarchyCard(this);"></i>
                            </div>
                        </span>
                    </div>
                    <div id="ville-${ville.codInsee}" class="nested-node dnested" style="display: block; color: var(--tree-view-color) !important;">
                        ${ville.voies && ville.voies.map ? ville.voies.map((voie, index) => `
                            <div class="tree-node" style="padding: 4px 4px 4px 20px;">
                                <span class="toggle-node" data-target="voie-${index}" style="padding-left: 25px;cursor: pointer; display: flex; align-items: center; justify-content: space-between; width: 100%;"
                                      data-type="nomvoie" data-cluster-id="${cluster.codeCluster}" data-ville-id="${ville.codInsee}" data-nomvoie-id="${voie.nomVoie}">
                                    <div style="display: flex; align-items: center;    width: 70%;">
                                        <img src="${flecheFields}" style="margin-right:10px;" class="flecheimg">
                                        <div style="height:12px; width:12px; background:#015D92; margin-right:5px; border-radius:2px;"></div>
                                        <span class="SpanStreetHiarchy">${voie.nomVoie}</span>
                                    </div>
                                    <div style="display: flex; align-items: center; margin-right: 5px;">
                                        <span style="color: var(--tree-view-color);">${voie.total || 0}</span>
                                        <i class="bi bi-x IconDeleteHiarchyCard" onclick="deleteHiarchyCard(this);"></i>
                                    </div>
                                </span>
                                <div id="voie-${index}" class="nested-node" style="display: none; padding-left: 20px;"></div>
                            </div>
                        `).join('') : ''}
                    </div>
                `).join('')}
            </div>
        </div>
    `).join('');
    var btn =`
    <div class="info-item"id="DistribuerBtn" onclick="updateUserDEstribution()"style="display:block;padding: 4px 10px 5px 10px;">
        <span class="Distribuer" > Distribuer</span>
    </div>
    `;
    //console.log('data', data,data.cluster.length);
    card.innerHTML = headerHTML + clustersHTML + `</div>`+btn;
    attachToggleEvents();
    if (data.cluster.length=== 0) {
        document.getElementById("DistribuerBtn").style.display = 'none';
    }
}
async function deleteHiarchyCard(event) {
    const targetElement = event.closest("[data-type]");
    if (!targetElement) return;

    const type = targetElement.getAttribute("data-type");
    const clusterId = targetElement.getAttribute("data-cluster-id");
    const villeId = targetElement.getAttribute("data-ville-id");
    const nomVoieId = targetElement.getAttribute("data-nomvoie-id");

    let data = lastGenratedHiarchyRue;

    let clusterInseeDatag = JSON.parse(localStorage.getItem("clusterInseeDatag")) || [];
    if (!Array.isArray(clusterInseeDatag)) clusterInseeDatag = clusterInseeDatag ? [clusterInseeDatag] : [];

    if (type === "cluster") {
        data.cluster = data.cluster.filter(cluster => cluster.codeCluster !== clusterId);
        clusterInseeDatag = clusterInseeDatag.filter(cluster => cluster.codeCluster !== clusterId);
    } else if (type === "ville") {
        data.cluster.forEach(cluster => {
            if (cluster.codeCluster === clusterId) {
                cluster.villes = cluster.villes.filter(ville => ville.codInsee !== villeId);
            }
        });

        clusterInseeDatag.forEach(cluster => {
            if (cluster.codeCluster === clusterId) {
                cluster.villes = cluster.villes.filter(ville => ville.codInsee !== villeId);
            }
        });
    } else if (type === "nomvoie") {
        data.cluster.forEach(cluster => {
            cluster.villes.forEach(ville => {
                if (ville.codInsee === villeId && Array.isArray(ville.voies)) {
                    ville.voies = ville.voies.filter(voie => voie.nomVoie !== nomVoieId);
                }
            });
        });

        clusterInseeDatag.forEach(cluster => {
            cluster.villes.forEach(ville => {
                if (ville.codInsee === villeId && Array.isArray(ville.voies)) {
                    ville.voies = ville.voies.filter(voie => voie.nomVoie !== nomVoieId);
                }
            });
        });
    }

    CallupdateHiarchyRue(clusterInseeDatag);
}

async function calculateTotalPrises(data) {
    return data.cluster.reduce((sum, cluster) => sum + (parseInt(cluster.totalprises) || 0), 0);
}

async function updateUserDEstribution() { 
   var spanId= document.getElementById("DistribuerBtn");
   spanId.style.cursor = 'not-allowed';
    //spanId.style.pointerEvents = 'none';
    var btn=document.querySelector(".Distribuer");
    btn.innerText = "En cours...";
    btn.style.cursor = 'not-allowed';
    btn.style.pointerEvents = 'none';
    //const dataString = localStorage.getItem("clusterInseeDatag");
    let dataString = localStorage.getItem("clusterInseeDatag");
    if (dataString) {
        dataString = JSON.parse(dataString);
    }
    if (!Array.isArray(dataString)) {
        dataString = [dataString];
    }
    let codeClusters = new Set();
    let codeInsees = new Set();
    let nomsVoies = new Set();
    dataString.forEach(cluster => {
        if (!cluster.villes || cluster.villes.length === 0) {
            codeClusters.add(cluster.codeCluster);
        } else {
            cluster.villes.forEach(ville => {
                if (!ville.voies || ville.voies.length === 0) {
                    codeInsees.add(ville.codInsee);
                } else {
                    ville.voies.forEach(voie => {
                        nomsVoies.add(`${ville.codInsee}|${voie.nomVoie}`);
                    });
                }
            });
        }
    });
    const codeClusterStr = Array.from(codeClusters).join(",");
    const codeInseeStr = Array.from(codeInsees).join(",");
    const nomsVoiesStr = Array.from(nomsVoies).join(",");
    var selectedUserId = localStorage.getItem('selecteduserid');
    console.log('selectedUserId', selectedUserId);
    try {
        const url = `https://api.nomadcloud.fr/api/interventions-places-update-user/${cpv}?codesClusters=${codeClusterStr}&codesInsees=${codeInseeStr}&nomsVoies=${nomsVoiesStr}`; 

        const response = await fetch(url, {
            method: "PATCH",
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                "userId": selectedUserId,
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`); 
        }
        try{
            await distributionScripts();
            const result = await response.json();
            console.log("API Response:", result);
            localStorage.removeItem('clusterInseeData');
            localStorage.removeItem('savedVoies');
            localStorage.removeItem('selectedUserId');
            localStorage.removeItem('selectednom');
            localStorage.removeItem('selectedprenom');
            localStorage.removeItem('clusterInseeDatag');
            localStorage.removeItem('lenghthsavedVoies');
            localStorage.removeItem('savedVilleslibelle');
            showSuccessMessageDistibution();
        } catch {
console.log('error in distributionScripts ');        }
        

        

    } catch (error) {
        console.error("Error updating interventions:", error); 
    }
}
function showSuccessMessageDistibution() {
    const successSpan = document.getElementById('RueHiarchyDataSucess');
    successSpan.style.display = 'block';
    document.getElementById("RueHiarchyData").style.display = 'none';
}

//const url = `/GeoMapp/effectifUser-data`;
//  fetch(url, {
//      method: 'PATCH',
//      headers: {
//          'Content-Type': 'application/json'
//      },
//      body: JSON.stringify({ selectedUserId, codeClusterStr, codeInseeStr, nomsVoiesStr })
//  })
//  .then(response => {
//      if (!response.ok) {
//          return response.text().then(text => { throw new Error(`HTTP error! status: ${response.status}, body: ${text}`); });
//      }
//      return response.json();
//  })
//  .then(data => {
//      console.log('Data updated successfully:', data);

//  })
//  .catch(error => {
//      console.error('Error updating data:', error);
//  });
 






