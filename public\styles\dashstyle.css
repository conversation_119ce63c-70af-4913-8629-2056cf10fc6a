		
		:root {
      --background-color: #fff;
      --text-color: #060607;
      --link-color: #060607;
      --sidebar-left-right-color:#F2F3F5;
      --sidebar-color:#E3E5E8;
      --icons-color:#313338;
      --hover-color:#DFE1E5;
      --bottom-bar-color:#EBEDEF;
      --sitem-color:#ffffff;
      --bootom-bordertop-color:transparent;
      --bootom-colortext:#060607;
      --rightblock-color:#fff;
      --topbar-borderbottom-color:#efefef;
      --lefttopbar-borderbottom-color:#d8d8d8;
      --message-input-container:#EBEDEF;
      --chat-message-hover:#F7F7F7;
      --text-chat-message:#313338;
      --channel-item-hover:#D7D9DC;
      --channel-item-color:#7a7a7c;
      --search-container-input:#ffffff;
      --icon-open-right-sidebar:#060607;
      --icon-topbar:#4E5058;
      --hover-activer-users: #c3c4c7;
      --hover-users:#cccccd;
      --modal-user:#fff;
      --modal-footer:#EBEBEB;
      --sidebar-section-header-hover:#060607;
      --dropdown-content:#fff;
      --status-btn:#fff;
      --dropdown-content-a: #989a9b;
      /* --dropdown-content-hr: rgba(114, 118, 139, .08); */
      --dropdown-content-hr :color-mix(in oklab, hsl(223 calc(1 * 5.8%) 52.9% / 0.24) 100%, hsl(var(--theme-base-color-hsl, 0 0% 0%) / 0.24) var(--theme-base-color-amount, 0%));
      --status-model:#fff;
      --status-model-color:#000;
      --owner-options-vertical:#F5F6F6;
      --owner-btn-hover: #b9bbc0;
      --modal-user-tag: #060607;
      --shadow-high: 0 12px 36px 0 hsla(0, 0%, 0%, 0.12);
      --buttonsContainer:#D7D9DC;
      --fieldsColor:#dddbdb;
      --hover-section:#D7D9DC;
      --accordion-body:#c3c4c7;
      --hover-formTitle:#D7D9DC;
      --modal-content:#8b8b8b;
      --footer-modal:#555555;
      --form-control-inputs:#6f7072;
      --text-placholder:black;
      --hover-Fields:##f2f2f2;
      --background-color-light: #fff;
      --text-color-light: #060607;
      --background-color-dark: #111214;
      --section-title-color:#111214;
      --sidebar-title-color:#111214;
      --text-normal:#111214;
      --text-muted:#111214;
      --icons-sidebar-color: #111214; /* Default icon color (white) */
      --icons-event-color: #111214; /* Icon for event (bi-calendar-event) */
      --icons-add-event-color: #111214; /* Icon for adding event (bi-calendar-plus) */
      --icons-no-event-color: #111214; /* Icon for no event (bi-calendar-x) */
      --icons-plus-circle-color: #111214; /* Icon for plus circle (bi-plus-circle) */
      --icons-folder-color: #111214; /* Icon for folder (bi-folder) */
      --icons-mic-color: #111214; /* Icon for mic (bi-mic) */
      --icons-chevron-color: #111214; /* Icon for chevron (bi-chevron-down) */
      --bgcard-table-color: #2b2d31;
      --bgcard-table-color2: #fff;
       /*tabledata styles*/
	--borders-colors: #ccc;
       --active-bar-colors: black;
     --payment-success-colors:#b5afce ;
   --payment-pending-colors: #ffc107 ;
   --payment-cancelled-colors:#ffc107  ;
   --status-fulfilled-colors:#c794ad ;
   --status-fulfilled-bg-colors:#f5f5f5 ;
   --status-unfulfilled-bg-colors: #f5f5f5;
   --status-cancelled-colors: #ffc107; 
   --status-cancelled-bg-colors:#f5f5f5 ;
   --top-bar-bg:#F2F3F5;

    }
    
    /* Dark mode */
    [data-theme="dark"] {
      --background-color: #333;
      --text-color: #b9bbbe;
      --sidebar-color:#2b3942;
      --sidebar-left-right-color:#1f2c34;
      --icons-color:#B5BAC1;
      --hover-color:#404249;
      --bottom-bar-color:#0f181f;
      --sitem-color:#0f181f;
      --bootom-bordertop-color:#333;
      --bootom-colortext:#fff;
      --rightblock-color:#0f181f;
      --topbar-borderbottom-color:#222427;
      --lefttopbar-borderbottom-color:#222427;
      --message-input-container:#383A40;
      --chat-message-hover:#2B2D31;
      --text-chat-message:#dbdee1db;
      --channel-item-hover:#404249;
      --channel-item-color:#949ba4;
      --search-container-input:#0f181f;
      --icon-open-right-sidebar:#CCCCCC;
      --icon-topbar:#CCCCCC;
      --hover-activer-users:#a2a4a9;
      --hover-users:#404249;
      --modal-user:#111214;
      --modal-footer:#1E1F22;
      --sidebar-section-header-hover:#fff;
      --dropdown-content:#111214;
      --status-btn: #36393f;
      --dropdown-content-a: #c0c6cc;
      --dropdown-content-hr:#3B3D44;
      --status-model:#111214;
      --status-model-color:#fff;
      --owner-options-vertical:#232528;
      --owner-btn-hover: #4b4c4f;
      --modal-user-tag: #B9BBBE;
      --buttonsContainer:#393C41;
      --fieldsColor:#393C41;
      --hover-section:#1d1e20;
      --accordion-body:#2b2d31;
      --hover-formTitle:#2e3035;
      --modal-content:#313338;
      --footer-modal:#2B2D31;
      --form-control-inputs:#1e1f22;
      --text-placholder:white;
      --hover-Fields:#393C41;
      --section-title-color:#fff;
      --sidebar-title-color:#fff;
      --text-normal:#fff;
      --text-muted:#7a7a7c;
      --background-color: var(--background-color-dark);
      --icons-sidebar-color: #bdc3c7; /* Default icon color (dark black) */
      --icons-event-color: #bdc3c7; /* Icon for event (bi-calendar-event) */
      --icons-add-event-color: #bdc3c7; /* Icon for adding event (bi-calendar-plus) */
      --icons-no-event-color: #bdc3c7; /* Icon for no event (bi-calendar-x) */
      --icons-plus-circle-color: #bdc3c7; /* Icon for plus circle (bi-plus-circle) */
      --icons-folder-color: #bdc3c7; /* Icon for folder (bi-folder) */
      --icons-mic-color: #bdc3c7; /* Icon for mic (bi-mic) */
      --icons-chevron-color: #bdc3c7; /* Icon for chevron (bi-chevron-down) */
      --bs-table-color: #f8f9fa;
      --bs-table-bg: #212529;
      --bs-table-striped-color: #f8f9fa;
      --bs-table-striped-bg: #2c3034;
      --bs-table-active-color: #f8f9fa;
      --bs-table-active-bg: #373b3e;
      --bs-table-hover-color: #f8f9fa;
      --bs-table-hover-bg: #323539;
      --bgcardtext-table-color: #fff;
      --bgcard-table-color2: #1e1f22;
      --top-bar-bg:#1f2c34;
	     /*tabledata styles*/
	 --borders-colors: #ccc;
   --active-bar-colors: white;
   --payment-success-colors:#32ba4b ;
   --payment-pending-colors: #bd0bd9;
   --payment-cancelled-colors: #bd0bd9 ;
   --status-fulfilled-colors:#fff ;
   --status-fulfilled-bg-colors:#32ba4b ;
   --status-unfulfilled-bg-colors: #f5f5f5;
   --status-cancelled-colors: #ffc107; 
   --status-cancelled-bg-colors:#f5f5f5 ;

    }
    
    * {
    box-sizing: border-box;
    }
    a,span, button, input[type="submit"], input[type="button"], input[type="reset"] {
      cursor: pointer;
    }[class*="clickable"], [onclick], [role="button"] {
      cursor: pointer;
    }
   
    .sidebar {
      width: 72px;
      background-color: var(--sidebar-color); 
      color: var(--text-color); 
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 20px;
      position: relative;
    }
    
  
    .main-content {
      flex-grow: 1;
      display: flex;
    }
    
    .left-block {
      display: flex;
      flex-direction: column;
      background-color:var(--sidebar-left-right-color);
    }
    
    .right-block {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      width: calc(100% - 240px);
      position: relative;
      overflow: hidden;
      background-color: var(--rightblock-color);
    }
    
    .topbar {
      color: var(--bootom-colortext);
      padding: 20px;
      border-bottom: 2px solid var(--topbar-borderbottom-color);
      height: 10px;
    }
    
    .content {
      flex-grow: 1;
      
      background-color: var(--sidebar-left-right-color);
    }
    
    .bottombar {
      background-color: var(--bottom-bar-color);
      color: #fff;
      padding: 0 8px 1px;
      border-top: 2px solid var(--bottombar-bordertop-color);
      font-weight: 500;
      height:52px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 300px;
      margin-left: -8px;
    }
    

    @media (max-width: 1366px) {
      .bottombar {
        width: 280px;
      }}
    .topbar-right-blcok
    {
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 1;
    }
    
   
   
    .content {
      
      font-family: "Ubuntu", system-ui;
      color: #ddd;

    }
    .sectionDivider {
      height: 2px;
      width: 100%;
      margin-top: 10px;margin-bottom: 10px;
      background-color: var(--lefttopbar-borderbottom-color);
    }
    
    .theme-options {
      list-style: none;
      padding: 0;
      margin: 0;
      position: absolute;
      background: var(--lefttopbar-borderbottom-color);
      width: 15%;
      border-radius: 5px;
      overflow: hidden;
      display: none;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      z-index: 10;
    }
    
    .theme-options li {
      padding: 8px 10px;
      color: var(--text-color);
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    
    .theme-options li:hover {
      background: var(--hover-color);
    }
    
    .theme-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .current-theme {
      
      color: var(--text-color);
      padding: 8px 10px;
      border-radius: 5px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .dark-mode-logo, .light-mode-logo {
      display: none;
    }
    [data-theme="dark"] .dark-mode-logo, [data-theme="light"] .light-mode-logo {
      display: block;
    }
    .div-url-group{background-color:#1e1f22;display:flex;border-radius:4px;width:410px;height:40px;margin-left: 11px;margin-top: 3px;margin-right:11px;}
    .copier-btn{background-color:#5865F2;height: 32px;width: 75px;border-radius:4px;border-color:transparent;margin-right: 6px;margin-top: 3px;}
    .url-group{border-color:transparent;border-top-left-radius:4px;border-bottom-left-radius:4px;}
        
  .right-sidebar-forms {
    width: 240px;
    top: 40px;
    background-color: var(--sidebar-left-right-color);
  }
  .sidebar-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--buttonsContainer);
    border: 1px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: var(--icon-topbar);
  }
  
  .sidebar-btn:hover {
    background-color: #f0f0f0;
  }
  .chat-area {
    height: 96vh;
  }

  /* sidebar items */
  .sitem {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--sitem-color);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  cursor: pointer;
  transition: background-color 0.3s, border-radius 0.3s;
  position: relative;
}
.sitems {
  width: 48px;
  height: 48px;
  border-radius:20%;
  background-color: var(--sitem-color);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  cursor: pointer;
  transition: background-color 0.3s, border-radius 0.3s;
  position: relative;
}

.param-items ~ .sidebar-item:hover {
  background-color: #23A559;
}

/*Second side bar style */
.sidebar-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 10.4px;
  padding: 4px 0;
}

.sidebar-header i {
  font-size: 16px;
  margin-right: 6px;
}

.sidebar-title {
  margin: 0;
  font-size: 18.32px;
  color: var(--sidebar-title-color)
}

.channel-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  padding-left: 16px;
}

.channel-list.show {
  display: block !important;
}

.channel-item {
  display: flex;
  align-items: center;
  padding: 6.4px 4px;
  color: var(--channel-item-color);
  cursor: pointer;
  font-size: 11.2px;
  border-radius: 3.2px;
  transition: background-color 0.3s ease;
}

.channel-item:hover {
  background-color: var(--channel-item-hover-color);
}

.toggle-section-sideb2 {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: 6.4px 4px;
  border-radius: 3.2px;
  transition: background-color 0.3s ease;
}

.toggle-section-sideb2:hover {
  background-color: var(--channel-item-hover-color);
}

.toggle-icon {
  font-size: 12.8px;
  margin-right: 6.4px;
  transition: transform 0.3s ease;
}

.arrow-icon {
  font-size: 12.8px;
  margin-left: auto;
  transition: transform 0.3s ease;
}

.collapsed .arrow-icon {
  transform: rotate(180deg);
}

.collapsed .toggle-icon {
  transform: rotate(-90deg);
}

.hover-icons {
  visibility: hidden;
  margin-left: auto;
}

.channel-item:hover .hover-icons {
  visibility: visible;
}

@media (max-width: 768px) {
  .sidebar-header {
    width: 100%;
  }
}


  .right-side-topbar {display: flex; align-items: center;gap: 10px; margin-right:1.5%;}
  .left-side-topbar {
  display: flex;
  align-items: center;
  }
  
  .left-side-topbar svg {
  margin-right: 10px;
  }
  
  .topbar-right-blcok {
  display: flex;
  gap: 15px;
  background-color: var(--top-bar-bg);
  }

  /* Evenement Modal */ 

  .modal-event {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content-event {
    background-color: #2B2D31;
    margin: 15% auto;
    border-radius: 10px;
    width: 600px;
    height: auto;
    max-height: 80vh;
    overflow-y: auto;
    color: #FFF;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
}

.modal-header-event {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    background-color: #1E1F22;
    padding: 0 20px;
}

.bloc1 {
    display: flex;
    align-items: center;
}

.dropdown-icon {
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.hr-modal {
    width: 1px;
    height: 14px;
    background-color: #666666;
    margin-left: 13px;
}

.create-event-btn {
    background-color: #5865F2;
    color: white;
    border: none;
    padding: 4.5px 10px;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 20px;
}

.close-btn {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.event-placeholder {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
}

.no-event {
    font-size: 20px;
    font-weight: bold;
    margin-top: 10px;
}

.info-text {
    font-size: 12px;
    color: #ccc;
}

    /* table & lazy loading layout */ 
    
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      visibility: hidden;
  }

  .spinner {
      border: 8px solid rgba(0, 0, 0, 0.1);
      border-left-color: #007bff;
      border-radius: 50%;
      width: 100px;
      height: 100px;
      animation: spin 1s linear infinite;
  }

  @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
  }

  .card {
      background-color: var(--hover-bg);
      color: var(--text-color);
  }

  
