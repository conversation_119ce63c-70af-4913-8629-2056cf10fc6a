<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Hiérarchie des Compétitions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 2rem 0;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .tree-view {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            border: 1px solid #dee2e6;
        }
        
        .tree-view ul {
            list-style: none;
            padding-left: 0;
        }
        
        .tree-view .nested {
            display: none;
            margin-left: 1.5rem;
            padding-left: 1rem;
            border-left: 2px solid #007bff;
        }
        
        .tree-view .nested.active {
            display: block;
        }
        
        .ClusterSpan {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin: 0.25rem 0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .ClusterSpan:hover {
            background: #e3f2fd;
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }
        
        .ClusterSpan.loaded {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .competition-icon {
            width: 16px;
            height: 16px;
            background: #007bff;
            border-radius: 3px;
            margin-right: 10px;
        }
        
        .club-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            margin: 0.25rem 0;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            display: flex;
            align-items: center;
        }
        
        .club-icon {
            width: 12px;
            height: 12px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .loading {
            color: #6c757d;
            font-style: italic;
        }
        
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            margin-left: auto;
        }
        
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="test-card">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-trophy text-warning"></i>
                        Test - Hiérarchie des Compétitions
                    </h1>
                    <p class="text-center text-muted mb-4">
                        Cliquez sur une compétition pour voir ses clubs
                    </p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="test-card">
                    <h3 class="mb-3">
                        <i class="fas fa-sitemap text-primary"></i>
                        Hiérarchie Actuelle
                    </h3>
                    <div class="tree-view">
                        <ul id="tree-root">
                            <!-- Le contenu sera généré par JavaScript -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="test-card">
                    <h4><i class="fas fa-info-circle text-info"></i> Données Chargées</h4>
                    <div id="data-info">
                        <p class="loading">Chargement des données...</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="test-card">
                    <h4><i class="fas fa-cogs text-secondary"></i> Actions de Test</h4>
                    <button class="btn btn-primary btn-sm mb-2" onclick="reloadData()">
                        <i class="fas fa-refresh"></i> Recharger les données
                    </button>
                    <br>
                    <button class="btn btn-success btn-sm mb-2" onclick="expandAll()">
                        <i class="fas fa-expand"></i> Tout développer
                    </button>
                    <br>
                    <button class="btn btn-warning btn-sm" onclick="collapseAll()">
                        <i class="fas fa-compress"></i> Tout réduire
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="Data/CompetitionData.js"></script>
    <script>
        // Variables globales
        let loadedCompetitions = new Set();
        
        // Initialisation au chargement de la page
        document.addEventListener("DOMContentLoaded", function () {
            console.log("🚀 Initialisation de la page de test");
            loadCompetitionData();
        });
        
        // Charger les données des compétitions
        function loadCompetitionData() {
            try {
                console.log("📊 Données HierarchyData:", HierarchyData);
                
                // Afficher les informations sur les données
                updateDataInfo();
                
                // Générer le HTML de la hiérarchie
                const htmlContent = generateHierarchyHtml(HierarchyData);
                const treeRoot = document.getElementById('tree-root');
                treeRoot.innerHTML = htmlContent;
                
                // Configurer les événements
                setupCompetitionListeners();
                
                console.log("✅ Hiérarchie générée avec succès");
            } catch (error) {
                console.error("❌ Erreur lors du chargement:", error);
                document.getElementById('data-info').innerHTML = 
                    '<p class="text-danger">Erreur: ' + error.message + '</p>';
            }
        }
        
        // Mettre à jour les informations sur les données
        function updateDataInfo() {
            const info = document.getElementById('data-info');
            info.innerHTML = `
                <p><strong>Nombre de compétitions:</strong> ${HierarchyData.length}</p>
                <p><strong>Continents:</strong> ${[...new Set(HierarchyData.map(c => c.continent))].join(', ')}</p>
                <p><strong>Pays:</strong> ${[...new Set(HierarchyData.map(c => c.country))].join(', ')}</p>
            `;
        }
        
        // Générer le HTML de la hiérarchie (copié du fichier original)
        function generateHierarchyHtml(data) {
            let htmlContent = '';
            
            data.forEach(competition => {
                htmlContent += `
                    <li>
                        <div class="ClusterSpan" data-competition-id="${competition.id}">
                            <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                                <div style="display: flex; align-items: center;">
                                    <div class="competition-icon"></div>
                                    <strong>${competition.name}</strong> 
                                </div>
                                <div class="total-cluster" style="margin-right: 10px;">
                                    (${competition.country})
                                    <span class="status-badge badge-info">${competition.clubs} clubs</span>
                                </div>
                            </div>
                        </div>
                        <ul class="nested">
                            <!-- Les clubs seront chargés ici -->
                        </ul>
                    </li>
                `;
            });
            
            return htmlContent;
        }
        
        // Configurer les événements de clic
        function setupCompetitionListeners() {
            const competitionElements = document.querySelectorAll('.ClusterSpan[data-competition-id]');
            
            competitionElements.forEach(elem => {
                elem.addEventListener('click', async function () {
                    const competitionId = this.getAttribute('data-competition-id');
                    const nestedUl = this.nextElementSibling;
                    
                    console.log(`🏆 Clic sur la compétition: ${competitionId}`);
                    
                    // Toggle visibility
                    nestedUl.classList.toggle('active');
                    
                    // Si déjà chargé, ne pas recharger
                    if (this.classList.contains('loaded')) {
                        console.log("📋 Clubs déjà chargés pour", competitionId);
                        return;
                    }
                    
                    this.classList.add('loaded');
                    loadedCompetitions.add(competitionId);
                    
                    // Afficher un indicateur de chargement
                    nestedUl.innerHTML = '<li class="loading">🔄 Chargement des clubs...</li>';
                    
                    try {
                        // Simuler un appel API (remplace l'appel réel)
                        console.log(`🌐 Chargement des clubs pour ${competitionId}...`);
                        
                        // Simulation de données de clubs
                        const mockClubs = generateMockClubs(competitionId);
                        
                        // Générer le HTML des clubs
                        let clubsHtml = '';
                        mockClubs.forEach(club => {
                            clubsHtml += `
                                <li class="club-item">
                                    <div class="club-icon"></div>
                                    <strong>${club.name}</strong>
                                    <span class="status-badge badge-success ms-auto">${club.country}</span>
                                </li>
                            `;
                        });
                        
                        nestedUl.innerHTML = clubsHtml;
                        console.log(`✅ ${mockClubs.length} clubs chargés pour ${competitionId}`);
                        
                    } catch (error) {
                        console.error(`❌ Erreur lors du chargement des clubs:`, error);
                        nestedUl.innerHTML = '<li class="text-danger">❌ Erreur de chargement</li>';
                    }
                });
            });
        }
        
        // Générer des clubs fictifs pour le test
        function generateMockClubs(competitionId) {
            const clubsByCompetition = {
                'GB1': [
                    { name: 'Manchester City', country: 'England' },
                    { name: 'Arsenal', country: 'England' },
                    { name: 'Liverpool', country: 'England' },
                    { name: 'Chelsea', country: 'England' }
                ],
                'ES1': [
                    { name: 'Real Madrid', country: 'Spain' },
                    { name: 'Barcelona', country: 'Spain' },
                    { name: 'Atletico Madrid', country: 'Spain' },
                    { name: 'Sevilla', country: 'Spain' }
                ],
                'IT1': [
                    { name: 'Juventus', country: 'Italy' },
                    { name: 'Inter Milan', country: 'Italy' },
                    { name: 'AC Milan', country: 'Italy' },
                    { name: 'Napoli', country: 'Italy' }
                ]
            };
            
            return clubsByCompetition[competitionId] || [
                { name: 'Club Example 1', country: 'Unknown' },
                { name: 'Club Example 2', country: 'Unknown' }
            ];
        }
        
        // Fonctions utilitaires pour les tests
        function reloadData() {
            console.log("🔄 Rechargement des données...");
            loadedCompetitions.clear();
            loadCompetitionData();
        }
        
        function expandAll() {
            console.log("📖 Expansion de toutes les compétitions...");
            document.querySelectorAll('.ClusterSpan[data-competition-id]').forEach(elem => {
                if (!elem.classList.contains('loaded')) {
                    elem.click();
                }
            });
        }
        
        function collapseAll() {
            console.log("📕 Réduction de toutes les compétitions...");
            document.querySelectorAll('.nested.active').forEach(nested => {
                nested.classList.remove('active');
            });
        }
    </script>
</body>
</html>
