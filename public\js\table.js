let originalProduits = window.produits || []; // or set it to an empty array initially
let currentPage = 1;
let rowsPerPage = 10;
const pagination = document.getElementById('pagination');
const tableBody = document.getElementById('table-body');
const lengthSelect = document.getElementById('dt-length-0');
const loadingOverlay = document.getElementById('loading-overlay');
const searchInput = document.getElementById('dt-search-0');

// Event listener for changing the number of rows per page
lengthSelect.addEventListener('change', function() {
    rowsPerPage = parseInt(this.value);
    currentPage = 1;
    renderTable();
});

// Function to render the table
function renderTable() {
    loadingOverlay.style.visibility = 'visible';
    tableBody.innerHTML = '';
    const start = (currentPage - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    const paginatedProduits = originalProduits.slice(start, end);
    paginatedProduits.forEach(produit => {
        const row = `<tr>
            <td>${produit.name}</td>
            <td>${produit.organizationNaming}</td>
            <td>${produit.category.name}</td>
            <td>${produit.price}</td>
            <td>${produit.organizationPrice}</td>
            <td>${produit.remunerationCategorieName}</td>
        </tr>`;
        tableBody.innerHTML += row;
    });

    loadingOverlay.style.visibility = 'hidden';
    renderPagination();
    updateInfo();
}

// Function to render pagination
function renderPagination() {
    pagination.innerHTML = '';
    const pageCount = Math.ceil(originalProduits.length / rowsPerPage);
    for (let i = 1; i <= pageCount; i++) {
        const li = document.createElement('li');
        li.className = 'page-item';
        li.innerHTML = `<button class="page-link" onclick="goToPage(${i})">${i}</button>`;
        pagination.appendChild(li);
    }
}

// Function to go to a specific page
function goToPage(page) {
    currentPage = page;
    renderTable();
}

// Function to update info about the current entries
function updateInfo() {
    const info = document.getElementById('example_info');
    const total = originalProduits.length;
    const start = (currentPage - 1) * rowsPerPage + 1;
    const end = Math.min(start + rowsPerPage - 1, total);
    info.innerText = `Affichage de ${start} à ${end} sur ${total} entrées`;
}

// Function to filter the table
function filterTable() {
    const searchTerm = searchInput.value.toLowerCase();
    originalProduits = window.produits.filter(produit => 
        produit.name.toLowerCase().includes(searchTerm) ||
        produit.organizationNaming.toLowerCase().includes(searchTerm) ||
        produit.category.name.toLowerCase().includes(searchTerm) ||
        produit.price.toString().includes(searchTerm) ||
        produit.organizationPrice.toString().includes(searchTerm) ||
        produit.remunerationCategorieName.toString().includes(searchTerm)
    );
    currentPage = 1;
    renderTable();
}

// Initial call to render the table
renderTable();
