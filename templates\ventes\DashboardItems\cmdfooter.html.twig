<style>
  .cmd-container {
  font-family: 'Ubuntu', sans-serif;
  color: #fff;
  position: fixed;
  bottom: 0;
  left: 22%; 
      width: 1050px;
}

    .header-bar {
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .header-bar-title {
      color: #34b7e7;
      font-weight: bold;
    }

    .header-bar-icons {
      display: flex;
      gap: 10px;
    }

    .header-bar-icons img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }

    .sales-content {
      background-color: #114b60;
      padding: 20px;
      border-radius: 8px;
    }

    h1 {
      text-align: center;
      color: #e3f2fd;
    }

    p {
      margin: 10px 0;
    }

    highlight-text {
      font-weight: bold;
      color: #ce00c4;
    }

    autocomplete-field {
      position: relative;
      display: flex;
              color: #fff;
      align-items: center;
    }

    autocomplete-field input {
      padding-right: 35px;
      height: 30px;
      font-size: 16px;
      flex: 1;
                              color: #fff;
      border-radius: 5px;
    }

    autocomplete-field .autocomplete-icon {
      position: absolute;
      right: 10px;
      width: 20px;
                              color: #fff;
      height: 20px;
    }



.input-line {
display: flex;
align-items: center;
}

.cmd-prompt {
margin-right: 5px;
}

#cmd-input {
background: none;
border: none;
color: white;
outline: none;
width: 100%;
}


.command-line {
    font-size: 14px;
    width: 100%;
    height: 65px;
    overflow-x: hidden;
    overflow-y: scroll
}

.command-line.light {
    background-color: #fff
}

.command-line.light .command-row {
    position: relative;
    margin-bottom: 5px
}

.command-line.light .command-row.active {
    background: #f5f5f5
}

.command-line .command-row {
    position: relative;
    margin-bottom: 5px
}


.command-line .command-row .command-time,.command-line .command-row .command-user {
    color: #e7e7e7;
    display: inline-block;
    padding-right: 5px
}

.command-line .command-row .command-user {
    font-weight: 700
}

.command-line .command-row .command-entry {
    padding-right: 5px;
    color: #fff;
    display: inline;
    overflow-wrap: break-word;
    word-wrap: break-word;
    -ms-word-break: break-all;
    word-break: break-all;
    -ms-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto
}

.command-line .command-row .command-entry.command-entry-protected:empty {
    display: none
}

.command-line .command-row .command-entry.block {
    display: block
}

.command-line .command-row .command-entry:focus {
    outline: none
}

.command-line .command-row .secret {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    opacity: 0
}

.command-line .command-row.error .command-entry {
    font-weight: 700;
    color: red
}

.command-line .command-row.success .command-entry {
    font-weight: 700;
    color: #00c300
}

.command-line .command-row.info .command-entry {
    font-weight: 700;
    color: #00a9ff
}

.command-line .command-row.warning .command-entry {
    font-weight: 700;
    color: orange
}


</style>
 
  <div class="cmd-container">
    <div class="sales-content">
      <div class="header-bar">
        <div class="header-bar-title highlight-text">DESKTOP-ON5C84Q ></div>
        <div class="header-bar-icons">
          <div class="autocomplete-field">
                                  <i class="bi bi-funnel" class="autocomplete-icon"></i>
            <input type="text" style="background-color: #101317;" placeholder="">
          </div>
          <i class="bi bi-download fs-5"></i>
          <img src="https://img.icons8.com/ios-glyphs/30/ffffff/add-file.png" alt="Add File">
          <img src="https://img.icons8.com/ios-glyphs/30/ffffff/settings.png" alt="Settings">
          <i class="bi bi-chevron-up"></i>
          <img src="https://img.icons8.com/ios-glyphs/30/ffffff/microphone.png" alt="Microphone">
          <img src="https://img.icons8.com/?size=100&id=45&format=png&color=FFFFFF" alt="close">
        </div>
      </div>

      <div style="line-height: 0.8;">
              <br>
  <div>
    <div id="command-line" class="command-line"></div>
  </div>
</div>  </div>

  <script src="{{ asset('cmd/bundle.min.js') }}"></script>
  <script src="{{ asset('cmd/app.min.js') }}"></script>
    <script src="{{ asset('cmd/script.js') }}"></script>