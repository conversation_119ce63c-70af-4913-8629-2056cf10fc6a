<?php

namespace App\Entity;

use App\Repository\CompetitionsRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CompetitionsRepository::class)]
class Competitions
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $competionId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $name = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $country = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $clubs = null;

    #[ORM\Column(nullable: true)]
    private ?int $players = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $continent = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $totalMarketValue = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $meanMarketValue = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $competitionId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $competionContinent = null;

    #[ORM\Column(length: 255)]
    private ?string $competitionName = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCompetionId(): ?string
    {
        return $this->competionId;
    }

    public function setCompetionId(?string $competionId): static
    {
        $this->competionId = $competionId;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): static
    {
        $this->country = $country;

        return $this;
    }

    public function getClubs(): ?string
    {
        return $this->clubs;
    }

    public function setClubs(?string $clubs): static
    {
        $this->clubs = $clubs;

        return $this;
    }

    public function getPlayers(): ?int
    {
        return $this->players;
    }

    public function setPlayers(?int $players): static
    {
        $this->players = $players;

        return $this;
    }

    public function getContinent(): ?string
    {
        return $this->continent;
    }

    public function setContinent(?string $continent): static
    {
        $this->continent = $continent;

        return $this;
    }

    public function getTotalMarketValue(): ?string
    {
        return $this->totalMarketValue;
    }

    public function setTotalMarketValue(?string $totalMarketValue): static
    {
        $this->totalMarketValue = $totalMarketValue;

        return $this;
    }

    public function getMeanMarketValue(): ?string
    {
        return $this->meanMarketValue;
    }

    public function setMeanMarketValue(?string $meanMarketValue): static
    {
        $this->meanMarketValue = $meanMarketValue;

        return $this;
    }

    public function getCompetitionId(): ?string
    {
        return $this->competitionId;
    }

    public function setCompetitionId(?string $competitionId): static
    {
        $this->competitionId = $competitionId;

        return $this;
    }

    public function getCompetionContinent(): ?string
    {
        return $this->competionContinent;
    }

    public function setCompetionContinent(?string $competionContinent): static
    {
        $this->competionContinent = $competionContinent;

        return $this;
    }

    public function getCompetitionName(): ?string
    {
        return $this->competitionName;
    }

    public function setCompetitionName(string $competitionName): static
    {
        $this->competitionName = $competitionName;

        return $this;
    }
}
