<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class ObjectifsController extends AbstractController
{
    #[Route('/objectifs/test1', name: 'app_objectifs')]
    public function index(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {

        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        // Initialize the $productions variable to ensure it's defined
        $productions = [];

        try {
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;


        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'user' => $userData,

            'showMainBloc' => false,
            'showClusters' => true,  // This variable must be set
            'showVentes' => false,   // Ensure this is false
            'neuves' => false,
        ]);
    }
    #[Route('/clusters/tp', name: 'penetration')]
    public function penetration(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {

        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }


        $productions = [];

        try {
           // Récupération des données utilisateur
           $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response1->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user data.');
            return $this->redirectToRoute('app_dashboard');
        }

        $DataUser = json_decode($response1->getContent(), true);
        $userid = $DataUser['user_id'] ?? null;

        if (!$userid) {
            $this->addFlash('error', 'User ID not found in the response.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Récupération des détails utilisateur
        $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
            ]
        ]);

        if ($response->getStatusCode() !== 200) {
            $this->addFlash('error', 'Failed to fetch user details.');
            return $this->redirectToRoute('app_dashboard');
        }

        $userData = json_decode($response->getContent(), true);


        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'user' => $userData,
            'taux' => true,
            'showMainBloc' => false,
            'showClusters' => false,  // This variable must be set
            'showVentes' => false,   // Ensure this is false
        ]);
    }

    #[Route('/prises/dashboard', name: 'priseDashboard')]
    public function ventesDashboard(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {

        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }


        try {
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('objectif/layout.html.twig', [
            'controller_name' => 'priseDashboard',
            'user' => $userData,

            'distribution' => false,
            'showMainBloc' => false,
            'showClusters' => false,  // This variable must be set
            'showPrise' => true,   // Ensure this is false
        ]);
    }
    #[Route('/prises/distribution', name: 'distributionDashboard')]
    public function distributionDashboard(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        // Get JWT token from the session
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        try {

            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('objectif/layout.html.twig', [
            'controller_name' => 'distributionDashboard',
            'user' => $userData,
            'showMainBloc' => false,
            'showClusters' => false,  // This variable must be set
            'showPrise' => false,
            'distribution' => true,  // Ensure this is false

        ]);
    }


    #[Route('/declaratif/agenda', name: 'agenda')]
    public function agenda(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        $productions = [];
        $kpiData = []; // Initialize variable for the new API data

        try {
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
            // Fetch KPI data
            $kpiResponse = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/declaratifs-kpi?month=12&year=2022&page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $kpiData = json_decode($kpiResponse->getContent(), true);
            error_log('KPI Data: ' . print_r($kpiData, true)); // Log KPI data

        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => true,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showtimeline'=>true,
            'synthese'=>false,
            'synthesedetaille'=>false,
            'synthesedetaillerue'=>false,
                        'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
            'detailsliste' => false,
            'userData' => $userData ?? null, // Pass user data to the template if available
            'kpiData' => $kpiData ?? null,   // Pass KPI data to the template if available
        ]);
    }

    #[Route('/prises/parc/migrables', name: 'liste')]
    public function liste(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $InterventionClustersTotals = [];
        $params = [
            'adsl' => $request->query->get('adsl', 'no'),
            'thd' => $request->query->get('thd', 'no'),
            'mobMono' => $request->query->get('mobMono', 'no'),
            'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
            'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
            'page' => $request->query->get('page', 1),
        ];
    
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Construct API URL with parameters
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                urlencode($cpv),
                urlencode($params['adsl']),
                urlencode($params['thd']),
                urlencode($params['mobMono']),
                urlencode($params['mobMultiThd']),
                urlencode($params['mobMultiAdsl']),
                $params['page']
            );
    
            // Fetch intervention clusters data
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response2->getContent(), true);
                error_log(print_r($InterventionClustersTotals, true));
            } else {
                $this->addFlash('error', 'Failed to fetch intervention cluster data.');
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }
    
        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => false,
            'liste' => true,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'synthese'=>false,
            'synthesedetaille'=>false,
            'synthesedetaillerue'=>false,
                        'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
            'user' => $userData,
            'Clusterstotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
        ]);
    }
    


    #[Route('/prises/parc/migrables/ville/{code_cluster}', name: 'detailslistes')]
    public function listeDetails(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        // Fetch user data
        $userData = [];
        try {
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
      
        // Initialiser les états des boutons à partir des paramètres de la requête
        $params = [
            'adsl' => $request->query->get('adsl', 'no'),
            'thd' => $request->query->get('thd', 'no'),
            'mobMono' => $request->query->get('mobMono', 'no'),
            'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
            'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
            'page' => $request->query->get('page', 1)
        ];

        $clusterDetails = [];
     
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?codeCluster=%s&adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                $cpv,
                $code_cluster,
                $params['adsl'],
                $params['thd'],
                $params['mobMono'],
                $params['mobMultiThd'],
                $params['mobMultiAdsl'],
                $params['page']
            );
            $response = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ],
            ]);
            $clusterDetails = json_decode($response->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster details: ' . $e->getMessage());
        }

        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'detailsliste' => true,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'synthese'=>false,
            'synthesedetaille'=>false,
            'synthesedetaillerue'=>false,
                        'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
            'user' => $userData,
            'clusterDetails' => $clusterDetails,
            'params' => $params // Transmettre les paramètres à la vue
        ]);
    }
  



    #[Route('/prises/parc/migrables/rue/{code_cluster}/{detailkpiinseeCode}', name: 'cluster_detailsrues')]
    public function clusterDetailsRues(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster, string $detailkpiinseeCode, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $clusterDetailsRues = [];
        try {
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            $params = [
                'adsl' => $request->query->get('adsl', 'no'),
                'thd' => $request->query->get('thd', 'no'),
                'mobMono' => $request->query->get('mobMono', 'no'),
                'mobMultiThd' => $request->query->get('mobMultiThd', 'no'),
                'mobMultiAdsl' => $request->query->get('mobMultiAdsl', 'no'),
                'page' => $request->query->get('page', 1)
            ];
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-kpi/%s?codeCluster=%s&codeInsee=%s&adsl=%s&thd=%s&mobMono=%s&mobMultiThd=%s&mobMultiAdsl=%s&page=%d',
                $cpv,
                $code_cluster,
                $detailkpiinseeCode,
                $params['adsl'],
                $params['thd'],
                $params['mobMono'],
                $params['mobMultiThd'],
                $params['mobMultiAdsl'],
                $params['page']
            );
    
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            $clusterDetailsRues = json_decode($response2->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching cluster details: ' . $e->getMessage());
        }
    
        return $this->render('taux/layout.html.twig', [
            'clusterDetailsRuess' => $clusterDetailsRues,
            'detailkpiinseeCode' => $detailkpiinseeCode,
            'controller_name' => 'Ventes Dashboard',
            'user' => $userData,
            'code_cluster' => $code_cluster,
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'detailsliste' => false,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'clusterDetailsRues' => true,
            'synthese'=>false,
            'synthesedetaille'=>false,
            'synthesedetaillerue'=>false,
                        'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
        ]);
    }

    #[Route('/prises/synthese', name: 'synthese')]
    public function synthese(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $InterventionClustersTotals = [];
        $params = [
            'page' => $request->query->get('page', 1),
        ];
    
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Construct API URL with parameters
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-hierarchy/%s?&page=%d',
                urlencode($cpv),
                $params['page']
            );
    
            // Fetch intervention clusters data
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response2->getContent(), true);
                $totalPages = $response2->getHeaders()['x-total-pages'][0] ?? 1; // Assumes the API returns total pages in the headers
            } else {
                $this->addFlash('error', 'Failed to fetch intervention cluster data.');
                $totalPages = 1;
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            $totalPages = 1;
        }
    
        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showtimeline' => false,
            'synthese' => true,
            'synthesedetaille' => false,
            'synthesedetaillerue' => false,
            'detailsliste' => false,
            'clusterDetailsRues' => false,
                        'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
            'user' => $userData,
            'synthesetotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'totalPages' => $totalPages,
        ]);
    }
    
    
    #[Route('/prises/synthese/detaille/{code_cluster}', name: 'synthesedetaille')]
    public function synthesedetaille(HttpClientInterface $httpClient, SessionInterface $session, Request $request,string $code_cluster): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $InterventionClustersTotals = [];
        $params = [
      
            'page' => $request->query->get('page', 1),
        ];
    
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Construct API URL with parameters
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-hierarchy/%s?&page=%d',
                urlencode($cpv),
   
                $params['page']
            );
    
            // Fetch intervention clusters data
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response2->getContent(), true);
               
            } else {
                $this->addFlash('error', 'Failed to fetch intervention cluster data.');
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }
    
        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showtimeline'=>false,
            'synthese'=>false,
            'synthesedetaille'=>true,
            'detailsliste' => false,
            'clusterDetailsRues' => false,
                        'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
            'user' => $userData,
            'synthesetotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'code_cluster' => $code_cluster,
        ]);
    }
    #[Route('/prises/synthese/detaille/rue/{code_cluster}/{detailkpiinseeCode}', name: 'synthesedetaillerue')]
    public function synthesedetaillerue(HttpClientInterface $httpClient, SessionInterface $session, string $code_cluster,string $detailkpiinseeCode, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $InterventionClustersTotals = [];
        $params = [
      
            'page' => $request->query->get('page', 2),
        ];
    
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $url = sprintf(
                'https://api.nomadcloud.fr/api/interventions-places-streets/%s/%s/%s?page=%d',
                urlencode($cpv),
                urlencode($code_cluster),
                urlencode($detailkpiinseeCode),
                $params['page']
            );
    
            // Fetch intervention clusters data
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response2->getContent(), true);
            
            } else {
                $this->addFlash('error', 'Failed to fetch intervention cluster data.');
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }
    
        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showtimeline'=>false,
            'synthese'=>false,
            'synthesedetaille'=>false,
            'synthesedetaillerue'=>true,
            'detailsliste' => false,
            'clusterDetailsRues' => false,
            'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
            'user' => $userData,
            'synthesetotalsrue' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'detailkpiinseeCode' => $detailkpiinseeCode,
            'code_cluster' => $code_cluster,
        ]);
    }
   
    #[Route('/prises/consolidation', name: 'consolidation')]
    public function consolidation(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $InterventionClustersTotals = [];
        $params = [
            'page' => $request->query->get('page', 1),
        ];
    
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $point_of_sale_id = $DataUser['point_of_sale_id'] ?? null;
    
            if (!$point_of_sale_id) {
                $this->addFlash('error', 'point_of_sale_id not found in user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Construct API URL with parameters
            $url = sprintf(
                'https://api.nomadcloud.fr/api/productions-consolidation/%s/B?page=1',
                urlencode($point_of_sale_id),
                $params['page']
            );
    
            // Fetch intervention clusters data
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response2->getContent(), true);
           
            } else {
                $this->addFlash('error', 'Failed to fetch intervention cluster data.');
             
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
           
        }
    
        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showtimeline' => false,
            'synthese' => false,
            'synthesedetaille' => false,
            'synthesedetaillerue' => false,
            'detailsliste' => false,
            'clusterDetailsRues' => false,
            'showconsolidation'=>true,
            'user' => $userData,
            'consolidationtotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            
        ]);
    }
    #[Route('/prises/consolidation/detaille/{code_cluster}', name: 'consolidationdetaille')]
    public function consolidationdetaille(HttpClientInterface $httpClient, SessionInterface $session, Request $request,string $code_cluster): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        $userData = [];
        $InterventionClustersTotals = [];
        $params = [
            'page' => $request->query->get('page', 1),
        ];
    
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $point_of_sale_id = $DataUser['point_of_sale_id'] ?? null;
    
            if (!$point_of_sale_id) {
                $this->addFlash('error', 'point_of_sale_id not found in user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Construct API URL with parameters
            $url = sprintf(
                'https://api.nomadcloud.fr/api/productions-consolidation/%s/B?page=1',
                urlencode($point_of_sale_id),
                $params['page']
            );
    
            // Fetch intervention clusters data
            $response2 = $httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response2->getStatusCode() === 200) {
                $InterventionClustersTotals = json_decode($response2->getContent(), true);
         
            } else {
                $this->addFlash('error', 'Failed to fetch intervention cluster data.');
             
            }
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
           
        }
    
        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'ObjectifsController',
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'showtimeline' => false,
            'synthese' => false,
            'synthesedetaille' => false,
            'synthesedetaillerue' => false,
            'detailsliste' => false,
            'clusterDetailsRues' => false,
            'showconsolidation'=>false,
            'showconsolidationdetaille'=>true,
            'user' => $userData,
            'consolidationdetailletotals' => $InterventionClustersTotals ?? [],
            'params' => $params,
            'code_cluster' => $code_cluster,
            
        ]);
    }
    #[Route('/timeline', name: 'timeline')]
    public function timeline(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
        
  
        $userData = [];

        
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }
        
        return $this->render('taux/layout.html.twig', [
            'controller_name' => 'Ventes Dashboard',
            'user' => $userData,
            'taux' => false,
            'agenda' => false,
            'liste' => false,
            'detailsliste' => false,
            'showMainBloc' => false,
            'showClusters' => false,
            'showVentes' => false,
            'clusterDetailsRues' => false,
            'showtimeline'=>true,
            'synthese'=>false,
            'synthesedetaille'=>false,
            'synthesedetaillerue'=>false,
                        'showconsolidation'=>false,
            'showconsolidationdetaille'=>false,
        ]);
    }
    #[Route('/prises/neuves/livraison', name: 'neuves')]
    public function neuves(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
        
        $clusters = [];
        $userData = [];
        $coordinates = [];
        $insee = [];
        $selectedInseeId = '07011'; // Default value
        
        try {
            // Fetch user data
            $response1 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response1->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $DataUser = json_decode($response1->getContent(), true);
            $userid = $DataUser['user_id'] ?? null;
    
            if (!$userid) {
                $this->addFlash('error', 'User ID not found in the response.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Fetch user details
            $response = $httpClient->request('GET', "https://api.nomadcloud.fr/api/users/{$userid}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
    
            if ($response->getStatusCode() !== 200) {
                $this->addFlash('error', 'Failed to fetch user details.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            $userData = json_decode($response->getContent(), true);
            $cpv = $DataUser['cpv'] ?? null;
    
            if (!$cpv) {
                $this->addFlash('error', 'CPV not found in the user data.');
                return $this->redirectToRoute('app_dashboard');
            }
    
            // Default values for parameters
            $codeCluster = $request->query->get('codeCluster', '07-01');
            
            // Validate codeCluster format
            if (!preg_match('/^\d{2}-\d{2}$/', $codeCluster)) {
                $this->addFlash('error', 'Invalid code cluster.');
                return $this->redirectToRoute('neuves');
            }
    
            // Fetch clusters data
            $response3 = $httpClient->request('GET', "http://api.nomadcloud.fr/api/clusters/{$cpv}?page=1", [
                'headers' => ['Authorization' => 'Bearer ' . $jwt]
            ]);
    
            if ($response3->getStatusCode() === 200) {
                $clusters = json_decode($response3->getContent(), true);
            } else {
                $this->addFlash('error', 'Failed to fetch clusters data.');
            }
    
            // Fetch INSEE data
            $url = sprintf('http://api.nomadcloud.fr/api/clusters/%s?codeCluster=%s', $cpv, $codeCluster);
            $response4 = $httpClient->request('GET', $url, [
                'headers' => ['Authorization' => 'Bearer ' . $jwt]
            ]);
            
            if ($response4->getStatusCode() === 200) {
                $insee = json_decode($response4->getContent(), true);
            }
    
          
            if (!empty($insee['codes_insee'])) {
                $selectedInseeId = $request->query->get('codeInsee', $insee['codes_insee'][0]['code_insee']);
            } else {
                // Handle case where 'codes_insee' is empty or not available
                $this->addFlash('error', 'No INSEE codes found.');
                $selectedInseeId = '07011'; // Fallback value or redirect as needed
            }
    
            // Pagination and fetching coordinates
            $page = max(1, (int) $request->query->get('page', 1)); // Always at least page 1
            $url = sprintf(
                'http://api.nomadcloud.fr/api/interventions-places/coordinates/%s?codeInsee=%s&page=%d',
                $codeCluster,
                $selectedInseeId,
                $page
            );
            $response2 = $httpClient->request('GET', $url, [
                'headers' => ['Authorization' => 'Bearer ' . $jwt]
            ]);
    
            if ($response2->getStatusCode() === 200) {
                $coordinates = json_decode($response2->getContent(), true);
            } else {
                $this->addFlash('error', 'Failed to fetch coordinates data.');
            }
            
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
        }
        
        return $this->render('neuves/index.html.twig', [
            'controller_name' => 'priseDashboard',
            'user' => $userData,
            'coordinates' => json_encode($coordinates['results'] ?? []),
            'clusters' => $clusters ?? [],
            'selectedClusterId' => $codeCluster,
            'insee' => $insee,
            'selectedInseeId' => $selectedInseeId,
        ]);
    }
}
