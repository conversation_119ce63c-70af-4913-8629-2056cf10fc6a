   
   <style>
    :root {
         --carddiscordrow-gap: 18px;
        --carddiscordcol-gap: 18px;
        --card-width: 300px;
        --card-height: 330px;
 
    }
    
    [data-theme="dark"] {
        --carddiscordrow-gap: 18px;
        --carddiscordcol-gap: 18px;
        --carddiscordcard-width: 300px;
        --card-height: 330px;
    
    }
    

</style>

<div id="carddiscord" style="overflow-y: auto; max-height: calc(100vh - 60px);">
    <div class="container py-5">
        <div class="row row-cols-md-3 g-3 nextrow">
            
        </div>
        <div class="row row-cols-md-3 g-3 nextrow">
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>

