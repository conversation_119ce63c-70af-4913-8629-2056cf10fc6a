<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

<style>
    body {
        font-family: 'Ubuntu', sans-serif;
    }
    .map {
        height: calc(100% - 90px);
    }
    .leaflet-container {
        background-color: #f0f4f3;    
    }
    .dot-graph {
        display: flex;
        align-items: center;
       
        padding: 10px;
        background-color: #e7f0ef;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
                z-index: 1000; 

    }

    .floating-title {
        position: absolute;
        top: 10px; 
        left: 10px; 
        font-size: 1.5em; 
        color: #333; 
        margin: 0; 
        pointer-events: none; 
        z-index: 1000; 
    }



    .circle {
        width: 90px;
        height: 75px;
        background: linear-gradient(135deg, #5fadbc, #74b490);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }
    .circle::before {
        content: '';
        width: 50px;
        height: 50px;
        background-color: white;
        border-radius: 50%;
        position: absolute;
    }
    .circle-text {
        color: #333;
        position: relative;
        font-weight: bold;
        z-index: 1;
            font-size: 17px;  
    }
    .dots-container {
        display: flex;
        margin-top: 20px
    }
       .dts-dot-container {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .dts-dot {
            width: 10.60px;  
            height: 10.60px;  
            border-radius: 50%;
            position: relative;
            margin: 0 10.60px;  
        }
        .dts-dot1 {
            background-color: #9f1b08;
            bottom: 19.95px;  
            left: 33.63px;  
        }
        .dts-dot2 {
            background-color: #f6a942;
            left: 9.84px;  
            top: 1.2px;
        }
        .dts-dot3 {
            background-color: #058300;
            bottom: 12.00px;  
            right: 10.60px;  
        }
        .dts-text-top,
        .dts-text-bottom {
            position: absolute;
            font-size: 12.15px;  
            font-weight: bold;  
            width: 100%;
            text-align: center;
        }
        .dts-text-top {
            top: -21.08px;  
        }
        .dts-text-bottom {
            bottom: -21.08px;  
        }
        .dts-line {
            position: relative;
            border-top: 1px dashed black;
            width: 53.13px;  
            margin-top: -3.52px;  
            transition: border-width 0.3s;
            transform-origin: left center; 
        }
        .dts-line:nth-child(4) {
            transform: translateY(-10.70px);  
        }
        .dts-line:nth-child(2) {
            transform: translateY(-23.40px) rotate(19.25deg);  
            left: 24.21px;  
            top: 8.37px;  
        }
        .dts-line:nth-child(4) {
            transform: translateY(3.52px) rotate(-12.15deg); 
        }
        .dts-line1,
        .dts-line2 {
            border-top-width: 1.77px;  
        }
</style>

<h3 class="floating-title">Clusters</h3> <!-- Floating title -->

<div class="map" id="map"></div>
<div class="bonus-expand-icon">
    <i class="bonus-expand-icon bi bi-fullscreen"></i> 
</div>
<div class="dot-graph">
    <div class="circle">
        <span class="circle-text">43.7%</span>
    </div>
    <div class="dots-container">
         <div class="dts-dot-container">
            <div class="dts-dot dts-dot1">
                <div class="dts-text-top">42</div>
                <div class="dts-text-bottom">297</div>
            </div>
            <div class="dts-line dts-line1"></div>
            <div class="dts-dot dts-dot2">
                <div class="dts-text-top">3</div>
                <div class="dts-text-bottom">85</div>
            </div>
            <div class="dts-line dts-line2"></div>
            <div class="dts-dot dts-dot3">
                <div class="dts-text-top">8</div>
                <div class="dts-text-bottom">333</div>
            </div>
        </div>
    </div>
</div>

<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var map = L.map('map', {
            attributionControl: false,
            zoomControl: false 
        }).setView([48.8214, 2.3522], 12); 

        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            maxZoom: 20,
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
        }).addTo(map);

        function addRandomMarkers() {
            for (let i = 0; i < 10; i++) {
                let lat = 48.8566 + (Math.random() - 0.5) * 0.1; 
                let lng = 2.3522 + (Math.random() - 0.5) * 0.1;

                const greenIcon = L.divIcon({
                    className: 'custom-icon',
                    html: '<i class="bi bi-geo-alt-fill" style="color: green; font-size: 20px;"></i>',
                    iconSize: [40, 40],
                    iconAnchor: [30, 20]
                });

                L.marker([lat, lng], { icon: greenIcon }).addTo(map);
            }
        }

        addRandomMarkers();
    });
</script>