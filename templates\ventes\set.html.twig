<link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">

	<style>:root
	{
		--card-width: 200px;
		--card-height: 135px;
		--card-gap-horizontal: 12.2px;
		--card-gap-vertical: 8px;
		--background-color-select: #ecf4ef;
		--background-color-cards: #e7f3fa;
		--text-selects-colors: #335d67;
		--text-inside-car-colors: #2c455c;
		--filled-progress-bar-color: #bfc7cc;
		--background-color-card-none: #e3e5e9;
		--buttonvalide-background-color: #e7f5e5;
		--buttonvalide-text-color: green;
		--buttonvalide-border-type: none;
		--btn-border-color: transparent;
		--small-ko-button-colorbg: #fef7fe;
		--scrollbar-track: #eee;
		--rightnav-bg:#ececf1;
		--active-bg:white ;
		--active-color:#333;
		--skeleton-bg:#e0e0e0
	}

	[data-theme="dark"] {
		--card-width: 200px;
		--card-height: 135px;
		--card-gap-horizontal: 12.2px;
		--card-gap-vertical: 12px;
		--background-color-select: #1e1f21;
		--background-color-cards: #1e1f21;
		--text-selects-colors: #7d7e81;
		--text-inside-car-colors: #888888;
		--filled-progress-bar-color: #3f3e40;
		--background-color-card-none: #272a31;
		--buttonvalide-background-color: transparent;
		--buttonvalide-text-color: #6a6a6e;
		--buttonvalide-border-type: 1px;
		--btn-border-color: #888888;
		--small-ko-button-colorbg: #242c2d;
		--scrollbar-thumb: #2B2D31;
			--rightnav-bg:#000;
			--active-bg:#333 ;
			--active-color:white;
			--skeleton-bg:#1e1f21;

	}
#rsuro {
	display: flex;
	flex-direction: column;
	scrollbar-width: none;
	scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
	overflow-y: auto; /* Enable scrolling when content overflows */
	max-height: 98vh; /* Limit the height, adjust as needed */
}

#rsuro .container {
	display: flex;
	flex-wrap: wrap;
	padding: 1px;
	gap: var(--card-gap-vertical) var(--card-gap-horizontal);


}

#rsuro .container > div {

	margin: 0;
}

#rsuro .card-custom {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-cards);
	padding: 5px;
	text-align: center;
	flex-direction: column;
}

#rsuro .card-custom-none {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-card-none);
	padding: 6px;
	text-align: center;
	flex-direction: column;
}

#red {
	border-left: 5px solid #ff4c4c;
}
#green {
	border-left: 5px solid #21b39f;
}
#orange {
	border-left: 5px solid #ea9765;
}

#yellow {
	border-left: 5px solid #f4af00;
}

#gray {
	border-left: 5px solid var(--filled-progress-bar-color);
}

#rsuro .card-header {
	font-size: 1.0rem;
	font-weight: 600;
	background-color: transparent;
	border: none;
	margin-bottom: 2px;
}
.card-header{
    padding: 0rem;
}
#headertextolor {
	color: var(--text-inside-car-colors);
}

#nonetext {
	color: var(--filled-progress-bar-color);
}



@media screen and (min-width: 1400px) {
#rsuro .col-auto {
	margin-left: -0.4%;
	margin-bottom: 10px;
}
}

@media screen and (max-width: 1366px) and (min-width: 400px) {
   #rsuro .col-auto {
	margin-left: -1.4%;
	margin-bottom: 10px;
}
}

#rsuro .card-number {
	margin-top:-5%;
	font-size: 2.2rem;
	font-weight: bold;
	margin-left: 27%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
}

#rsuro .card-number-none {
	margin-top: -15%;
	font-size: 2.3rem;
	font-weight: bold;
	margin-left: 11%;
	color: var(--filled-progress-bar-color);
	background-color: transparent;
}

#rsuro .card-subtext {
	font-size: 0.7rem;
	font-weight: 500;
	margin-left: 1%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
	text-align: left;
}

#rsuro .ko-button {
	background-color: var(--small-ko-button-colorbg);
	color: white;
	border-radius: 30%;
	padding: 3px 8px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	font-size: 0.65rem;
	border: none;
}

#rsuro .ko-text {
	color: red;
	margin-right: 10%;
}

#rsuro .number {
	color: var(--text-inside-car-colors);
}

#rsuro .card-footer {
	font-size: 0.68rem;
	color: var(--text-inside-car-colors);
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: transparent;

	border: none;
}

#rsuro .card-footer-none {
	font-size: 0.68rem;
	color: var(--filled-progress-bar-color);
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
}

#rsuro .progress-bar-custom {
	height: 4px;
	background-color: #515254;
	border-radius: 2px;
	width: 100%;
}

#rsuro .progress-bar-fill {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 48%;
}

#rsuro .progress-bar-none {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 0;
}
#rsuro .navbar {
	background-color: transparent;
	padding: 15px;
}

#rsuro .navbar .form-select {
	height: 90%;
	margin-right: 9px;
	font-size: 90%;
}

#rsuro .navbar .form-select.date-select {
	background-color: var(--background-color-select);
	border: none;
	width: 126px;
	color: var(--text-selects-colors);
	font-size: 90%;
	border-radius: 9px;
}

#rsuro .navbar .form-select.week-select {
	width: 153px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%;
	border-radius: 9px;
}

#rsuro .navbar .form-select.manager-select {
	width: 160px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%;
	border-radius: 9px;
}

#rsuro .navbar .btn {
	width: 90px;
	height: 75%;
}

#rsuro .btnicon {
	color: var(--text-selects-colors);
	background-color: var(--background-color-select);
	border: none;
	height: 90%;
	margin-right: 9px;
	border-radius: 9px;
}

#rsuro .btn {
	color: var(--buttonvalide-text-color);
	background-color: var(--buttonvalide-background-color);
	border: var(--buttonvalide-border-type) solid var(--btn-border-color);
	border-radius: 9px;
}

#rsuro .btncqt {
	color: #ffff;
	background-color: #33b4e8;
	border: none;
	height: 35px;
	width: 90px;
	border-radius: 10.8px;
}

#rsuro .btnmig {
	color: #3d7fa8;
	background-color: transparent;
	border: 1px solid #3d7fa8;
	height: 35px;
	width: 90px;
	border-radius: 10.8px;
}


#rsuro .select-wrapper {
	display: flex;
	align-items: center;
	background-color: var(--background-color-cards);
	border-radius: 9px;
	padding: 0 2px;
	margin-right: 9px;
}

#rsuro .select-wrapper .bi {
	color: var(--text-selects-colors);
	margin-left: 1.5%;
}
#rsuro .select-wrapper .form-select {
	border: none;
	margin: 0;
	background: none;
	box-shadow: none;
	border-radius: 0;
	width: auto;
}

#rsuro .start-date {
	border-top-left-radius: 9px;
	border-bottom-left-radius: 9px;
}

#rsuro .end-date {
	border-top-right-radius: 9px;
	/ border-bottom-right-radius: 9px;
}

#rsuro .cardscont {}
.rightnav {
	background-color: var(--rightnav-bg);  /* Dark background */
	padding: 5px;
	border-radius: 8px;
	display: inline-flex;
	gap: 10px;

}

.btns {
	color: #999;
	font-size: 14px;
	font-weight: 500;
	background-color: transparent; /* Button background */
	border: none;
	padding: 2px 15px;
	border-radius: 5px;
	display: flex;
	align-items: center;
	gap: 5px; /* Space between icon and text */
	transition: background-color 0.2s;
}
.active {
	background-color: var(--active-bg);
	color: var(--active-color);
	
}
.btns:hover {
background-color: var(--active-bg);/* Hover state */
}

.bi {
	font-size: 16px; /* Icon size */
}
@media screen and(min-width: 1920px) and(min-height: 1080px){:root {
	--card-width: 320px;
	--card-height: 180px;
	--card-gap-horizontal: 15.2px;
	--card-gap-vertical: 12px;
	--background-color-select: #1e1f21;
	--background-color-cards: #1e1f21;
	--text-selects-colors: #7d7e81;
	--text-inside-car-colors: #888888;
	--filled-progress-bar-color: #3f3e40;
	--background-color-card-none: #272a31;
	--buttonvalide-background-color: transparent;
	--buttonvalide-text-color: #6a6a6e;
	--buttonvalide-border-type: 1px;

	--btn-border-color: #888888;
	--small-ko-button-colorbg: #242c2d;
}

[data-theme="dark"] {
	--card-width: 320px;
	--card-height: 180px;
	--card-gap-horizontal: 10.2px;
	--card-gap-vertical: 12px;
	--background-color-select: #1e1f21;
	--background-color-cards: #1e1f21;
	--text-selects-colors: #7d7e81;
	--text-inside-car-colors: #888888;
	--filled-progress-bar-color: #3f3e40;
	--background-color-card-none: #272a31;
	--buttonvalide-background-color: transparent;
	--buttonvalide-text-color: #6a6a6e;
	--buttonvalidemig-text-color: #40a9dd;
	--buttonvalidemig-text-color: #40a9dd;
	--buttonvalide-border-type: 1px;
	--btn-border-color: #888888;
	--btnmig-border-color: #40a9dd;
	--small-ko-button-colorbg: #242c2d;

}

#rsuro .col-auto {
    margin-left: 2%;
    margin-bottom: 10px;
}


#rsuro .container {
	display: flex;
	flex-wrap: wrap;
	padding: 1px;
	gap: var(--card-gap-vertical) var(--card-gap-horizontal);
	
}

#rsuro .container > div {
	gap:5px
	margin: 0;
}

#rsuro .card-custom {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-cards);
	padding: 30px;
	text-align: center;
	flex-direction: column;
}

#rsuro .card-custom-none {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-card-none);
	padding: 30px;
	text-align: center;
	flex-direction: column;
}

#red {
	border-left: 5px solid #ff4c4c;
}
#green {
	border-left: 5px solid #21b39f;
}
#orange {
	border-left: 5px solid #ea9765;
}

#yellow {
	border-left: 5px solid #f4af00;
}

#gray {
	border-left: 5px solid var(--filled-progress-bar-color);
}

#rsuro .card-header {
	font-size: 1.0rem;
	font-weight: 600;
	background-color: transparent;
	border: none;
	margin-right: 4%;
	margin-top: -10%;
	margin-bottom: 2px;
	padding: 0rem;
}
.card-header{
    padding: 0rem;
}
#headertextolor {
	color: var(--text-inside-car-colors);
}

#nonetext {
	color: var(--filled-progress-bar-color);
}

#rsuro .card-number {
	font-size: 3rem;
	font-weight: bold;
	margin-top: -13%;
	margin-left: 11%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
}

#rsuro .card-number-none {
	margin-top: -13%;
	font-size: 3rem;
	font-weight: bold;
	margin-left: 11%;
	color: var(--filled-progress-bar-color);
	background-color: transparent;
}

#rsuro .card-subtext {
	font-size: 0.7rem;
	font-weight: 500;
	margin-left: 1%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
	text-align: left;
}

#rsuro .ko-button {
	background-color: var(--small-ko-button-colorbg);
	color: white;
	border-radius: 30%;
	padding: 3px 8px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	font-size: 1.2rem;
	border: none;
}

#rsuro .ko-text {
	color: red;
	margin-right: 10%;
}

#rsuro .number {
	color: var(--text-inside-car-colors);
}

#rsuro .card-footer {
	font-size: 1rem;
	color: var(--text-inside-car-colors);
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: transparent;

	border: none;
}

#rsuro .card-footer-none {
	font-size: 1rem;
	color: var(--filled-progress-bar-color);
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
}

#rsuro .progress-bar-custom {
	height: 4px;
	background-color: #515254;
	border-radius: 2px;
	width: 100%;
}

#rsuro .progress-bar-fill {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 48%;
}

#rsuro .progress-bar-none {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 0;
}
#rsuro .navbar {
	background-color: transparent;
	padding: 18px;
}

#rsuro .navbar .form-select {
	height: 108%;
	margin-right: 10.8px;
	font-size: 108%;
}

#rsuro .navbar .form-select.date-select {
	background-color: var(--background-color-select);
	border: none;
	width: 151.2px;
	color: var(--text-selects-colors);
	font-size: 108%;
	border-radius: 10.8px;
}

#rsuro .navbar .form-select.week-select {
	width: 183.6px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 108%;
	border-radius: 10.8px;
}

#rsuro .navbar .form-select.manager-select {
	width: 192px;
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 108%;
	border-radius: 10.8px;
}

#rsuro .navbar .btn {
	width: 108px;
	height: 97.2%;
}

#rsuro .btnicon {
	color: var(--text-selects-colors);
	background-color: var(--background-color-select);
	border: none;
	height: 108%;
	margin-right: 10.8px;
	border-radius: 10.8px;
}

#rsuro .btns {
	color: var(--buttonvalide-text-color);
	background-color: var(--buttonvalide-background-color);
	border: var(--buttonvalide-border-type) solid var(--btn-border-color);
	border-radius: 10.8px;
}


#rsuro .btncqt {
	color: #ffff;
	background-color: #33b4e8;
	border: none;
	height: 40px;
	width: 100px;
	border-radius: 10.8px;
}

#rsuro .btnmig {
	color: #3d7fa8;
	background-color: transparent;
	border: 1px solid #3d7fa8;
	height: 40px;
	width: 100px;
	border-radius: 10.8px;
}


#rsuro .select-wrapper {
	display: flex;
	align-items: center;
	background-color: var(--background-color-cards);
	border-radius: 10.8px;
	padding: 0 2.4px;
	margin-right: 10.8px;
}

#rsuro .select-wrapper .bi {
	color: var(--text-selects-colors);
	margin-left: 1.8%;
}

#rsuro .start-date {
	border-top-left-radius: 10.8px;
	border-bottom-left-radius: 10.8px;
}

#rsuro .end-date {
	border-top-right-radius: 10.8px;
	border-bottom-right-radius: 10.8px;
}

#rsuro .cardscont {
	margin-left: 0.5%;
}
.rightnav {
	background-color: var(--rightnav-bg); /* Dark background */
	padding: 5px;
	border-radius: 8px;
	display: inline-flex;
	gap: 10px;
}

.btns {
	color: white;
	font-size: 14px;
	font-weight: 500;
	background-color: transparent; /* Button background */
	border: none;
	padding: 2px 15px;
	border-radius: 5px;
	display: flex;
	align-items: center;
	gap: 5px; /* Space between icon and text */
	transition: background-color 0.2s;
}
.active {
	background-color: #333
}
.btns:hover {
	background-color: #333; /* Hover state */
}

.bi {
	font-size: 16px; /* Icon size */
}
}
</style>

<style>
       :root {
    --skeleton-light-bg: #e0e0e0; /* Light mode skeleton base color */
    --skeleton-dark-bg: #555555;  /* Dark mode skeleton base color */
    --skeleton-light-shimmer: #f0f0f0;
    --skeleton-dark-shimmer: #777777;
    --text-light: #fff;            /* Light text color for dark mode */
    --text-dark: #000;             /* Dark text color for light mode */
}

/* Styles for dark mode */
[data-theme="dark"] {
    --skeleton-light-bg: #444444;  /* Darker skeleton color in dark mode */
    --skeleton-light-shimmer: #666666;
    --text-light: #fff;            /* Light text color */
    --text-dark: #333333;          /* Darker text color for better contrast */
}

/* Style for the skeleton loaders */
.skeleton-header,
.skeleton-number,
.skeleton-progress,
.skeleton-footer {
    background-color: var(--skeleton-light-bg); /* Base skeleton background color */
    border-radius: 4px;
    animation: shimmer 1.5s infinite; /* Animation for shimmering effect */
    margin: 10px auto;
}

.skeleton-header {
    width: 60%;
    height: 20px;
}

.skeleton-number {
    width: 40%;
    height: 40px;
}

.skeleton-progress {
    width: 80%;
    height: 10px;
}

.skeleton-footer {
    width: 50%;
    height: 20px;
}

/* Shimmer animation effect */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: 200px 0;
    }
}

/* Skeleton background gradient effect */
.skeleton-header,
.skeleton-number,
.skeleton-progress,
.skeleton-footer {
    background: linear-gradient(
        to right,
        var(--skeleton-light-bg) 8%,
        var(--skeleton-light-shimmer) 18%,
        var(--skeleton-light-bg) 33%
    );
    background-size: 800px 104px;
}

/* Hide real content when loading */
.card-custom.loading .card-header,
.card-custom.loading .card-footer,
.card-custom.loading .progress-bar-custom {
    display: none; /* Hide actual content during loading */
}

.card-custom.loading .skeleton-header,
.card-custom.loading .skeleton-number,
.card-custom.loading .skeleton-progress,
.card-custom.loading .skeleton-footer {
    display: block; /* Show skeletons when loading */
}

.card-custom .skeleton-header,
.card-custom .skeleton-number,
.card-custom .skeleton-progress,
.card-custom .skeleton-footer {
    display: none; /* Hide skeletons by default */
}

    </style>


<div id="rsuro">
    <nav class="navbar">
        <div class="container">
            <div class="d-flex align-items-center">
                <button class="btnicon btn-primary ms-auto">
                    <i class="bi bi-geo-alt-fill" style="font-size: 1.3rem;"></i>
                </button>
                <div class="select-wrapper">
                    <i class="bi bi-calendar-week" style="font-size: 1.2rem;"></i>

                    <!-- Start Date Picker -->
                    <input type="text" class="form-select date-select start-date" id="startDate" name="startDate" value="01-10-2024">

                    <span class="date-separator">-</span>

                    <!-- End Date Picker -->
                    <input type="text" class="form-select date-select end-date" id="endDate" name="endDate" value="31-10-2024">
                </div>
                <select class="form-select week-select me-2">
                    <option selected>Toutes semaines</option>
                </select>
                <select class="form-select manager-select me-2">
                    <option selected>Tous gestionnaires</option>
                </select>

                <!-- Form submission button -->
                <form action="{{ path('rsuro_livraison') }}" method="get" id="dateForm">
                    <input type="hidden" id="hiddenStartDate" name="startDate" value="01-10-2024">
                    <input type="hidden" id="hiddenEndDate" name="endDate" value="31-10-2024">
                    <input type="hidden" id="hiddenClusterId" name="clusterId">
                    <button class="btn btn-primary ms-auto" type="submit">Valider</button>
                </form>
            </div>
            <div>
		<form action="{{ path('rsuro_livraison') }}" method="get" id="dateForm">
    <input type="hidden" id="hiddenStartDate" name="startDate" value="01-10-2024">
    <input type="hidden" id="hiddenEndDate" name="endDate" value="31-10-2024">
    <input type="hidden" id="hiddenClusterId" name="clusterId">
    <i class="bi bi-chevron-left chevron-icon" id="submitIcon"></i>
</form>
			</div>

<div class="rightnav">
    {% for category in categories %}
        <button class="btns btn-cqt ms-auto me-2" data-id="{{ category.id }}">
            <i class="bi bi-arrow-clockwise"></i>
            {{ category.name }}
        </button>
    {% else %}
        <p>No categories available.</p>
    {% endfor %}
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Select all buttons with the category class
        const categoryButtons = document.querySelectorAll('.btn-cqt');

        // Add event listener to each button
        categoryButtons.forEach(button => {
            button.addEventListener('click', function () {
                const categoryId = this.getAttribute('data-id');

                // Redirect to the new URL with the categoryId as a query parameter
                const url = new URL(window.location.href);
                url.searchParams.set('categoryId', categoryId);
                window.location.href = url.toString();
            });
        });
    });
</script>



        </div>
    </nav>


<script>
    // Ajoute un événement au clic sur l'icône pour soumettre le formulaire
    document.getElementById('submitIcon').addEventListener('click', function() {
        document.getElementById('dateForm').submit();
    });
</script>
    <style>
        .chevron-icon {
            font-size: 2rem; /* Larger icon size */
            color: white;    /* White color */
        }
    </style>

    <script>
    flatpickr("#startDate", {
        dateFormat: "d-m-Y",
        defaultDate: "01-10-2024",
        onChange: function(selectedDates, dateStr, instance) {
            updateHiddenInputs();
        }
    });

    flatpickr("#endDate", {
        dateFormat: "d-m-Y",
        defaultDate: "31-10-2024",
        onChange: function(selectedDates, dateStr, instance) {
            updateHiddenInputs();
        }
    });

    function updateHiddenInputs() {
        const startDate = document.querySelector('#startDate').value;
        const endDate = document.querySelector('#endDate').value;

        document.querySelector('#hiddenStartDate').value = startDate;
        document.querySelector('#hiddenEndDate').value = endDate;
    }

    function setClusterIdAndSubmit(idCluster) {
        document.querySelector('#hiddenClusterId').value = idCluster;
        document.querySelector('#dateForm').submit();
    }

    function defaultFetch() {
        document.querySelector('#hiddenClusterId').value = "";
        document.querySelector('#dateForm').submit();
    }

    updateHiddenInputs();

    // Event listener for the default fetch icon
    document.querySelector('.bi-chevron-left.chevron-icon').addEventListener('click', defaultFetch);
</script>

<div class="cardscont">
    <div class="test" style="padding-left: 20px;">
        <div class="row  ">
            <div class="cardscont">
                <div class="row  ">
             <!-- Loop through each year -->
{% for year, production in productionskpis %}
    {% for month, data in production %}
        {% if data.clusters is defined %}
            {% for kpi in data.clusters %}
                <div class="col-auto">
                    <div class="card-custom" id="red" data-cluster-id="{{ kpi.codeCluster }}" onclick="setClusterIdAndSubmit('{{ kpi.codeCluster }}')">
                        <!-- Skeleton Loading -->
                        <div class="skeleton-header"></div>
                        <div class="skeleton-number"></div>
                        <div class="skeleton-progress"></div>
                        <div class="skeleton-footer"></div>

                        <!-- Actual content hidden by default -->
                        <div class="card-header" id="headertextolor">
                            {% if kpi.ville is defined %}
                                {{ kpi.ville }}
                            {% else %}
                                {# {% set words = kpi.nomCluster|split(' ') %} #}
                                {# {{ words[0] }} #}
                                {# {% if words[1] is defined %}
                                    <span style="font-size: 12px;">{{ words[1] }}</span>
                                {% endif %} #}
                            {% endif %}
                        </div>
                        <div class="card-footer">
                            <div class="card-number">
    {{ data.moyenneTotal is defined ? data.moyenneTotal|number_format(2) : 'N/A' }}
</div>

                        </div>
                        <div class="card-footer">
                            <span>{{ kpi.moyenneParCluster is defined ? kpi.moyenneParCluster : 'N/A' }}</span>
                            <span>{{ kpi.codeCluster is defined ? kpi.codeCluster : 'N/A' }}</span>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% endif %}
    {% endfor %}
{% endfor %}

                </div>
            </div>
        </div>
    </div>
</div>

    <script>
    // Fonction pour afficher les skeletons pendant 2 secondes
    function loadData() {
        const cards = document.querySelectorAll('.card-custom');

        // Ajouter la classe 'loading' pour afficher les skeletons
        cards.forEach(card => {
            card.classList.add('loading');
        });

        // Simuler un délai de 2 secondes avant de cacher les skeletons et afficher les vrais contenus
        setTimeout(() => {
            // Enlever la classe 'loading' une fois les données chargées
            cards.forEach(card => {
                card.classList.remove('loading');
            });
        }, 2000);  // 2 secondes
    }

    // Écoute l'événement DOMContentLoaded pour lancer le chargement des données après le chargement complet du DOM
    document.addEventListener("DOMContentLoaded", loadData);
    </script>





<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>


<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<script>
const buttons = document.querySelectorAll('.btns');
buttons.forEach(button => {
button.addEventListener('click', () => {

buttons.forEach(btn => btn.classList.remove('active'));

button.classList.add('active');
});
});</script>
