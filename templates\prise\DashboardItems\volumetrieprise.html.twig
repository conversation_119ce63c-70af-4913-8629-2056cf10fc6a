<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif; /* Corrected font-family */
            transition: background-color 0.3s, color 0.3s; /* Smooth transition for dark mode */
        }
        .top-flop-cluster {
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            display: flex;
            width: 50%;
            position: relative;
        }
        .expand-icon {
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 0.1rem;
            color: #888;
            cursor: pointer;
        }
        .container {
            display: flex;
            align-items: center;
            margin-top: -17px;
        }
        .chart-container {
            width: 40%;
            height: 100px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .tables-container {
            width: 45%;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
          
        }
        .sales-table,
        .sales-table th,
        .sales-table td {
            border: none !important;
            background-color: transparent;
        }
        .sales-table th {
            color: #000;
            background-color: transparent;
            font-size: 0.8em;
            padding: 1px 11px;
            text-align: left;
        }
        .sales-table td {
            color: #72ac9d;
            padding: 1px 11px;
            font-size: 0.8em;
            text-align: left;
        }
        .sales-table thead {
            font-weight: bold;
            border-bottom: 2px solid transparent;
        }
        .sales-table tbody tr:nth-child(even) {
            background-color: transparent;
        }
        .sales-table .negative-sales {
            color: red;
        }
        .sales-table-bad {
            color: black;
            border-radius: 15px;
            border-collapse: collapse;
            margin-top: 10px;
            text-align: left;
            border: none;
        }
        .sales-table-bad td {
            padding: 1px 11px;
            font-size: 0.8em;
            color: red;
            text-align: left;
            border: none;
        }
        .text-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #000;
            z-index: 10;
        }
        #global-text {
            font-size: 1.1em;
            color: #333;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            margin: 0;
            line-height: 1.2;
        }
        #total-sales {
            font-size: 1.8em;
            font-weight: bold;
            color: #785EF0;
            margin: 0;
            line-height: 1.2;
        }
        .sales-info {
            font-size: 10px;
            color: #333;
            margin: 0;
            line-height: 1;
        }
        #sales-bad {
            font-size: 0.6em;
            color: red;
            margin: 0;
            font-weight: bold;
            line-height: 1.2;
        }
        .icons {
            display: flex;
            align-items: center;
            padding: -10px 0;
        }
        .icons i {
            margin: 0 -13px;
        }
        .cardes {
            border: 1px solid #ccc;
            border-radius: 15px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            width: 85px;
            height: 55px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            text-align: center;
            font-size: 23px;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .circule {
            border: 1px solid #ccc;
            border-radius: 50%;
            background-color: transparent;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            width: 50px;
            height: 50px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            border-color: #000;
            z-index: auto;
        }

        /* Dark Mode Styles */
           body.dark-mode .top-flop-cluster {
            border-color: #222; /* Dark border */
			background-color: #222;
        }
        body.dark-mode .sales-table th,
        body.dark-mode .sales-table td {
            color: #fff; /* Light text for table */
        }
        body.dark-mode .sales-table {
            background-color: #444; /* Dark background for tables */
        }
        body.dark-mode #total-sales {
            color: #785EF0; /* Light color for total sales */
        }
        body.dark-mode .cardes {
            background-color: #333; /* Dark background for cards */
            color: #fff; /* Light text color */
			border-color: transparent;
        }
        body.dark-mode .circule {
            border-color: #333; /* Light border for circle */
        }
		  body.dark-mode .sales-info {
			color: #fff;
		  }
		    </style>
</head>
<body>

<h5 style="padding: 5px;">Volumetrie</h5>

<i class="expand-icon" data-feather="maximize"></i>

<div class="container">
    <div class="chart-container">
        <canvas id="globalSalesChart"></canvas>
        <div style="margin-top: 5px;" class="circule">
            <div class="text-overlay">
                <div id="total-sales">146</div>
                <div class="sales-info">Prise</div>
            </div>
        </div>
    </div>
    <div class="tables-container">
        <div class="row">
            <!-- First Column -->
            <div class="col-5">
                <div class="cardes" style="color: #785EF0;">
                    <img src="{{ asset('image/Icon-Adsl.svg') }}" alt="Router Icon" width="40" height="40">
                    <div>14</div>
                </div>
                <div class="cardes" style="color: #785EF0;">
                    <img src="{{ asset('image/Icon-Mob.svg') }}" alt="Router Icon" width="40" height="40">
                    <div>11</div>
                </div>
                <div class="cardes" style="color: #785EF0; width: 175px;">
                    <div class="icons">
                        <img src="{{ asset('image/Icon-Mob-adsl.svg') }}" alt="Router Icon" width="40" height="40">
                    </div>
                    <div>6</div>
                </div>
            </div>

            <!-- Second Column -->
            <div class="col-4">
                <div class="cardes" style="color: #785EF0;">
                    <div class="icon">
                        <img src="{{ asset('image/Icon-Fibre.svg') }}" alt="Router Icon" width="40" height="40">
                    </div>
                    <div>14</div>
                </div>
                <div class="cardes" style="color: #785EF0;">
                    <div class="icons">
                        <img src="{{ asset('image/Icon-Mob-Fibre.svg') }}" alt="Router Icon" width="40" height="40">
                    </div>
                    <div>3</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>
<script>
    feather.replace(); // Initialize feather icons

    // Initial colors for light and dark mode
    const lightModeColors = ['#3b2089', '#4AC1E6'];
    const darkModeColors = ['#9b7eec', '#8bd8f7']; // Adjusted for dark mode

    const ctx = document.getElementById('globalSalesChart').getContext('2d');
    const globalSalesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [50, 50],
                backgroundColor: lightModeColors, // Default colors
                borderWidth: 0 
            }]
        },
        options: {
            cutout: '70%',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    enabled: false
                },
                hover: {
                    mode: null
                }
            }
        }
    });

    // Dark mode toggle functionality
    const darkModeToggle = document.getElementById('darkModeToggle');
    darkModeToggle.addEventListener('change', () => {
        document.body.classList.toggle('dark-mode');

        // Update chart colors based on the dark mode state
        if (document.body.classList.contains('dark-mode')) {
            globalSalesChart.data.datasets[0].backgroundColor = darkModeColors;
        } else {
            globalSalesChart.data.datasets[0].backgroundColor = lightModeColors;
        }
        globalSalesChart.update(); // Refresh the chart
    });
</script>

</body>
</html>
