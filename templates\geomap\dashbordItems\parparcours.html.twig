
<style>

.parparcours-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.parparcours-header {
    font-size: 0.8em;
    margin: 0;
}

.parparcours-chart-and-legend {
    display: flex;
    align-items: center;
    margin-top: 5%;
    gap: 20px;
}

.parparcours-chart-container {
    height: 112px;
    width: 112px;
}

.parparcours-legend {
    list-style: none;
    padding: 0;
    margin: 0;
}

.parparcours-legend li {
    display: flex;
    align-items: center;
    font-size: 0.8em;
     color: var( --4text-colors);
    margin-bottom: 5px;
}

.parparcours-legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 30%;
}
</style>
<div class="bonustechno-container">
    <h5 class="text-header4s">Par parcours</h5>
    <div class="ChartsByType bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="parparcoursPieChart" width="400" height="400"></canvas> <!-- Added width and height -->
        </div>
        <ul class="bonustechno-legend" id="legendContainer">
            <!-- Custom legend will be inserted here -->
        </ul>
    </div>
</div>


<script>
let parparcoursPieChart = null;

function ParParcourrChart(data) {
    function updateChart() {
        var chartData = data.Categorie;
        var canvasElement = document.getElementById('parparcoursPieChart');
        var bonustechnoCtx = canvasElement.getContext('2d');

        // Détruire l'ancien graphique s'il existe
        if (parparcoursPieChart) {
            parparcoursPieChart.destroy();
        }

        // Définition des gradients
        var bonustechnoGradientA = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
        bonustechnoGradientA.addColorStop(0, '#9a4eca');
        bonustechnoGradientA.addColorStop(1, '#b673db');

        var bonustechnoGradientB = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
        bonustechnoGradientB.addColorStop(0, '#145277');
        bonustechnoGradientB.addColorStop(1, '#3a7d9c');

        var bonustechnoGradientC = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
        bonustechnoGradientC.addColorStop(0, '#62eade');
        bonustechnoGradientC.addColorStop(1, '#8ff4eb');

        var labels = [];
        var dataValues = [];
        var backgroundColors = []; 
        var legendHtml = '';

        chartData.forEach(function(item) {
            labels.push(item.categoryName);
            dataValues.push(item.totalVentes);

            let color;
            if (item.categoryName === 'CONQUETE') {
                color = bonustechnoGradientA;
            } else if (item.categoryName === 'MIGRATION') {
                color = bonustechnoGradientB;
            } else if (item.categoryName === 'MOBILES') {
                color = bonustechnoGradientC;
            } else {
                color = '#888';
            }
            backgroundColors.push(color);

            // Pour afficher la couleur en CSS, on doit utiliser une couleur statique
            let colorStatic = item.categoryName === 'CONQUETE' ? '#9a4eca' :
                              item.categoryName === 'MIGRATION' ? '#145277' :
                              item.categoryName === 'MOBILES' ? '#62eade' : '#888';

            legendHtml += `
                <li style="list-style: none;">
                    <span class="bonustechno-legend-color" style="display:inline-block; width:15px; height:15px; background-color:${colorStatic};"></span>
                    ${item.categoryName}
                </li>
            `;
        });

        document.getElementById('legendContainer').innerHTML = legendHtml;

        // Création du graphique
        parparcoursPieChart = new Chart(bonustechnoCtx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: dataValues,
                    backgroundColor: backgroundColors,
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '80%'
            }
        });
    }
    updateChart();
}

</script>
