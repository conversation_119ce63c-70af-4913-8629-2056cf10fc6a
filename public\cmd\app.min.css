@import "http://fonts.googleapis.com/css?family=Source+Code+Pro:200,700";* {
    font-family: Source Code Pro
}

.command-line {
    font-size: 14px;
    width: 100%;
    height: 400px;
    overflow-x: hidden;
    overflow-y: scroll
}

.command-line.light {
    background-color: #fff
}

.command-line.light .command-row {
    position: relative;
    margin-bottom: 5px
}

.command-line.light .command-row.active {
    background: #f5f5f5
}



.command-line .command-row {
    position: relative;
    margin-bottom: 5px
}


.command-line .command-row .command-time,.command-line .command-row .command-user {
    color: #e7e7e7;
    display: inline-block;
    padding-right: 5px
}

.command-line .command-row .command-user {
    font-weight: 700
}

.command-line .command-row .command-entry {
    padding-right: 5px;
    color: #fff;
    display: inline;
    overflow-wrap: break-word;
    word-wrap: break-word;
    -ms-word-break: break-all;
    word-break: break-all;
    -ms-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto
}

.command-line .command-row .command-entry.command-entry-protected:empty {
    display: none
}

.command-line .command-row .command-entry.block {
    display: block
}

.command-line .command-row .command-entry:focus {
    outline: none
}

.command-line .command-row .secret {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    opacity: 0
}

.command-line .command-row.error .command-entry {
    font-weight: 700;
    color: red
}

.command-line .command-row.success .command-entry {
    font-weight: 700;
    color: #00c300
}

.command-line .command-row.info .command-entry {
    font-weight: 700;
    color: #00a9ff
}

.command-line .command-row.warning .command-entry {
    font-weight: 700;
    color: orange
}

.mac-window {
    border-radius: 5px;
    margin: 40px auto;
    overflow: hidden;
    width: 600px;
    box-shadow: 0 10px 60px rgba(0,0,0,.2)
}

.mac-window.minimize {
    top: 125%;
    -webkit-transform: translateY(-50%) translateX(-50%) scale(1);
    transform: translateY(-50%) translateX(-50%) scale(1);
    opacity: 1;
    -webkit-transition: all .5s;
    transition: all .5s
}

.mac-window.minimize:hover {
    top: 120%;
    -webkit-transition: all .5s;
    transition: all .5s
}

.mac-window.maximize {
    height: 100%;
    max-height: 100%;
    width: 100%;
    max-width: 100%;
    -webkit-transform: translateY(-50%) translateX(-50%) scale(1);
    transform: translateY(-50%) translateX(-50%) scale(1)
}

.mac-window .title-bar {
    background: #d0cfd0;
    background: -webkit-gradient(linear,left bottom,left top,from(#c8c5c8),to(#eae7ea));
    background: -webkit-linear-gradient(bottom,#c8c5c8,#eae7ea);
    background: linear-gradient(0deg,#c8c5c8,#eae7ea);
    height: 20px;
    border-bottom: 1px solid #b4b4b4;
    width: 100%;
    clear: both
}

.mac-window .title-bar .buttons {
    height: 100%;
    width: 51px;
    float: left;
    margin-left: 9px
}

.mac-window .title-bar .buttons .close,.mac-window .title-bar .buttons .maximize,.mac-window .title-bar .buttons .minimize {
    float: left;
    height: 10px;
    width: 10px;
    border-radius: 50%;
    margin-top: 5px;
    background: #fb4948;
    border: 1px solid rgba(214,46,48,.15);
    position: relative
}

.mac-window .title-bar .buttons .close:before,.mac-window .title-bar .buttons .maximize:before,.mac-window .title-bar .buttons .minimize:before {
    content: "";
    position: absolute;
    height: 1px;
    width: 8px;
    background: #360000;
    top: 50%;
    left: 50%;
    -webkit-transform: translateY(-50%) translateX(-50%) rotate(45deg);
    transform: translateY(-50%) translateX(-50%) rotate(45deg);
    opacity: 0
}

.mac-window .title-bar .buttons .close:after,.mac-window .title-bar .buttons .maximize:after,.mac-window .title-bar .buttons .minimize:after {
    content: "";
    position: absolute;
    height: 1px;
    width: 8px;
    background: #360000;
    top: 50%;
    left: 50%;
    -webkit-transform: translateY(-50%) translateX(-50%) rotate(-45deg);
    transform: translateY(-50%) translateX(-50%) rotate(-45deg);
    opacity: 0
}

.mac-window .title-bar .buttons .minimize {
    background: #fdb225;
    margin-left: 8.5px;
    border-color: rgba(213,142,27,.15);
    position: relative
}

.mac-window .title-bar .buttons .minimize:before {
    content: "";
    position: absolute;
    height: 1px;
    width: 8px;
    background: #864502;
    top: 50%;
    left: 50%;
    -webkit-transform: translateY(-50%) translateX(-50%);
    transform: translateY(-50%) translateX(-50%)
}

.mac-window .title-bar .buttons .minimize:after {
    display: none
}

.mac-window .title-bar .buttons .maximize {
    float: right;
    background: #2ac833;
    border-color: rgba(30,159,32,.15)
}

.mac-window .title-bar .buttons .maximize:before {
    width: 6px;
    height: 6px;
    background: #0b5401;
    -webkit-transform: translateY(-50%) translateX(-50%);
    transform: translateY(-50%) translateX(-50%);
    border: 1px solid #2ac833;
    border-radius: 2px
}

.mac-window .title-bar .buttons .maximize:after {
    width: 10px;
    height: 2px;
    background: #2ac833;
    -webkit-transform: translateY(-50%) translateX(-50%) rotate(45deg);
    transform: translateY(-50%) translateX(-50%) rotate(45deg)
}

.mac-window .title-bar .buttons:hover .close:after,.mac-window .title-bar .buttons:hover .close:before,.mac-window .title-bar .buttons:hover .maximize:after,.mac-window .title-bar .buttons:hover .maximize:before,.mac-window .title-bar .buttons:hover .minimize:after,.mac-window .title-bar .buttons:hover .minimize:before {
    opacity: 1
}

.mac-window .title-bar .title {
    height: 100%;
    text-align: center;
    margin-right: 60px;
    font-family: Helvetica Neue,helvetica,arial,sans-serif;
    line-height: 21px;
    font-size: 13px;
    color: #222022
}

.mac-window .window {
    background: #fff;
    max-height: 90vh;
    height: 100%
}
