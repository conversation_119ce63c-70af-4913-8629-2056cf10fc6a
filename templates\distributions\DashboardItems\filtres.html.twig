<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

<style>


	.floating-title {
		position: absolute;
		font-size: 1.5em;
		color: #333;
		margin-left: 8px;
		pointer-events: none;
		z-index: 1000;
		font-weight: bold;
		font-family: 'Ubuntu';
		margin-bottom: 20px;
	}


	.dts-text-top,
	.dts-text-bottom {
		position: absolute;
		font-size: 12.15px;
		font-weight: bold;
		text-align: center;
		width: 100%;
	}


	body.dark-mode #total-prise {
		color: #f0f4f3;
	}

	body.dark-mode .circle::before {
		background-color: #222;
	}

	body.dark-mode .test {
		color: #333;
	}

	.form-container {
		background: linear-gradient(to right, #6e67ff, #83B8FF);
		border-radius: 0 0 15px 15px;
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
		display: flex;
		height: inherit;


	}
	.form-container::before {
		content: "";
		position: absolute;
		margin-top: 55px;
		left: 200px;
		width: 140px;
		height: 40px;
		background-image: url('{{ asset('image/motif.png') }}'); /* Replace with the path to your image */

	}

	.checkbox-col {
		display: flex;
		flex-direction: column; /* Les cases à cocher s'alignent en colonne */
		margin-left: 30px; /* Ajoute un espace entre les colonnes */
		margin-top: 12px;


	}

	.checkbox-group {
		display: flex;
		align-items: center; /* Aligne le texte et la case à cocher verticalement */
		/* Ajoute un espace entre les groupes de cases à cocher */
		line-height: 0.5;
	}


	.form-group {
		margin: 6px;
		line-height: 0.9;
	}

	.form-group label {
		display: block;
		color: #222;
		font-weight: bold;
		margin-bottom: 7px;
		line-height: 0.9;
	}


	.form-group input,
	.form-group select {
		width: 100%;
		padding: 8px;
		border-radius: 10px;
		border: none;
		outline: none;
		font-size: 14px;
	}

	.checkbox-group {
		display: flex;
		line-height: 1.3;
	}

	.checkbox-group label {
		color: #fff;
		display: flex;
		align-items: center;
		font-size: 14px;
		font-family: 'Ubuntu';

	}

	.checkbox-group input {
		margin-right: 5px;
		size: 20px;
		box-sizing: border-box;
		width: 15px; /* Set the width */
		height: 15px;
		border-radius: 15px;
	}

	.form-row {
		display: flex;
		justify-content: space-between; /* optional: adjust spacing */


	}

	.form-group {
		flex: 1; /* optional: makes both groups equal width */
	}
	.valueColeur {
		color: #785EF0;
	}
	body.dark-mode .clusters-info {
		border-color: #222; /* Dark border */
		background-color: #222;
	}
	.dark-mode .floating-title {
		color: #fff;
	}
	.dark-mode .form-group label {
		color: #fff;
	}
	.dark-mode .valueColeur {
		background-color: #000;
	}
	body.dark-mode .floating-titles {
		color: #fff;
		background-image: linear-gradient(to top, transparent,#4d254d); /* Texture fumée et dégradé vers le haut */
		/* Fusionner les deux arrière-plans */

	}
</style>

<div class="floating-titles filtarge">
	<h6 style="padding: 8px;">Filtres et Sélections</h6>
</div>

<br><br>
<div style="padding-left: 10px; padding-right: 10px;">
	<div class="form-row">
		<div class="form-group">
			<label for="cluster">Cluster</label>
			<select class="valueColeur" id="cluster">
				<option value="lyon-ouest">LYON OUEST</option>
				<!-- Add more options as needed -->
			</select>
		</div>
		<div class="form-group">
			<label for="ville">Ville</label>
			<select class="valueColeur" id="ville">
				<option value="lyon">LYON</option>
				<!-- Add more options as needed -->
			</select>
		</div>
	</div>

	<div class="form-row">
		<div class="form-group">
			<label for="rue">Rue</label>
			<select class="valueColeur" id="rue" style="width: 260px;">
				<option value="RUE DU GÉNÉRAL DE GAULLE">LYON</option>
				<!-- Add more options as needed -->
			</select>
		</div>
		<div class="form-group">
			<label for="num-rue">N° Rue</label>
			<input class="valueColeur" style="width: 60px; background-color: #fff;" type="number" id="num-rue" value="253">
		</div>
	</div>

	<div class="form-row">
		<div class="form-group">
			<label for="code-iris">Code Iris</label>
			<select class="valueColeur" id="code-iris">
				<option value="79003">79003</option>
				<!-- Add more options as needed -->
			</select>
		</div>
		<div class="form-group">
			<label for="ref-pm">Réf. PM</label>
			<select class="valueColeur" id="ref-pm">
				<option value="145188997">145188997</option>
				<!-- Add more options as needed -->
			</select>
		</div>
	</div>
</div>
</br>
<div class="form-container">
	<div class="checkbox-col">
		<div class="checkbox-group">
			<label><input type="checkbox" aria-label="THD">
				THD</label>
		</div>
		<div class="checkbox-group">
			<label><input type="checkbox" aria-label="MOBILE">
				MOBILE</label>
		</div>
		<div class="checkbox-group">
			<label><input type="checkbox" aria-label="ADSL">
				ADSL</label>
		</div>
	</div>
	<div class="checkbox-col" style="margin-left: 100px;">
		<div class="checkbox-group">
			<label><input type="checkbox" aria-label="ADSL + MOB">
				ADSL + MOB</label>
		</div>
		<div class="checkbox-group">
			<label><input type="checkbox" aria-label="THD + MOB">
				THD + MOB</label>
		</div>
	</div>
</div>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
	const darkModeToggle = document.getElementById('chk');
darkModeToggle.addEventListener('change', () => {
document.body.classList.toggle('dark-mode');

// Update chart colors based on the dark mode state
if (document.body.classList.contains('dark-mode')) {
globalSalesChart.data.datasets[0].backgroundColor = darkModeColors;
} else {
globalSalesChart.data.datasets[0].backgroundColor = lightModeColors;
}
globalSalesChart.update(); // Refresh the chart
});
</script>
