{% extends 'base.html.twig' %}

{% block body %}

<html lang="en" style="    scrollbar-width: none;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ventes Liste</title>
    <style>
        .container-fluid {
            scrollbar-width: thin;
        }
    </style>
</head>
	<style>


	
	</style>
	<body data-theme="light">
		<div class="container-fluid d-flex" style="margin: 0; padding: 0; min-height: 100vh;">
			<div class="sidebar">
				{% include 'livedash/LeftSideBar/content.html.twig' %}
			</div>
			<div id="leftSidebar" class="left-block col-auto col-md-3 col-xl-2 px-sm-2 px-0">
				<div class="content">
					{% include 'livedash/LeftSideBar2/content.html.twig' %}
				</div>
				<div class="bottombar footer">
					{% include 'livedash/bottombar/content.html.twig' %}
				</div>
			</div>
			<div class="main-content col">
				<div class="right-block col-24">
					<div class="topbar topbar-right-blcok">
						{% include 'livedash/Navbar/content.html.twig' %}
					</div>
					<div class="chat-areablock" id="right-sidebar">
						<div class="table-area">
							{% include 'neuves/MainBloc/content.html.twig' %}
						</div>
                     
					
					</div>
				</div>
			</div>
		</div>
	</body>
</html>
	<script>
		const checkbox = document.getElementById('chk');

function applyTheme(theme) {
if (theme === 'dark') {
document.body.setAttribute('data-theme', 'dark');
localStorage.setItem('theme', 'dark');
} else {
document.body.setAttribute('data-theme', 'light');
localStorage.setItem('theme', 'light');
}
}

checkbox.addEventListener('change', () => {
applyTheme(checkbox.checked ? 'dark' : 'light');
});

const currentTheme = localStorage.getItem('theme') || 'light';
checkbox.checked = currentTheme === 'dark';
applyTheme(currentTheme);
	</script>


{% endblock %}
