	

    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.Default.css" />
    		<style>:root
		{
			/* Couleurs par défaut (mode clair) */
			--bg-color: #fff;
			--text-color: #000;
			--header-bg: #fff;
			--header-text: #333;
			--card-bg: #fff;
			--card-text: #444;
			--accent-color: #539AF8;
			--border-color: #ddd;
			--scrollbar-thumb: #bbb;
			--scrollbar-track: #eee;
			--chiffre-color: #000;
			--header-span-color: #ddd;
			--pourcent-color: #555;
		}

		[data-theme="dark"] {
			/* Couleurs pour le mode sombre */
			--bg-color: #111;
			--text-color: #fff;
			--header-bg: #222;
			--header-text: #b0b0b0;
			--card-bg: #111;
			--card-text: #b0b0b0;
			--accent-color: #539AF8;
			--border-color: #444;
			--scrollbar-thumb: #2B2D31;
			--scrollbar-track: #555;
			--chiffre-color: #fff;
			--header-span-color: rgba(255, 255, 255, 0.6);
			--pourcent-color: #b0b0b0;
		}

		/* Application des couleurs dans vos styles */
		body {
			margin: 0;
			font-family: 'Roboto', sans-serif;
			background-color: var(--bg-color);
			color: var(--text-color);
		}

		.dashboard {
			display: flex;
			flex-direction: column;
			gap: 10px;

			overflow-y: auto; /* Enable scrolling when content overflows */
			max-height: 95vh; /* Limit the height, adjust as needed */
			scrollbar-width: thin;
			scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
			font-family: 'Roboto', sans-serif;
		}

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			color: var(--header-text);
		}

		.header h1 {
			font-size: 1.5rem;
			color: var(--text-color);
		}

		.header button {
			padding: 5px 15px;
			border: 2px solid var(--border-color);
			color: var(--accent-color);
			background-color: var(--header-bg);
			border-radius: 5px;
			cursor: pointer;
			font-size: 0.8em;
			z-index: 100;
		}

		.map-section {
			background-color: var(--card-bg);
			padding: 10px -20px !important;
		}

		#map {
			width: 100%;
			height: 530px;
		}

		.stats-section {
			display: flex;
			flex-wrap: wrap; /* This allows the cards to wrap to the next line when necessary */
			gap: 14px;
			padding-left: 32px;
			justify-content: flex-start; /* Align cards to the left */
		}

		.stats-card {
			background-color: var(--card-bg);
			padding: 20px;
			border-radius: 8px;
			color: var(--card-text);
			width: 306px; /* Ensure a fixed width */
			border: 1px solid var(--border-color);
			height: 402px;
			overflow: hidden;
			margin-bottom: 20px; /* Adds space between rows */
		}

		.stats-cards {
			position: absolute;
			top: -263px;

			z-index: 2000; /* Make sure it is above the map */
			background-color: var(--card-bg);
			padding: 15px;
			border-radius: 8px;
			width: 626px;
			height: 262px;

		}


		.stats-cards h2,
		.stats-card h2 {
			font-size: 1.2rem;
			color: var(--card-text);

		}

		.chart-container {
			margin-top: 15px;
		}

		.table-section {
			margin-top: 20px;
		}

		.table-section table {
			width: 100%;
			border-collapse: collapse;
		}

		.table-section td {
			padding: 8px;
			border-bottom: 1px solid var(--border-color);
		}

		.table-section td:first-child {
			font-weight: bold;
		}

		.rows {
			display: flex;
			justify-content: space-between; /* Ou center, space-around, etc. selon vos besoins */
			align-items: center; /* Pour centrer verticalement les éléments */
		}

		.p {
			font-size: 10px;
		}

		.source-item {
			display: flex;
			justify-content: space-between;
			padding: 3px 0;
			border-bottom: 1px solid var(--border-color);
		}

		.progress-bar-custom {
			width: 270px;
			height: 3px;
			background-color: var(--border-color);
		}

		.progress-bar-fill {
			height: 100%;
			background-color: var(--accent-color);
		}


		.u {
			width: 50%;
			margin: 20px auto;
			border: 1px solid #000;

		}

		.containers {
			position: relative; /* This makes the container the reference point for absolute positioning */
			padding-left: 32px;
		}
		.chiffre {
			flex: 1;
			color: var(--chiffre-color);
		}
		.header .span {
			color: var(--header-span-color)
		}
		.pourcent {
			color: var(--pourcent-color);
		}
		.textp {
			color: var(--chiffre-color);
			font-size: 24px;
		}
		@media screen(min-width: 1400px) {}
	</style>
	<link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">

	<style>:root
	{
		--card-width: 200px;
		--card-height: 135px;
		--card-gap-horizontal: 12.2px;
		--card-gap-vertical: 8px;
		--background-color-select: #ecf4ef;
		--background-color-cards: #e7f3fa;
		--text-selects-colors: #335d67;
		--text-inside-car-colors: #2c455c;
		--filled-progress-bar-color: #bfc7cc;
		--background-color-card-none: #e3e5e9;
		--buttonvalide-background-color: #e7f5e5;
		--buttonvalide-text-color: green;
		--buttonvalide-border-type: none;
		--btn-border-color: transparent;
		--small-ko-button-colorbg: #fef7fe;
		--scrollbar-track: #eee;
		--rightnav-bg: #ececf1;
		--active-bg: white;
		--active-color: #333;
		--skeleton-bg: #e0e0e0
	}

	[data-theme="dark"] {
		--card-width: 200px;
		--card-height: 135px;
		--card-gap-horizontal: 12.2px;
		--card-gap-vertical: 12px;
		--background-color-select: #1e1f21;
		--background-color-cards: #1e1f21;
		--text-selects-colors: #7d7e81;
		--text-inside-car-colors: #888888;
		--filled-progress-bar-color: #3f3e40;
		--background-color-card-none: #272a31;
		--buttonvalide-background-color: transparent;
		--buttonvalide-text-color: #6a6a6e;
		--buttonvalide-border-type: 1px;
		--btn-border-color: #888888;
		--small-ko-button-colorbg: #242c2d;
		--scrollbar-thumb: #2B2D31;
		--rightnav-bg: #000;
		--active-bg: #333;
		--active-color: white;
		--skeleton-bg: #1e1f21;

	}

	#rsuro .navbar {
		background-color: transparent;
 
	}

	#rsuro .navbar .form-select {
		height: 90%;
		margin-right: 9px;
		font-size: 90%;
	}

	#rsuro .navbar .form-select.date-select {
		background-color: var(--background-color-select);
		border: none;
		width: 126px;
		color: var(--text-selects-colors);
		font-size: 90%;
		border-radius: 9px;
	}

	#rsuro .navbar .form-select.week-select {
		width: 153px;
		background-color: var(--background-color-select);
		border: none;
		color: var(--text-selects-colors);
		font-size: 90%;
		border-radius: 9px;
	}

	#rsuro .navbar .form-select.manager-select {
		width: 160px;
		background-color: var(--background-color-select);
		border: none;
		color: var(--text-selects-colors);
		font-size: 90%;
		border-radius: 9px;
	}

	#rsuro .navbar .btn {
		width: 90px;
		height: 75%;
	}

	#rsuro .btnicon {
		color: var(--text-selects-colors);
		background-color: var(--background-color-select);
		border: none;
		height: 90%;
		margin-right: 9px;
		border-radius: 9px;
	}

	#rsuro .btn {
		color: var(--buttonvalide-text-color);
		background-color: var(--buttonvalide-background-color);
		border: var(--buttonvalide-border-type) solid var(--btn-border-color);
		border-radius: 9px;
	}

	#rsuro .btncqt {
		color: #ffff;
		background-color: #33b4e8;
		border: none;
		height: 35px;
		width: 90px;
		border-radius: 10.8px;
	}

	#rsuro .btnmig {
		color: #3d7fa8;
		background-color: transparent;
		border: 1px solid #3d7fa8;
		height: 35px;
		width: 90px;
		border-radius: 10.8px;
	}


	#rsuro .select-wrapper {
		display: flex;
		align-items: center;
		background-color: var(--background-color-cards);
		border-radius: 9px;
		padding: 0 2px;
		margin-right: 9px;
	}


	.btns {
		color: #999;
		font-size: 14px;
		font-weight: 500;
		background-color: transparent; /* Button background */
		border: none;
		padding: 2px 15px;
		border-radius: 5px;
		display: flex;
		align-items: center;
		gap: 5px; /* Space between icon and text */
		transition: background-color 0.2s;
	}
	.active {
		background-color: var(--active-bg);
		color: var(--active-color);

	}
	.btns:hover {
		background-color: var(--active-bg); /* Hover state */
	}

	.bi {
		font-size: 16px; /* Icon size */
	}
	@media screen and(min-width: 1920px) and(min-height: 1080px){:root {
		--card-width: 320px;
		--card-height: 180px;
		--card-gap-horizontal: 15.2px;
		--card-gap-vertical: 12px;
		--background-color-select: #1e1f21;
		--background-color-cards: #1e1f21;
		--text-selects-colors: #7d7e81;
		--text-inside-car-colors: #888888;
		--filled-progress-bar-color: #3f3e40;
		--background-color-card-none: #272a31;
		--buttonvalide-background-color: transparent;
		--buttonvalide-text-color: #6a6a6e;
		--buttonvalide-border-type: 1px;

		--btn-border-color: #888888;
		--small-ko-button-colorbg: #242c2d;
	}

	[data-theme="dark"] {
		--card-width: 320px;
		--card-height: 180px;
		--card-gap-horizontal: 10.2px;
		--card-gap-vertical: 12px;
		--background-color-select: #1e1f21;
		--background-color-cards: #1e1f21;
		--text-selects-colors: #7d7e81;
		--text-inside-car-colors: #888888;
		--filled-progress-bar-color: #3f3e40;
		--background-color-card-none: #272a31;
		--buttonvalide-background-color: transparent;
		--buttonvalide-text-color: #6a6a6e;
		--buttonvalidemig-text-color: #40a9dd;
		--buttonvalidemig-text-color: #40a9dd;
		--buttonvalide-border-type: 1px;
		--btn-border-color: #888888;
		--btnmig-border-color: #40a9dd;
		--small-ko-button-colorbg: #242c2d;

	}

	#rsuro .navbar {
		background-color: transparent;
		
	}

	#rsuro .navbar .form-select {
		height: 108%;
		margin-right: 10.8px;
		font-size: 108%;
	}

	#rsuro .navbar .form-select.date-select {
		background-color: var(--background-color-select);
		border: none;
		width: 151.2px;
		color: var(--text-selects-colors);
		font-size: 108%;
		border-radius: 10.8px;
	}

	#rsuro .navbar .form-select.week-select {
		width: 183.6px;
		background-color: var(--background-color-select);
		border: none;
		color: var(--text-selects-colors);
		font-size: 108%;
		border-radius: 10.8px;
	}

	#rsuro .navbar .form-select.manager-select {
		width: 192px;
		background-color: var(--background-color-select);
		border: none;
		color: var(--text-selects-colors);
		font-size: 108%;
		border-radius: 10.8px;
	}

	#rsuro .navbar .btn {
		width: 108px;
		height: 97.2%;
	}


}
  /* Navbar Styles */
        #smallprisesnav .navbar {
            display: flex;
           
            justify-content: flex-start;
            align-items: center;
           
        }

        #smallprisesnav .nav-button {
            display: flex;
            align-items: center;
            gap: 7px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 1rem;
            font-weight: bold;
            color: var(--bigtext-color);
            background-color: transparent;
            border: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        #smallprisesnav .nav-button.active {
            background-color: var(--navbackground-color);
            color: var(--bigtext-color);
        }

        #smallprisesnav .nav-button .icon {
            width: 24px;
            height: 24px;
        }
</style>


    <main>

		<div id="rsuro">
			<nav class="navbar">
				<div class="container">
					<div class="d-flex align-items-center">
						<button class="btnicon btn-primary ms-auto">
							<i class="bi bi-geo-alt-fill" style="font-size: 1.3rem;"></i>
						</button>
						<div class="select-wrapper">
							<i class="bi bi-calendar-week" style="font-size: 1.2rem;"></i>

							<!-- Start Date Picker -->
							<input type="text" class="form-select date-select start-date" id="startDate" name="startDate" value="01-10-2024">

							<span class="date-separator">-</span>

							<!-- End Date Picker -->
							<input type="text" class="form-select date-select end-date" id="endDate" name="endDate" value="31-10-2024">
						</div>
				
		<select class="form-select manager-select me-2" id="clusterSelect">
    {% for cluster in clusters %}
        <option value="{{ cluster.code_cluster }}" {% if cluster.code_cluster == selectedClusterId %} selected {% endif %}>
            {{ cluster.libelle_cluster }}
        </option>
    {% else %}
        <p>Aucun cluster disponible.</p>
    {% endfor %}
</select>

<script>
    document.getElementById('clusterSelect').addEventListener('change', function () {
        const selectedClusterId = this.value;
        const url = new URL(window.location.href);
        url.searchParams.set('codeCluster', selectedClusterId);
        window.location.href = url;
    });
</script>

<select class="form-select week-select me-2" id="inseeSelect">
    {% if insee is not empty and insee.codes_insee is not empty %}
        {% for inse in insee.codes_insee %}
            <option value="{{ inse.code_insee }}" 
                {% if inse.code_insee == selectedInseeId or loop.first %} selected {% endif %}>
                {{ inse.ville }}
            </option>
        {% endfor %}
    {% else %}
        <option disabled>Aucune ville disponible.</option>
    {% endif %}
</select>


<script>
    document.getElementById('inseeSelect').addEventListener('change', function () {
        const selectedInseeId = this.value;
        const url = new URL(window.location.href);
        url.searchParams.set('codeInsee', selectedInseeId);
        window.location.href = url;
    });
</script>


						<!-- Form submission button -->
						<form action="" method="get" id="dateForm">
							<input type="hidden" id="hiddenStartDate" name="startDate" value="01-10-2024">
							<input type="hidden" id="hiddenEndDate" name="endDate" value="31-10-2024">
							<input type="hidden" id="hiddenClusterId" name="clusterId">
							<button class="btn btn-primary ms-auto" type="submit">Valider</button>
						</form>
					</div>
				   <div id="smallprisesnav">
        <div class="navbar">
            <button class="nav-button active">
                <img src="{{ asset('image/icon/adsl-gris.svg') }}" alt="Router Icon" class="icon" />
                <span>12 526</span>
            </button>
            <button class="nav-button">
                <img src="{{ asset('image/icon/Fibre-gris.svg') }}" alt="Plug Icon" class="icon" />
                <span>9 562</span>
            </button>
            <button class="nav-button">
                <img src="{{ asset('image/icon/Mobile-gris.svg') }}" alt="Mobile Icon" class="icon" />
                <span>5 662</span>
            </button>
            <button class="nav-button">
                <img src="{{ asset('image/icon/mobile-fibre-gris.svg') }}" alt="Plug Icon" class="icon" />
                <span>3 222</span>
            </button>
            <button class="nav-button active">
                <img src="{{ asset('image/icon/mobile-adsl-gris.svg') }}" alt="Plug Icon" class="icon" />
                <span>9 056</span>
            </button>
        </div>
    </div>


				</div>
			</nav>


		</div>

<style>
  /* Skeleton Loader Styles */
  :root {
    --loader-bg-light: #e0e0e0;
    --loader-bg-dark: #555;
    --skeleton-bg-light: #e0e0e0;
    --skeleton-bg-dark: #555;
    --skeleton-highlight-light: #c0c0c0;
    --skeleton-highlight-dark: #666;
  }

  [data-theme="dark"] {
    --skeleton-bg: #55555;
    --skeleton-highlight: #666666;
  }

  .skeleton-loader {
    position: absolute;
    margin-top: 105px;
    width: 100%;
    height: 540px;
    background-color: var(--skeleton-bg-light);
    animation: pulse 1.5s infinite;
    top: 0;
    left: 0;
    z-index: 999;
  }

  [data-theme="dark"] .skeleton-loader {
    background-color: var(--skeleton-bg);
  }

  @keyframes pulse {
    0% {
      background-color: var(--skeleton-bg-light);
    }
    50% {
      background-color: var(--skeleton-highlight-light);
    }
    100% {
      background-color: var(--skeleton-bg-light);
    }
  }
</style>
<style>
/* Skeleton Loader Styles */
.card-placeholder {
    display: flex;
    flex-direction: column;
    background-color: #f0f0f0;
    border-radius: 8px;
    
    margin-bottom: 20px;
	height: 230px;
}

.header-placeholder .text-placeholder {
    background: #e0e0e0;
    height: 20px;
    width: 70%;
    margin-bottom: 10px;
    border-radius: 4px;
}

.content-placeholder .chiffre-placeholder {
    background: #e0e0e0;
    height: 30px;
    width: 50%;
    margin-bottom: 10px;
    border-radius: 4px;
}

.chart-placeholder {
    background: #e0e0e0;
    height: 100px;
    margin-top: 18px;
    border-radius: 8px;
}

/* Actual Content Styles */
.card-content {
    display: none; /* Hide content initially, it will be shown once data is loaded */
}

/* Skeleton Animation */
@keyframes shimmer {
    0% {
        background-position: -100%;
    }
    100% {
        background-position: 100%;
    }
}

.card-placeholder .text-placeholder,
.card-placeholder .chiffre-placeholder,
.card-placeholder .chart-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

</style>

<section class="map-section">
        <div id="map"></div>
        <div class="skeleton-loader" id="skeletonLoader"></div>
      {% include 'neuves/MainBloc/popape.html.twig' %}



<script>
    document.addEventListener("DOMContentLoaded", function () {
        const popup = document.getElementById("popupContent");
        const openPopupButton = document.getElementById("openPopup");
        const closePopupButton = document.getElementById("closePopup");

        // Ouvrir le pop-up
        openPopupButton.addEventListener("click", function () {
            popup.classList.add("open");
        });

        // Fermer le pop-up
        closePopupButton.addEventListener("click", function () {
            popup.classList.remove("open");
        });
    });
</script>


        <div style="position: inherit;">
           <div class="containers">
    <div class="stats-cards">
        <div class="card-placeholder">
            <div class="header-placeholder">
                <div class="text-placeholder"></div>
                <div class="text-placeholder"></div>
            </div>
            <div class="content-placeholder">
                <div class="chiffre-placeholder"></div>
                <div class="chiffre-placeholder"></div>
            </div>
            <div class="chart-placeholder"></div>
        </div>
        
        <!-- Actual Content (to be displayed after loading) -->
        <div class="card-content">
            <div style="color: #b0b0b0; font-family: Arial, sans-serif;">
                <div style="display: flex; justify-content: space-between; font-size: 12px; text-transform: uppercase;">
                    <div style="max-width: 310px; text-decoration: underline; font-size: 14px;">
                        Utilisateurs actifs au cours des 30 dernières minutes
                    </div>
                    <div style="max-width: 310px; text-decoration: underline; font-size: 14px;">
                        Utilisateurs actifs au cours des 30 dernières minutes
                    </div>
                </div>
                <div style="display: flex; font-size: 30px; line-height: 1;">
                    <div class="chiffre">360
                        <div style="color: #b0b0b0; font-size: 12px; text-transform: uppercase; padding-right: 20px;">
                            Utilisateurs actifs par minute
                        </div>
                    </div>
                    <div class="chiffre">54</div>
                </div>
            </div>
            
            <div>
                <div style="width: 580px; margin-top: 18px" class="chart-container">
                    <canvas style="width: 580px; height: 130px;" id="users30MinutesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

        </div>
</section>

<script>
  // Hide the skeleton loader after 2 seconds
  setTimeout(function() {
    document.getElementById('skeletonLoader').style.display = 'none';
  }, 2000);
</script>
<script>
// Simulate loading for 2 seconds before displaying content
setTimeout(function() {
    // Hide the skeleton loader
    document.querySelector('.card-placeholder').style.display = 'none';
    
    // Show the actual content
    document.querySelector('.card-content').style.display = 'block';
}, 2000);  // 2000ms = 2 seconds

</script>

	</main>

  <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.markercluster/dist/leaflet.markercluster.js"></script>
 <script>
 // Assuming you are using a map library like Google Maps or Leaflet
window.onload = function() {
  setTimeout(function() {
    // Remove the skeleton loader
    document.getElementById("map").classList.remove("skeleton-map");
    
    // Initialize the map (you can replace this with your map initialization code)
    initializeMap();
  }, 3000); // Stop the skeleton loader after 2 seconds
};

function initializeMap() {
  // Your map initialization code here
  // Example for Google Maps:
  // const map = new google.maps.Map(document.getElementById('map'), {
  //   center: { lat: -34.397, lng: 150.644 },
  //   zoom: 8
  // });
}

 </script>
<script>
    var map = L.map('map').setView([48.8566, 2.3522], 5); // Paris

    // Couche de base claire
    var tileLayerInverted = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 20
    }).addTo(map);

    // Couche de base sombre
    var tileLayerDark = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 20
    });

    // Groupe de cluster pour les marqueurs
    var clusterGroup = L.markerClusterGroup();
    var individualMarkers = []; // Initialisation vide du tableau des marqueurs

    // Récupérer les coordonnées depuis le template Twig
    var coordinates = {{ coordinates|raw }}; 

    // Vérifiez si des coordonnées sont disponibles et parcourez-les pour ajouter des marqueurs
    if (coordinates && coordinates.length > 0) {
        coordinates.forEach(function(result) {
            if (result.voies && result.voies.length > 0) {
                result.voies.forEach(function(voie) {
                    if (voie.coordinates && voie.coordinates.length > 0) {
                        voie.coordinates.forEach(function(coord) {
                            var marker = L.marker([ coord.longitude,coord.latitude,])
                                .bindPopup(`<b>${voie.nom_voie}</b><br>Numéro: ${coord.numr_voie || 'N/A'}`);
                            
                            clusterGroup.addLayer(marker);
                            individualMarkers.push(marker);
                        });
                    }
                });
            }
        });
    }

    // Ajouter le groupe de clusters sur la carte
    map.addLayer(clusterGroup);

    // Logique de changement de zoom et d'affichage des marqueurs individuels
    map.on('zoomend', function () {
        if (map.getZoom() > 16) {
            individualMarkers.forEach(function(marker) {
                marker.addTo(map);
            });
        } else {
            individualMarkers.forEach(function(marker) {
                map.removeLayer(marker);
            });
            map.addLayer(clusterGroup);
        }
    });

    // Fonction pour basculer entre le mode clair et sombre
    function toggleDarkMode() {
        const isChecked = document.getElementById('chk').checked;
        if (isChecked) {
            map.removeLayer(tileLayerInverted);
            tileLayerDark.addTo(map);
            document.body.classList.add('dark-mode');
        } else {
            map.removeLayer(tileLayerDark);
            tileLayerInverted.addTo(map);
            document.body.classList.remove('dark-mode');
        }
    }

    // Écouter le changement de la case pour changer de mode
    document.getElementById('chk').addEventListener('change', toggleDarkMode);
</script>



	

{# <script>

const map = L.map('map').setView([
20, 0
], 2);

const tileLayerInverted = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {attribution: '&copy; OpenStreetMap contributors, &copy; CartoDB'});

const tileLayerDark = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {attribution: '&copy; OpenStreetMap contributors, &copy; CartoDB'});

tileLayerInverted.addTo(map);



</script> #}
<script>

// Users in the Last 30 Minutes Chart
const users30MinutesCtx = document.getElementById('users30MinutesChart').getContext('2d');
new Chart(users30MinutesCtx, {
type: 'bar',
data: {
labels: Array.from(
{
length: 30
},
(_, i) => `-${
30 - i
} `
), // Création des labels
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 30
},
() => Math.floor(Math.random() * 100)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 0
}
]
},
options: {
responsive: true, // Chart responsive
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Cacher les ticks sur l'axe Y
},
grid: {
display: false // Cacher les lignes de la grille Y
}
},
x: {
ticks: {
display: true, // Assurez-vous que les ticks sont affichés
font: {

size: 14, // Taille de police lisible
style: 'normal', // Style normal (pas en italique)
weight: '400', // Poids de police normal
lineHeight: 1.5 // Hauteur de ligne
},


// Utiliser maxRotation et minRotation pour garantir des labels horizontaux
maxRotation: 0, // Pas de rotation maximum
minRotation: 0 // Pas de rotation minimum
},
grid: {
display: false // Cacher les lignes de la grille X
}
}
},
plugins: {
legend: {
display: false // Cacher la légende
}
}
}
});
</script>
