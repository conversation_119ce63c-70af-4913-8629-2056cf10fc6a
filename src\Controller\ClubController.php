<?php

namespace App\Controller;

use App\Entity\Competitions;
use App\Entity\PlayersClub;
use App\Service\ClubService;
use App\Service\PlayersService;
use App\Service\ClubDataService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpFoundation\Request;

class ClubController extends AbstractController
{

    private $clubService;
    private $playersService;
    private $entityManager;
    private $client;
    private $clubDataService;

    public function __construct(
        ClubService $clubService,
        PlayersService $playersService,
        EntityManagerInterface $entityManager,
        HttpClientInterface $client,
        ClubDataService $clubDataService
    ) {
        $this->clubService = $clubService;
        $this->playersService = $playersService;
        $this->entityManager = $entityManager;
        $this->client = $client;
        $this->clubDataService = $clubDataService;
    }

    #[Route('/clubsList', name: 'app_clubs')]
    public function ClubPage(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
        return $this->render('clubs/index.html.twig', [
            'jwt' => $jwt,
        ]);
    }
    #[Route('/competition', name: 'competition')]
    public function competitionPage(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');

        // MODE DÉVELOPPEMENT : Contourner l'authentification
        if (!$jwt) {
            // Créer un JWT factice pour le développement
            $jwt = 'dev-token-' . time();
            $session->set('jwt', $jwt);
        }

        return $this->render('competition/competition.html.twig', [
            'jwt' => $jwt,
        ]);
    }

    #[Route('/clubs_list_by_search', name: 'app_clubs_list_by_search')]
    public function ClubListPage(SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        $search = $request->get('search');
        $page_number = 1;

        $data = $this->clubService->getClubsBySearch($search, $page_number);

        return $this->json($data, 200);
    }

    #[Route('/players_list_by_club_id', name: 'app_players_list_by_club_id')]
    public function PlayersListByClubId(SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        $club_id = $request->get('club_id');

        $data = $this->playersService->getPlayersByClubId($club_id);

        return $this->json($data, 200);
    }

    #[Route('/competions_list', name: 'app_players_list_by_club_id')]
    public function competitionsList(SessionInterface $session, Request $request): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        try {
            // Fetch user data
            $responsePlayers = $this->client->request('GET',
                'https://transfermarkt-api.fly.dev/competitions/search/football', [
                    'headers' => [
                        'accept' => 'application/json',
                    ]
                ]);
            $competitionData = json_decode($responsePlayers->getContent(), true);

            if(is_array($competitionData) && array_key_exists('results', $competitionData) && array_key_exists('pageNumber', $competitionData) && array_key_exists('lastPageNumber', $competitionData)){
                $pageNumber = $competitionData['pageNumber'];
                $lastPageNumber = $competitionData['lastPageNumber'];
                if($pageNumber <= $lastPageNumber){
                    for ($i=1; $i<=$lastPageNumber; $i++){
                        try {
                            // Fetch user data
                            $response = $this->client->request('GET',
                                'https://transfermarkt-api.fly.dev/competitions/search/football?page_number='.$i, [
                                    'headers' => [
                                        'accept' => 'application/json',
                                    ]
                                ]);
                            $data = json_decode($response->getContent(), true);

                            if(is_array($data) && array_key_exists('results', $competitionData)){
                                foreach ($data['results'] as $competition){
                                    $competiontionEntity = new Competitions();

                                    $competiontionEntity->setCompetionId($competition['id']);
                                    $competiontionEntity->setName($competition['name']);
                                    $competiontionEntity->setContinent($competition['continent']);
                                    $competiontionEntity->setCountry($competition['clubs']);
                                    $competiontionEntity->setPlayers($competition['players']);
                                    $competiontionEntity->setTotalMarketValue($competition['totalMarketValue']);
                                    $competiontionEntity->setMeanMarketValue($competition['meanMarketValue']);
                                    $competiontionEntity->setClubs($competition['name']);

                                    $this->entityManager->persist($competiontionEntity);
                                    $this->entityManager->flush();
                                }
                            }
                        } catch (\Exception $e) {
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
        }

        return $this->json([], 200);
    }

    #[Route('/playersList', name: 'app_players')]
    public function playersListPage(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');

        // MODE DÉVELOPPEMENT : Contourner l'authentification
        if (!$jwt) {
            $jwt = 'dev-token-' . time();
            $session->set('jwt', $jwt);
        }

        $competions = $this->entityManager->getRepository(Competitions::class)
            ->findAll();
            return $this->render('players/jbscout.html.twig', [
            'jwt' => $jwt,
            'competions' => $competions
        ]);
        // return $this->render('players/index.html.twig', [
        //     'jwt' => $jwt,
        //     'competions' => $competions
        // ]);



    }

    #[Route('/playersListV2', name: 'app_players_v2')]
    public function playersListPageV2(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }


        return $this->render('players/indexV2.html.twig', [
            'jwt' => $jwt,
        ]);
    }

    #[Route('/players_by_filter', name: 'app_players_by_filter')]
    public function PlayersByFilter(Request $request): Response
    {


        $data=$request->getContent();
        $data=json_decode($data, true);
        $ageMin = $data['ageMin'];
        $ageMax = $data['ageMax'];
        $marketMin = $data['marketMin'];
        $marketMax = $data['marketMax'];
        $position = $data['position'];

        $playersList=[];
        $repo=$this->entityManager->getRepository(PlayersClub::class);
        $players=$repo->findAll();

        foreach ($players as $item){
            foreach ($item->getPlayers() as $player){
                if(array_key_exists('age',$player) && array_key_exists('marketValue',$player) && array_key_exists('position',$player)){
                    if($player['age']>=$ageMin && $player['age']<=$ageMax && $player['marketValue']>=$marketMin
                        && $player['marketValue']<=$marketMax && strpos(strtolower($player['position']), strtolower($position)) !== false){
                        $playersList[]=$player;
                    }
                }

            }
        }
        return $this->json($playersList, 200);
    }

    /**
     * API endpoint pour récupérer les clubs d'une compétition depuis les fichiers JSON locaux
     * Pour l'instant, ne supporte que la Premier League (GB1)
     */
    #[Route('/api/competitions/{competitionId}/clubs', name: 'api_competition_clubs', methods: ['GET'])]
    public function getClubsByCompetition(string $competitionId): JsonResponse
    {
        try {
            $clubsData = $this->clubDataService->getClubsByCompetition($competitionId);

            if ($clubsData === null) {
                return $this->json([
                    'error' => 'Competition not found or data not available',
                    'competitionId' => $competitionId,
                    'message' => 'Currently only Premier League (GB1) is supported'
                ], 404);
            }

            // Retourner les données dans le même format que l'API Transfermarkt
            // pour que le JavaScript existant fonctionne sans modification
            return $this->json($clubsData);

        } catch (\Exception $e) {
            return $this->json([
                'error' => 'Server error',
                'message' => 'Unable to load clubs data'
            ], 500);
        }
    }
}
