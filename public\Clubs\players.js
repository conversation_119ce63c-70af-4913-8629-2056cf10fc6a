
async function fetchCompetitionsByCountry(country){
    try {
        // const response = await fetch(
        //     'https://transfermarkt-api.fly.dev/competitions/search/'+country,
        //     {
        //         method: 'GET',
        //         headers: {
        //             'accept': 'application/json'
        //         }
        //     });
        // const result = await response.json(); // Assuming it returns an array of strings
        const res=[];
        // Clear old suggestions
        const response = await fetch(
            'https://transfermarkt-api.fly.dev/competitions/FR1/clubs',
            {
                method: 'GET',
                headers: {
                    'accept': 'application/json'
                }
            });
        const result1 = await response.json(); // Assuming it returns an array of strings

        // Clear old suggestions
        if(result1.clubs){
            for (const item1 of (result1.clubs)) {
                const response = await fetch(
                    'https://transfermarkt-api.fly.dev/clubs/'+(item1.id)+'/players',
                    {
                        method: 'GET',
                        headers: {
                            'accept': 'application/json'
                        }
                    });
                const players = await response.json(); // Assuming it returns an array of strings
                (players.players).forEach(player => {
                    res.push(player);
                });
            }
        }

        // for (const item of (result.results)) {
        //
        // }

        console.log(res);
        return res;

    } catch (error) {
        console.error('Error fetching suggestions:', error);
    }
}

async function fetchClubsByCompetition(competitionId){
    try {
        const response = await fetch(
            'https://transfermarkt-api.fly.dev/competitions/'+competitionId+'/clubs',
            {
                method: 'GET',
                headers: {
                    'accept': 'application/json'
                }
            });
        const result = await response.json(); // Assuming it returns an array of strings

        // Clear old suggestions
        const res = [];
        if(result.clubs){
            (result.clubs).forEach(item => {
                console.log(item.id);
                const data = fetchPlayersByClub((item.id));
                data.forEach(player => {
                    res.push(player);
                });
            });
        }
        return res;


    } catch (error) {
        return [];
    }
}

async function fetchPlayersByClub(clubId){

    try {
        const response = await fetch(
            'https://transfermarkt-api.fly.dev/clubs/'+clubId+'/players',
            {
                method: 'GET',
                headers: {
                    'accept': 'application/json'
                }
            });
        const players = await response.json(); // Assuming it returns an array of strings
        return (players.players);

    } catch (error) {
        return [];
    }
}

async function searchPlayer(){

    let country=$('#country').val();
    let ageMin=$('#ageMin').val();
    let ageMax=$('#ageMax').val();
    let marketMin=$('#marketMin').val();
    let marketMax=$('#marketMax').val();
    let position=$('#position').val();


    if(country == ''){ alert('Merci de selectionner un club');}
    else{
        $('#players').html = '';

        const response = await fetch(
            'https://transfermarkt-api.fly.dev/competitions/FR1/clubs',
            {
                method: 'GET',
                headers: {
                    'accept': 'application/json'
                }
            });
        const result1 = await response.json(); // Assuming it returns an array of strings

        // Clear old suggestions
        if(result1.clubs){
            for (const item1 of (result1.clubs)) {
                const response = await fetch(
                    'https://transfermarkt-api.fly.dev/clubs/'+(item1.id)+'/players',
                    {
                        method: 'GET',
                        headers: {
                            'accept': 'application/json'
                        }
                    });
                const players = await response.json(); // Assuming it returns an array of strings
                (players.players).forEach(player => {

                    if(((player.position).indexOf(position))!== -1 && parseInt(player.age) >= parseInt(ageMin)  && parseInt(player.age) <= parseInt(ageMax) && parseInt(player.marketValue) >= parseInt(marketMin) && parseInt(player.marketValue) <= parseInt(marketMax)){
                        $('#players').append('<div class="player">\n' +
                            '        <img src="" width="50"> <strong>'+(player.name)+'</strong><br>\n' +
                            '        Âge : '+(player.age)+' ans<br>\n' +
                            '        Nationalité : '+(player.nationality)[0]+'<br>\n' +
                            '        Poste : '+(player.position)+
                            '    </div><hr/>');
                    }
                });
            }
        }
    }
}

