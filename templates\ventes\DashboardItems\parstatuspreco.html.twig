<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

<style>
* {
    font-family: 'Ubuntu', sans-serif;
}

:root {
    --statuspreco-color-background-light: #C0E7F7;
    --statuspreco-color-background-dark: #e9f5fd;
}

[data-theme="dark"] {
    --statuspreco-color-background-light: #1e1e1e;
    --statuspreco-color-background-dark: #2a2a2a;
}

.statuspreco-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.statuspreco-header {
    font-size: 0.8em;
    margin: 0;
}

.statuspreco-chart-and-legend {
    display: flex;
    align-items: center;
    margin-top: 5%;
    gap: 20px;
}

.statuspreco-chart-container {
    height: 112px;
    width: 112px;
}

.statuspreco-legend {
    list-style: none;
    padding: 0;
    margin: 0;
}

.statuspreco-legend li {
    display: flex;
    align-items: center;
    font-size: 0.8em;
     color: var( --4text-colors);
    margin-bottom: 5px;
}

.statuspreco-legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 30%;
}
</style>
<div class="bonustechno-container">
    <h5 class="text-header4s">Par Statut Preco</h5>
    <div class="bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="statusprecoPieChart"></canvas>
        </div>
        <ul class="bonustechno-legend">
            {% for item in productionsGroupData.PreCommande %}
                {% set gradient = "linear-gradient(to right, #027fc0, #4bb3e5)" %}
                <li><span class="bonustechno-legend-color" style="background: {{ gradient }};"></span></li>
            {% endfor %}
        </ul>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    var chartData = {{ productionsGroupData.PreCommande | json_encode() | raw }}; // Pass data from PHP to JS

    var bonustechnoCtx = document.getElementById('statusprecoPieChart').getContext('2d');

    // Create gradients for the segments (you can add more gradients if needed)
    var bonustechnoGradientA = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientA.addColorStop(0, '#49b9ee');
    bonustechnoGradientA.addColorStop(1, '#a8e2f5');

    var bonustechnoGradientB = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientB.addColorStop(0, '#027fc0');
    bonustechnoGradientB.addColorStop(1, '#4bb3e5');

    var bonustechnoGradientC = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientC.addColorStop(0, '#145277');
    bonustechnoGradientC.addColorStop(1, '#3a7d9c');

    // Process chartData for the chart's labels and data
    var labels = [];
    var data = [];
    var backgroundColors = []; // Array to hold background colors for each segment

    chartData.forEach(function(item, index) {
        labels.push(item.categorie); // Assuming 'categorie' contains the category name
        data.push(item.totalVentes); // Add totalVentes as data

        // Assign different background colors dynamically
        // You can use a condition to change the colors based on the data
        if (index % 3 === 0) {
            backgroundColors.push(bonustechnoGradientA);
        } else if (index % 3 === 1) {
            backgroundColors.push(bonustechnoGradientB);
        } else {
            backgroundColors.push(bonustechnoGradientC);
        }
    });

    // Create pie chart with the processed data
    var statusprecoPieChart = new Chart(bonustechnoCtx, {
        type: 'pie',
        data: {
            labels: labels, // Category names as labels
            datasets: [{
                data: data, // Sales data for each category
                backgroundColor: backgroundColors, // Assign the background colors array dynamically
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false // Hide the default legend (you've created a custom one)
                }
            }
        }
    });
</script>