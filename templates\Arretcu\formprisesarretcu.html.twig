{# Tableau de correspondance des mois en français #}


{% set mois_fr = {
    'January': 'Janvier',
    'February': 'Février',
    'March': 'Mars',
    'April': 'Avril',
    'May': 'Mai',
    'June': 'Juin',
    'July': '<PERSON><PERSON><PERSON>',
    'August': 'Août',
    'September': 'Septembre',
    'October': 'Octobre',
    'November': 'Novembre',
    'December': 'Décembre'
} %}
<style>
.selectmonth{
   
    background-color: var(--card-background-color);
    border: none;
    color: var(--bigtext-color) !important;
    font-size: 90%;
    border-radius: 9px;
    padding: 5px;
}
</style>
<form id="fetch-form" action="{{ action }}" method="GET">
    <label class="toggle-switch">
        <input type="checkbox" id="toggle-switch" {% if app.request.get('moisArretCuivre') %} checked {% endif %} />
        <span class="slider"></span>
    </label>

    <!-- Sélecteur pour le mois et l'année -->
    <select id="month-year-select" class="selectmonth" {% if not app.request.get('moisArretCuivre') %} disabled {% endif %}>
        <option value="">Sélectionnez</option>
        {% for month in months %}
            {% for year in years %}
                {% set mois_annee = month|date('m') ~ '-' ~ year %}
                {% set mois_nom = month|date('F') %}
                <option value="{{ mois_annee }}"
                    {% if mois_annee == app.request.get('moisArretCuivre') ~ '-' ~ app.request.get('anneeArretCuivre') %}
                        selected
                    {% endif %}
                >
                    {{ mois_fr[mois_nom] }} {{ year }} {# Utiliser le tableau de correspondance #}
                </option>
            {% endfor %}
        {% endfor %}
    </select>
</form>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const toggleSwitch = document.getElementById('toggle-switch');
    const monthYearSelect = document.getElementById('month-year-select');

    // Activer/désactiver le select en fonction du toggle
    toggleSwitch.addEventListener('change', () => {
        const isChecked = toggleSwitch.checked;
        monthYearSelect.disabled = !isChecked;

        // Si le toggle est désactivé, réinitialiser la sélection
        if (!isChecked) {
            monthYearSelect.value = '';
        }
    });

    // Écoute du changement de la sélection pour soumettre directement le formulaire
    monthYearSelect.addEventListener('change', function () {
        const monthYearValue = monthYearSelect.value;

        if (monthYearValue) {
            // Séparer le mois et l'année
            const parts = monthYearValue.split('-');

            // Créer les champs cachés pour le mois et l'année
            const moisInput = document.createElement('input');
            moisInput.type = 'hidden';
            moisInput.name = 'moisArretCuivre';  // Utiliser le bon nom pour le mois
            moisInput.value = parts[0];
            document.getElementById('fetch-form').appendChild(moisInput);

            const anneeInput = document.createElement('input');
            anneeInput.type = 'hidden';
            anneeInput.name = 'anneeArretCuivre';  // Utiliser le bon nom pour l'année
            anneeInput.value = parts[1];
            document.getElementById('fetch-form').appendChild(anneeInput);

            // Soumettre le formulaire
            document.getElementById('fetch-form').submit();
        }
    });
});
</script>
