
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/all.css" />
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/duotone.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

   <style>
     :root {
            --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 320px;
            --prisescardcard-height: 165px;
            --navbackground-color: #edecfc;
            --card-background-color: #edecfd;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #fcfbff;
            --icons-colors-i: #949fa9;
            --smaller-text-color-numbers: #c1c9ce;
            --bigtext-color: #394d5f;
        }

        [data-theme="dark"] {
              --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 300px;
            --prisescardcard-height: 165px;
            --navbackground-color: #2b3942;
            --card-background-color:#2b3942;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #1f2c34;
            --icons-colors-i: #6e6f71;
            --smaller-text-color-numbers: #6e6f71;
            --bigtext-color: #e9ecf0;
            --bigtext-colors: #6e6f71;
        }

      

@media screen and (min-width: 1400px) {
    #prisescard {
        display: grid;
       grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 20px;
    }
      .prisesdashcard-container {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            scrollbar-width: none;
            scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
            overflow-y: auto; 
            max-height: 98vh; 
       
        }
}

@media screen and (max-width: 1366px) and (min-width: 400px) {
    #prisescard {
        display: grid;
        grid-template-columns: repeat(3, minmax(300px, 340px)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 10px;
    }
      .prisesdashcard-container {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            scrollbar-width: none;
            scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
            overflow-y: auto; 
            max-height: 98vh; 
   
        }
}

        #prisescard .custom-card {
            background-color: var(--card-background-color);
            border-radius: 12px;
            padding: 10px;
            color: white;
            width: var(--prisescardcard-width);
            height: var(--prisescardcard-height);
            opacity: 0; /* Initial state for lazy loading */
            transition: opacity 0.3s ease-out;
        }

        #prisescard .card-header {
            background-color: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            border: none;
            box-shadow: none;
        }

        #prisescard .card-header i {
            color: #bb86fc;
            margin-right: 5px;
        }

        #prisescard .card-title {
            font-weight: bold;
            color: var(--bigtext-color);
            font-size: 0.8em;
        }

        #prisescard .number {
            color: var(--bigtext-color);
            font-weight: bold;
            font-size: 0.8em;
            margin-top: -3%;
        }

        #prisescard .data-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 0.85em;
        }

        #prisescard .data-row {
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }

        #prisescard .data-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            background-color: var(--small-cards-bg-colors);
            border-radius: 6px;
            width: 160px;
            font-size: 1.1em;
        }

        #prisescard .data-box i {
            color: var(--icons-colors-i);
        }

        #prisescard .data-box .texticon {
            color: var(--icons-colors-i);
            font-weight: bold;
        }

        #prisescard .data-box .number {
            color: var(--smaller-text-color-numbers);
            font-size: 1em;
        }

        /* Navbar Styles */
        #smallprisesnav .navbar {
            display: flex;
            gap: 8px;
            padding: 20px;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
        }

        #smallprisesnav .nav-button {
            display: flex;
            align-items: center;
            gap: 7px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 1rem;
            font-weight: bold;
           color: var(--bigtext-colors);
            background-color: transparent;
            border: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        #smallprisesnav .nav-button.active {
            background-color: var(--navbackground-color);
            color: var(--bigtext-color);
        }

        #smallprisesnav .nav-button .icon {
            width: 24px;
            height: 24px;
        }

</style>
<style>
 :root {
    /* Default Variables */

    --skeleton-bg: #e0e0e0;
    --skeleton-highlight: #f0f0f0;
    --prisescardcard-width: 300px;
    --prisescardcard-height: 150px;
}

/* Styles for dark mode */
[data-theme="dark"] {

    --skeleton-bg: #1f2c34;
    --skeleton-highlight: #2b3942;
}

.skeleton-card {
    background-color: var(--card-background-color);
    border-radius: 12px;
    padding: 10px;
    width: var(--prisescardcard-width);
    height: var(--prisescardcard-height);
    display: flex;
    flex-direction: column;
    gap: 10px;
    animation: shimmer 1.5s infinite;
}

/* Header Skeleton */
.skeleton-card-header {
    height: 20px;
    width: 80%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
    margin-bottom: 8px;
}

/* Data Container Skeleton */
.skeleton-data-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.skeleton-data-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.skeleton-data-row div {
    height: 30px;
    width: 45%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
}

/* Animation for Shimmer Effect */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: 200px 0;
    }
}

.skeleton-card-header,
.skeleton-data-row div {
    background: linear-gradient(
        90deg,
        var(--skeleton-bg) 25%,
        var(--skeleton-highlight) 50%,
        var(--skeleton-bg) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Hide actual cards during loading */
.custom-card.loading {
    display: none;
}

.custom-card.loaded {
    opacity: 1;
    display: block;
}

/* Responsive Design */
@media (max-width: 600px) {
    :root {
        --prisescardcard-width: 100%;
        --prisescardcard-height: auto;
    }
    
    .skeleton-card {
        padding: 8px;
    }
    
    .skeleton-data-row div {
        height: 20px;
    }
}
[data-theme="dark"] #btn-adsl img {
    content: url("{{ asset('image/icon/adsl-gris.svg') }}");
}
[data-theme="dark"] #btn-fibre img {
    content: url("{{ asset('image/icon/Fibre-gris.svg') }}");
}
[data-theme="dark"] #btn-mob img {
    content: url("{{ asset('image/icon/Mobile-gris.svg') }}");
}
[data-theme="dark"] #btn-mob-fibre img {
    content: url("{{ asset('image/icon/mobile-fibre-gris.svg') }}");
}
[data-theme="dark"] #btn-mob-adsl img {
    content: url("{{ asset('image/icon/mobile-adsl-gris.svg') }}");
}

.breadcrumb-bar {

    border-radius: 5px; /* Coins arrondis */
 
    display: flex; /* Utilisation de Flexbox */
    justify-content: center; /* Centrage horizontal */
    align-items: center; /* Centrage vertical */
    height: 30px; /* Hauteur pour centrer verticalement */
}

.breadcrumb {
    margin-bottom: 0; /* Supprime la marge par défaut */
}

.breadcrumb a {
    color: #007bff; /* Couleur des liens */
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline; /* Soulignement au survol */
      color: #007bff; /* Couleur des liens */
}
.breadcrumb-item+.breadcrumb-item::before{
     color: #007bff; /* Couleur des liens */  
}
</style>


<style>
    .no-data-message {
        text-align: center;
        color: #555;
        margin-top: 20px;
        padding: 20px;
        background-color: #f9f9f9;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
    }

    .no-data-icon {
        font-size: 2rem;
        color: #ff6b6b;
        margin-bottom: 10px;
    }

    .no-data-message p {
        margin: 0;
        font-size: 1.2rem;
        color: #666;
    }

    .btn-secondary {
        background-color: #6c757d;
        border: none;
    }
</style>
<script>
// Save cluster details in localStorage
const clusterLibelle = "{{ clusterDetailsRue[1].libelle_cluster }}";
const clusterVille = "{{ clusterDetailsRue[1].vill }}";

// Storing values in localStorage
localStorage.setItem("libelleCluster", clusterLibelle);
localStorage.setItem("vill", clusterVille);

</script>

{# <div class="breadcrumb-bar">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="#" id="libelleCluster">{{ clusterDetailsRue[1].libelle_cluster }}</a>
            </li>
            <li class="breadcrumb-item">
                <a href="#" id="vill">{{ clusterDetailsRue[1].vill }}</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page"></li>
        </ol>
    </nav>
</div> #}


<div class="prisesdashcard-container">
     {% set total_nb_fyr_adsl = 0 %}
        {% set total_nb_fyr_mob_mono = 0 %}
        {% set total_nb_fyr_mob_multi_thd = 0 %}
        {% set total_nb_fyr_mob_multi_adsl = 0 %}
        {% set total_nb_fyr_thd = 0 %}
              {% if Clusterstotals is not empty %}
                {% for cluster in Clusterstotals.nom_voie %}
            {% for date, values in cluster %}
                {% for cluster_key, cluster_data in cluster %}

                    {# Accumulate the sum of each value #}
                    {% set total_nb_fyr_adsl = total_nb_fyr_adsl + values.data.nb_fyr_adsl %}
                    {% set total_nb_fyr_mob_mono = total_nb_fyr_mob_mono + values.data.nb_fyr_mob_mono %}
                    {% set total_nb_fyr_mob_multi_thd = total_nb_fyr_mob_multi_thd + values.data.nb_fyr_mob_multi_thd %}
                    {% set total_nb_fyr_mob_multi_adsl = total_nb_fyr_mob_multi_adsl + values.data.nb_fyr_mob_multi_adsl %}
                    {% set total_nb_fyr_thd = total_nb_fyr_thd + values.data.nb_fyr_thd %}
                           {% endfor %}
            {% endfor %}

        {% endfor %}   
    {% else %}
    {% endif %} 

             {% set total_totalPrisesFTTB = 0 %}
        {% set total_totalPrisesFTTH = 0 %}
        {% set total_totalImmeubles = 0 %}
        {% set total_totalPavillons = 0 %}
    {% for cluster in clusterDetailsRue  %}

                    {# Accumulate the sum of each value #}
                    {% set total_totalPrisesFTTB = total_totalPrisesFTTB+ cluster.totalPrisesFTTB %}
                    {% set total_totalPrisesFTTH = total_totalPrisesFTTH + cluster.totalPrisesFTTH   %}
                    {% set total_totalImmeubles = total_totalImmeubles+  cluster.totalImmeubles %}
                    {% set total_totalPavillons = total_totalPavillons + cluster.totalPavillons%}
        {% endfor %}   
<div id="smallprisesnav">


     <div class="navbar">

    <button class="nav-button" id="btn-adsl" data-param="adsl" 
            data-src-light="{{ asset('image/icon/adsl-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/adsl-gris.svg') }}">
        <img src="{{ asset('image/icon/adsl-violet.svg') }}" alt="Router Icon" class="icon" />
        <span>{{ total_nb_fyr_adsl }}</span>
    </button>
    <button class="nav-button" id="btn-fibre" data-param="thd" 
            data-src-light="{{ asset('image/icon/Fibre-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/Fibre-gris.svg') }}">
        <img src="{{ asset('image/icon/Fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_thd }}</span>
    </button>
    <button class="nav-button" id="btn-mob" data-param="mobMono" 
            data-src-light="{{ asset('image/icon/Mobile-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/Mobile-gris.svg') }}">
        <img src="{{ asset('image/icon/Mobile-violet.svg') }}" alt="Mobile Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_mono }}</span>
    </button>
    <button class="nav-button" id="btn-mob-fibre" data-param="mobMultiThd" 
            data-src-light="{{ asset('image/icon/mobile-fibre-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/mobile-fibre-gris.svg') }}">
        <img src="{{ asset('image/icon/mobile-fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_multi_thd }}</span>
    </button>
    <button class="nav-button" id="btn-mob-adsl" data-param="mobMultiAdsl" 
            data-src-light="{{ asset('image/icon/mobile-adsl-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/mobile-adsl-gris.svg') }}">
        <img src="{{ asset('image/icon/mobile-adsl-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_multi_adsl }}</span>
    </button>

    <button class="nav-button" id="btn-adsl" data-param="adsl" 
       >
                    <span class="texticon">FTTB</span>
                    <span class="number">{{ total_totalPrisesFTTB  }}</span>
    </button>
    <button class="nav-button" id="btn-fibre" data-param="thd" 
         >
        <span class="texticon">FTTH</span>
                    <span class="number">{{ total_totalPrisesFTTH}}</span>
    </button>
    <button class="nav-button" id="btn-mob" data-param="mobMono" 
            >
             <i class="fas fa-home"></i>
                    <span class="number">{{ total_totalImmeubles  }}</span>
    </button>
    <button class="nav-button" id="btn-mob-fibre" data-param="mobMultiThd" >
     <i class="fas fa-building"></i>
                    <span class="number">{{ total_totalPavillons}}</span>
    </button>
   


</div>
</div>

   <div id="prisescard">
    {% if clusterDetailsRue  is empty %}
        <div class="no-data-message">
            <i class="fas fa-exclamation-circle no-data-icon"></i>
            <p>No Rue details found for this cluster.</p>
            <a href="{{ path('detail_cluster_datalist', { 'code_cluster': code_cluster }) }}" class="btn btn-secondary mt-2">View Data List Version</a>
        </div>
    {% else %}
        <!-- Skeleton Cards -->
        {% for detail in clusterDetailsRue %}
        <div class="custom-card skeleton-card">
            <div class="skeleton-card-header"></div>
            <div class="skeleton-data-container">
                <div class="skeleton-data-row">
                    <div></div>
                    <div></div>
                </div>
                <div class="skeleton-data-row">
                    <div></div>
                    <div></div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Actual Cards -->
        {% for detail in clusterDetailsRue %}
        <div class="custom-card" data-lazy-load="true" data-cluster-code="{{ detail.nom_voie }}">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-map-marker-alt"></i> {{ detail.nom_voie }}
                </div>
                <div class="number">
                    <i class="fa-solid fa-plug"></i> {{ detail.totalPrises }}
                </div>
            </div>
            <div class="data-container">
                <div class="data-row">
                    <div class="data-box">
                        <span class="texticon">FTTB</span>
                        <span class="number">{{ detail.totalPrisesFTTB }}</span>
                    </div>
                    <div class="data-box">
                        <span class="texticon">FTTH</span>
                        <span class="number">{{ detail.totalPrisesFTTH }}</span>
                    </div>
                </div>
                <div class="data-row">
                    <div class="data-box">
                        <i class="fas fa-home"></i>
                        <span class="number">{{ detail.totalPavillons }}</span>
                    </div>
                    <div class="data-box">
                        <i class="fas fa-building"></i>
                        <span class="number">{{ detail.totalImmeubles }}</span>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% endif %}
</div>

</div>

<script>
document.addEventListener("DOMContentLoaded", () => {
    const skeletons = document.querySelectorAll('.skeleton-card');
    const realCards = document.querySelectorAll('.custom-card[data-lazy-load="true"]');

    // Show skeletons initially
    skeletons.forEach(skeleton => skeleton.style.display = 'block');
    realCards.forEach(card => card.style.display = 'none');

    // Simulate loading delay
    setTimeout(() => {
        // Hide skeletons
        skeletons.forEach(skeleton => skeleton.style.display = 'none');

        // Show real cards
        realCards.forEach(card => {
            card.style.display = 'block';
            card.classList.add('loaded'); // Optional for smooth transition
        });
    }, 2000); // 2 seconds delay
});

</script>
  <script>
 document.addEventListener('DOMContentLoaded', () => {
    const cards = document.querySelectorAll('.custom-card');
    cards.forEach(card => {
        card.addEventListener('click', () => {
            const nomrue = card.getAttribute('data-cluster-code');
            const code_cluster = "{{ code_cluster }}"; 
            const detailkpiinseeCode = "{{ detailkpiinseeCode }}"
            window.location.href = `/clusters/details/rue/${code_cluster}/${detailkpiinseeCode}/${nomrue}`;
        });
    });
});

</script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // IntersectionObserver to handle lazy loading
        const options = {
            root: null, // use the viewport
            rootMargin: '0px',
            threshold: 0.1 // trigger when 10% of the card is visible
        };

        const loadCard = (entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = 1; // Fade-in effect
                    observer.unobserve(entry.target); // Stop observing
                }
            });
        };

        const observer = new IntersectionObserver(loadCard, options);
        const cards = document.querySelectorAll('.custom-card');
        cards.forEach(card => {
            observer.observe(card); // Start observing each card
        });
    });
</script>

