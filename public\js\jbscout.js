// Custom fetch function for reusability
function customFetch(url, callback) {
    const loader = document.getElementById('loader');
    loader.style.display = 'block';  // Show loader

    fetch(url, { method: 'GET', headers: { 'accept': 'application/json' }})
    .then(response => {
        // Simulate a delay to see the loader
        return new Promise(resolve => setTimeout(() => resolve(response.json()), 2000));
    })
    .then(data => {
        loader.style.display = 'none';  // Hide loader
        callback(data);
    })
    .catch(error => {
        loader.style.display = 'none';  // Hide loader
        console.error('Fetch error:', error);
        displayError(error);  // Display error in the UI
    });

}

function displayError(error) {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = `<p>Error fetching data: ${error.message}</p>`;
}

function searchPlayer() {
    const playerName = document.getElementById('playerName').value;
    const url = `https://transfermarkt-api.fly.dev/players/search/${playerName}`;
    customFetch(url, displayResults);
    document.getElementById('returnBtn').style.display = 'block';
}
let currentResultsSearch = null;
function displayResults(data) {
    currentResultsSearch = data;
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = '';
    console.log("Processing results:", data.results.length);

    if (data.results && data.results.length > 0) {
        data.results.forEach(player => {
            const playerDiv = document.createElement('div');
            playerDiv.className = 'player-info';
            let marketValueFormatted = player.marketValue ? `€${player.marketValue.toLocaleString()}` : 'Not available';
            playerDiv.innerHTML = `
            <span style="display:flex;align-items:center;gap: 10px;">
                <h3 id="player_${player.id}" style="cursor:pointer;">${player.name} (${player.position})</h3>
                <i class="bi bi-bar-chart-line" style="margin-left: 15px;color:#26a69a;cursor:pointer;" id="displayStat_${player.id}"></i>
            </span>
                <p><span class="club-name" id="club_${player.club.id}" style="cursor:pointer; text-decoration:underline;">Club: ${player.club.name}</span></p>
                <p>Age: ${player.age}</p>
                <p>Nationalities: ${player.nationalities.join(', ')}</p>
                <p id="MarketValue_${player.id}" style="cursor:pointer; text-decoration:underline;">Market Value: ${marketValueFormatted}</p>
            `;
            playerDiv.querySelector(`#player_${player.id}`).addEventListener('click', () => fetchPlayerProfile(player.id));
            playerDiv.querySelector(`#club_${player.club.id}`).addEventListener('click', () => fetchClubDetails(player.club.id));
            playerDiv.querySelector(`#displayStat_${player.id}`).addEventListener('click', () => fetchPlayerStats(player.id));
            if (player.marketValue) {
                playerDiv.querySelector(`#MarketValue_${player.id}`).addEventListener('click', () => fetchMarketValueDetails(player.id));
            }
            resultsContainer.appendChild(playerDiv);
        });
    } else {
        resultsContainer.innerHTML = '<p>No results found.</p>';
    }
}

function fetchPlayerStats(playerId) {
    const url = `https://transfermarkt-api.fly.dev/players/${playerId}/stats`;
    const loader = document.getElementById('loader');
    loader.style.display = 'block';  // Show loader
    fetch(url, {
        method: 'GET',
        headers: {
            'accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        loader.style.display = 'none';
        displayPlayerStats(data);

    })
    .catch(error => {
        console.error('Error fetching player stats:', error);
        alert("Failed to fetch player stats: " + error.message);
    });
}

function displayPlayerStats(data) {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = '';

    const header = document.createElement('h1');
    header.textContent = `Statistics for ${data.name}`;
    resultsContainer.appendChild(header);

    // Prepare data for charts
    const seasons = data.stats.map(stat => stat.seasonId);
    const goals = data.stats.map(stat => stat.goals);
    const minutesPlayed = data.stats.map(stat => stat.minutesPlayed);

    // Create canvas elements for charts
    const goalsChartCanvas = document.createElement('canvas');
    goalsChartCanvas.id = 'goalsChart';
    resultsContainer.appendChild(goalsChartCanvas);

    const minutesChartCanvas = document.createElement('canvas');
    minutesChartCanvas.id = 'minutesChart';
    resultsContainer.appendChild(minutesChartCanvas);

    // Generate the Goals Chart
    const goalsChart = new Chart(goalsChartCanvas, {
        type: 'bar',
        data: {
            labels: seasons,
            datasets: [{
                label: 'Goals',
                data: goals,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Generate the Minutes Played Chart
    const minutesChart = new Chart(minutesChartCanvas, {
        type: 'line',
        data: {
            labels: seasons,
            datasets: [{
                label: 'Minutes Played',
                data: minutesPlayed,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    const table = document.createElement('table');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';

    // Create the header row
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr style="background-color: #f2f2f2;">
            <th>Season</th>
            <th>Competition</th>
            <th>Appearances</th>
            <th>Goals</th>
            <th>Assists</th>
            <th>Minutes Played</th>
        </tr>
    `;
    table.appendChild(thead);

    // Create the body of the table
    const tbody = document.createElement('tbody');
    data.stats.forEach(stat => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${stat.seasonId}</td>
            <td>${stat.competitionName}</td>
            <td>${stat.appearances}</td>
            <td>${stat.goals}</td>
            <td>${stat.assists}</td>
            <td>${stat.minutesPlayed}</td>
        `;
        tbody.appendChild(row);
    });
    table.appendChild(tbody);
    resultsContainer.appendChild(table);
}




function fetchPlayerProfile(playerId) {
    const url = `https://transfermarkt-api.fly.dev/players/${playerId}/profile`;
    customFetch(url, displayPlayerProfile);
}

function displayPlayerProfile(player) {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = `
        <div>
            <h1>${player.name} - Profile</h1>
            <div style="display:flex;align-items:center;gap: 50px;">
                <div><img src="${player.imageUrl}" alt="${player.name}" style="width:200px;height:auto;"></div>
                <div>
                    <p>Name in Home Country: ${player.nameInHomeCountry}</p>
                    <p>Position: ${player.position.main}</p>
                    <p>Club: ${player.club.name}</p>
                    <p>Age: ${player.age}</p>
                    <p>Nationalities: ${player.citizenship.join(', ')}</p>
                    <p>Market Value: ${player.marketValue ? `€${player.marketValue.toLocaleString()}` : 'Not available'}</p>
                </div>
            </div>
        </div>
    `;
}

function fetchClubDetails(clubId) {
    const url = `https://transfermarkt-api.fly.dev/clubs/${clubId}/profile`;
    customFetch(url, displayClubDetails);
}

function displayClubDetails(club) {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = `
        <h1>${club.name} - Club Profile</h1>
        <div style="display:flex;align-items:center;gap: 50px;">
            <div><img src="${club.image}" alt="${club.name}" style="width:200px;height:auto;"></div>
            <div>
                <p>Official Name: ${club.officialName}</p>
                <p>Address: ${club.addressLine1}, ${club.addressLine2}, ${club.addressLine3}</p>
                <p>Telephone: ${club.tel}</p>
                <p>Fax: ${club.fax}</p>
                <p>Website: <a href="${club.website}" target="_blank">${club.website}</a></p>
                <p>Founded On: ${club.foundedOn}</p>
                <p>Members: ${club.members} (as of ${club.membersDate})</p>
                <p>Colors: ${club.colors && club.colors.length > 0 ? club.colors.join(', ') : 'Not available'}</p>
                <p>Other Sports: ${club.otherSports && club.otherSports.length > 0 ? club.otherSports.join(', ') : 'Not available'}</p>
            </div>
        </div>
    `;
}

function fetchMarketValueDetails(playerId) {
    const url = `https://transfermarkt-api.fly.dev/players/${playerId}/market_value`;
    customFetch(url, displayMarketValueDetails);
}

function displayMarketValueDetails(data) {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = '';

    const header = document.createElement('h1');
    header.textContent = `Market Value History for ${data.name}`;
    resultsContainer.appendChild(header);

    const canvas = document.createElement('canvas');
    canvas.id = 'marketValueChart';
    resultsContainer.appendChild(canvas);

    

    // Create a table and a tbody element
    const table = document.createElement('table');
    table.className = 'market-value-table';
    const tbody = document.createElement('tbody');

    // Header row
    const headerRow = document.createElement('tr');
    headerRow.innerHTML = `<th>Date</th><th>Club</th><th>Market Value</th>`;
    tbody.appendChild(headerRow);

    // Data rows
    data.marketValueHistory.forEach(entry => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${entry.date}</td>
            <td>${entry.clubName}</td>
            <td>€${entry.marketValue.toLocaleString()}</td>
        `;
        tbody.appendChild(row);
    });

    table.appendChild(tbody);
    resultsContainer.appendChild(table);

    const rankingInfo = document.createElement('p');
    rankingInfo.className = 'ranking-info';
    rankingInfo.textContent = `Current Worldwide Ranking: ${data.ranking.Worldwide}`;
    resultsContainer.appendChild(rankingInfo);

    // Assuming you have included Chart.js in your project
    plotMarketValueHistory(data.marketValueHistory);
}

function plotMarketValueHistory(history) {
    const ctx = document.getElementById('marketValueChart').getContext('2d');
    const labels = history.map(item => item.date);
    const data = history.map(item => item.marketValue);

    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Market Value',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                data: data,
            }]
        },
        options: {}
    });
}


function returnToMainPage() {
    const resultsContainer = document.getElementById('results');
    const returnButton = document.getElementById('returnBtn');
    resultsContainer.innerHTML = '';
    displayResults(currentResultsSearch);
}
