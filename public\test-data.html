<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Analyse des Données</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 2rem 0;
        }
        
        .data-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .competition-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .competition-item:hover {
            background: #e3f2fd;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }
        
        .competition-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .competition-name {
            font-weight: bold;
            color: #007bff;
            font-size: 1.1rem;
        }
        
        .competition-id {
            background: #007bff;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .competition-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .detail-item {
            background: white;
            padding: 0.5rem;
            border-radius: 6px;
            border-left: 3px solid #28a745;
        }
        
        .detail-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }
        
        .detail-value {
            font-weight: bold;
            color: #495057;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="data-card">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-database text-primary"></i>
                        Analyse des Données - CompetitionData.js
                    </h1>
                    <p class="text-center text-muted mb-4">
                        Exploration des données actuelles du projet JBScout
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Statistiques générales -->
        <div class="row">
            <div class="col-12">
                <div class="data-card">
                    <h3 class="mb-3">
                        <i class="fas fa-chart-bar text-success"></i>
                        Statistiques Générales
                    </h3>
                    <div class="stats-grid" id="stats-grid">
                        <!-- Généré par JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Liste des compétitions -->
        <div class="row">
            <div class="col-12">
                <div class="data-card">
                    <h3 class="mb-3">
                        <i class="fas fa-list text-warning"></i>
                        Liste des Compétitions
                        <button class="btn btn-sm btn-outline-primary ms-2" onclick="toggleJsonView()">
                            <i class="fas fa-code"></i> Voir JSON
                        </button>
                    </h3>
                    <div id="competitions-list">
                        <!-- Généré par JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Vue JSON -->
        <div class="row" id="json-section" style="display: none;">
            <div class="col-12">
                <div class="data-card">
                    <h3 class="mb-3">
                        <i class="fas fa-code text-info"></i>
                        Données JSON Brutes
                    </h3>
                    <div class="json-viewer" id="json-viewer">
                        <!-- Généré par JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="Data/CompetitionData.js"></script>
    <script>
        // Initialisation
        document.addEventListener("DOMContentLoaded", function () {
            console.log("🚀 Initialisation de l'analyse des données");
            analyzeData();
        });
        
        function analyzeData() {
            try {
                console.log("📊 Données HierarchyData:", HierarchyData);
                
                // Générer les statistiques
                generateStats();
                
                // Générer la liste des compétitions
                generateCompetitionsList();
                
                // Préparer la vue JSON
                prepareJsonView();
                
                console.log("✅ Analyse terminée avec succès");
            } catch (error) {
                console.error("❌ Erreur lors de l'analyse:", error);
            }
        }
        
        function generateStats() {
            const data = HierarchyData;
            
            // Calculer les statistiques
            const totalCompetitions = data.length;
            const totalClubs = data.reduce((sum, comp) => sum + (parseInt(comp.clubs) || 0), 0);
            const totalPlayers = data.reduce((sum, comp) => sum + (comp.players || 0), 0);
            const continents = [...new Set(data.map(c => c.continent))];
            const countries = [...new Set(data.map(c => c.country))];
            
            // Générer le HTML des statistiques
            const statsHtml = `
                <div class="stat-card">
                    <div class="stat-number">${totalCompetitions}</div>
                    <div class="stat-label">Compétitions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalClubs}</div>
                    <div class="stat-label">Clubs Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalPlayers.toLocaleString()}</div>
                    <div class="stat-label">Joueurs Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${continents.length}</div>
                    <div class="stat-label">Continents</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${countries.length}</div>
                    <div class="stat-label">Pays</div>
                </div>
            `;
            
            document.getElementById('stats-grid').innerHTML = statsHtml;
        }
        
        function generateCompetitionsList() {
            const data = HierarchyData;
            let listHtml = '';
            
            data.forEach((competition, index) => {
                const totalValue = competition.totalMarketValue || 0;
                const meanValue = competition.meanMarketValue || 0;
                
                listHtml += `
                    <div class="competition-item">
                        <div class="competition-header">
                            <div class="competition-name">
                                <i class="fas fa-trophy text-warning me-2"></i>
                                ${competition.name}
                            </div>
                            <div class="competition-id">${competition.id}</div>
                        </div>
                        <div class="competition-details">
                            <div class="detail-item">
                                <div class="detail-label">Pays</div>
                                <div class="detail-value">${competition.country}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Continent</div>
                                <div class="detail-value">${competition.continent}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Clubs</div>
                                <div class="detail-value">${competition.clubs}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Joueurs</div>
                                <div class="detail-value">${competition.players?.toLocaleString() || 'N/A'}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Valeur Totale</div>
                                <div class="detail-value">${formatCurrency(totalValue)}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Valeur Moyenne</div>
                                <div class="detail-value">${formatCurrency(meanValue)}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('competitions-list').innerHTML = listHtml;
        }
        
        function prepareJsonView() {
            const jsonString = JSON.stringify(HierarchyData, null, 2);
            document.getElementById('json-viewer').textContent = jsonString;
        }
        
        function formatCurrency(value) {
            if (!value || value === 0) return 'N/A';
            
            if (value >= 1000000000) {
                return (value / 1000000000).toFixed(1) + 'B €';
            } else if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M €';
            } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K €';
            }
            return value.toLocaleString() + ' €';
        }
        
        function toggleJsonView() {
            const jsonSection = document.getElementById('json-section');
            if (jsonSection.style.display === 'none') {
                jsonSection.style.display = 'block';
                jsonSection.scrollIntoView({ behavior: 'smooth' });
            } else {
                jsonSection.style.display = 'none';
            }
        }
    </script>
</body>
</html>
