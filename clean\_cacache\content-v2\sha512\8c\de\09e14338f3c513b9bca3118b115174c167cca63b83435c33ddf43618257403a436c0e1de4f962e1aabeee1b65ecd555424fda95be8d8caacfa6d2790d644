{"name": "expo-notifications", "dist-tags": {"sdk-50": "0.27.6", "sdk-51": "0.28.3", "canary": "1.0.0-canary-20241008-90b13ad", "latest": "0.28.18"}, "versions": {"0.1.0-alpha.0": {"name": "expo-notifications", "version": "0.1.0-alpha.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "3dfc1715c75efca16cefc5b9660aee32317e2677", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.0.tgz", "fileCount": 201, "integrity": "sha512-iVofBoeBDjZTCY4O/XUuXDQOxzTqMYy9o3erkcV21hxuiwV7LzoagQ7PuhVe8yVeK29NH5hzRg/1i69terRUiw==", "signatures": [{"sig": "MEUCIAc+xrZMm7kvQ4CWV8KAXNBMkYwcS9jl7J6yeW7f+45LAiEAotKV6wTRW381o7TcXCF3Bs7qVg90MERWhoWy09HKZD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegg3nCRA9TVsSAnZWagAA2wkP/RPnl/8lKsNdrW3W5jiA\nSKyvooLy//luBRLz1WxuhI3Gfghlp5AyN4QAByPgIARIhXJ0+FX5I4eturIW\n3UrUFe8QjMgoc+AmyCUyV33klRPIvM9RYP2GY4hANTc48efO5rEltwNxOsLo\nA1Yx9YjXsnpZyvYCJkMcT2/ihtfX7yohhjupttJIr+So6Nzstr//BN5G3FZz\nAn7CIIwZm3FoM9LW22hr3yMAEkyUVilzz5dnbTHPRaBs3P6xYcy3wNjRIVJJ\nWSz2SCvunN5lzwDuzTP/RXEF/5m5rVxHyzcmoP3p+sSpNmcpgebVkO667XxV\n0x6LPlDvZd8FevmH+n7zoR0ddREKVApJfJ6VJ0baLcPtxvNYw+V7ylOFG6BV\nbTY/xHxIwvwYwmJX6ekPng4QfD4L7Wux3SAgLxIqXVj1+2BRXHLeLcEERt8q\nX0nEQAeDJNnUU7w3C2egBXEMK20f/XalhRexmTaDdp0mmCAkjH5ZnqdNHBpu\nUOYYQgyWFx4eZRN/KK73/CtqAiqmdd8hdG7EfNLWGdp5EVlBC6f5jAJh6QJA\ni3p8KM6APZYBnbiWxtR//ldmzp0XSxfPlR5VVpfcU7QnaA2SNzugnP0SrqyM\nygIxhKmLVAW27Rge+F8oLD318aw3CSaxWoSKaF97QrnXRnaQBrwqhP06Ec6r\nGXyP\r\n=jMxD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.1": {"name": "expo-notifications", "version": "0.1.0-alpha.1", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "adfbd0b096458197029818169234fe562f9fbdbb", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.1.tgz", "fileCount": 201, "integrity": "sha512-zn7nTMnWpezn9vS+iToCzIiwjRfl/emNdYS9diesgPLaYnLf3LO74hg7UlTyrkgX8FEXivRjNGnYQtUYzzoziQ==", "signatures": [{"sig": "MEQCIDq/PYXactqtVE/uHpFagrXtDlivhrWFdFlDf0IYiIUdAiBFDocQAjBUOAmp0w2QESYVMMfqjtA37YaZ/b0MSFXQqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeghTwCRA9TVsSAnZWagAA/dgQAIP0Op6f/TWhdcr4fANG\nZgo5ULMZWD5/0/JTqfKP1IRqVkOXBNN82uS15RriOsztN4EOKkRQKctm8R4W\n+lFKogdKz+s2YqbEzkXrQaA43iXqUoqhmGi8Ny5yFuVcTh0NLb5sLT983Fos\nVoMaFI5C6mCbOsdNYiWMiBkULSJYM1PmC6yoE7Ae872UG3DmjQNb6wj/igRS\neki2yzAUS1mhamLJ4MouOjYaVg5aVG22IB3VnAvlYsV9a6OpIzmaOnQ4vM1W\npqPZYupkY8v3Xannlsn0q4staWUiAFQfrm5HxNjnQQ/osvI2ACQspMtyRQuC\n8i32LAbqKk1ybfYLTvtog/4Xfzy5qROd8wvfxHGnOVxpZBAd8aLvAXxFNFyN\nhibvIyvtFGgt4YuzKbFZ0oXTj9etN4JfsjRiZIJbdROKHHBQ3kEjyj2FnzF2\nftC30DIuKeAJIytqIIECYUy0fmSBY1qz7sB+aWFekHs98MShUGDUi/vG4rAz\nW+G+4oGrJawKzen0F0vVopk+Q6xQzkYCCpDavErk4LAO9I4vToNwfZ08wnaB\nC0CGRK2TmaK5LDSXQCnRa/cpwJoJW0VkczZjkz1e97YQegdO0g9wn2GncAul\ncVTl2UahFI9Uts2OJdYjSpNln7/afjVLYg6Z6bG4E17dWCHpWJoXpUC23RiQ\nc7YA\r\n=SqIi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.2": {"name": "expo-notifications", "version": "0.1.0-alpha.2", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "5e2002b2343de2ce1faa8078a2a8f76dab216c35", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.2.tgz", "fileCount": 809, "integrity": "sha512-5w5ugXPSUPgtXYY1aLGheVMB71XRZjbISsDnONMKBjn2S+23lGlJzc9RthHiTA/VVIjRiZLWAgt3OtgZJrOajg==", "signatures": [{"sig": "MEQCIElVefaZR7r1r+HX/SUcbuQ5toMVpdmKz8RzA1FO5Up3AiAojohFtXznXIurSBEGLjRRgCs5RxejjQTu7HhHq11qtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2285716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeghdWCRA9TVsSAnZWagAAm6cQAJJS9fvxe4tza47dgy7S\n1niOBQEtG6AkSVHEvSJs8TUKbUnko4FNwiRciPFk0C/2UEwKx2+aRbp7d2Dz\ncSVuI7fvqydUx9CE4A6q6q51RxChPwGXVXQ9vM/qexBGuQYDNfMdGMNC+Qk8\nKa1BWMV5OOHXQN2ZnbIZziLOMkEI/Gi0ZZb3uddIlMksCzW2S3jv0fXj2Jsd\nkPJE9h9Lc5Bif1JAMuqxg5fIVYK+IPrSvsen6ZmiNh+WolGUvN0Xc2A2OzrC\n6eJ3REDIPargGOXvl/CA7z43an9CrhvQSEGxmQFktVUUSGLn8nu3lAud/j2t\nz5mzAwhm8L/ffBngDk6cBKv6lwTbI6KU3layxUzeXbbn6YH4+vMk8h9eMPHm\nFNATCyW1P31yWrd335XyY/I6IWN0V54QPxlWxpEhLb2PuhJC/f+1id0PdHme\nYIDGcfu/5bWdddk4pHXOoVQrnjCpHqiMu58WHgLOWJDLMeONvw6m06ghC+Bu\nKGMKnLylXBuDTDWtMUAhDsaVaao/QFVFXQOO+RaXVjR52dOzvF1eh2DMYmLt\nTs14zUZmyYSgqEO7uuyfbO6JCr/43CT28BvZ5YWXR/pyRGRm7LHRp350bjep\n8vrLQpaM8bOJxxJj0yRWKHyOTlbFMLOmp6lFP2/gZsPgtjU31QNrewdi7Z2z\nGU5j\r\n=OSeC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.3": {"name": "expo-notifications", "version": "0.1.0-alpha.3", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "abefb30e9c90205de98dc1055bc5efe10f0c47b2", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.3.tgz", "fileCount": 809, "integrity": "sha512-qgGItXWaL7KkAnHJcEgKTNiZelIYWgfMYQgXXyPhdyL39lh8ETxSyQUCB23Z5vCdarxZGv1PGdA22O+kpgGecw==", "signatures": [{"sig": "MEUCIQCEKCIC87YMwB9bwhqLhuudeGIjv41crpuTMP/Lgfqc5AIgL0MoOWEvON4B34KLcPN1qc5utmUUTTWlMUbW/X86F60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2286094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegh1tCRA9TVsSAnZWagAAvIYP/0z3okgd4z+z7+aZwJQc\nr/xM3elZsOWvq+HBfhw9fxAdEL3z7cbsSmS2gsv1yC/14Pvmw+/hFP4+x9jY\n0uuJlwhHIv5rZhNbZQYOHzx/l9TlJZpLng2etmeY2RVYXe4921tJI25y69Lq\nn6rklSng0yggdoQMoqCeAt9YzM7kUnkNHfOEhkf4JnPjeUmmvhXVe1DVnFTh\nstqWw5vZ/R/MwVUwebKzO/tCBYSNs3uQnehhRh9raeDfNnt+wC7w93e/bUK6\nRu7O4PzH04YNWxzlzSyftgOO8X6ER7jHgvlh5r/zHzXaa+k0/GpGNrThUw2E\n9e90qjPGjvUc2NxtWGe+UuFocHFiuXiAsDqmUIyGuK6XNj+s8QJW/0Oi6NvE\n5Bdo09IW6fKC55DBx+Rk90s+FdM/7UK7r7rtgqN5KnhFSig36k3m6iJhj4/p\nsG+TM+eeE6Fe67g0eduIp1zaKcez9kIVairP5lfDjIj8Mk6b6DL9Mbw4t8xp\nfGIWFP9w/H3EIfVLQsXTIOGX3U9M5n9jNlwwCFDvGnp5R6e39elERtSW3C7G\nEKkrviReDrOyNgKia3hCRJVu0C/VFmd5xtWz12Hf409QRwpOqvstheLloalJ\nGyHErVzPjIyfgKhAotvi0wqhzLya9i4neUb7inH+FUJw5TpVkgyiPokMuuUW\nowal\r\n=kSak\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.4": {"name": "expo-notifications", "version": "0.1.0-alpha.4", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "optionalDependencies": {"expo-application": "*"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "f76c652342dc7bd28f3bf6570342f84a6c3dc77a", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.4.tgz", "fileCount": 809, "integrity": "sha512-h+Zxw/YOpl5i9CJpdfpu9ZjzU5z9jlLx5uNzNRqWaXkun9ymPiR6YY6ItMqqm1XBuI556JF13b38jhRrnrClEQ==", "signatures": [{"sig": "MEUCIQDsQyNSdP3ixbxsO6LPJSFXnmnPQdqf0hDvAStTW8FDIgIgWvQBeAMxHMpgMXXZ1DT5UJ+7oKcY1TbEKU+ltARR534=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2286155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegihfCRA9TVsSAnZWagAANacP/iLcACxT+zNHCduCisd9\nnJAUJkhbkHDl6JOUkG81xdCbTnm7UfsDPbcktTCd+hVPGEkpRqZ0YZuJmJkW\ngcws5uhRazZQDqc/MUbPApSdJDzXd9j3FN+etcRPsGzdBRck6FJtO3ru4QZl\nkWcLRxX9agHXP7D4kDHnc0rh1l3cuvNS6znkiTO4feZru2T5w51cvoYnY+1g\nTwiUh9zgDEO4pMw6UPegculwDCBkmgavyD6yGbwxcTk2AcsUe9UQZi2aWded\nrqVUVhHvel4x4ixqLHfbi4YCoEmN2jasvA3/UTWtJBeGJ4w292SfI6/hUAz1\nzqwjSK+jkRw2fnJXNuNVg5eZPSdSsJqwh7ZQEcw3GAYFQbbF8Yp0JGYo6JeT\nf87K4ErsmWDhRq8SXk2Mlr8s7csExPKQOsZ1oDkOaZUlgxC3WRgc1izEpexR\nVavRYFyq/zhqcStyFm8JnpAc7kgW0Hunac9HjGQQGMMq0i26k8l9i/DM/LBB\nbdjx9i8ZKlDOtRrjEIysB2BqZhB3zVmDuMASPl6tOxv849Pw5JmMz3ZNFGsE\nKMNNNRUNyWu4bB2kCjrBTUNUgfth0BxIbGJj6JGHgvok693PtlqCA3MVp7X8\nrQ+yRHOvFFq1XRa8/5Jud8qh0moey63DQ5NerYvQVfR5VGO17wfC50FK5gcW\npQqA\r\n=N+pH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.5": {"name": "expo-notifications", "version": "0.1.0-alpha.5", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "peerDependencies": {"expo-constants": "*", "@unimodules/core": "*", "expo-application": "*", "unimodules-permissions-interface": "*"}, "dist": {"shasum": "7c040bc6fafd5cda22aeb3ed78cd3603a7782361", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.5.tgz", "fileCount": 809, "integrity": "sha512-0Cwo8FjYT2rchFTPzGDDBszP98duuGjaNuKGIUUh3vwDCGRy8GSxntG4W2hq5kkCtCwB8HnrIjnFw82nmJ8p6w==", "signatures": [{"sig": "MEQCIF80BaqwFkyZ2KA+GR/wUcPEV8/3bqjkk0/D77f6JGzqAiAHMnCfhso+1VNp7KBSnvjrfFDkgx/7JFNT0r9IsgXYkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2286347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegiuLCRA9TVsSAnZWagAAud8P/i1dlhx3mwR50HDLGDhw\ncMEMRCn4ZELo61gapdDUsn8uou1lxqI6Qocr2d88PZ5toU1u10q8kIHkoNYX\npNlPVgmeB37qgVfzYiS6VQln77HdM3Bh88DwGCRYZzpzh7I4gq5WK8uckJDo\n0qDBR/4GzEGSHMtdSKi84uyfy1cYH5EbIhZXY0QddoYdihYoqbpIDyqJQ1VH\nLNvS0Wb47UbekqPEbPLLOtZnj/01ty5qQmF0oPn0K5bciNtbjH2vieSfswE9\naMu365v8OWM19K8TC2tkvi/c586PuFIMwTmqA4W8aNcLHS24nUi1TD2O5O9y\n4i2W2Yy+GiOpMVNT2/wZMDtwTL5oXIl/3dNip4s7U1V8pmwP29fEm3bAbTz9\n+nP5Sm4iActeSVU83M+Nmg5ACrorVbIm6vPKKsgHwxWckQz3U/iM+1W+yTWJ\n1d6ewFfmKRnu6V//XmiYqxkc99CNMIU6g49xaN0aY+/JOeqq4VHKfF9cGEe8\nLokFDOvBI+sy/AR/o77PGGHklSufsh/TNZTHHBWSYxeIF5k4phN0a44FeUv3\nkZS6tloU9MSnD0s4zV9ZckUuxejdyCdlfO1JBcipGrMCExbppAoiZ3puz1+H\nRCRgxaHtAcHpP4VFGn0RBi54418X74nKub1xOc4im3wlD1b5vbPuKxcfq8l1\nxh05\r\n=wZAr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.6": {"name": "expo-notifications", "version": "0.1.0-alpha.6", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "peerDependencies": {"expo-constants": "*", "expo-application": "*"}, "dist": {"shasum": "6627b054f1b87f9e021aa3957fe51b0e846804cc", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.6.tgz", "fileCount": 809, "integrity": "sha512-agOqHK56t/vd0u4XS4H44GwCh7y18UtNlkckH4CfqHf/mJWzHQcwpt2lwVpSkl/uwSLwbsJAzLwHrvheiBpk/w==", "signatures": [{"sig": "MEUCIBZqjT59pBaKpqawsqzhWdKCCDg9f8jhMksLqtjXjlYmAiEA9AVCHkGw0HOpfTn+66Usx/AzTfKYSz1Rm6y0raUL0Fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2286644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegi6/CRA9TVsSAnZWagAAq8MP/j0XhlEUgXChKsLVDwSX\nhLkTU0UHXTwotBwU9a8Av2u9HoNqfEEWpS/wucRESensbGqGmQV/OfqGS8h5\nV1J3jmNCwuJX7c03+S/UbcIytp9MLeCe118zBwMQzoRAzt3FbO67RiPDwGJD\na/k7vKtrAAYZHUkGEopa+8zzdOliYRCKQfVLy3BD1W7PcVNE3VAxnjApeqkK\ntTR4lfirNntdazOjiMihbxA+fkRek3nc8czDPY/uXZxAjZTJQYGF/pQ7e6/l\nzJp7IR+IuM1/plhlfMmj5SHmdcwZL8ofv3+Cvalv5X+PKTDs0yPjOgtXjNa4\nSs+uzQbItwmh0rbgKKyHF303kuaauuDdfjLwVCisQ6YRbniLXa6sIWMYw3x5\n+wFr96ae2jgesa9yzensdR6vtjVo9TiB67yFMk8hxUTPnc/W5/LadvVaWfJ4\npoFHdlGQjWNMc6aeD/ICobtT0fJgR0PiuR+TT0cDiK8RpLrAzxaVCSUvTIzT\noDqbmIMfZwS38kbw1zUttx7p8jD3eT9Z7DOS4f0aFEvhPDY0xlFP0w/qUu32\n8xTJ/MCviyMVeGoI2G/c/htMAJ9GBbX4ucqsrwqhB0FPZGk4PL6YAFQG0FeI\neMceAj1BlfsgodOoGlsg5v9RBpBjwe5Yg2s/0/q35YCJ1K2OCfW9fHyKO+5T\nbG/L\r\n=hP2I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.7": {"name": "expo-notifications", "version": "0.1.0-alpha.7", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "peerDependencies": {"expo-constants": "*", "expo-application": "*"}, "dist": {"shasum": "2ee2ba98a633af978e5cf1fcb59907a3059d64be", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.7.tgz", "fileCount": 809, "integrity": "sha512-k9piYo+CUBRC9oXv1CdvYNp5QGG6B7sxXAKaLRDJzwV55rasmSd/HuH4CoXOCc3wkYzY6O4zG+ce3Uj1EtUhWQ==", "signatures": [{"sig": "MEQCICO4sQGstFzXzhFtNW4mfiHDz73eXYzcQFB5c21wMgGxAiADOMWVmCkjzG19zC8hbtaZXimw1qzdea5Q1THAYn4kAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2286799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegi/7CRA9TVsSAnZWagAAoA0P/A/9HAbZkLTfWkgyv+IZ\nVfBPSzqJVdUkgWS6N8N7eIyKCl/Z1Ul6J6XY3CJrgzTxUdVb/ra/KtyPB1Ch\n1CqYfhR/xpc2OLNFUrYDY86uUAIgiJSyK4kGhd9uhfOpja586CLQJLQT9UXy\nJMRwWmlTBNTkLVCWVpfBL9mJVSVFYj4j6aJ8o2cnpL7xmTYO+1HpKxYkQEkS\nLl7eY0jljpjc7c1GDEtb6eHQWNey7qcg/6orKjuPZlEgBcuf3QHuB2V+il/b\noEz8EEN7XDhKB8JmYNydPLEgZd+XTxNpSKHTe4OJAcRiJXDAgjGRIRBadmMo\nz5kGXE3gSw40azlP3Y67TplcsSAQdUzMKZGjw/8uIQnrNcire5npKswlVlIE\nuZhEl80E3VuDye5AQbArulk89RgSZCXQquV+OtBbu2DdCMn+w/PmOC+IEe4u\nHFcRnhmQsR5i5mwyrKpcNdfuO5IdWQrnE41drw/DGzrdrjJXQzVvaUz+f2Jg\nM0PtmhFtZY1b3gxNSdm9OrWzHy8guNEIzgnWQFNBNuYOrqTHo7p3/CiY2XHD\nNAZQpuQ57Y1BQ5Hb1re0iwNTLCCdthkgNe7yh8NhPWWBvoMnRnYDQxQMYzQJ\nbiJvcZ9iNZYa+1raHu4VHZRC/Um49ISWKfqLCzHZ9TfEjxn1IpM9ai65Mkdw\nOKt/\r\n=AD/M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-alpha.8": {"name": "expo-notifications", "version": "0.1.0-alpha.8", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "^2.1.0"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "d14599e6b2d2947ca48c2ce8ee91e689d93b1e09", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0-alpha.8.tgz", "fileCount": 809, "integrity": "sha512-+Ys09jWpcC/fQgyjxqbIerus3gZKtRtxNDzlqDvED5XNJc4khdvu2eBCDN7ATP8MjdA3Npm8iPG4mqtHQySj5A==", "signatures": [{"sig": "MEQCIB0qhMbTTQlmmkZXIlPTvOl3FpfcrOzrmiCvP0UCYQrXAiATKds6Oyljsu1EwtRS2u7+hsYr7CAVn+EQb5/gX/xAzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2285794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegjR+CRA9TVsSAnZWagAAkoEP/0yExnc9l7INE1uLsJHt\nhu6QNer716GdSV269lBtjd2klCzavcsPH9IyQKFXY+Nf5epxsJrfxqeQiwXx\n/Em3UOu50txhAkknT1gpuYINcLOsoXGx28NgeW2OmeygxXrKFLTM39NKD1Vi\nCIf2DtBSYUkxJX1ig0xFqb9+MYynYFttAALhr+PhJe5xNYL3NgNdEXrEwoou\nyGGgBGsj4q3UFIdN84iyBz5ZrnmAicb8vj6+bhIpMvu9LXkY36Oc448GINzR\nRF3LC3nYGvefXp27OBe6Ue6GDpqURCf9MLFpe2y162c40hR+Is3I0yFrylD4\nAxQiF3hVmpXc0KaNhB1cG6pazRZeoGxotWwmcFp5VFCsrewbBER6wcGGYSvb\nEgP2olEB2RYP8jlvJeD7w7gqv6/I/P4mDhwNtmGc2sNv12LpbCpCOVHE/IoD\nohzev43fTeI9LjYp2P5A/4paw5qwnEVvZLJVDJRhpIpag7whBPpc6VV+BNVT\n3462rcobIbkA69CLBR1xmXKuhSVJJ/Rmqg9PBoEfMg+BJOzFs2/hjJKjaY8p\nz3VZnOTB0H9/h5ynVPDANCSIGD6wftyxSJWc25xjGuwAy+CKcUGxptIBYWYm\nXtqo9tH07Cbt2CugO5qmtzTI5k7s/aZXWIPJJoAsVfUZ3hqM0eFqCVekqRZz\nDik9\r\n=K3Gp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "expo-notifications", "version": "0.1.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "^2.1.0", "expo-module-scripts": "~1.2.0"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "a2fbf117739005ad54e2448ccb25f7a5180abd01", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.0.tgz", "fileCount": 318, "integrity": "sha512-Bngejs/tMdf+I5F8LopFBfjXsApl9JMO90OLqHJvm0eTaY4Do27kh3+y1LnApl1ZFuETdHg6d70l6di9gAnkqQ==", "signatures": [{"sig": "MEQCIDEcyiM5+814V5OV73dECrIV3b9DgkzOPEUNLsUfp9LDAiAzLZ3DiGTk7I2M87bMpT4sAmRDnQ4pWf6L1IjWpy1zPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 504071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg53NCRA9TVsSAnZWagAAgwYP/0fYD7TZhFjEt9rWVtvc\nrZFDhV0Xk3lS9FyyOgPTFjfviwf0ZTQrz6GuXpJtwL0DbXHy4FsHD2rKitFW\nNcyfgFKj0t76kDq0xGGyVW8gSQWle5a4ytjne+7giVvcSYW5eq2o5Z/Sh4au\n8mvpw0gSLV29NTopS1qxJcOJdSGDcaxqb7HcO8Qqysaa3ze488o2vjCXy3xy\n0jFGVx82iABdEXhUR8l3bBAtgaydLIFNd7getvstH2VsSUyIp1/fRX+nd2IW\ncF2L+j2G2MHDuD7CmuDjuMUGlPUkWdrpfzDGnEC5wyr2MBujoVLvj5GPDUxq\nYkEuj60HB6mg6oCBBo0gk8ENtdqd4V3d2+AWcDlvqPCtKlRvngujExPUa/eI\nqqhqUhppB3UC14GXlZZazK3MDgQjSpp+Rv+jlFQ85p4CPEWGzzDW7mZpp1Tk\nTeqhYdlwNT1i5YFHmcLXF0ZdkNUcFGKZoj2QI7+Xzi9S2VrMOlUq0ji9D3f5\nJPt36QMV0v8iUsFNjGgOmHBJZf/GxafZHXEmAaoytpzJ7LuVzsEIyKEDuV8Z\nq+U1BH8os/mASQsuQUIV7PeTfUKdSe2pd8XfPHANDqLAgvQKgD2/EP9Ov/+I\nKDiGvqX/qRDJ1dXrdPAt+MpVKeG82wPEHXfovcWX5Xs5VhF+QiCIMD24H24F\nGxTC\r\n=6QLA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "expo-notifications", "version": "0.1.1", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "^2.1.0", "expo-module-scripts": "~1.2.0"}, "devDependencies": {"@types/uuid": "^3.4.7"}, "dist": {"shasum": "a4b81cb8d666fa9de15f4d1f9dba0c51800ac01a", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.1.tgz", "fileCount": 318, "integrity": "sha512-5ZM7naDoTvuEmjD2V75cjMwRxIE0zD+WKlJBMsaajq2pWn9nPCbPAPZBdj+j3zEQKg4n5xWBLk+eqd9+gAAMlQ==", "signatures": [{"sig": "MEYCIQCeAmrUm6Ce9kajg/VVPzAGUNsfmzRfRgZWdmLJEJFuwwIhAKjpcXt16a0MkSZ0pQJZdVV+fWJIomb6tetU0EQaUXc2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 504071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg56LCRA9TVsSAnZWagAACFoP/1hnyRK8oRz7xj2CHALv\n8/n5RCnGWvlmfv72sc7DoDlT0HjVy1RxxvjCV17mSO4fL18n4dbopR/mVMuk\nl6jEUTWopfGe9xDiL4wkqiGpoy56Gub9OSZUpa2ExbNkVyqzRmVG7Mw3z3gu\nUQdaPAF6IkYS/MofrDuziAM1SP3QDhLsA6Pr1WWc++v4n3xDOexD8Oe5dLAs\nqCe/Qintmfw/uZPgTyXZchs0EypmEMEoB7AIF21Sl6gv+Nn+uFbwCLcu4tun\ntwCLmWM++tdITBzhLxLMqbrhTuCDUVZ/hmQRVdP9EoZA10E7U2vO2SxIfewc\nIA/FDCvDM9J0B32jjbw9v/ID1OTjOzfhGi2LH/2I2WTZ9G3zKVj8rHifXi8+\njPbzijJdBwbCrK3MMYb0bPX0MDCJLaRXyrd05WvChb6HAerI5hzg0JdMIVDO\nYvQBhBtrTmToWK84PEcUGaW3JLpQzAE5qK4GAbPigdxcu9pFfSX5aqg51BtT\nJyxWsWVAUtQp2zauwO4d7pPwqy2GatPk7xONGXzbgjdP5pGY/x5LERjyEgrX\nJqdEDFUbEG8sPHGmxPj4E7UUPLHiObYMEVnCGCfzzhmvVwjbqnVBrHgB/GOA\nvUXUESPOYcOU3zSZ2DnW5FPVzArQbHWS+SvBmaLvUHpReO4OMhVF1QEaVY7M\nOgWE\r\n=yS+7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-alpha.0": {"name": "expo-notifications", "version": "0.1.2-alpha.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "^2.1.0"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "360de56ba35abd8bc9996ab79f52167d6b8954e3", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.2-alpha.0.tgz", "fileCount": 319, "integrity": "sha512-EoAYzIR4A0KYyrrUZ9INrXcOFGr87Fp3MmzX0iDAncT2CFzjc4FbGz4c2it/7EStOVGPfGaD62/yyEgwD3zggQ==", "signatures": [{"sig": "MEYCIQDUV2Mw/eVg2Y5SQ6WNYt5pkUKi6Sofh/9QBVrGN09DtgIhAOV8ImHM7zjt75VnOzSidVSL2XYgV9cl/0Fwgv8C0+tm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 508184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei08jCRA9TVsSAnZWagAAPZUQAKEaFvqBPVKq8FEBOzvA\nzvwpHkftDX6YDdmCVX31avfmJkzcGUJ4Ol8dbJCqYaEeS/kcbrr6YD+8AsyP\nuFIPnktwizLi9+tMxKJwweQA/AuQ5FG2kX8VIL1h1kdLp759wwPhQzBfnjK2\nlNHZpaFHNI2K/aYPdSsNiIFvGtVKogbtqpRKHRN/4jPGYGQNRt5kFrEHEESd\nvd2+oKYIJ1QxjCAyIAyXeh0+n0tRlrMBJ6ERe70bEcE5IG5iNhdlz8iXo6vl\nzD0exvTM34cuJ56YIIlInkG3T0ZKFr957+Cjnin6C/IPG6WlIsXgn6J/B03L\nWQhjjscss/KALqu5Zmhj99z+6jBb+7E6+NKWzhtmrI6Sr4XWq33c/l30okSz\nFmMXtfUw48gojW+DBbrH/8n5WlwhBhwQcUdR8GYKv6iLwafY+LuHffIfka2E\nFw8A0VIKGKpA49aMulxkGbxQCfFEEZL/Q5+HtVcPrsJ5xLGC0pG3yk4I/JMm\n3/GLKqiYavWV+pgjswkIZ4cCTrtsWNZTgbQO4ATnYFuzeIw6GxdvaMqJT3s8\ndr+KbT8wEVaJ+nUeBy/TmxCIQ7JOxnNxCcZmouiZwEmJyOsXVLyKC3ik/f+Q\nS1J6c6djD+VeIsVmlkT/MfA3yqNgYX9SIORj0/NpWHrXY80XfRs2nme3SVSi\noG7+\r\n=3YpZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "expo-notifications", "version": "0.1.2", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "~2.1.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "dba02d64f8269bb49713dff29e5c0739a754eb4f", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.2.tgz", "fileCount": 318, "integrity": "sha512-+zJbnkEHeX93cTc9VhwOGJBGprD4phstFaSR3J2mKhPnPKi4u4ezLzTcbMdmK62vnCJC/OE6VIlnSKGwbyEb8g==", "signatures": [{"sig": "MEUCIQDC/fk2V287wZFJCY7JWTNPpl9UT8Oz++7NvUYVJQg91AIgQL5qs2bN1B0qXkLiP7hSj9ev5oGdqywswbbvJEcCqRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 505485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenuAjCRA9TVsSAnZWagAAH7kP/3MZmkDVZrMHVyC3tbjQ\nbUFnDbT3/TYv5fyttlOhfiWlInjdsG6yURiQwK3RnxRYwdhgKOWIbFFmFF2T\nsSgziksWzkA4SK2XkpzU4lGHVCgPaXfctk3uUvzTT6I9M35zbXviHpXuJUjD\nVidRMo1h7O2mgmefHxe+3nBgz2XqGrVraCws3fl7lA6JJ9/pfuUHRRi6k0Zl\nJUn3T0MbyDCv8pqKigIP19AiQCcfWf94xcaPSwRb0LeyV/utRYjh0RA2PP+o\nrOCZXuDv55OxwH8CfzG2mIEN7JHh01RTS2tiymvQB6aI9TBNruqnH+cAybCO\nrKBkaletpal3YH9WSGKmqLRP+kMJ4TDG6zWo9pImpzpfWNY1tFPzlgYw7rzG\nKuN7fMbOn3a2H94UyzvLUXX07L9xbX0sAcdAdsjDpXjm+4Hj/JZJf+R8g3t0\n23ifEFQzxZWJsAH1EnhkO5qIeqJt2eUm1bzat4jASQqIXhUK60/o9anYNll2\n81qC7GyyXKOXj8Wd1A005jC80yfZfG5/i1lVaE8Ih6hRpL86A7MFco4ewSXe\nNWMowuQnDYcxMIx2sN/mltjnRIjXUy5PnTM8Ga56JdLj9snmeIIXsE1ILeUj\neLDHLKVSr5+/qYX9PzEBrJ6TjRk2+s6zGnvbaD9gc5zO7BTL7YrOi91eAzgY\npT9c\r\n=TO6N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.0": {"name": "expo-notifications", "version": "0.1.3-rc.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "~2.1.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "bcae82c0d144d90a4606506a8b2397c4fed69322", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.3-rc.0.tgz", "fileCount": 318, "integrity": "sha512-ZJKAmsPPyF2ZbF683xsBZESwFIoKKUbkTkgjnmG6lkVOfE+ewOCgl5hilxaHCbY1bBzaY91/Wa/SJO1zxQPpog==", "signatures": [{"sig": "MEUCIGftq9XXXiwrrcuS3ClylqvkgJ9xT9l5d0gJ3KWX2UK9AiEA2p8IjOB7lEQoY6gMo4mpSEV4SSNL2EWj0hVgD5IGMsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 506106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoDXBCRA9TVsSAnZWagAATicP/RJ6cTLCJvGl5AMuovhY\nF8slugM9RQQzQddOrZgyHpzmB2MPQoD0qzbUG5wfsIVM5tt8U/qmrtqe9Gx3\nRyE3TSXAZxSrTZEuwwO8DSakXw7j5upptOKw9fRRhHFsGCSsh6+THKkVIvQi\nFnbuLnak1sVWC58krNBAUkI5iaLAy7mUZkGcJf1c5iUrggn4qUC5BU4EwURS\nb5ANtdLC2eY5rxryh96EEfESEoV6hn+SvvLwRf6z0ipsNMou9j4ka83gNpX9\nrYadg5Wt3luYRAKcVyZY83PWocq/Pm7D/1fupAFdSUx+eKIQmvAnVyDZsqiq\nhpJcLjLPFT+47NYg2aO3rfMsNAOovKvFF4BL3G7IT8WgtcyxQHViDzQ8SUHx\nx0wICqubDpYdt8QB0fmSJ2JXqlKazUCUOMt92jqeuirxYnFvioqQACpJAcK3\n+IRqkh3OPG4G13ZW6H7NebZuQkvnmMBQRFaJ5UTt++rJpicxJm1x1FwLjWBc\niBXNOJwubJOX4TFmKqAxzCMKRJbO2cCJ6hNmNlCZ8aZlUDQ2TY7iRmZJLnQ4\ncwZt1L2aLvvqLOz+yRkzWIs+Xvmtl4kTJHmo/Kr6FITv2ChzbaCK5oQMnlUy\nR5Udb9KOdf3PZuSt+2PPWpZeE4xZ4X4SLOLdZngRq9/lV8BnJ9t0HHLY5wQu\n24g2\r\n=+Yl/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "expo-notifications", "version": "0.1.3", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "~2.1.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "d0ee5e94a25a5dbf758642cd6c77ad03616bcfd9", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.3.tgz", "fileCount": 319, "integrity": "sha512-HXKBHcHtaT9rw88bFN5SC46vOHXEi0NigKkpM7/gn4iTmMURVjnw03L5ixXEnAL3ZMBxStgXQe3K/kTyaPK+FA==", "signatures": [{"sig": "MEUCIQDIzrFwfaiaPe/syH3IXO56FwOkAToNU/J2fBnUsIRVhAIgKWs1Kb0yWyJs8kINTEynRelXdJX+3HVZWT/l46kXcHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 507995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqtHbCRA9TVsSAnZWagAAJIQP/31VjhN0PUk297M7tfIh\nwpnYt4wdha43q1bzg5DYafxKXb3eMaTtoA/okGhAMfajdPyfF2zaj80LhbmV\nGKjbjeUiRF7H6nwePaOi/kE1H5REarj7SL7IzyFSayiR8bFkT//2vQ4QhXOo\nUcUKyMaZ1nbdC1K7xWPQIgcHSRu9+m6rZlBQfCl9Mr3qOdnzj8HM5Zspsv5h\nUyWtBr8IJdSQGandwzcewQBL+EXgPnhpIEjAkbeWq6V5mjaMC+oo1htr9tpu\nBcKEcUGHXJwMehIGs5UIMgB/mHYpDJNr8+b9uKl1MzQtCj1zkbLkQlWgi9JJ\nwua7dqnt0CKPBMC4I00Hmr7LbwG6o0xPWgq1pG0DaDinDxqbOO5YbyQuRF2s\nhE6Wuh2Frj5u9M9h3iuRENAfIwGPZfnWuHQBHKJCbB36hWmD6JX6fkqAO+/n\nDXNoPj67cc0aV5xzz09drk9BmpZrUqYjANWBnilgZBYZUfUfxMHYWAzWc4vN\nI10eiq0uzbvmXj+67iqI1siz7xtZeOsuUL/8LgaRm3vEuLpeaCPmqYVzBHa5\n+kQC5gI+XY5uB7DZ1iMwR8YknkfDoQ/TAZmbCQHBDllcUet/XZfqhJeJGPkp\n78GvGIoUHG/8HK8IDvmioirGoqQvxeW0nFYNqeqoO/VaL9DJtV5gHdgLmXD1\n345F\r\n=RQDe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "expo-notifications", "version": "0.1.4", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "~2.1.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "4e2ce18f8e718c0fc9a9129a423f1cf7f7eff3bd", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.4.tgz", "fileCount": 319, "integrity": "sha512-y4pMuC9IKy6NLbUbfaGcERmSXFlomeB5cUA020k087TMmk4S/cvyFACFqZTI/JfFa5DXcYzSYmf7/UIkp3O0qA==", "signatures": [{"sig": "MEQCIBOI4uIA04Xe45AMSFQn9BlXEA8zF5NHUQ8ALNuwZlXyAiACicMI3L/KdPjT3JqeqiSy/2ZHN3tUT4mwG4kU6YcdyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 514785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJer/NJCRA9TVsSAnZWagAAf7wQAKGP8Tn594LKOerek6oH\nmA1a9TiP7TwzJV27SUibTlmw7+fcYYgqYY4AV/Dx8Q3PQhBezsXVrpVJpCz2\nsPsNzh0kQcEGLYk8ccGE3uTA1DUgkQl9MFyKTRaYifQFJ7B3ETBWdQiJXB1u\nvnmYZevsTuzL6Ye7CD1Mtkm1tXehRFn3zGUazaYVIdUwGFW2N2qGxU7us8vk\nvOkqF4KxXjCotcae3MyELNzK3ZXNeC9r8/HW75ofvw+ahak7PyyjOc9CkEXx\nSY356k3Oo4b5SVmuIAPAE+3EwsxUcFC3bBHQq4PF4yjOytWh5Ju8Uk1ZXBLZ\n8sQKiFq6BW0iJxqujqIuOjKbZbGGBSwgHWpkCro8hhaOfYHQpVDk9TFuB/iB\n8fIDBZQjEdEAjwqBjiJvLteHh1vEU19izKkPSeoUsDe9r8va3NhDZIyEbzQV\nnqNLua7D8x04j+jSl2YLqSSDC64lmVxNjBl0QGEzlSc4RCxY7wubd3OxFq4u\nXKxXojOgjR7OK/6IWdVNkY/S44HFBM3Zv0mN5Wh5L2Mp+VpHGijuD8Mp48pU\n2oyMEEGrPfF08xjV9IuVMzl/JwzUE/QymGYXtynwW8fkrJbsBsFc3ZILUg6D\ninYb0APM5KR90zsHB9AmRpM6QjZhnFm7la4/G5VB7uvs5Oa848XzIjbaSj4w\nzWQN\r\n=jan9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "expo-notifications", "version": "0.1.5", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "@unimodules/core": ">=5.1.1", "expo-application": "~2.1.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "0a407b3e0738799dfe899eccacdfcaccdc6c6aa8", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.5.tgz", "fileCount": 319, "integrity": "sha512-Reh5koJWNu1p3CbkzQALN6hyDA+7MSD+QoBBMbhYyvchb+IRPy/KFrRVIC4aZeutA0W2dkr6gHnxJfaJjekpaA==", "signatures": [{"sig": "MEUCIB1uh30ZEr1tUNwLwFEASCj2xBY5owyZysR/jXcwIepDAiEAqm8y+tSykROpzEcTA+5iI3/jMPeUD2wWlZtGUE6Z1ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 515850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesW2DCRA9TVsSAnZWagAAI4EP/jdJDmsgsukbYML+6VlL\nenyLl3HdH2b02AB8mAjOWQipje+M18G9u9QAzM0lC/G2dapkj8RVumPdAk9B\nyTG+01Og56ntoM1KwP+DAZom618UlvhwDqAN1QkmSZGhhn2DE4CFmAg3a1qY\n8pqX/lk3614KeMR8N0cSLRVMbTHx9u2fwYN0l4a/Gcj0vKNmNpcD9p6W7DeO\n2WTEqWSPtZTndUMX/9lTsilo35t+yCnvtRhTkuGF2MeySrFQvl1U1yzjoKQS\nQQwWIEFEa/UfXgtJWC9jSM1IKf4/jkOY/5beSLcW8ZknrtqEKd3yGd5rouSf\nffK25dMrarRbKwfRyMA0u1uWBLllUYanBQy8IOdd05pkiuoclj3KAAk3r+xQ\nsbebX18rcyye8z5J34c1Q719eM0tGufiFExTk+eCVk2jePS9FuLQbIGwlDFV\nBJ0YbyFqMZ3rfJ7YRLqPzck4UdG8pD0zgQft9A6Q+49FTQgeX139i5Bhgban\nXXFZ5TKmtvD8rtbFwBFXQnJ6PlQa9JzgfZAk1i+tt6w/h+wchv6R/YK4VdLN\naWoel1y7NwMLEh+IpgfgKrrt8v8a15krr7GpDu5U5xCtTux87U2ZMPenmkMv\n4g3Z/oawv0AFJhFRP9TU8I2NSCEuM0ItpO7z1o5IJOxw5oC0Ir9aI54v+/sM\nuNCx\r\n=5RxK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6": {"name": "expo-notifications", "version": "0.1.6", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "@unimodules/core": "~5.1.2", "expo-application": "~2.1.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "4d8ba070382eac5e9fc2482b8e7186a05cfc300b", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.6.tgz", "fileCount": 319, "integrity": "sha512-agxHAU9BTNMn/d3d43TiS4wHj6ZisNJw++lg+DL+NSiXc+WKWLq1J+4C/lpoAuXMgxMW/GTiRYJcubhWdhH+Qg==", "signatures": [{"sig": "MEYCIQD3B7HSYkFEvnvg1AJ2aITfdoFawjm6k7PLyXpxLoUbXgIhALkR/hXBtxcx8kYY4oHvgwscWKzFvB+G0IdObSUoBLET", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 516445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesZjGCRA9TVsSAnZWagAApjcP/1v+0B9NxgXucKl8A7Nt\n21fJuAA5ikZknZkxrPBCacMC9RuYtleo2CsAbrq82M7sn2r816ldCmmg6Etg\nCch7xcro6RlrUX3r5/Tq+Zovl7NhPl5IfZBPgQ0EgToZXp8o9e9uNKPIAORJ\nxx5TVPADW3a3Kn71N6sZE7DnBXXOF/9/EGz1iHO25fIac9PwdZHDm8AnmEoD\noibay3plwTttPVorikzuxMsNKGvcYtd3FYOV3rMJbqllSYn+1sFiI5TkZsJ9\ncRXhjpdwaAy79sE4OQw7Juz9EAhfw989Yx4fOVaGIxCb6Od/3YDsw9DzltaC\nxxFZPG62YfVeVqtDq4otw6GL/LtDyaY3/KyK9YcNR2wJGamN5YJ1nz5S1pd+\njQ78Ut//XoMuUxsS9yqqrKxfGeUKxh6PmFTy2T6H0TfBAa+y4YX14eMMm+km\n9aytKhqouZmKU9gp9zaapkclpgEuebHcU9jxhMZdtCajU9i9pfKEDS4/vFDo\niiLtJnINbY2+0KVD0vP5pwVeP4wMvLAJLNwFUxHRD4x1V8BkA3K9EfK+ZYwm\nM5bkn1nuackQ+dy8vKK38QSkNT124Zfyq2IHIYw5hkLN4eLcBIJXRHNUZtDB\nFwknriVYahvEhVv7gp/UtrK/O/D7afUdGrxbvTKGqS9vZXnHNnq1u/SfztyJ\nlKM3\r\n=fL5u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7": {"name": "expo-notifications", "version": "0.1.7", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "<10.0.0", "expo-application": "~2.1.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "a0a62376f4271f7cd1403c22749af0bb0d338ad1", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.1.7.tgz", "fileCount": 319, "integrity": "sha512-yWGoCWQjQMVSMIsbhwR9pq/voAMTCgof+A7swV5uQM0X0WcfkqJMsB6Y5XMksdpkm+vEhvxRFyw0NXDso6pcjg==", "signatures": [{"sig": "MEYCIQDHhciOYSJ1HJLSWKjANwrjMLv/L0tkyfgrmZMGhxM72gIhAPprGnetDnm+N07Y473inDc/8bpSb+MdqDaCfstbDck9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 516996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesajECRA9TVsSAnZWagAAqXsP/2zpTYPMU7iE77x87gce\n28W5Z3ykO8t/R+hppW99mWIcJxIbgJQlgwpDEHlw6iPZUMPUdr4Mai1PAAS/\nHjIyeVDruuSsMoiF+r5z6avwsOjaa1oGs4+uOh+p1+R5TZ/g6XG1L2L7XftT\nFUshulBRDeZ3jOfG6QEh7L41G+46DXQWqFsRUDCGEpII4oK4+uJTA83sFK9h\nq05FRPq5dCH7lkonCAUw28dJr5lHj7Z5S8NLXosUwgYbYGCTJ6KE1eQOJmD3\nUwz2V44SQgiMu8Ma+oC1/KIReS8yqbEBLkzNT3IHoFPg9bIaEtWgJPDy+LD7\nxjwEdcWwOf+g1NpSy/i22AvgZfpGwCdxC86kLtJ2Ey1IvGsDJn7Hqzpqci7v\ngkq3usQCRaLWthf3fYBQE4Pewa2cbLKGyGLJpIjAkcibE7Ohttlltu/Hq8cC\nvKeobfmXNmE+WpSCtSCJmoYj0G6H+AfZR/NzssfF+geacQvUpZvG6QwHVR6e\naQ9s2iDn1MBNAgaVCenHI1dCuMY1S0YFVpjvQ1L3WYkSl7eLW0Q5YyPJ8kKt\n0f6I8zPKLwEISa0XkWWJrzW1K48jVpzQOFJSWRp4ztpRmx1jSQAU9bhywiGV\nEPPhdqr46fQp5sLinEg4N6/4EuFD0cuyGtw9wBJaOEM4KwnR4ZTZREdJrZMk\nHwCw\r\n=EeYq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "expo-notifications", "version": "0.2.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.0", "expo-application": "~2.2.0"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "3bc624d9dd066e1529bdc3f91d51649f0c5a4ccf", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.2.0.tgz", "fileCount": 320, "integrity": "sha512-Za7D4btHnDdxwtmj0JX7R7YINO1d62Wt8Js+XizOCg0bFh59lx+FNMxTioWfm4v/3eTuuQ8WFduDKUOLgzOqkg==", "signatures": [{"sig": "MEYCIQCMq6aRMAzRs1qPdWxBhTMj3gU/9z5XgNVsGZiBVh2SuQIhAMSQY3NiO1eX99jXIVqcBYKsCio9haJ/VaE4spArLKcA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 547981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezo4NCRA9TVsSAnZWagAAxk8QAJf6kx05X2c//s2xRAv4\nhbwmVqMyhSATGEcrbfjTlYHqsacg5Lr0m9b7YEpRKPiByXqntvYUMPFGZ4Vt\nIxbp/Cqyew27FxgRn8Ktpop3YXQn9DYVZjb0oaKvDIuOZQINS9LkF6iSC5BH\n/fqroMRPB2hcW+zJiRFWOwK2ioHjR2fWE6EuMp7y40Zxf3x7ms+78V310GEm\nVgvDEvRd1oxmsnN2vnStnwbuJhq74QmsbdnruEYzLrSnsxdIR1XugM0qzWn+\nD7DCH+BSswmJCPCazmm92zmkTtxv/xplQrf5d7P2M0xQSzV2ldlGPzlE6T4/\nOxAiKhnH+G5FF0KYOxQYruB68uFEowzgs8tcL+b7HGQi3qkMgER78eznsevq\ndvLrYZdRC6qqJhmq1F40Xeg9FZNZwXoqM2Tch3qZ50X/NvqlJuIOJzBYQyom\niFBKIxzRFgnAWfBE0QznXzgN6vzMoSY/QzLSQlgICh1uKHf9TjEJRHyop+ql\ngg3iGijs+sCdGpHf6Itilswv4SYobEdoZ4Mej+kyqAfHcQ3UxXa3DVKKk+FN\nwbWHAmmtiRbQMD2LtOoGP5agUW+r3nCZVyfnivjQPvZFPHUX3ob12uCk14Wv\n3fQFBxZN3BzP9M+ORE1m9rIhvLmPcenxngbTR2i/k6g0XRfPBbOwoz6g9P3G\nAwrB\r\n=nLUt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.0": {"name": "expo-notifications", "version": "0.3.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.1", "expo-application": "~2.2.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "ced62886492c35f1de9787f8642c91635df7f1ff", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.3.0.tgz", "fileCount": 324, "integrity": "sha512-NkVwjOlh0011ii1HASfgzaZdsEMDkDV+QuK2iMrLfmg34TCTdz3ISOxpAee77N5HyepyNUi9CIzIyQjHjxLETg==", "signatures": [{"sig": "MEUCIQCrPkCObc1xZBcSXnFFz9RvE2l2OuHVdxaAdobbTomgdwIgFbf3Q68UGFZBW/V7m5G9H5qypOT4O3ISNsVNIki+F9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 558041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0AKVCRA9TVsSAnZWagAAkHAP/R7lNmSIO0OmPfLs8mdM\nM+nlXAe+5XZrQJUeaQCByL2qaevTKJ0Ip2V8zaBOkSkjSq3c6mMQjheA/QdD\nu+NtnDtkFf+Sp0FttQ7/1ZAOlhA+1Y7e3aZk8JUi0Ul+liLRU0PqTiityKGK\nFmiG6SpUI35/0/XjwndDx3oA0JQ8+YAwhIbM2KglWSqV9ivk5g2uOdLOOlo2\nJ+uIHqSDl/lfJ9fK+Yrl85MYn8GPynLc1HQ47B/Pn9EnulcgZtHknMCgvBfA\nVnJQ8eSKz9KFOVz6JZ2/oS6Po/wUDTZAoaRtc0SbttjATVblSDAbA6WGuQ5T\nDZXwLnyc0m/hDBWMm2eCe8W0Qhtit9+wXHrvfqqvS6Jw4nK7ZY67y32k0u/C\nKbztjtDKMJY+F4tg/MMi4o7rT3KkDs2W4hnG3V4UTISM2OhzzOrmKQeas68j\nRFpv2203z6u30JSOamy71en+UP50jpvESBJL5Ebl0WFkmjo/dn57UFd2h5rv\nXDViyN6rQeWP8ieMGO2KsrsxpUmjTN2L55c67qUjK2cpLp2++fq1fWDcHUcQ\ncAmntkBeePLrG4kLiQNsuLbhOM+ykKHPRWjMSkDP5Hxz8p9oUlBsgNVXva4W\nR0vuoJwjLPbbGdkjqS5x8HcGNNZT8AAJbRzR5aJokeKJjQ+nXEOp8Q4dVUiW\nXJIP\r\n=5qEY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.1": {"name": "expo-notifications", "version": "0.3.1", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.1", "expo-application": "~2.2.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "5b8ac3c5211ddf0833539f1b3458f16f00b194b9", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.3.1.tgz", "fileCount": 324, "integrity": "sha512-KRwI5+i37V6HjHHfMZnNbx/Z+dtzxwTHXiuPaDfAzWwWdPmVZ+nQcmTt/M4xHfouoNi0OZgCQvldeIIdaMpXBg==", "signatures": [{"sig": "MEYCIQCPUyITmpraDS7qD7sZS8yX1g6SHFIv+XGGwvJ+BHBszgIhANlkryiVrF8ZXWJnfZNTbz+nJER3FWOK9gcKwJiYegVc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe19i1CRA9TVsSAnZWagAAx2EP/jW05tGC92N6yeoY/Rm9\nbSdL6qpIAv5v8UXiFbj3XhfniXkyhOElOwIxFtEKnJdFk7VL3Q1mA9+37Dki\n+9sXNA6mJ15KBDhPbc2YECesCXwaTRhl2rx/xKUFxvC3VSfNfSne8oxY3KCt\n20ydzMFNlsU3KKzQXF6U/gyU0+iA8fIJbd9uEr1mYGZYizmuqjtl7C89vTWj\nQT4nMYCbTyN2d8a70HXTFRYJ9rDDdo1Puu3A7dP7cx9/YF7nXbpqFLXhZ4wp\nf2X+0sglKMaX2I3ed/CTqv7E7Psgb8oibdxgDtt28RoMJj4g9i5bFRYRoTYS\nYEb8LOfk41hL3L9Zbz8hCESOHL06yfLTWVf2uwVcsEAFY6Hu3258fwsfWrRd\nEqLCypzncuPTWmQ63g9Xg6a5lmRHOziRMbZ1pVFCNEWLzKCI5hbQp93dKjor\nt51j+4fChCRmQ2OlScD9wUS8poFLrDWcQdaVgyl8XZ6xAQ3iAHRsQdkGqBo+\nkc2DAkjYglemRPO+aKtxMNO9CTsuPFnEK82IiFlAKL8+ob5GF+sSA1rKYyFT\n6MJJZHq4ArYWhTFKZjPFCAh/SADqctf5+uUgqlhpLCMzB5okLL4GX005zxex\nQLpW33VT+wGJQue8MkbqGyDPiMqtYa7t88EViL9MR9nfJ8SyuFHLOVko8UnD\nZ4mb\r\n=exnS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.2": {"name": "expo-notifications", "version": "0.3.2", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.1", "expo-application": "~2.2.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "ffac63ff814e9fa5fd1e51d8a0aa49c08cf2e860", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.3.2.tgz", "fileCount": 324, "integrity": "sha512-4hnQu5/rCDztXH06gsqHVKETcBWRAiRFm96frt+jh4yUfTCEOYPE1mLd3SVmcrNG0TTgVg6L+FVajtq1BtPkZw==", "signatures": [{"sig": "MEQCIGa4zDtdXtE9IYcE96CTgfI/XglUGt/zBxtZpQ5nWO6uAiBHXHJGgbll96MtulehbSd60e7ssblWFc1LStPT7gBP2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4MPPCRA9TVsSAnZWagAA7WIQAJbMnmzkrz5E0547Zi8K\n+R9Gs/Gq4vLAcXVs1gYtp375Z1OizCu9B7NF+F5SqUk/WpW/MMJVynB4SME1\nAhd2XFZl70EY6OPyjXwpvN8Yf2cwPeo/xp0okhTGQ0Wv7c+JL22Oo2KI7J4y\nrtZZ2ODCNlvMQOgYknmiwqRtpZdHCqeC7stTiWoi38D8enDE0dQFBr8+IWzn\nK8G0uLCdqDUoIKa8y7be2luiBmeaFxqtA2JMMloSr6cDnujEYyeRzudXhCw+\nFPrkZPScMR/yLZo6wS9uzA4OLHDofdeUNeS5v71dmwkc8t/Is2Gl+H7cfV17\nbiFBw0WLtACl/QeoD3Wq7Onb1Rv2ylNxKMkDv6z12ooov8rlxfSijI1aFo1G\nYiQS+vXS4j85S4FGgH/vr58x/RbO7t1oGo0fuIYVpT2PxKgQxBI4csECmR4k\nj3MymVKIwWtBcUJm+vmxYr9s7/1gN/CqP6Oy2lPGbZFvmqTxbxUcV9uOWjjV\nGjp7LDA0pIicDmZ3PSEboS4HwmeMy39m7hjCtQKQ3Sf1c33bTd/b1uDnGNBF\nnE9ViCyT/ni498GNKkzgoqofKtya3a6HQRLrxXLDD3DniBYkBPz6liy5fCA2\nQgmnUXD9KFR5CjS4HZoO6RiIK4Q0a1qUajTDHgEKN48d5WikJhZ3JGxiSDXX\nBdZA\r\n=TB2X\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.0": {"name": "expo-notifications", "version": "0.4.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.1", "expo-application": "~2.2.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "32e2949ebad528463932f53c8983ab07e8c7ae4d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.4.0.tgz", "fileCount": 385, "integrity": "sha512-x9vvRo/S5ui0GSEUkICa5TXRuhpAD4FeJPxs34oYtopprQsoHprobg5MPd5wPDzbxY/lRUeNceRcdYSS6FqzTA==", "signatures": [{"sig": "MEUCIQDloLAl1QLEGsUIxT/i41AfieHn4jKia0swMhZa5uPxhAIgaRL5MpcvnREtTe1OW5F2oNjaej5I/KV3JG1toJp6YiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 587989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe88qoCRA9TVsSAnZWagAAOGsP/jyKGqkDdeQGvk6WS+Yi\nzRudAR1FCCP2+bhJ27v1Xqrk2bLfeBS+T14DIFM3Ll8HgTZjhI8nnuK1hZCm\n9kSOaJsR1YloxbNfsUYYJmid2qpsGZVmGV6PQ3LvA5OOGmvllkolllF3Qr+o\nfmRWVLiwIDz8vvYwornnokfKOFzMSCBl4Uzt4GLsx2RNj8Mayo/QyugdFwDH\ne6h+iaQHuLG5o4+lmVUpNj5h/Os5iMJt7eCEPFsuhCVOUCA2IoXDXW4pHzRa\nN9Uol1Jx3sBdO5/a91gKeBbxDesYMO7WPIotWw8ou8IFElCdcLphonvZZz7b\nWnVW9kbD/kWP07ZA+h9FsmUnd05ZWhUC2OBFu4NtRkoo0dOGBV9nB/5TZt9K\nbVbMhzIgKoJqIoz6lp/r1NS6IlKzAhj+iNGUFepeYH3qEH1VNCCcMgXiTXQn\nb594XxY0BrvrEZOlXTOtSYIUjyApyV+4R+gs2SXt/yisnVucGihsaFiG9BmW\nJQ09w8JzcZBOY6UuqsbzyhWKFaiHPROLk1fNhXz2O1uNBkHLOODJfV3AyDD+\n+Eo4zUS6QCDp8TcMB78Tprr7z09LJrF8WbAN1uWVY1UmSEgDlSpFwS/q5ntV\nATt4gWleheOnUqYh99uRX/P+TYjhhmgz8UzQLDbBlNLGLsWnyMUY0vtrvLzY\n2YRL\r\n=xtNx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.3": {"name": "expo-notifications", "version": "0.3.3", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.1", "expo-application": "~2.2.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "3c7b7275516028853e02f5c796e3365d4fb572ad", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.3.3.tgz", "fileCount": 385, "integrity": "sha512-iBqvlbB6UVkU5yF4W9K/sqdPIbJ52VVeL7543G4IA3ARWhx6wdhfzcuGTCtUgV0XmkJu7vmPs+sC31aXjRa0Kg==", "signatures": [{"sig": "MEYCIQC81mze1GuUsUR+m+NWi9l0hr2VLX0SFMZYL3Xf7TxQtQIhAOxYOrVgdcAx4KmXsyTovf8bxfMXiBh13bv5h6v5ms1s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 587278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe89EgCRA9TVsSAnZWagAAIgIP/3LLjE4mPnjUcZMkIUaI\nJO666bV3X7cXJZKig7NdWgcRCNh6inxoupYE/NRXQEWb7WSmNmMTwlq/z88H\nGejv7SIUCEP6WOVcGE9fm/xvgl3sVnk/mP/1l4c8lDQK0uqTZCd/orov+e2a\nwD9zKku712+gselA+pLFeAXhhp/1/kPacp4iBe+5i47My87hrlBf1YLLhdm+\ntz8WZnV7o5mxG0ncX/HvTetl6UWTde4NY9DOfbmHJojadmUwJPG2UUSL7LPB\nQZkGNdD79u5WYNReVCM9dB48r5vLarpy6DA9udzmB+gR4HY9QDrx78vkUkMK\npyR5hwppAgKHkAbxrrDUTfodpfuapjxCN2u1+F0DSI6tbyHZZzFoUOH7+Efw\n9O9kDCYJXYi6TUlrFx5ZChjpxlnZ3HTEvMm3U5mwpKyahYDj74IaR3jRcYWw\np9ifVpnmIHsQuVkE/s953vU1v0xUd9UlVrZfpAZXyIJxlrtbtDcwRChLjjxP\nzTLJRe2j3w+abOpeHkLvpVxOY//0LVzrx208bOQ1dEIgmZNgIikynCEv4LaC\ng0ir58vGrEivdHLlktz2YwaOrQyLkA/QGgvRLSi6MG9/IEA0uGT8VYc584LA\nJrJYKSpH1Agnx19hxE7++1vMJjGDaubQLbu8jtysjtRH0EODJT7NRgNwa5PU\nRoVJ\r\n=9+KM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.0": {"name": "expo-notifications", "version": "0.5.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.1", "expo-application": "~2.2.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "c3b6a681d6db2992be87a8cf252d44675bed81b5", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.5.0.tgz", "fileCount": 394, "integrity": "sha512-kiBErnMQRQ9G5N+d5IqnDs2LRHcv4ZZNAvjTj6HhxUYV6vywOg4Fjrvik/4uidazeMxmqC1BkdghQx3kx6p1yw==", "signatures": [{"sig": "MEQCIDPMwoZiG+3H22uQVyOyJYauDiJNBB9Bvp1QFVVxuCXSAiAGcNX6sjcqqGBsTEzzAwDHSl5EizfAF7IZSiAmmAffCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 609079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHn52CRA9TVsSAnZWagAAwF8P/jAub0ypXrF6ygF6qv8D\neaXGNJXXoG+8OxlXXaXl3gkPKluDOAPnD6Ec1X3rYuIpN4uhNzEQuK5JtRDw\nyfZAdfyXgXkfgzux+yUSj5TWOSFEp1UIdGRmjxIwTuxA9tDGtcT1WhdeeKdg\nREWjv/fjm1ZtjcF7bbV5vlctSx9bMptRWeu24mCjtlcW/+Vby1s/+Uo5+5h3\n536Or1wQmqBfQoVRJtybOS5AifxGtWud3zTuvtvBELrKqKM0J12NH9VU40tD\nNKhy6gQ6fEA64LDCIpHTnkdlb7Y2+nM2+QRSu/GfVycRc5QdD9+QI9UIw/PL\nwBEsqUb1OmOVG++FGOwpmRukY42GlvZzLJBjEwLo6SXMtD1UrbfdcX/7MfgQ\n9dAH5eEb3HZY/2mvNaWwj5XEuetrNAK62ur06leRWI1m7G34969AH+3ku//Z\nkjom0qcBjrW8Nabt210tqJaeWyRQnRsvp1hZV8LRVElGullnnTejFpFbeqxO\nUT6nlKdfgydl19DTIuo82oWGC3TiU3t1z03BwqXr4zBPxDxqf3oX0+xbt3n+\n82IBU5ZIZmtjtIvONk5n6QV8vGir2JSs2ukJHt7O/0Vd+HYQ1vtzfQqSyzsV\nZPixlC9MYc9VErYFNiyHQoRMRohZ961f0YY+Ml6np2o60Si2siMs3MIVRMot\nY8zX\r\n=71NN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.0": {"name": "expo-notifications", "version": "0.6.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.1.1", "expo-application": "~2.2.1"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "be6785f24398f04181515171af8898acaa88c838", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.6.0.tgz", "fileCount": 429, "integrity": "sha512-dNtzmqk7iyUNKTEC+BPLt3QwATYljDulHApGmKyNTgd9B4OYdLaRh+FOJyAziJ/jL8IaYMPJO1PHd0YErx76YA==", "signatures": [{"sig": "MEQCIHnZQ2LkVJGVZ1WEhlrpjgVdrBW0IUPFLa+QYS0WfpviAiB+E4jwXIRjla8Bc1e17NN6V3maIr7+TtKXunz36nWZwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 689803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIYHYCRA9TVsSAnZWagAA9f8P/jsnevlSqzssLp5Ux4W9\nBFu2u1OXqbFCrcIc/7fOFdC+nAklEqF/94c8izm9b4QCbN83bR+mreEMqCoM\nW8gvRaCLUsGS/N1yWRYMFSm/7PZVvNpd70J3TeJS0/BMsOwpjLsGTU4Jma6h\nXdWY9Ypw7IJCVVjhP2bZPLxi1/5LPlmnD5a/lQrYQWuzo+qWTVK1dvkUEAeP\nSSxJSSznKqNJkSaS0iL4nuVUoHNRSjhHbxY83n1k8cB5CBdb3fz8TwduGWT2\na39mgwTS+ZrgHyIah/j8jnP2uS5PXvrFQDfwbVryOr1QFMhpw8FG3O687KXC\nopmEPx3jtm9HZsOSaT9bz48+zZRoQSOH5wXVtWQDGHRvgCYo0BGxaWZeCevn\nEQd4l1h6DPrGqzL1+j9akU3H7YK8CIX14QhDhG4bSycUanO6nh1W7CglrsMR\nQdrhBMIog0PXqR54AjgFKtnBaUNRcgXMB0XJqIkTDXP0/5OpBF8+YSAyQO4v\nhIOrf+NF/sqqJtXyrSVy2Cnkiw6J1OzPL03fVGx926dpVPK1nqe0M0c/s3QO\n44yOCIyg8u9NBOQqCYaMnkAW55YJN2OJTjWhx9IN7E+qBeJ1LVNlRgxJpcD0\ndTp10ncbt4L6yYvsH2i13Smcjt0+cuApSj/V1By7dTUyCgY+WfZklPL6FhWw\nTWUa\r\n=1k/i\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.0": {"name": "expo-notifications", "version": "0.7.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.2.0", "expo-application": "~2.3.0"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "7b2ae47b1af219e6ec73c01b86e59c7cd4b791d4", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.7.0.tgz", "fileCount": 435, "integrity": "sha512-+XfRsmhWeXnYW8HcRYRcCRxjiBjow6bwW9tQRTfjsAmBBy7tdZSstwW3jQxIPWOIXP8QAyYRLRIFUMLrwhlKBQ==", "signatures": [{"sig": "MEUCICZDBAfAizQhvV+2SMFBoRgzND/ltv920MWCa/PvMxjmAiEAo1c1opg6SLQ7Rw281iJYs5KMxTXDmXD9H+J0Og+GjO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 690173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfO+jOCRA9TVsSAnZWagAAWC8P/RWfT/x8sGLWVgLPwY33\ndFlVojcRR3vfsC7Q/Ks25fGIo9epnVNgnQqDBFSH3vA5qt12ybVrx6e6Okp6\nAh6XG7UZvYB2t1Gir3+XoqZSQvqt+uNWLzQVrfZs47be71VIus0ilUV5Pgma\nnl9Ep0YiX6b8DFfHkXW8HwVOv/yoaSDhTtVWqx4s+Y0QQb0MVKTDkTaNSTca\n7rHh9Kmx5rgcn5DdmSeBJb8U/oiLy+lgqhvOqWJG4BundtHaRMtJ5gdvLr15\nIZsV1PEkLpnwuWF28X/xR/oNF6AqVc9S7cs3Juno6vx9AMOvkCh5H+47OCf8\nsn0qkU9j5inv4SPo4HPk/WvW6nl8SvgRNGlXA5/BEY2/4D4fDBoWztAKBzQp\n4R9ws8wJfjL1sCPh/SkuXDJKyhWsrcPBzcoqrk19PlmyssgAkSDzAyFqdOjq\nG0yfRtOxG1Dy9w2EwnUGa1lLUegUy0iS7rL8BidTDIVYi8i6N6NICJWQXFe5\nzH7SfWkwGJgV4Ng0fs9YaqxNPC7UreOR1GNtnsHUiT66oqUX1/ONzLH/uW6M\nUOO8bn0Wjb1EFOOuFYlPcGjPFRPRcPg4C3vg/loGBs3Af9ii9NGIJtHqi27s\njHmHk8a/M0w5HWuPZOXoa5bHW36L2e6lIQC8D6tUlKjcknNrm2DrLkRE9Tek\n57Ns\r\n=5vjE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.1": {"name": "expo-notifications", "version": "0.7.1", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.2.0", "expo-application": "~2.3.0"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "ac0eb7ad87dbe945cdac90c75bd8bd15c826aa78", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.7.1.tgz", "fileCount": 435, "integrity": "sha512-A1eqSwbvIfOt/N5vOQOEDs2nr3jSEaY1VY7f0CqBiXgk13wG5UTrNemozU/Lrgca/vEUs+nHp2s7Cz35w4tGUA==", "signatures": [{"sig": "MEUCIQDA4NtCgHkvt/RAphPiXW6RLlrkhbIQ9Wv0uVXp0brIygIgF44XF+76wDZ6UillZCmdvKD8PoSCi5jArqGd/9JjboM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 688132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRi/3CRA9TVsSAnZWagAA5FYP/2EYpoEMSe9JKoSkrDlf\nawItzMdSPZQ7LygzFqmTdmo/hu7ncZ2B7l+jrtMgmCsmZiY2sinZ+jO1pehh\nu2oUDCtWs0uyRRTtmgyfQ8XVVfmz0b56MBbj4XFWdHtHMG5E4zTgiOkj/PVa\n3rpbFtevJptQ1OKpoVoWSMaBd8XfRrp97Gv2ZR56/B6rZoQgLSXn3gvL2Mv7\nPvk5FybFo/kkgrF5hkE3Y0sNe+bO2QH4hQSAXt8zA5lQhUfcZZpX19ZJ8SsC\n/6gpNKIlP7Vb3B9dmH5oAC4GzEB9ctA7m7O+XO8hv7/aJq1lLZ0xwxvqDzW1\nUNBy7f//hNWshqbkrQ9ArsfwUm1sVmXGRDYxwei8w7ks42iUOdBXR3zUpoLu\n/FUWAIFE0/tNyepfbzVHo7rtLPE1wbwH8mNH+LCFbRo42G0lZ5d385lgtO0P\noFfR6Vp+aDZ1zSicB1NhE5D8ls31zkYsv6BN4hMvMPwXlekzE7z2m42VAgHy\nh8kiBRxjFVGT021VePCqQi2bL4p8i5HEEY8iNq8EYbYXxft13KMn5kJyaM6S\n9nluApDiVrRebirZ+gyHav+SzprzY6F0YxCtiflK7tzI1beKnY4k1aljYf4s\nDUkA4ZDuJuoKJTDp8pUl5Sa+D8NhZbM4dWZtLF1RztWHZLGTVNGTpFtgLd6h\nRz7f\r\n=x8VV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.2": {"name": "expo-notifications", "version": "0.7.2", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.2.0", "expo-application": "~2.3.0"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "2e275036c4f8cc6223a4de263117828bf253a95f", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.7.2.tgz", "fileCount": 435, "integrity": "sha512-a5QagTFObadEyWTDhAeHnzscLIUTIBF+iLsfKftegZjQIcv20mxu9Aom/e6OfMNg9470XV/HuuHaO9p+JaY9cg==", "signatures": [{"sig": "MEUCIQD03gCYRaXGGRtdyS50OzXFzZoxexnid1FKw93C+V6kkAIgCZx2NLhoolv1LreynSUHcY2BDSn0xUINOmqv5JcyVpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 688080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYxo3CRA9TVsSAnZWagAAGqgQAJ/QDBZ5ruf0Fjlg2mSf\nNlYBnv4j/QOCEXX+s7gA7axn9KQxF4L7msUzCmI3COjZhWmtweqHGtNGqcQj\nttGkNiVkowIKretusZad99HnWG1h5cjQrYaLAX14CmDUDgmnsTrqfCofIy/z\nn8jJwUJsnZL7JmU0whLnCkHUiH1GluE7vj3/jsr3qGCLmZ5PK/SkTwcmDf0t\n416L6D+/Tv3DGhJsaFJ/BNTF8+yec/gOE4XvBvPk2k5djGZRM00kh7HzFbmC\nXtaphybk8sOunG/0ocO+FRw8m3YgHi9FXaXRPFKNDAXFHaMoz3Fh6hmEDUg3\nYuxU9tp+pOchl/qB5UgrbkrAQ5W/Fv6glFGW61e6CFagVA4tsPZ9IU2FHS2W\nGMv8DBDATPDavclN8kTY2NEiOpqLRli80tcD86UnMmqD8Ai9WUY8c9zDg9gY\nSnjNSSSAFbc9vHjG+1V6+NTQjfoDO0dvVLmNu6FoCgotllf8K1WARSSPJuGu\nA6Xe6v7TjIByUbbvMhRVH8VPV3s3EZHfaZn3kC2iz0Ow3YXsShFCfXZIrzNv\nNq4eHirKb9KH452O0rB+mlD7DhdC0UQe2cYsOdJ9PPvB9Yh8iCIk9KjBb027\nZ+Eh9yTeScpGA4Qikze7kIM5qN+oTgrMocgyMCJ7YAJEDSp8rDQm+n062KY9\nMv7U\r\n=4gG6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.0": {"name": "expo-notifications", "version": "0.8.0", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "expo-constants": "9.3.0", "expo-application": "~2.4.0"}, "devDependencies": {"@types/uuid": "^3.4.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "a63fb9068c2b8e0a1baab2079e69854cb5460c27", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.8.0.tgz", "fileCount": 446, "integrity": "sha512-DvV+2UUmDiPl+DyA3ea4iegLZYWkSYqaMBWqDhTVkSG14savQhdpxWgUTwY2CsWlB7e3VlBKcpwEebrceq07Kg==", "signatures": [{"sig": "MEUCIDlZFY6EmLXRcqFOnPu4vgV+kJzBPyyNPnVO1vRqpBV5AiEAkjOg+JGJitIGcSnu0tdR5/fvVvsdYi1XzgnEcKqpueE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 733589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfs1i6CRA9TVsSAnZWagAAMccQAIwnUJUcsedYnZEM+xxH\nZg6HFYFdXrmeTHzAsT8RbK/J2MZVRdVm5QOkIUoxTm7CClcnYkYFpEu6iuSm\nVOZFpFqM02j3HkJcM5s8xRCViKkLKdjUJ6Cz7l1Tq5hjYSKUbylUm3Zf0ege\nMiJaheWU6E/WYILgG5a2o5ygGI9cgcxTN044iEIMzifkoEquI0guPbhxa214\nQ07mydc4XO9mOdxTcdS1WpEzLMHIj1/1GsAMCa/SyAiyjOS0ozXQ5WlDz/oY\ni2FcJqr8hsYBm4C1BrdRsBkWoqyEvmdqsrWJj5AzYRFHiVzswJlZUVFls1B+\nuIjYfZ5MnqvkSFypnT+O8lZJ4di1pShT18p9BhlFmQCm+uGK/bwKZz6wGcx1\naxgVFKcN3F2mrEsmcsoz0YkkYQ/fF2n9YeFR1BjlpludcH25yFEfyjLYjOTV\nfJqN6DL3EiVLeI3AZzTeZnTP+Pda2Y/zM4OQfujaEqp+z9ef9xnae6nVmOZJ\ni73I1ieuQjb/FqSxSobWsxt5I7HXSB3wbmy7COZSxyfaWAp454VMSxtx3YDd\nHUCaamgORpKn/i46xOhzn7Mmmw4nsWPyysuX1JRmGGzOv6xE3/lP5CEluYhf\noDjwfaJq5216GIfLgmwGZKUDk/ShSWDYZmrCmjPYvCmrCz8EwPm5BfchnQ2t\nwH4e\r\n=fpn4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.1": {"name": "expo-notifications", "version": "0.8.1", "dependencies": {"uuid": "^3.4.0", "badgin": "^1.1.5", "@ide/backoff": "^1.0.0", "expo-constants": "9.3.1", "abort-controller": "^3.0.0", "expo-application": "~2.4.1"}, "devDependencies": {"node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "f495f78df0ae21103d41879be6f94f2cfc07ca1f", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.8.1.tgz", "fileCount": 455, "integrity": "sha512-QGhP4Y/iTNbwvgsjdkoE2iQRbXcWLC9WPVWTLSkPt434Ot01M/XFoZtlLgeZyjiOm+/26IORUJus3vNG32KzEA==", "signatures": [{"sig": "MEUCIE+BsOi9ZtQ8qav5J3vBwXHeMZh6wqGDIyCM5TL67TSvAiEAqt2punwpYhmhO+Ba2Mz7+QG908k6oOOB5mQSGcw6ev0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 783479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvuevCRA9TVsSAnZWagAAH5YP/R6GN3AjyPao6M/5F2jh\n0+go4xu+VK+xhYvceSUKWOnekHtNzDZxSZ/K5pNR61lZKdDRuarq9iS2dAQs\nE2ZU1Yx20ZDiyrYfkSxajxDKZ7SObZJqtCyVg7qIMSb/Rzpg3mEtaBv/+dkW\n15Y+W7juumb7g4p1KirMDS0zWxkVLtAWkghhPgvANrFa1Cz3jeJDbVc3IdRx\nzsrkWYmPPvg3eMI5wRslXkckAOef73mViXG4dfygTPZV7Juu8rOcafXZCfX5\nCTyu67oeDdguylVmsKMgAhMJoyubNUXddaalK7Or8X0iup+F3VewSOaMUn6c\nGhvYqob5AHsR3SwIs4FJFPZX422l6b52LGmslRw46/yNewifFUFf1pbN9X0/\nIQunR3DLDzawi4hL7oLuV8ncisn4rFYxb6raVSiXzdVbPQjvIesnqaVf1HjE\ng0GO87ZGIzGexOdAXdu5vyrHflFMc83p+F8QOO6m37aD7zJTpFh4UHY5COQe\nqk+GLmSg+EpV0fvMdDbTzK1Dpxjdqs39exkI1FWd+Ty2c34TrPb1Ovm8RiEL\nk1NeStiQDO38f96zwxMGZ7BQ2ilObEWzRjJQai/sBXYYZbvNjSi2GAWI2B6Y\nKSC2vgfpMn7pCCFE7nMc4nEBmhjrpANa2fZ5MCa8DAN5p7JC84Aw6Gp8rPnX\nEGGM\r\n=bl/k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.2": {"name": "expo-notifications", "version": "0.8.2", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "@ide/backoff": "^1.0.0", "expo-constants": "9.3.1", "abort-controller": "^3.0.0", "expo-application": "~2.4.1"}, "devDependencies": {"node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "69e04a4e48ec6bafaeb354d284fbc23c26f2d62d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.8.2.tgz", "fileCount": 455, "integrity": "sha512-eX/HB96FqXzSMAwtA/fhxB1tGYELQywPm7oBTfnALHH3MFHy1bW7NZYGcU82sDF+DF09uLZ4Fn4p5ValMWA5TA==", "signatures": [{"sig": "MEUCIGnKK0Gnl4aRwHP8cO6wJ2MPiLUzYUWRWNVsk26NwTFFAiEAx5RbZcNL/5O9LLXG1P669GsWfZJhCwZ9QtqrGh/ttdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 783742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxW4yCRA9TVsSAnZWagAAgvUP/RzuFo95x3nohWCxn7Qt\nMNHAtsFZ8RgYw2hvc3b3BDng55g12HRCLc5A+woY1HcLrFvv5/IgdDAFgK3Z\nbg0sLfb9KvYrEst8IjpZZiyeyZ46Ig4zn73mC1MJNS9bj5OH/mJj6cAY1ksH\n7yEuLAdkAzJdN5p+O3zQKsR+1mZ7AsWdTw7XvS71642LEeRQ+jZadwPA7wHb\n0ooDCfirJ1sIdT1XRULf1jIyhK9CcNp4UDSpUUGXtEwHPSa7sBEzN5HKLEH0\nuwwTlZzM775taTBqLWsLHK1hgOBStGNzPefZyoRAGgdlDRnPzapRkSTDl/qp\n0XzHxWDv8IMnJUDH7TLaQ76dwT9gKP9Wrn6ZsJg1XX7qjqeEGcK+n90VLrqL\npotNmzVlNvKXkXMV6FpHz7hai2sa5rNkBONR1dAnGkw3E2miAA/jFtHvcjP+\n8vwl0TXj9ldn3mY7eDGSwzz9EvFZoHOwPOxFY3HoKdGBnNX0f8yLDfX/MkS4\nCRpEjL6FZvlGJA0sOvpAhA/BhkRTaulYmYF9XJEwUcIVeRirAc0XCInEfU0M\nHL2inQCEx9UKS22MHunJZTlBGYQRd1BEDfT1KG6cTw563hajctDJ5Zq2DoEC\n0u13MNxuA5Q+fgS9jQEPjt+iAEA3BCvYHWYwQLpkFqx6p2494/byRkMdoxQH\nXYXY\r\n=EMv6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.0": {"name": "expo-notifications", "version": "0.9.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.0.0", "abort-controller": "^3.0.0", "expo-application": "~3.0.0", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.14"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "~1.2.0"}, "dist": {"shasum": "ea1ba4574284c6096fc0cb40967be9ad727ced75", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.9.0.tgz", "fileCount": 472, "integrity": "sha512-xOOQNOXVlns9aHc4nV0IEIg6PvkVudWb6CDbVN7HkRx1X+Mkts9ZHtlF2nb/c86JLBCA8z28EPiSz1R2RlkePQ==", "signatures": [{"sig": "MEQCIBe4AEI8dKTUMuxHrHPfDF7+sNArD3VBVayfE3e0eervAiB6Q5DiuEe3lUbjmGDaU8R1dhV2mZ4M9DhEQPhfhhvnEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2945283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAgr/CRA9TVsSAnZWagAAwcsP/RvXbKxONJEU2M+K4qH2\nVutBvMqXrx0tFMZg1+bqEkNe0iXRPSSf5f/UqQt5PlFYUOi0pvrbWpyOnqSb\nB9+kuFiEvt5o/vOgyq7/M7eE1lawmv0O4D5dwJrM3e9iTBeuYxmi55lO0yHw\n8DJndIQsh31KgFUo0aBdamvHnwEz5aH9jaLoDQcwsp0Joj3qcHZ2YqCVbRan\nx/UV+2e2+SBkJHT0Qsv/gsvPZh6O2WwD9up6ksvxAMMMAU5Ii45EX0n7PZeP\n1lHkQ4amDgneYJlxRb2Yd6x5bwow7G5auH6xZvTNQs1ZA87SUyX293/WV+Gr\nVzS+e75Aazgrv232lOD20zIpWpk/ZYtHQSz7mP+5Q9N5dCpBcE6xa22poqIi\nZsk5TwAccUzH187IKfWUfxsEec6JEnx9X56UCXgbA67sXNDj7Fh5gZ0om7sx\nrhlAuaudBuJPCANy14Kfc5MtK9vC4a1QZMRNn8RU5+ZPTtWeQzXXzgL4rSms\nfHCwbsYPmajSRf7qy8lguk0p46PWlugzAj9Gq//Pga6x+oKSjnbr9rto4zVZ\nKWGB41VY9e8hLi9TWjn5iK2suqryTMbXgvUdlaEbMr0/THBN5kqCrYSnmWtP\n32i7PZGLbcJyFHJi++OXznLWCPdyg/9yqBjQ1cDWW+qHKRNW8smJxTQaP76V\nyjlT\r\n=jpTk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.10.0": {"name": "expo-notifications", "version": "0.10.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.0.1", "abort-controller": "^3.0.0", "expo-application": "~3.0.0", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "3c5fa13438d42167576a3b4f9524885687b263b5", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.10.0.tgz", "fileCount": 473, "integrity": "sha512-UCqodiOUWCsdJ6NZauDXzRlRb6ts16g51Zx0lcFwB6qqINYoBBwnop5BlmLmxGCcQ+wvF21iVP4c+pasDzLXzQ==", "signatures": [{"sig": "MEUCIQC6CVzx8K42ywSUQzGnMJ6Jt8zkzAiF6gyuV1ykwaATwAIgblr0r4N7Uw0WE32ShbelAwrsJiCgRrc3hBz29NeOnNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2971911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP/MZCRA9TVsSAnZWagAA7HkQAJie1qgdZ4d0QkgPj6+T\nBGO+YYnwWfLlB4ZZ7z24U2TRoBHzq9T4lBPxfwwI5+xLBk3Xp+ZKQpf5gVob\nCHkuG+Xu45PUJQiiNFLWOJDdaII/rNn6KMz7oTnkd7MqI3ru3dBfKiVb1zwq\nBx4sHIlaGG1OQ+ouqUTXOxBOtdKZpEBnC3/M+TBvyVZZ6u/FNVP0Ik+EFXNy\nKjHWKsGCk5hSuDjOsafj91O1dLLm2yhLMpOOfZaS2/g36rImt6L/lZIP+Ptp\ndQedmvITjG9OnAp8hNCyqW0JjdevQ5jeJSI2RWg+FHE6SELIwBpZbJgNvYjm\n6U53LkWk22Ljs6bGe8fh1+CD/ScQN7ZAGCR1xbaYXP0v84QzTSbR5HE/urRn\nEP9r1qmz2+vWItN2HzCRUJqwYBrm52nF6Vm/L6XlCkome4GguB42lu7OUufW\n7q3Fh6zxyFXC6QRcLWtVnmYBf+v46wXo26hJqdY5v/3xDI3f0cCa3i5UIyqd\nMMJUx4mitgckqz/7DBtM5RynN0IvNL1yhUHxnzx29ujKYPOwXwC2Tb6p7cLQ\n8WsyPssRVVBMeTMpmgtBC0h5KlD37+o9g3Cqw8sxrdvC4S/t5TD9DdqwaR+r\n1zV5FynNBHeG+K06ME15hSoK3S5M2tpu+BnwGcqHFn/OgREITNWeNgz3g/nu\nUyv6\r\n=XyWb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.11.0": {"name": "expo-notifications", "version": "0.11.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.1.0", "abort-controller": "^3.0.0", "expo-application": "~3.1.0", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "b0b0f6128923287eae5408678739d0e2b0fb2314", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.11.0.tgz", "fileCount": 473, "integrity": "sha512-0im4LWF2kCWQ/RFm0wwhJA7SNE8msMj4TPcF3ufj7IAu8stW21T9qkZvgksvrX66kUvlD53YhDoGZEJ3UwJJ0A==", "signatures": [{"sig": "MEQCIFf0pi7PvcxbEqNWNIz/JckRGTOOOz4qyNH+nsQYAEu6AiB6UhUmJG1mAO4/PgjZQdu8e8pO29MzIhnWwjzEpz9Rew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2975917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSCPgCRA9TVsSAnZWagAAWfcP/1x87TIYFkNSEfDLBNDo\nV9xSTmNv/NgWV5NChRVEmGv+V7cr1U5x2XasGCA73aR3dVh6gWBAGNsnZhBs\nq8GPQgUbVs6FuRAC870WWHdzFdHM3I9ankT19O3ErFjbemypWaLwzJh48owu\ny4ES1OE29An1SJB/itz5NSG1jOVDcs2vmWhAV5y3mH9lgFG1SAkfVrUv+HNP\npYUHk/ZksZH7cZi8sdJZazt57Gs+HA9IKNzajzFp+pOyctKNTtcSqc0b2IPp\nIq17k96o1rOgHyXvURMVcP/R7+/gQU7FJvDm6SaP4BKN0lNfB0VnxF9zpOrk\nAdhkqN2obs4Q5CrgBcCYAkHDsCApyAG4kPYSkSYD/ZAKuorzQxGPQfQGdMW5\nnbOGRWBa18WouvCbN3lmkdqbsAiRsJX63Qh37ADA57EhLyAp+S69SUZC3qzW\nlaoh/w0n8aEO3q+gmRsW8ossvOhCPBhRPR2x+tzwxWCLBaMVSNjjHLaAnjkO\nEcFrKoy/DeE6uxEsQGODv9dpnT8F8WaHcpw7Ugeh6ZqMdVsHEC7DMvOtcKHv\nHAkEw2PsPf76IrraqArhJ1GmbaCpqhTts05NOOr0+mQXE/+CfPyKBMKZ+ciC\nbyBxDw11YgIbC41L/s/wffMFa5fpFoheMGsahS4uvZd8Uz/eBTLiCsr7EoCI\nUU90\r\n=B6Ky\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.11.1": {"name": "expo-notifications", "version": "0.11.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.1.1", "abort-controller": "^3.0.0", "expo-application": "~3.1.0", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "cd43807c6e83b2a84895fed081e607c74b3fb877", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.11.1.tgz", "fileCount": 473, "integrity": "sha512-KLyD5SN/aEKN74bZ2MOaUnMmDTNyle2QzXBY1qWZGZVEHLEMD7WvBQtnxvjNCnwbNurwB0cfg9QZsVz3YewOPw==", "signatures": [{"sig": "MEUCIA5pv761eWrLBUkLkU0ip7CuikAZ9IYDE2vBYDY/UjRIAiEAjsUkdcePqEThdEqPTPHnMXJcRPuPtBnhFB/bis59jEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2978424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWW/ACRA9TVsSAnZWagAAE5AP/Rr0tu8PpWe7d8RsP+Sk\ne6aHFjNr87EktTn69qqvv8gHK7yiL0DEOpIQkH0PIZaKDXAErKbi3eR6YYBa\nNIxdzUlmzWUc4U//fa+41nBRfnE9XRvGX3dCGb0sChf6MIiMNGBk/yscUSnf\nN74ixGNJgRQu2E4nIklBkXH0vQXse1L+PLjP7TdqlZY+XmyQr5kkT79i0vFt\n8Zd5zarG6fvKaZUTNNPAA+taVUoYWM3sAkb8hoGMk4fynFcvvobxDGKtwQbg\nSHJuS1waAt+PutluQgSw03aBMyTUdfDY8sh1BNRGR6W9Opkt1GOgVhWwJIfq\ngYCoTCgF/UM9EjcqGaypi/4+x1aHCo8hb3yeSvB1P+FqURwrvtFVxq0V1Zpz\nWB9Qy5crSSfxz2CJGVOfmAKWexGfTry8V8stK6R9tYusnhurWheK9r58oYFq\nCMLd9/iGYjMEFelFzzjoPz7KqAQz1QlkRUKBoZPF84ZNJ9SV3NQ/s08wUdH4\nIqvPUz8kU6pfOh+yevhDOJSqy8lSRX7VHWkkxRC1Pg0q8BeBk9WoEt3qUAAO\nu5X/yrcbHacs1bCRYGwuLba3wbKu0h0Rm00EeA+7Y+lgQCzltKjaj7JErHmJ\nrrR5dcHKhRkC1i/srfDO67ifOqPccJPSXPAbHmNAqwm8XnBt/sc97D+dMfpr\nXOUY\r\n=j6R2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.11.2": {"name": "expo-notifications", "version": "0.11.2", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.1.1", "abort-controller": "^3.0.0", "expo-application": "~3.1.0", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "9b11614730b9db644f53107dc72e4dd69d2c76a3", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.11.2.tgz", "fileCount": 473, "integrity": "sha512-RDgaU+pSx6TsTxU7UDyU6/UsV7fyiPgjgspM1c3UGCQQrYD19kAdEEdVMEltntnM0mCUOZqRqZK0Jb21y0C3lA==", "signatures": [{"sig": "MEUCIHh8Kg1F4qtLh2Owrmc+Em7rms5uM8CO1iH+mPHJBOrpAiEAo62EIPPEn1mMLlyo3oJXZ+VIERMdhc7rFsCVWpDEYeo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2978838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgY1v4CRA9TVsSAnZWagAA0GMP/ifzQkPUGvet0+G1QZs9\nRV4Ly5n3fvoJwwpAJWUrul5kOZWOwlu2K4qofqCVrdfzsK0hGX/Upa74TTvp\nyuVSfF7KdiPfffIdA+ueU1sh8oH90+fS3JuYBrNJvfnesNP9l8qme5xrr5ci\nKM33CaR5vff8lTclG4zgvVrv5DIPskrfIrHOXpvHySpofJT4kITWIsmtRUVi\ngTKDqD+viusyWtMMruedGnEsSWso7oiqy9DrQWlXrrE+BjZFjKXMTbrEfnJB\niwA4tCVkJH8cGYLJ/jCm/JguWetpTIkhhk9RocE9yJRPy/lcM9YHEt47TSpe\niSlBNJSrjSkoV4/FaTQhCH8lCF4+TquY8gb6CtyCmPPq3EU9dziJRFDRHaS5\nyhPCnBLQG2Vr7MTRougRR1/Gj5tWqyueov8FDJLSv5CI9ySN8AJ7HaN+8DZY\nFG0XxD7H0Y0OufRrcWpEnDsEQckZSYtBCpbLcM5BxAfJIqh0mxqZJhCVvUxy\nnrRELHqS91dOzEy89pwxK2xCCPFl8jCNk7w4dRO0AerRKO3M3P1PAPnZ1vFN\nIMx3n7E8p8yvq4NYCfPLIgh10ohp2laDSBY/QcWhCB1446SYqfYtNS70Qlvc\n6zguYjfAz1PaxOw6CDk7A995Omf1G2n1UH/CnhGOJGhYR6eWkxOBTEETTn16\nzSgL\r\n=/EFN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.11.3": {"name": "expo-notifications", "version": "0.11.3", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.1.1", "abort-controller": "^3.0.0", "expo-application": "~3.1.0", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18", "unimodules-permissions-interface": "~6.1.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "6ceb5e739b5b7a957d249557275e16715d0ea235", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.11.3.tgz", "fileCount": 473, "integrity": "sha512-3IZg4Pe6cbeb+ct+6d+6LcbtWtd7N+DOjchx00KfVf1FWo6254e0CeRvlNXAPbKGdBvE0ByqT4kSMqcbuUkSfQ==", "signatures": [{"sig": "MEYCIQDp142jn6+XO/AcoBKuo9XyCzWNA/Pc6+HHOMVVqgafaAIhAOqnTnMt37K7K7dqlBzdMyp5yt8xPlopDcKV12EvP5lt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2978974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZLS9CRA9TVsSAnZWagAA/icP+QFUgS/o/qxKFRarUkhw\nj++XY2gFQcf/v872vquwRhGz+KK4pXb4pdL+F8Priulm2v1F2E8G9nlc+fs2\nG4IrbAJ093Iy5MINzx+FuBlbI7CpgtJsROme39yl4UcBaqa9ikXMPARecHJP\nGEhqjN/uVRvETyQatVLn47Ot8dEUFltd/U2qqey9TBEJ2j8RPdY5KqY+Yy/I\n/VWGuvN352Ab4oilxd1n7Y44/xEtJn86M5db6MtWHAFk5qG+oZKsJHgAaoCD\nsrJhuXUEMDcIbjoY1UA5A3C3J2sfdxBzAkoWZEUbDc5832FQ/adrnddvxzbp\nNPJT6Ea3UvwRoaIycfTenXIe8v5QfSHcMvhzoGUoPikVIVkPVQpug+VCYtMm\ns1TfLNBhHcCFzFdwreLTVaHOF/FqgdyJDO4ECqfO3HFfgRNELEDpKz9Ncg/V\ngytN0+T2luvgAoJUaJJ7g0WXqfg1FK9KbW+VbIrmyVGgD7CCvwATSc5FiD1P\nPuxfCFLVEOj2Uaz9SxfuoMlk21CMU6xZjNAB94xxvTIMRpFt/PY+FvDa1/KD\nmhZJw0ylKtVe0VucBQfAK0W9Dt3EIqlcECTc8PCGcRhdgWIgVvJuC/UE0jcV\nP/cus5AT/wIEa8MGjkmI0pS3Y0Hp7SghBSYl2v6wpBOpnx+dvA/yB0s0RWBf\nZqTH\r\n=Eqw4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.11.4": {"name": "expo-notifications", "version": "0.11.4", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.1.2", "abort-controller": "^3.0.0", "expo-application": "~3.1.1", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18", "unimodules-permissions-interface": "~6.1.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "4098ee99c088ef788a07c5b3c0c1b31071478837", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.11.4.tgz", "fileCount": 473, "integrity": "sha512-esmVLsuyvJI5pcws3MFi+2fBnV2+UMiVrnmKmRHeytepuQrfM8hQlA65GB8sasHPivZ0q8LC/MqCwmdp69eeMw==", "signatures": [{"sig": "MEUCIQC2b2MYSOCXoOyI71H2SiSoeLcNtwfmPedlGvA8McECCQIgQ+mffDwk69ikeanDPrEZtIws6NVEKCpm98Ov3xq/+rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2979236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcNfsCRA9TVsSAnZWagAAduAP/2JhEYF+hwfNXnSv28as\nOOhOLWoFoklVJCTRPCT9goIPBuxzHQkAibz6MDQU21w4sz9JUPRciVXbmaFc\nMm9YL28vAfQl+8f23vFyqffonpE1PAMWZ636ZGilPY4aSldGKG2OEmUDMlnN\nKkVhPQXa7oiWuJDZK1oPZL+OYjU1wqMjPGypM98Q9m3xxJ7kG4RTtXVb0OGe\nNb9MfhUcX9ZqC/r/DS/CiBWT5ecBJa08AvhyoqcStpdQ9JM4th4NEssa/gn7\nrc7YbLkcPymdpGBjegM4+7B9EOeiBg2Bh8dWcQMVAQM36HuhjsCsRB1eTBFE\nAGhOl3AfN4icRtgUBsuETxuspHUsRDm5VUAOE/3A2hcwjXole/ZGQ7a6ttCC\n3r3K97ZBz8qM0yuYdo8GWEPCXk+8cHSyjYe10piV2m4jTlCwiiI8vgpgidwM\nnEPpYaFagIlaibyJYjjhWYdkvqpQp4qVds7DnAkvOm7NwqeMefLT03VVEj6a\nHS4SXHbprbaqaY+DT/pH0BVJNaw5snGD6PbG1E5MdirrVPRZv6zOjkRnNX1K\nSZFPPxQu2IMbB4Um0d39bsQqkSxp4R1UUlZZVSOOxXxkqxj+jt3vYQKGSBfx\nt/psx6UAW69MARSOpLIBkHcMeN9zUm75TTNOEgbDnqHIgtsKgK9E/7WDDLL1\n4/YC\r\n=a+7M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.11.5": {"name": "expo-notifications", "version": "0.11.5", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.1.3", "abort-controller": "^3.0.0", "expo-application": "~3.1.2", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18", "unimodules-permissions-interface": "~6.1.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "9cabfb2c81f55117eb076c983bc7a9479cff68d9", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.11.5.tgz", "fileCount": 473, "integrity": "sha512-<PERSON>aeYaYuv0ElGDq2s6wantI/VPUI1K31CwRC5UBUOPDXVJcRf4vcLWNvKiRo4ZscsdWpbkiiYqTK5Xaa5zmMYw==", "signatures": [{"sig": "MEMCIEngjb6YheTdY29ylz+HeQhc5KyFOKN/DCO3tqdnHZM/Ah9buRBqIRZqVUmkwckz6burZm5eisR2wn0Mcv91BrBM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2979264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdfe/CRA9TVsSAnZWagAAVFQP/1h/tduFPQ2Tl+Disc0o\ny7/tQCd4d1UNW+nJjyoirT4LicGg95e38MdUpzzsb6M5ktXvMwb7W77qVfki\nk/EklMdRFEntZlQxhZfOsBhiEgGQkM9kD+TjpUH/9eeGVKT0yb2T/oVM9Gcq\n4YkAM7WOjkU2naYaqaxWkENVyJkknF4fvm8XhcHHR3/j5vHx0VHtwEpUg1Zz\nWpbnq3uEGGpaGMUhvp4vJq9jQGsKa6UmRmdndjKthmsYwRe06qEtHTLSg7oS\np0/vtvu3XYGzLR1x1t3HitCJUKAKYJoXYJrEdgJFOjc2F9H6vksfsbomqPu8\nwDvk9nKHjQNMEse24Tk+6kyqxEUnCadoJEBcIvuJv1xpUV5Ks38XdqmLCHbu\nsV4EfSvEU4iZoe3mxC5FCd0qFZMScaC82bOlXzBRRwpA9pnt6zdumSM1sFYl\n+a+jwPauMZETR7f5GlV9AvyaxGqi6inIBdKzLg0BuwNn0fmPuQfyT9hcNrlj\nUQgzR9fW5EA5rfpQ1pzjjGzzkOjUT3OxHmqmzvFjFoA01uJ4/50kQrbz4oa3\nAM/pOgm8WvenFV7a+FbqhpyWWhT1qHjLoBZ3YECMEiPOB+dK694bNw3yXapL\nAmjf/REGxoGrYFTwsDO6FeIvGhO0u8BNpHNQCW4U5Tk4dfFglECSGRvv4fNh\nNwkb\r\n=85Al\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.11.6": {"name": "expo-notifications", "version": "0.11.6", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "10.1.3", "abort-controller": "^3.0.0", "expo-application": "~3.1.2", "@expo/image-utils": "^0.3.10", "@expo/config-plugins": "^1.0.18", "unimodules-permissions-interface": "~6.1.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/fs-extra": "^9.0.6", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "0ea45b23b1f5572b40d64afee24abaa5a1c10800", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.11.6.tgz", "fileCount": 473, "integrity": "sha512-skVEOHXKXrXBWofCQ/Zn7yKhAFA2EN0fd9mXqpkbF6nE4NKE7pn63gTWpyCy6sXC9M3YUal8yT0vp9GkD2FsmA==", "signatures": [{"sig": "MEUCICjuHh2JQcaeobdIzrqB5JJdImUGYnvELJobfIB5viEhAiEAj9ZVHE6mTUQBX5IJ9rvKzdj5Pl2eqHwRthknlxxb5i8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2979526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf3IdCRA9TVsSAnZWagAAIQkP/1b/ZUoyzMbvB0fuv5A9\nGESLnyUTkIja5yI9xT/97iclZwywWjGTfde2/cpsKNdNuEDyzuOElL3iO1wS\noNeNHaRo3r/g8aHV0WhxB6eqorh2cZ8cwh1l3E29iPTHfrDecp5kxMVRkRJl\ntykHzkYC4kSnOSkkvYodoDqBTtTk7qKEFbBs+IS+T/Z3N+ijDrST1T5It0+K\nyRK6Fy/c0he5HRGXZO9hzlBivXJ+7yGsDgMYY1UCCCP+wZXi07nY8ibEFqU8\n4behM2TbPfbgI96QDjAJR0sWnfeYUVrS0KTyOhhn5bpBCHkdK7zgOIiudpXV\nISAYt9r8j+VwxJpKrKxe1XQvOM822M8CTRM85ORONmnZ/1x1hizZuiSpHNrW\nbXePn2/YsOVKw2H9lfkQRGAPHs0JNBCYQnliWf3GtAA9pbTgjb99XJ7YWn9S\nagdg5BoS42jyE0x4/pKR35Muqs1n1WoPOux5uzfMkWyL2Vtim71gj6PIunL5\nkX/Nb41BDNqC6rM2kUVp9YjQP53ifxum6r7Xy/vU+CjoTqjyaWLGX4FhDw3b\nDGXaPlqHoOBE/1a5DijOmMv4O+gDLdVfxJwK9QqlRbdxdTdnrPNBIRb20Kjs\nOqellN0Cy3Tr/Qc8JH0nwmxhu8alE9FKDlJBCsCd7IBbjJHtvUPzGxmdugAb\nJwpW\r\n=YzyJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.12.0": {"name": "expo-notifications", "version": "0.12.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "11.0.0", "abort-controller": "^3.0.0", "expo-application": "~3.2.0", "@expo/image-utils": "^0.3.10", "expo-modules-core": "~0.1.1", "@expo/config-plugins": "^2.0.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "80872756e3f3cc2f8c4f5cf4e79560be9749d861", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.12.0.tgz", "fileCount": 499, "integrity": "sha512-tn2W1XcmNHrTvWxaueA3oLFQi865NaN2rmrwODrmQP9HgCOfIoWyc4BBDSC08KvtO7uqNv1wAqLrTTGqn4Y4RQ==", "signatures": [{"sig": "MEUCIQDEfYK8qahA9dIhzdq74M/2shGOrVsuaHQK3RQL48E/TAIgUnx2JqXzGO1EypWS695TCjs/LYiFqlVv9Q2KBVZ7VxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3143758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyk1MCRA9TVsSAnZWagAAz5YQAIGhN8jn8wmLebX8zXDd\n01zPnPhJ4LN8XPnNsx8TqKC1oRwHfeiEVkCMwRlAXPFYyfE4sTLjzJ0Uj8As\nlXVx5HV3Ve5ilj89JSw3LMR4a46djFFSdF0zwNnUlUNyG71gQm4e0DLbhOXG\nyIxpgcrzsfF6f+cxNCfVyTYYeZMZzixOUl2vJVvRqeJmrF2hslb4h0cVMLuG\nEAcbaQRlc61H8V6oE8S0TbErPD5SodxUx5lM1sNeylaNJpNIl0MxbrRJLeGr\n6P8mDjHIaDwfhBd4xONPQp1tHO6tq+SfpFiQUsjKH9JSY/KICkgpjyVGwZIi\n7H1yU+5hz0aQ9fcWAzqQNMtHATnfsyd56rOU4s9XCZLsKjjvzRwB3wdaogWK\nflf8T6RDH/JQnIWBSeNQ4dN/4w4P2VagTlb4x8hs2SPZ0XUtu2HJlkrXX1ST\nHjsB+gsdSUJwBqQFF78jqm76tvZCbdo3xedcAi2MPgrZsSS8jZtgDpQQY9uk\nIPO10/QGsOpSyNd4Y/Z5SASsQk+NLzPiVIuXxhTHJmWwF/krFE43mJym1VRB\nluXUkwJW+dE7WCluFe9s5Fvx320MyHK5bCDPWlsJ1Jsqp/TdsXpQovo/T3u+\nLyW5kUpJnmAYMn+jlPt5hDhZVu4W5Jb9nkTY6nvdTwfrwsEPw5sBJcNr27Qo\ng638\r\n=hnzW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.12.1": {"name": "expo-notifications", "version": "0.12.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "11.0.1", "abort-controller": "^3.0.0", "expo-application": "~3.2.0", "@expo/image-utils": "^0.3.10", "expo-modules-core": "~0.2.0", "@expo/config-plugins": "^2.0.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "dd143406b408ce531c0c5e1244655e4475a7e791", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.12.1.tgz", "fileCount": 499, "integrity": "sha512-LpbgzBcJyjlaXWzIaE2n30mpVZWCDOms5f8bn/vy5Br/HK8WE52YyMYkBVv+ibiyhUdrd066q35DtYlJTn9lJg==", "signatures": [{"sig": "MEQCIBvgtPfAsTybD/xs9ssi67/zcj2bokB2mJXmMmBhbzp4AiAlLU/mRkOUiGqhuwrdntf2kF9Os5eFBpQLgzjdY2CajA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3143844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0S3mCRA9TVsSAnZWagAAb9wP/3LzHTc7KJLQssnENmJ1\nShJjIgODZyFlEhmq6vo/aRKgQVSy3OPeuT6hehtPWht61DafffKgmHUjCPx6\ngcAzKXSNhLzrYcUHdV4A5v3xtyu3asUUxTJpBWfIBLUW6SSC67fzXObPfokG\nkpRRz74as/iX66O4zQk4jqvlizuoFAD0s9hwoERGRPHXlkle4WadwcQMdVKj\nQdilaXF6D29kvahIv7CIrQTTNJvxlLlo6EvU8HwobTk9RrX/0Fb0etzpAqYa\n7w7upvaaBHfm+6o7Cumt4QIqXcFpVzyr1ERPHmzC4hQsAHPhhdj5mav95/cA\nGuN2IK8bk2vMMv2dIK8gJaF2KLtqRDw6lyNFffIH+1nS0hWvShV/GQMsIfn5\ne9kp3vEdu/EN7u+//YNpUk1Hj+8KMFqGO/8VejO2p5Dgl+O/fCfpnqGyLaz7\n8rOlgo18G8xPEKVZn3NuLMd/9e2a4VI9U/RuFsfle9rF2toTJ4bzP9pELL1i\n17ZwO8F45KdXD3oy74rODEeej1s2Qv2Sf0KV6lNUnDGElKW8Gy3d0BaXi1ZZ\nSLSyQ90rjJlBQ7DTRbzVSpO/gCFZb5pc9ZHwPwAmBfDCpk+DjP0cs4CBLfwA\nET1eT0FRsV5E/EBwiqrszr+il//O3PCFB4RJ8sRRTY/xubEwngGGsETj8pmE\n6kQw\r\n=1M0k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.12.2": {"name": "expo-notifications", "version": "0.12.2", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "11.0.1", "abort-controller": "^3.0.0", "expo-application": "~3.2.0", "@expo/image-utils": "^0.3.10", "expo-modules-core": "~0.2.0", "@expo/config-plugins": "^3.0.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "54b29b522e0a441f44b2749d86d3e9448f1f423d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.12.2.tgz", "fileCount": 499, "integrity": "sha512-SCI2nBdhZXZxR2LR4/ixcit/tNOkC7eHD+fWkTpxky/I4ZSBCpP/nhrdD5Sr0G3o/r86/OSXY8XRRrp2pKG8iA==", "signatures": [{"sig": "MEUCIQCxXKRNW43qh5D1MS9cFtqXiSwn32X5rrKna6zCdBlpJwIgEvcnI0qiJFa/EsjQNVQ661C1v90KSwcl8xz9trK5+xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3142850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1OfMCRA9TVsSAnZWagAAmk8P/293RmW8cd/DDtK9uRLF\neVFvWKcHgXw9Le9Gswup3EicRuNpOBxEd69MeYI7si1Wf7wRzE8YONATk3bK\nfBCT380Ov9evsErhzNE7UVqzKNC8wqCOPBy8M9wnFLXSXQza1BdBNwxNtNbG\nYn9NZleeq3lk1MTHN2jyktNkPeeQHluujMwoV8BI60QZ7SS3rTDEYYbwzQS2\npOnC5CcWS0eW2OvOhJaaC4whHl/tD3GRDsPY+uBzoV9ycOOXdmUUrAZD76kl\nqHbw5oACUdyDfu3HrXcwgfXUKCbTp0Or2vTilGMS8E/FfEMq46QlC2zqMzF4\n0bFSgN2qHE6235hau5CLVVrKref8Zfq23kgr/hJRq9nvqAppxDRXju3Ao6DB\nrUcgASOJdwMq5FrmAeqLSdX/nqnhd5X1D2+jS2hyK3g2Op7a3eAjwnQTARCt\n6OxhPKAslUPD0TyuTyzGnNzMK+9I+fVkPy/mYeyF95a9b8sCQOcxmIk/wzLj\nq+pU+hwRx/jI27QKoLd6jK5zmFzkfWyW8NWwYwybECLOMj5VO3bvB6L1OifJ\naP2f+RzdeFcqaBJ/WSAXbo1hKboGXUF5aDr/hHUtU1eho1veGNA7b3wJa4sm\n3OZt7ZyaoU5rX4Qa6siEpIKPTUvOBP2xLYvyaiwgjcHKiXngId6IMmeotVWh\nsEvm\r\n=l+Aj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.12.3": {"name": "expo-notifications", "version": "0.12.3", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "11.0.1", "abort-controller": "^3.0.0", "expo-application": "~3.2.0", "@expo/image-utils": "^0.3.10", "expo-modules-core": "~0.2.0", "@expo/config-plugins": "^3.0.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "19a574a05634540670a69e242de5c8cb3b8d14d5", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.12.3.tgz", "fileCount": 499, "integrity": "sha512-4N8uoDJwYYXeIH+iGtjA9H6dF4yaEdDyFoYcUbHwsC1MUCL9CquDXpxoO1X2GCBFpwE0kqkyXTOCDqLAQlj5PA==", "signatures": [{"sig": "MEYCIQCSgcfoDy7q4VaG+1nOTO9DMN0f9j6DbhPgpw+6agxejQIhALwlEV74SG05RM5Q96YqK5SXoiGuHX0JzSuzF82LaDdm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3143193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg52A8CRA9TVsSAnZWagAAth0P/jRnyougKAYDz5691e+n\nR94Hk249IlGP49EEbeAS2/l8HJPlaYx/QwjeOMS5CSlYDUy6hVOfSe4b2vHM\nlfivK1PKOp5YBMhGICPlje2E5Uaq+wkOmyGvzX/uJ6tWKMKHFreEVJ/BGfOk\npKe0yj/T4MaPmC33i8m53olJZDTnvAu9rQf97Jd0P/h+FYv8PE3314sIdvC3\nujWXsmTT6MjJvRNlb7a4Icm1SIsjPxiUKJlF5frk0Qeh8nzufJdEk5LR8mtx\nfTESYmF7oOLpAxKmImf8+lohKTfvIeHrnXr5TET6LWWrJUnvmfNaQYS+SKcb\nWybMsamCxKBgZscFYEH9wxrcY2kpcueTR1pyJKaq1RbhnpDC+FqXFyOJ1gDK\n02pAyTWPu8L9L0EJUniafjC2TWUpVMcNSG6Aar1pGu4doTtYRcggxq2LmNZO\n9C/TLfCITy9Q6/6Ge1elZmV/vpqELwwfdfKfB5PBM4g/IPQV73Cdp3JP85/y\nfmI8CjJyeh89wDoaFUIJaoTAZhux+9OTeyZY3zzCMLNjPBydFn22H/ESOXOg\nhCG2bt2zf78mxfCgJ2PArQomCrvAOidyewZttr984BG1vFflj9LkFhbon2j1\nUFttdgRquKf37rZBUOOWc9ZOohO0bzZeqdQK3bF37c3AaBXPfChRq0aLmOBV\nHuIt\r\n=m4xp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.13.0": {"name": "expo-notifications", "version": "0.13.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "~12.0.0", "abort-controller": "^3.0.0", "expo-application": "~4.0.0", "@expo/image-utils": "^0.3.16", "expo-modules-core": "~0.4.0", "@expo/config-plugins": "^3.1.0", "unimodules-task-manager-interface": "~7.0.0"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "7092a004e9f0b9f101c2c5ff741b79fe47c5803a", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.13.0.tgz", "fileCount": 499, "integrity": "sha512-GoxOyzSzBNhlWqJTTIZGlbDAjuBeM59/1hIAmNKJJe+eGTeRRnCAyZ3Ak6OAdLF27Ue0uqRq9q0JVtzHphJ4iA==", "signatures": [{"sig": "MEQCID3kNdO8xlG51n9Ab+3bejDk89CR7SyC4H9UOLh0dQkJAiBdNG8NbmSMhXfIm+bpAEPYHaDLIE8AiKKqFJgDDlYMlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3139427}}, "0.13.1": {"name": "expo-notifications", "version": "0.13.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "~12.1.0", "abort-controller": "^3.0.0", "expo-application": "~4.0.0", "@expo/image-utils": "^0.3.16", "expo-modules-core": "~0.4.2", "@expo/config-plugins": "^3.1.0", "unimodules-task-manager-interface": "~7.0.1"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "c3c1302fc8056055f0852e2aadfef43d6ccc006d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.13.1.tgz", "fileCount": 499, "integrity": "sha512-mWzsT2o1mXPdcoVhr7sApljXYzqyCUHNsdRFAUTeznJrw2NeoWQtFtGiI1lM4dWoz1b/rgFdDRxPe+3NTkuHLA==", "signatures": [{"sig": "MEUCIAfLnlu18YSrEkYcse9hX+ntbGSuBBof6LeXbDmuJVVeAiEAlWDsUxShgWZJnFIuDtvAKbHksbKO2PcLBOlmt1QLUWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3139850}}, "0.13.2": {"name": "expo-notifications", "version": "0.13.2", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "~12.1.1", "abort-controller": "^3.0.0", "expo-application": "~4.0.0", "@expo/image-utils": "^0.3.16", "expo-modules-core": "~0.4.3", "@expo/config-plugins": "^3.1.0", "unimodules-task-manager-interface": "~7.0.2"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "e2671b6dcb77feddb7d0727cf53fcc02dc2af0a1", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.13.2.tgz", "fileCount": 499, "integrity": "sha512-x+u+bTBws7YuKF37BaSUcoVzpv9tkFZ8HZi1g6TdIFGG03iSXtrgjWrqXi3JGBLiR+TDPpQt1dJSo2g32Nn6UA==", "signatures": [{"sig": "MEQCIBOKK3mdOKSECQ0DDoSlzTSmcJFr6DJgoYwB0E72L18cAiAm4tRKUAL3JMnjiwb2Oj3Z0BpZ24YajTRohczIWvG2Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3139936}}, "0.13.3": {"name": "expo-notifications", "version": "0.13.3", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.0.1", "@ide/backoff": "^1.0.0", "expo-constants": "~12.1.2", "abort-controller": "^3.0.0", "expo-application": "~4.0.0", "@expo/image-utils": "^0.3.16", "expo-modules-core": "~0.4.4", "@expo/config-plugins": "^4.0.2", "unimodules-task-manager-interface": "~7.0.3"}, "devDependencies": {"memfs": "^2.15.5", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "dist": {"shasum": "94e638f8ef56de4ddb67ed8cc2a36510a1d90cbc", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.13.3.tgz", "fileCount": 499, "integrity": "sha512-2iQVydhLZ+S3s/tKdcBPRWQyqztQOyex9WaSDj/2noKLWBpIqFouV/Dln+8vYj/BAKb0JoeHko6XA+wnel7qdA==", "signatures": [{"sig": "MEUCIH8TM3BxKO+7V/KvP1Edxer0AQZ2Zu6raCdv3bSm0wPCAiEAsJAghBtVmsEHr+/90v9auNdPDt9F97xkfQHPA/l7aHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3140022}}, "0.14.0": {"name": "expo-notifications", "version": "0.14.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.0.0", "abort-controller": "^3.0.0", "expo-application": "~4.0.0", "@expo/image-utils": "^0.3.16", "@expo/config-plugins": "^4.0.2", "unimodules-task-manager-interface": "~7.1.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "e58c13e2d47e26a1b8c6b4a64cf181771f5d69f4", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.14.0.tgz", "fileCount": 499, "integrity": "sha512-6Fj58FrPng6GS+TBQPdSL5wZWztJS5qPxL/hEoB0olirsgw07+C5CPy0TkF5bUM1PjwDKuqqyfVsLxdNodB0Dg==", "signatures": [{"sig": "MEYCIQC51+oPw+m/TmV1tVx6Srqmw5DKBLJcqzan1RTvQJ+krgIhAPOlg0i/LLY34paMtk/HI/6Zp29OLizfCtFYwGcqdeFv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3140159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqohFCRA9TVsSAnZWagAADFUP/1dbE0qi9j3NeczaEs3f\nJG1ifQy6mIKk4jGH3DcK4advlPhjLVjhOgBy3ZHijI/QLGahSpFneH0x8pFw\n6fr1GjlVNQD1b20dTgtKSJpZ7Eoau+GhvogAoi7BDFbch9OnsXtt5/PQUmzm\nW38Rm0IfbjE6qm6zcym4KJ+RX4XumRrUk0VIbeRBjP/l1pF1itpbn7yF7Mv7\nI5Z+joG5h516RqdlHkifKaW7Z3zcheSpiOMdlkIlVY5BXlgRQjNhyWsXXD8D\nAi+0Pr/WcSWkxMEWcQMu/COz/+q/pF+oAs892FB+J1tc0Bb3bfgxRQsCgV3k\nnVMYAzp+G+IDq7d5Rsa9v7pirhWxLiZ1v+UFXf/IrcVMYMkerDEgNDpAO+tf\nay1pAorYkVOirOAYnUfxcFVQZlueZgIpUvYhAgGqefESyVetL1hVUfy25RVd\nBY5noCcEx6UhBfansUPjSyMQ4BySWG/29waslgKZm09mJRFb6Ne8ASXTPAYY\n1Ddornz50L8EtAp58neaCBQ0KeCEvI1zaM06oTnscwJBiL6TbWF0QlAmUE09\n7gluztmOHzqe6dxIVdalM5QBEkfoIupOuaACljjI47AAtlwa4w0rVZMwoRGD\ndwyDAZBHdakD0ZJOQzoyVAa54Smv/I98ak3YPZuV5AhPCSPfl/lWVv32WfmV\niEJ7\r\n=T5t2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.14.1": {"name": "expo-notifications", "version": "0.14.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.0.0", "abort-controller": "^3.0.0", "expo-application": "~4.0.0", "@expo/image-utils": "^0.3.16", "@expo/config-plugins": "^4.0.2", "unimodules-task-manager-interface": "~7.1.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.1", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "fe2b122ed79488e754b7f993f9b802eaf60b51a3", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.14.1.tgz", "fileCount": 499, "integrity": "sha512-0L+DFMQmVfMhonza8MFP4aI/6ADKEx/Mn5NzQlBZpo6PinpsPFpQP7of+pihx2onCkmeN8OIsLFS5O6QqzH3rA==", "signatures": [{"sig": "MEQCIH365l4K0LCACBlmOS2NfZH9UO3HwLnzgeE6V/AWM2qGAiBFtMebY9wj1p9Npzr/QQ1xs+cEPokk1E5KA7ZlTvPjxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3140320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+YjGCRA9TVsSAnZWagAA2OQP+wR+oONHpnELuxIA42V+\nekBYgIpshMggzhurb0UeFDrupLCp8SRD+TFWUgPud+Rn+ipacYiKulcoPxpH\nHCbVS2TuDOJs5NPXXgKcexKTu/pCjJ1wjo9qs7aLlUMC760pfsm4pHPevrjl\nmpkThiMRTglyk7XGB8Giv5NyLhhmG47xIPqtJ5i//g6FfDHpU8QKNfveFgus\nnEpnGRnpWT39e4EPMcKg4sb28DiiItzUH9FOCqNGUD+/cgm51bH4mxM0sPlV\nrGyTg8xhTaeMpqcLHS+fb7G5IXF8Dl+DSdw/HADn2paFeLsxN3SeEH1mg3KT\npvkiJvveFD+O0/Trh1FCcwLe26Bi+HTODlnNE9VSro+CJwFTwgHZD9Ib8WfQ\nYs6SnRUz7KKuUN5+P0Mr6A5p7S4GqgCOf7hAOXIOi+EP3hR2uaOUqw+EQQIx\n9cgNMb02yWNBKgkyZqtcO2VcroNWF2WeWDbsE1NyTPuZIoJrJXpTh6oHxoAf\nXuBVe3WLSUlNwA9zt6LPN+28hhksRogFvCNzBnfBsumuxSjfjRbnBPxu31RW\nT+aWRBvRplZbyC/91Ed4pcB96qc9ZWhGL3x6k1pbRVXHNENjECyp9a8qmDz0\nWY7FaTQhTEg8LSVAEhXwozypR43K9rPmnkEvZjr3yX2bjQyIskJuRxFPDiXh\nrxJO\r\n=DqEJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.15.0": {"name": "expo-notifications", "version": "0.15.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.1.0", "abort-controller": "^3.0.0", "expo-application": "~4.1.0", "@expo/image-utils": "^0.3.18", "@expo/config-plugins": "^4.0.14"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "4ef5f1d58ec17cda841a53fe5be5904f9e322dfb", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.15.0.tgz", "fileCount": 583, "integrity": "sha512-iD7Q7t/J3m1TTI5AXFsFOTKuFotVXrTWmOfD6A4vT2tB0svBCUgJoAIipB+8trTRh86z25uMMPoPq83W2keI8w==", "signatures": [{"sig": "MEUCIEB2GWyDioHwOANRSlBdCFQEA5fWGOFsFfM5pZW+XO5PAiEA1vqDzKWZRz5EzQZ0VtEBic9pyPSR5d/B+oVASscgh8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3165310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXX+6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhshAAminzWNSPRR66b1ud44rWO4GGHz+IDW1905cs9l1fyzCLJCt5\r\nF6KXZZeCQJFgWEe00MWJP++BYWl36j+Km9x5k7PZzFVJSliQjZaQAGrd1d38\r\nMGenWyJaTs43r5qPn/hSlSUsIQLzST80UU1Lcjg02LBoEP7DqFu8fB4zzRmy\r\nA9bVzg3diQfnjV7U5m1XESlIdg5ViajDuZPt6hi3nUJP1888ZmCufpad4yQ0\r\nmJQabyY72NSBNVmbzOiXH4rPH6v0Xv21EgoOouSYLnd598g4m+HZHX0BRrVH\r\nkMpgqm/dOz2e75Lu3exrnVLVyPjQE00JgC59JIF/rvivj0GQPTZ2+NowREb9\r\nUdGDzz73Kz7p8KzG1R94a54sDBhd8gc8eFd3g1fJxD7FnCB/7umqrJxNK3oq\r\nYqI361LhIUYiO/u7JWSR5nrzHZuCrljhIHeO87dENcNBx0nx19sZX5+B/i62\r\nTA/eXZ1uN1uLB4fpcq/jamce/EL0jhs30xfmDxSZgHBTH2PUNujoJpQJkOW3\r\nHsFY7iNVqX7cSX06LLqCxNiFkZU6VIRHynlGosbN0oPF8FsoRUqTEUatZzd2\r\nWLNZhkqrrIbeDHTDjjKjhmEZPWmZV2esfDKzvkxnLIo1mTaFDibNKoKSkBju\r\nr8ftERo+Zxf2McARA5aCSgr7T6eHYpMHEAE=\r\n=9Z1S\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.15.1": {"name": "expo-notifications", "version": "0.15.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.1.0", "abort-controller": "^3.0.0", "expo-application": "~4.1.0", "@expo/image-utils": "^0.3.18", "@expo/config-plugins": "^4.0.14"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "96b8962a7e7fe5a7843d82428d5f3ed0dbca9def", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.15.1.tgz", "fileCount": 581, "integrity": "sha512-gvfnB4GeWcxeYM2/tQadsM1yeL5VkWQhzGJOdyD9GUoZkLc2Pp33Ay2W0cuPn9OfjL0/Hx+MOEwOObjjEc8gQg==", "signatures": [{"sig": "MEQCIGkI77g01WxGpBptbUfk2SP/4Jbdd6PyY/rIxSzovSUvAiBuFn7QXXBUOwnXQglibMD9QXfTaDpkfdLPmx1DuchN1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3161184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaVCxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8JBAAlyUxkYO6+xAOvMO3qnmW56NFRXJMtfrsgzM0fncPrhQ96JM5\r\njPDFGd0qso14RBNXzfXpBN2vuSi9eE7CdiJzDlpZcMBwv8DLATV8BHxPQo70\r\nKmnVtSB8uXk/y945THexiScyWiyQ46y8vIwbfHlXd8MFbWLVrSPQ/KPg1y6q\r\ngZPmxuFTCI8+P89QT352srGOu/ELhaJtKKH1+w8WsyuIPBsSGkE0qbW7hc4B\r\n/EY6khzC4XiZPuCQywSMZMyHA+TBu7bhOvwGdyGt34rQENei2IJ/vBRBhGTz\r\nEElxE3ZivrkgljgIBRLySapZadpsTtr0ZnqkQCDJ/9EBir8xCwL+b5zJe9xB\r\nyBw+9JCNXR5h1F4m8M3pmP4bg49VYUK/iK7hTt0NDmIZJdd1VbMb6gh5ju61\r\nIEGJBAoKqj7jud5sxlUejYIx8J225p4IclT2fN6OFLqzsGivgAtxcE+exJPG\r\njbrUgsK4A8uycUf5DbPrNg6+zTqZm0GLjbwlo00TG0S19MXpPV8DsOkx+SvO\r\nLEOyOsnf7JKg5llPkcs93duPLH4j+5+yX3fydGIxK2Q7U+136wfvGe6QHDih\r\nTeGa7mx1XemM0WICcW6bX8G1GoxZoC87M2tuyYF7CejQNCqjXcic8f3S7L5t\r\nK37TM79F+0r7xyshxiH7vTnj+EjaWJFyAfk=\r\n=fzD3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.15.2": {"name": "expo-notifications", "version": "0.15.2", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.1.0", "abort-controller": "^3.0.0", "expo-application": "~4.1.0", "@expo/image-utils": "^0.3.18", "@expo/config-plugins": "^4.0.14"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "8c9d3b43624e7a08bfc12ccec714717bfeb30c10", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.15.2.tgz", "fileCount": 581, "integrity": "sha512-ZlOzQ8Lp1MgkwmiBsgg95nQHHbTOg8LHwmB816qxNKWeFvlyr/wQSgTZJNklL1uuv/+jC/YyKWoANfodahcAbQ==", "signatures": [{"sig": "MEUCIQDbqyOanoCUcJSBkJN3BBuUc+1bFyWa+ucplVMxgrW+5QIgVMlQhxfZCbyoUUtpXD7zvtnAy7iHjZQFtPXK4+QFyPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3162531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidCFSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrThg/9GJbCsF9dPB9kLH5NLW4oJ8ThLRQRThNxD+sZhMPx4vIeagPI\r\nElKSbH40Bb2AvdJABJdTUVzOnd+XGMmGbev+88csxOqOdyYWz+AJ/KLCQfjA\r\nKstRUkDuYk4cW7+zlqt/Jlpu5YPPfRECQJgZwkWttG+tsIQ03JcJaorULrbl\r\nvMH+dtJwA52LyJBXR7oSRXdFnamW7tjVDTtKIo9doaevQK9K2YLZqjlOf/Id\r\ntGbuGDJDCibOZxwn7upLsoBslhhtDm+07GzyKkP6Tw5Z5qm3Nm3kaWHvqFRH\r\ngPSyfEI7dn9KhJTcYOxZONAx1xzz0wmREx2abP210zDL/HjkGooFtOtD4cc1\r\nP3YMN6UadVzwxUkYMzuYCF2ZaP1dYaai69vDeM2rLEZmzCZC8SiNqJ96fN3j\r\njA0fE7Xex6mUJ81Ex9BctkVltXQ99LkSxf6rjwhx8PgTUglZ3/rPwPZNDJOz\r\n6d7bDmDOyzFGYzQmRSg6jK83fi1E2FKtuOFOY4pBzGBpNp5c/px8Wvq9f0j4\r\nowhYG39yFEUgS8Ty28z+0tlHsg+7bFM/HOszT2D2GUrMaXWdPohDEYyDhKRc\r\n0eKjv1TTJJUlwyMJ+cKhLsRDdcy/kZY3IwbORmbXqbeI6PCJK0HMzmwA6bQd\r\ngPFGDKmY0LnR2J9JqCrVys2gw4YIig1VGlk=\r\n=dCqL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.15.3": {"name": "expo-notifications", "version": "0.15.3", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.1.0", "abort-controller": "^3.0.0", "expo-application": "~4.1.0", "@expo/image-utils": "^0.3.18", "@expo/config-plugins": "^4.0.14"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "d87dfc2601620ea89ed1a154a3e71ff7e99256d2", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.15.3.tgz", "fileCount": 587, "integrity": "sha512-ln2aFWxKj3jEr5fRTgudxq5v0my+GyYUcfKiKJ7Z/7flRczjZ2Kp8WChZllRYAvncybpghMzpksKg8f5YtAZ2g==", "signatures": [{"sig": "MEUCIQDFyOgtFG5Rf7Ncpmqa9sQT8LIDZ9otnJaYVia8X/IkYwIgMC2S6B1kyWJs8EYKhks++T3n0ZPTRH2PTjYZmU0dttU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3177865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirMvEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcOxAAiCw0k3dCbzLL9dzDivqbXtDny2glUiMBux6nRs7M7dluLQub\r\naHwzD12ADZvAqszm5z5DmYQDHpdfZhazsXNpGNKdK0foQ663tXNKHFNRMrLU\r\n+ywCxXOVyGcL98L78brGK0UMAqEZ9Vjr+K6SoiwhxAA7KWEQWBOfIxDrGew/\r\ngXn8057c4DGMpFsiPWNz/ORAVffw5cRewaOqiDxgonQbWTvhxHb+2RD2RsOl\r\nA2F7MiR2ZFbu366Jec4EFNPES8DeZiMD81O5pId1zdpuTD3X/3ob29BK2s3/\r\nbi0K+4ghZgvPkNEYhCJAh8uyRua2iwPvTiDSBPqGX5YF5Va/BcReRMhWKQZJ\r\ng87GL5GQGrr4KsOa5wqpSrfkojMrRJHszv6mNKbd4QwNJrWkiaaiutNoPNiv\r\naCUrNAwYgQovpqh2oMKeLII4G7IY2WlW7bERMyccFDMLaRk6UL3AO/iWVA0A\r\nV7Ww2x3SVJ6jn28TqvrKFWfR0qe421LgDb7kKbn9Z5G6F12wS6EqQzBEgQNL\r\n1lAwKchXOdcoeLyHu14z7xZ9u++zKJ0oWYmpSVbVdo4MISAeFcH+uf8qgoHk\r\nKlRRxS2ZyAxvwpW2HY98w4i5kKbeUqS7N8FvLeRYzgVDjboyYqKVm6v27lbp\r\ngi7t3zEOWn+iBsEr+1DOeYI6fqM6hfSaEhg=\r\n=9bb3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.15.4": {"name": "expo-notifications", "version": "0.15.4", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.1.0", "abort-controller": "^3.0.0", "expo-application": "~4.1.0", "@expo/image-utils": "^0.3.18", "@expo/config-plugins": "^4.0.14"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "e255927a239a1b49bf6dfe3d2d6d3fee308165e2", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.15.4.tgz", "fileCount": 587, "integrity": "sha512-CHjzA0d4NQU9Es//tTSEKQl3Hfj9hdrV6k79G+2/dnAXIa+KjvDGUTjPlhVqvt9ApT4Ly6UbwoY5Cb5qgKMEWA==", "signatures": [{"sig": "MEUCIGgF6ClSf78VGbZ9Icb/PONgYoTkGrSZH1xYJapphAvrAiEAvly0pz6jQsMy3qgWhjvcY11U133Jucaz+xCSRJWgzzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3178138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuekoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXJA/9G27hMzlAPv8KVEybnwsVVKPXxHtoRbw5YpOZ0paJ9lJX664f\r\ni78CD32gB6wiHSX40GSm6rnQbWDHORcCx2lciQ+/gaVSPIGifMzYOf/T90JD\r\nWWNEmVhQKMO7UTzygwwRKM8/1PwFBxhu/mvkdIXZKtARTYQEWv4i80uOS6dn\r\nWHIhBN6sj84iiuVa3rcElVSmaXZx5SKXnJj8/zdcs5I61sD2T3+TvBNGXIKy\r\njUe52jxUoBOVt5gUOnsEP9vA3cuXf3E2aNFlGz3d3Uz0zh9qL4m7ch+VAp98\r\nOqXKtsHb/Ir+Yyf/n5B5qilVO78FXGF4hgwm7gT+KJ148+bpOy4X2gsndu/S\r\ngQ06j8bnUlNLg7conNEIZ/DOWmtL3goOo7uDdy3CIvG50Z8J00HhOFtbm+JQ\r\nhF01n4OQbdVkdaQ4YzZhaVTpa0d0xgC0U98wlbp7NMpx2n3DivvRWbjQsEGS\r\nCv8Qh4DyOwhMAhIHD0RDkQQocvq56SYjGzNumSQZ28vCBu6vn44Hy2NqzGu+\r\nM2lTzlMw5SHuC9jAfnTzeztPylioPhr0NpxX1FulCr6lWbzJhxZQYvLz+Pqh\r\nHW90XWN7ZSTs5JD7KZkXYhmZZRLjFLCx0vWY/u7b5dFAdAzPQzZbZFN2S2Z2\r\nTkN4JyELoU782dbo9JCV8cR5pnJrnA+K67U=\r\n=kMB1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.16.0": {"name": "expo-notifications", "version": "0.16.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.2.0", "abort-controller": "^3.0.0", "expo-application": "~4.2.0", "@expo/image-utils": "^0.3.18", "@expo/config-plugins": "~5.0.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "0de62ffeef55b841f86bb678e17880788acd4a43", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.16.0.tgz", "fileCount": 587, "integrity": "sha512-Q0pbjh5mWR8BxS1UvcKZskEyYd5p72IfU1/VHemm1q44kKBbH+0oFMaPzkNpQcy73L6p4rl/jiQ+4xUpjYm68g==", "signatures": [{"sig": "MEUCIF6x5XSW8DpJmTBdhxQgP0WRrdFeot14zYRp4H2xFv8HAiEAj2iP3TyWneA5adNsECIwPk82jVxOmUJ4TAqmItjTQ90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3178058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix2opACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmFw/8De28kpBV5OGimrVy19y7y2vDrQB3PDQl064laD2LfLqKljA/\r\naUrnB6ti6TATdRZTt2d2jia8jKL2uezOO+ClqtfY9xc66K66QIhsdvCmf8wZ\r\nEI3XKgBu/zTZxsw92aZkkQ5mRy37Scl6ULBWR2wvSnB5Cco3Q4J/Pa4MXJD4\r\nHNpXO4MZ1iOiNCfL3FSMvC+b85wS1QFitu0Sj8bpkmJU77RofatTkXfo/m5Z\r\nEjD0h3sBL7vaK/d3u8KOa8mnZP24m/X6sdjMmWiHX33py/gEFbvH/fbkWKxl\r\n5S/2NPkQXpDXbp7yg10OM/5quE0uOQRNGA8qxp9iaqogR19BF0C3L/Z7otns\r\nM0rrkbFmeBzogUY0noYQxtqvQGN7IfUrebfySsi0zE9UzZ3un7QlL+Ng9NqW\r\nrf7w5Oknea/oKkL9jFvq4zP1tPyCYm8lziX2IkuoG1w9y/9Q/oQPqSbG3nWS\r\nLU+CDnyJ3F2vKtN/LPaSZfwXFJrEituZ1nDZw3fBb05de3wN3Lr7/isG2+d5\r\njlsa9SKkEYjjiXpSZnY0u4TRVZcQY0SuOk5rhm17ALscx7fPr6U018kDNDtF\r\nbMh2K0Zb/4LglQShW+pNYzFDYIW6W7eS9TrmjOMi8baNb6eUKkiutYKvE7Ll\r\nSftIw29Mpx9y3FdvOkF1SsYXjAEr9TZU6eE=\r\n=OJn3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.16.1": {"name": "expo-notifications", "version": "0.16.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~13.2.0", "abort-controller": "^3.0.0", "expo-application": "~4.2.0", "@expo/image-utils": "^0.3.18", "@expo/config-plugins": "~5.0.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^2.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "4e6c74fb6b3aa615a36f587e0fd4876ca48ecf91", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.16.1.tgz", "fileCount": 587, "integrity": "sha512-GYXZnR5VCU79tM/2senPJ0jWq04PFAvh3chFYaz9qvUjJwl/AV/BQz0xOUNXGXmm8nBf6FA8B4exDV8h4KhV6g==", "signatures": [{"sig": "MEUCIQCzLjRBguLnBVZa+xVwzipuEzv2deKJg3LdYFTx/srlSQIgPndtKCb1DgTsgTl5agfgpVlbp8KxS5I/H8jKwQXjm0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3176505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0vu8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5mg//VWe84bNkMJksR7DXtwx7UwhqmxOcucTkXo0zvWtiG3Y4OGx7\r\n1TJWr5b3Y8K/EsU9FhmtBTd1DHidwT9i0Kxxh3N403PTM/bNzzMYGuEY9pze\r\n/mT1sPtHhtAPYuF4wTNSqCtwIELCnr2nvvWj4n4jx3rTP3MSqDaWmZOzGvQH\r\nP1rbfTgouvRH8B2CXtbgdhHXW4LGFlcJybXBY7Fpp9DVHIUaidjVE86EbUPw\r\nZd5uM5nNM2zmGEulns20yRZ/YxP2xKkBYPNZN5vmAzxN53duaon5ydpGLn7t\r\nDWUEFn2CV0A0/+Zu1vCu4YN+pzvlFSU6FGPlwFuzaulxhLEV8BZkQs1JwE1E\r\njjHrnFy8xG0Btx5um3lr3VycHCTTgSdDwhcvyNdSq69GoA0aT2/y3UJIIxDu\r\nhg5lpX34iji9+VmGhcstmN+D2kB5TRgJFgrN7eQ8bAyMQcc/mLWbSjVC7A3x\r\nndmRERdzipJ9J6lLYCKLZXQ8iXYH7nitSbZ7qhTGS9wgBIgxQkEzTVUGQ6aH\r\nw0fej+VkfOxtZNEOQZSJ0yFKRztpqNnR4tVTeaeGHJRF3MvttmcmFnY+tcyW\r\nYR1Q1lS+iebzXeMgc/M6U7zGfaM5lTGZbgcNllQI+xISBU1oEOjLxtbe0a2o\r\n4TmPDfoYF3OgxVjJt+ARa6FFsqSFwOK/G+0=\r\n=Rd37\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.17.0": {"name": "expo-notifications", "version": "0.17.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.0.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "c99198cf67d9acae044e6e5acc5b481230751bd8", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.17.0.tgz", "fileCount": 587, "integrity": "sha512-PPjV5WaL5iIKAqi/qsNUVf0g4htz+Mx+6tZ/Py7vIurAHbA2PymgV7UPSEB1gUbhzi5PZMGj/DNFJpSZwgqmNg==", "signatures": [{"sig": "MEYCIQCAzJlzlOM9ujhKXA7sEHzH8mwjcsnLhFsmOVD6p5CmSQIhALCWafmREHF9DWoBGWYOCxEVYL6JwuMCHPDooej1ArMY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2201317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWAHCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfgQ/+OR82OsISuQNdr2JFAoqDCuctChF4Xb/gVmSiOBBJvGUUnUfT\r\nHlEQHvAUh4zAc8vYt/NwVA4wJktrpCSv4NzjM+kZ7bB7AggZ/WoDznfp7JRo\r\nJM9qt/L8zv6I69eij49kmVzXG0cVLKfiXMZdUSlvIwQiA7/siDK1FxBydaLT\r\njDcHP2tEZOZAmrL9wbgFFX6CPUcPdYrVzFtC3mxyvKJIY+a3Nc3afbXGsWif\r\nyYy4zHWwlcm8fMfZCE1lcKlcpBli9dyso8yk5855i72wyc/RgPceZvfuE0Ws\r\njVqNhjNjpBYNVX6MItnyfFUEdf/gd8+WgeRqPePYm3XNsB0zu7X+jfNRRedY\r\neCJ/ZW881cnwSudpuZ/QY80tw1fiwIHMg/Y/BY300vOaHrWthGZqjeM62Mih\r\nHr8K9Mbuqj8VJpafxxI+sEkAxQzSCyk/KgcRsj1m+wANx7P/dtEtB0ffqsFm\r\nR7yr/A33WNs8KnPH8nosVujym1Tu4DUtAZv6ku2fOAD9Y+lVYnXa/VjwlHH9\r\nb12Qi5c1mmMBSMVeMTAxsIpSNud1PVFRCw9VIF61bZ9S/qXXE0ad4771DoV7\r\nnb/Kwr5vBl/ViWbMSCrTv5Hcv+y1HiAbbgf7hLaqme87rNmIFjxNnvXRIa5j\r\nZHi99TrxFerODbJvkBTXyc7okr1/W6To8kc=\r\n=qznb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.18.0": {"name": "expo-notifications", "version": "0.18.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.2.0", "abort-controller": "^3.0.0", "expo-application": "~5.1.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "a23eb8b678a6a064b8406d24ebd72239572adf1f", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.18.0.tgz", "fileCount": 582, "integrity": "sha512-4N+ADyIU0EKcdzoAJTRaUBEUZJBaY/rZBOLNFbYeWWdLouJuyTp5wwtHVeuzvbIervzQAbsG2MJvGhIMMPNv5w==", "signatures": [{"sig": "MEUCIEw+8QRaCwQyFOjhla7uuO+nRWeqPbpeNwua2SfFCjsXAiEAwEpmpLUNMkurDWJ6m+UDn0pxKH3SmiP/wrOfcZ/yYKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3GYpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrgwg/+OkEbBMNYIdfzKODcAxAeykEJZ0y+Wr5/zBpcMPIUZaVd2yGd\r\n4cdw9DIz81KhIPcVS+08cQFc2QEUMQQ/oU+xp4fuaoiXsMft/WQmmIYh/g9O\r\nUiDlN13poAkKAqL7o0Olmus3GqLZN+EAYZlzgH8PV9hSFX0Mvw9YjJQLmi/D\r\niLfpAJkQD/zOzeE4iAkO42lMhNLN2ojdyIv2fFbAo3Rv+AEq+52ynHe7JOP+\r\nNSYsg73WrrTfYTJ5vCpR5Ug8mujTy9plG8sACUpZ2kRRSYPRCzVeJSh57wv8\r\n3H4u1iSHvZyGIfZSTg+lReClvRhFwPaTv8pmppKiFtfQKwx8BQSzK6io8eAP\r\nqhDV9ZKluFnAkodaKuYEzCko4bDixyiQ8Kr65Nr5oApTqo9KxCgHnn6lft0Y\r\nq4fUbrSWETEjMbjT3ePqJydXvXIh3M9PhbGjpIgxrD+VMz73hLtHg/7hj9iF\r\nOXnAMh44B2L1yIuecyCEzUJaA7nYntpBxroGxL7xRDgewTUM7tJooPXPHzkI\r\nw4sRIh3iwrTlJlfzW+fpEGr2Jygc7Jisqf2lGrkCRZFEHDCXz/KBqgqne4x+\r\nQuL1Sb5eQFVMZA+Et5KcoxrToo1VdfpzCgNvwGf7se9EJR019PraK6ymKQnR\r\nlTW5VvgX92Bj68vhihOYKMSDkEhC7AdQFww=\r\n=e5kI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.18.1": {"name": "expo-notifications", "version": "0.18.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.2.0", "abort-controller": "^3.0.0", "expo-application": "~5.1.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "c726bee7b6691d5f154b874afeda3b4561571cfc", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.18.1.tgz", "fileCount": 582, "integrity": "sha512-lOEiuPE6ubkS5u7Nj/57gkmUGD/MxsRTC6bg9SGJqXIitBQZk3Tmv9y8bjTrn71n7DsrH8K7xCZTbVwr+kLQGg==", "signatures": [{"sig": "MEYCIQDXmsqhk08aF0tRhWrzcg7VbOGZKbNhtBMY0o8csRiOwwIhAO7c+hGOlykCQwQeiMVNA+OMrV8uOw/flyGwXOVz8RFo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1097228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5VGXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMSA/+MBR0iXicvrDdOtRBEH/vGqCWD/4faPfTupXoxWlg0FQtABdj\r\nf3jEIUC9BbH5u7yyG0qZIbJ8Lqq/hPkovzv3ApTtqXqyQWBui5XhiC45TMmr\r\nj6+A/PX6UoIevyZPEH2abmFRtAwcqFAT1R/29Gl99FczZYSxMSnRxaT8f5eD\r\n0t0CAJ6CdWsSWfqoazQ6oAhpjfzyeFAicuF4nzPXkn9v8mDaf6fYD/UJsZRG\r\nnYH5bZS1bANs4HavL4EVFR6uq7I0g59bxLbTTqOyPPpT94AI8tMqcp6glfqO\r\nioDpoJXDsLLqtBNJnn5z3Y+Gbk6tP9dOjNoMkJZm3g/gWuV5pykz5+mwjr1c\r\nwv2x1jVL2k/F54C88NKxOhV79js847KAU5bEcgRNBp1IoUKkjl07ImYkOb/y\r\nqLkBOGn3vQyBZbkZzPvmX7Om05lf01deRzbE6apFPEhrpqGK/85jsJ7JiHF5\r\n6shRfLET1aKTkmqdZ+EWWk+C7MOme3Zd/ErgExDaOJotv1DpgT8vBnTlK4u0\r\nSGYYB2PKsWveznvO/1RaY0yhrytdGRCDD1smR5n5gkmW1BTpmo14zpsI0ken\r\noCNT4vgtFi2hDe1gGWe16v0Uec3/9wDNaqZp17x8UtS09TC0E0UoAhOadKPx\r\nf2DhroIDi4oAjF07VD+JV3Branle7C6w4LE=\r\n=cYn4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.19.0": {"name": "expo-notifications", "version": "0.19.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.3.0", "abort-controller": "^3.0.0", "expo-application": "~5.2.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "4677e149e648fff16d98bd0548b4c50835f5e456", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.19.0.tgz", "fileCount": 582, "integrity": "sha512-3KPCtUPi5DoOPRZXqg7s/A807NLplfI8lNs5woOxuE0NL7lEcYoxoLgxqWepaZ6QXsBFdrod9IJyc3OATz22gQ==", "signatures": [{"sig": "MEUCIQDmmTEbfsmAUmu0O1OC8/08dCzudbLhhMzVC2d/F1KvVwIgRhOHSIANWGXyVeAGc18BQoCuBThTpAE/rQfYYcCxCIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095031}}, "0.20.0": {"name": "expo-notifications", "version": "0.20.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.3.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "ba93d059a3f593ae9b35a84821c5a876eddb6e53", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.20.0.tgz", "fileCount": 582, "integrity": "sha512-AUiNIXlj1ubFqhRxlGFr48LIIpOpuk8gS+pFQSa2J5yJvpvK1sejCvlGFUmFoLlCBEP6LurAfv37yYD0M0vtjg==", "signatures": [{"sig": "MEYCIQDhsqDGwmEbig59NdMybvLr2n3HEOfrs2yefQ49D5To9AIhAMEkAjPJSxSShvaXX7h7b4ViE9RTKJaLi8HnoJ/1mQbG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095122}}, "0.20.1": {"name": "expo-notifications", "version": "0.20.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.4.2", "abort-controller": "^3.0.0", "expo-application": "~5.3.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "55479e3bbde31ab5ccf6f6fd3ed288c6c91101d2", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.20.1.tgz", "fileCount": 582, "integrity": "sha512-Y4Y8IWYj/cSWCP/P167z3GRg//5ZlsznfMXi4ABdWOOpF0RGNpd5N17TxioNivtt7tMhZ/o1vmzFxulhy0nBWg==", "signatures": [{"sig": "MEUCIBkbdeJlrPlqbnXRwBLHya6XZzu8sB1wmvgsEw6pDM0EAiEA7qg4TPpcRMlQEA0+fF7BiF3/H1gIPwwgLNjdGOaw7B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095208}}, "0.21.0": {"name": "expo-notifications", "version": "0.21.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.5.0", "abort-controller": "^3.0.0", "expo-application": "~5.3.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "ec50a0dda9724ef4f5f006a1029e9d108c1dc5bc", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.21.0.tgz", "fileCount": 582, "integrity": "sha512-4X51pHQBZmTPOTqXaGCbqOqHGPd5aVIR+YAr7onb+RZ/PPyPO8KNVU/TNK7HbddHkC5VAF5ftH9u//jSRTvJfA==", "signatures": [{"sig": "MEUCIQDHeRoA+aawJtKqQ2flX5A2zPzTMwC9iI4jrA0xgahwdgIgejWfgse+lF1LP6aC0kSdqaSCUNsoZiAGWU84NO3VnI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095053}}, "0.22.0": {"name": "expo-notifications", "version": "0.22.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~14.5.0", "abort-controller": "^3.0.0", "expo-application": "~5.3.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "fa4a6727f6a530904c9aa562c5e434b741e9ed9a", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.22.0.tgz", "fileCount": 582, "integrity": "sha512-JYINnm7LcbP2jgGru/LZFEQgbIVR/BzuAYwP3HdRitBN6oHTlW9wlTrqtiFTOb7peyWL9jPJu8xVsq69NA1zSg==", "signatures": [{"sig": "MEQCIE9GDKkUq5d04lFXLBktB9td74afJMhuKZdvYvjLy+cpAiAuR0/m2V1TkSPy3JkEDLXTEZ6lnDveGDszy7s68U9EMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095119}}, "0.23.0": {"name": "expo-notifications", "version": "0.23.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.4.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "54c32d579e26767ef085527b7ffc4878ca24a1c7", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.23.0.tgz", "fileCount": 582, "integrity": "sha512-57EBi56ipjkVKkG31+Y/GvAvK+IlFq3xZA2U4p50++LJhJmeDrwptrWFdwlJpYHGNVPgYLOVsFJmZelnh3+QFg==", "signatures": [{"sig": "MEUCIQDZpcAcynFCKe07jCJqRH635PU/GtKO5nETKPP3mR99TwIgPsCYi3w0Kj+NAvv+MKKCb1uxQaSR7lDIPkh3HiHjT0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1096195}}, "0.24.0": {"name": "expo-notifications", "version": "0.24.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.1.0", "abort-controller": "^3.0.0", "expo-application": "~5.5.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "7623f506ead4da35a46b0a367b2b354f6050a4ce", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.24.0.tgz", "fileCount": 580, "integrity": "sha512-yfP5IsWGIRB57xlxQ0styg5iAzHOCdkDA8Xw6ki7EtdpcIgORC7Xj6E8Cmwxn2FPo1LIhw8afeSTrqFR1r9hBA==", "signatures": [{"sig": "MEUCIDrI7I9xUmT3qigAwzHfLGkfPCArBBHzbQTAieo7B4V1AiEAq0PblJBQ/k+wvN8/1hcMvLkIBusrw+xDVhLm9ISP4ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1094987}}, "0.24.1": {"name": "expo-notifications", "version": "0.24.1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.1.0", "abort-controller": "^3.0.0", "expo-application": "~5.5.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "5af80d091094023f9e1c21e2100ed2e258a29578", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.24.1.tgz", "fileCount": 580, "integrity": "sha512-Cu0ttNvsWvGkdPA1zMsS48iVhheIRlklwjMVReY+qostgA3Srm1BXMfnlt2qMINyh46KK9mnEdZ1KWpnJmqCwQ==", "signatures": [{"sig": "MEYCIQDmlIFjgLUafyE3PFCZf6x6QEWa7C9d1fFLBa2NGSWO9AIhAKacKXTiIT6d8Gb52/0KWZr1g8Q7UCk8ad+MR6am3kow", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1094758}}, "0.24.2": {"name": "expo-notifications", "version": "0.24.2", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.1.0", "abort-controller": "^3.0.0", "expo-application": "~5.5.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "f887e9f46c18ecdd8e6ced33b63b13196b2d1dd8", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.24.2.tgz", "fileCount": 581, "integrity": "sha512-sDqg4lUqEGINVjTPWAhqe9zit3oIyK7vwZS2+GYyL2cDqFKWF9vqhZ/yChS3VHgu/h5F349okkUiwQUcZIw07Q==", "signatures": [{"sig": "MEUCIQD809IV3htemYZicHABD1CydOAVIlqhopg0Ix2jITv4QQIgGTvAokwFWbl0PfOVme4+9NE2a1SGM0ZrrtKONKSXL9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1091488}}, "0.25.0": {"name": "expo-notifications", "version": "0.25.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.2.0", "abort-controller": "^3.0.0", "expo-application": "~5.6.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "38b02e94f25b43e8d72bfbdd19cc223bfd4d1bd4", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.25.0.tgz", "fileCount": 583, "integrity": "sha512-D5ARpSWHouvTeHSK74VtRnYFgPHb65Qvd7rbXOOVfhhOiwqDtewJGVKNDe/+Aq5R+ZQSP8SJYaAO2UgQ2Z85vQ==", "signatures": [{"sig": "MEQCIDODb3hBvAQ/jP6QSlDnwMbEL7J+TOHbZnctF3uw+Qb7AiAOyw/LATFeSXNkh0cQCvLxcRPXZjV/rC8Pv15nLcEWQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1090527}}, "0.26.0": {"name": "expo-notifications", "version": "0.26.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.3.0", "abort-controller": "^3.0.0", "expo-application": "~5.7.0", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "e0634d47832d2ae7c2111e4b682e53b3b2fd5150", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.26.0.tgz", "fileCount": 583, "integrity": "sha512-AisbnZleucAcpRnG7H8p3Pqb6iuQNXuWcCCQUxoJ7C6/xfUz9pRFmhznjjv3FFZveXm6bnCwHdnyaqXNwHjIaw==", "signatures": [{"sig": "MEQCIEDeH2CmkcQ5gg2yVjC8Z61NGQGC0NPCzCZN1STWUmWNAiBVuhdv/bWZDTEtp7X+7qyX5ozlRXfoiaWN6Nf/q9JT2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1090903}}, "0.0.0-canary-20231123-1b19f96": {"name": "expo-notifications", "version": "0.0.0-canary-20231123-1b19f96", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.0-canary-20231123-1b19f96", "abort-controller": "^3.0.0", "expo-application": "0.0.0-canary-20231123-1b19f96", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.0-canary-20231123-1b19f96"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "cc9fefb54abcd5ad0a0a65ed335355d116c0fd0e", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.0-canary-20231123-1b19f96.tgz", "fileCount": 584, "integrity": "sha512-KqDSFdapiqobQGiG+TQXMeQAhYmJ6BdHE6aQwtWcz4syiwipU/gJiqwI3dL9UhewWZi0kdUxN/FbS/NG8oYtNw==", "signatures": [{"sig": "MEYCIQD+Ziu2zKIzbFp1tcuxz0R7ARVi2v1F8YHd2MXqsfutEgIhAMSJRQ251yBW/MCB309W1OwBExLco3eBdlIpmfzZk2JA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315455}}, "0.0.1-canary-20231125-d600e44": {"name": "expo-notifications", "version": "0.0.1-canary-20231125-d600e44", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20231125-d600e44", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20231125-d600e44", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20231125-d600e44"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "fb90fd0a2d7179b5c1680b4a4e7da28ce2ae4b30", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20231125-d600e44.tgz", "fileCount": 584, "integrity": "sha512-BkZhjHxphgb0qRTaElf99KpcQqxVZtlL5W8eALnGWDiFqc1uJYDs1cTOVOVc2B5v2XRoIRn4iuf74tvWXetd9w==", "signatures": [{"sig": "MEUCIF4xHz9anFGyKdcE+rFonOVnZRDDuW2qh8eMUObFK3CdAiEAz27jPvHwqje35CmO3U2L1yN4ZmZkY9WuttbGVoz9CYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315455}}, "0.0.1-canary-20231129-c0ec023": {"name": "expo-notifications", "version": "0.0.1-canary-20231129-c0ec023", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20231129-c0ec023", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20231129-c0ec023", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20231129-c0ec023"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "88c253dffba44210d0b99e4fd303d995ead54daf", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20231129-c0ec023.tgz", "fileCount": 584, "integrity": "sha512-brKVxCVjnMzTDheXRpQXQjiZnptcBb2oNSYaGkqn16l70DTW/fP3xMPx57P7ToZQ+OHBzbIKOSPcc+2HCoFWhw==", "signatures": [{"sig": "MEUCIFdywJYYihhnqbkYxi4NWLEm5AXj6FgOLi+BJDpik5yqAiEAvCkJ6w6vPMBTUiiNSWh2/JEMcYXUtBjYnXNx0FaZjNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315454}}, "0.0.1-canary-20231130-ede75a7": {"name": "expo-notifications", "version": "0.0.1-canary-20231130-ede75a7", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20231130-ede75a7", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20231130-ede75a7", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20231130-ede75a7"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "1cc759124cd9e714d69aa1d58a4d45413c08005c", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20231130-ede75a7.tgz", "fileCount": 584, "integrity": "sha512-bAVubyobakWsdrjjpeS0dXdhYOLMPqnKXsvQ57CkUET9OjLfV/Gxs7iKhAZDlzkZXbkbawZSfgsZ0aEEh7cN1g==", "signatures": [{"sig": "MEUCIADUuKrKcF2cWUDuZiy07zYoykvTjuZVvKS8m94wia8rAiEAjeOtp/LnlqrLH88nfTNqrSvuz1gkkcHSQufhYkhl+vA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315454}}, "0.0.1-canary-20231130-ede75a7-1": {"name": "expo-notifications", "version": "0.0.1-canary-20231130-ede75a7-1", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20231130-ede75a7-1", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20231130-ede75a7-1", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20231130-ede75a7-1"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "3cb06ac1e30e32884774229b335798cc88812934", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20231130-ede75a7-1.tgz", "fileCount": 585, "integrity": "sha512-Q4RZUZ1YhVG5qUVjKgMVUWaYvZ4syBaaes0oDwI/A189PY7zrUed119ilWPcRXlwpmJeBlrxAticpFdm0mqAlA==", "signatures": [{"sig": "MEUCIGgNCOWoF8vg7byFaKGc4GFY/vFIHJyRF71naPliegUWAiEA4Gvdnmsfc3fnI/cUNtBrClf2hPi7VRffRMk0ggSB+4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1766356}}, "0.0.1-canary-20231130-c8a9bf9": {"name": "expo-notifications", "version": "0.0.1-canary-20231130-c8a9bf9", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20231130-c8a9bf9", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20231130-c8a9bf9", "@expo/image-utils": "^0.3.18"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20231130-c8a9bf9"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "ef942a1c259bcf334851aa27b465d2ef2d6671dd", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20231130-c8a9bf9.tgz", "fileCount": 584, "integrity": "sha512-CAVYdDvAIxomS1O+3CYZgJbNPUDUVCZD5JaMiiEIyhwJneoaZXvPwunl2Ql1w5pImiNVc65pSH09b4MXBt7DMA==", "signatures": [{"sig": "MEUCIHRZzYa6GJxIh0hw6iNhaHU3spIfBFCLESMVzY3yvF1QAiEAquctM5rxe+Pzzl8I6qNxO9TwBF9NHW8lfR3snBLrvZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315456}}, "0.0.1-canary-20231205-250b31f": {"name": "expo-notifications", "version": "0.0.1-canary-20231205-250b31f", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20231205-250b31f", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20231205-250b31f", "@expo/image-utils": "0.0.1-canary-20231205-250b31f"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20231205-250b31f"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "e8bfee1c3d0d3ab7b091312387389a98187befab", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20231205-250b31f.tgz", "fileCount": 584, "integrity": "sha512-p04c8lpsksEVqwjJR7DKR73WN7E9cuCNIcyue1JGO8aWUb6dBvLjew+BcSR41/idXcGDe/U794EFi2D0/VCRdQ==", "signatures": [{"sig": "MEUCIQDJh2WnNS6j8gXTCMlulqmhvikqot3Xp8wORi6/6HWkEAIgRA1GYgm+NY0BP4Erg9/fRPPrCUcgWpD+0ox0fnWfs0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315466}}, "0.27.0": {"name": "expo-notifications", "version": "0.27.0", "dependencies": {"uuid": "^3.4.0", "assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/uuid": "^3.4.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "54d52c2e45ea8b1c3a4dae81a2ae5035bfdca14c", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.0.tgz", "fileCount": 583, "integrity": "sha512-HLc11qKROo4No9swSnLVEKpDCd/9n8NwFTl5GTa5q/llEztlU0Xpn4Lt10rrL9RKinG/XDSPeFfqjxBJgdGXeA==", "signatures": [{"sig": "MEUCIQD72ckwV1e3qz/6hTKQ8zMZIObGJ++uTWVN9W7awW1IFgIgejCvGzx9VlEwhWr/M+Dgk6FsE+A7jVw6Ro3qlyUVTdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1091088}}, "0.27.1": {"name": "expo-notifications", "version": "0.27.1", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "44466ce9bc44a9a11eaa4d49c735b906a46daca3", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.1.tgz", "fileCount": 583, "integrity": "sha512-AXU/SiPPdyEKYxSUvHGmzUp5rl3ehQKdRpkwui959UP5TxBVartBmPVxruyYJogp6QGQPfAMJEQPD6w/tomP3g==", "signatures": [{"sig": "MEUCIQD5oBBx5ZJgfW7obmZHnKvbKojT48K3LsesVpxmstHuSwIgPxe8V1pTsnAGmkgv8V625Tn/ap5BAoOH8CkwGivHH0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1091123}}, "0.27.2": {"name": "expo-notifications", "version": "0.27.2", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "06e25b20a44ed95be9f64cd01844d52c03ae12db", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.2.tgz", "fileCount": 583, "integrity": "sha512-Pp54ek92hkRgNN4k6kH6TUhf18ZQeM5LQ54KyPFHu5IllwcmuniuVYhVIq4hx152OUyf/hBONncxBFuVJ/CMQg==", "signatures": [{"sig": "MEQCIHVGQPSSHhnP6rHHfw11a1C0gn4TSENT0Q+fv/hNVytSAiBY2zlaZuHtqBEfXiNYJ14QdnEra8dhd6h0NpzKPbw1TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1091213}}, "0.0.1-canary-20240109-93608d8": {"name": "expo-notifications", "version": "0.0.1-canary-20240109-93608d8", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240109-93608d8", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240109-93608d8", "@expo/image-utils": "0.0.1-canary-20240109-93608d8"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240109-93608d8"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "d8047db33bebb797e36e31d5e61d9890fbf20e09", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240109-93608d8.tgz", "fileCount": 584, "integrity": "sha512-z5WKX5MItBzZjiVWFyu/dXEUKqdnyBuw87OZ5irisPLqHBq7zQ2i0aD3LgY8HERsHYiVwC1PuLn3N/GG+p+zmQ==", "signatures": [{"sig": "MEQCIG5rsiUg6Qt9KcWnpFJRUZ0rnivXjNaMDdSOLXeSY375AiAIBceCnuvGQu+UMHO6PFEFKVWGW/lDlm4uqaepIWIvNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1316168}}, "0.27.3": {"name": "expo-notifications", "version": "0.27.3", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "18f3d6dadc14aefc32937bddc54572bbf68236a7", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.3.tgz", "fileCount": 583, "integrity": "sha512-P4zhbVYDhTtV7xCdcxpk4tTbWh33P+A0ZTbpUrp6vcpZk8QprhjhB2okr1/R1z0fpxUPv74KYex5fnAHxTVIvw==", "signatures": [{"sig": "MEYCIQDZO9YDtZm5JZ0xdzY3Qhh4A6k9MMhmGXidoEYmldfQJAIhANm4JRQI7khAaE6lqpc3gyOgtCox5RVTxf+jaSJ+sTE2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1091422}}, "0.27.4": {"name": "expo-notifications", "version": "0.27.4", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "04f36a8393049ee2fc7658494f1dc58c390da3d9", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.4.tgz", "fileCount": 583, "integrity": "sha512-CryQvd2x868GpBI2DzabPPRL2HAD8O+rm2hwYH1sN5lTb39kmC9jm2Riu6RdWoL5XYzayCp9IHIjL6Dnu9hHAw==", "signatures": [{"sig": "MEUCIBFmyy7kLIAOfO49ozkiFM6Yq1G/p8QvHfuVZ5kTs8t7AiEA80EK20u7JJMcPcdU51f2MomGLyIdQEGaMoX/yKsa6I8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1092167}}, "0.27.5": {"name": "expo-notifications", "version": "0.27.5", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "5697583754789f6d8cac25ccf05c691bf4267c37", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.5.tgz", "fileCount": 583, "integrity": "sha512-Ym1vl6/w5X4qEmVmO78T2GapsVeRp2/G2d+0NEUJufh57l6S/KTOlhjtXPJakDmefPcftc2Co5vHsF03Wpd2Bw==", "signatures": [{"sig": "MEYCIQD8VH+OWnZDACF7pdobgA1qO45Jj3uYYuqsDHj+Zxd1uQIhAOlC3FARr32nbFEVovsZ0j19JV4aB1okv6xw7lR8+Wbf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1092668}}, "0.27.6": {"name": "expo-notifications", "version": "0.27.6", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "ef7c95504034ac8b5fa360e13f5b037c5bf7e80d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.6.tgz", "fileCount": 583, "integrity": "sha512-F2iu/lzsrvfMyHA5BfnbZfE8fVLV8aQmNLk3NPztZ0g7911QEriZzH7BK/NKOZ5UHhJYI+hhYvcZCq2nFm1NLA==", "signatures": [{"sig": "MEUCIEenJTRQzs3Jc7ypMcabmV7xY6gIBaCopJ2D7mMZj67uAiEAyHnm6l8fcGAtuVJToaunsKvpsoA9VJQB7/UqUiSNm8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093220}}, "0.0.1-canary-20240222-c729a13": {"name": "expo-notifications", "version": "0.0.1-canary-20240222-c729a13", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240222-c729a13", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240222-c729a13", "@expo/image-utils": "0.0.1-canary-20240222-c729a13"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240222-c729a13"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "305de4187967c9e9164718ef957d40719a8b9205", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240222-c729a13.tgz", "fileCount": 584, "integrity": "sha512-XcdqMnCRdZv7Amcg3rS7iNkS9Q6QkrhE0dYQrZlGATPh4opqoTPNiFkZrD+x73Zuzj/A/JZlY8ANmSQBxbN4tQ==", "signatures": [{"sig": "MEQCIE6qi/wcG6o4916D/o61MznzcQjeUPS7/xW4rl7b46MUAiBCY8D5NtPlcJn/5S+4Mzbsmih3fKGhuz98q8kR8f3xQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1317355}}, "0.0.1-canary-20240228-7cee619": {"name": "expo-notifications", "version": "0.0.1-canary-20240228-7cee619", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240228-7cee619", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240228-7cee619", "@expo/image-utils": "0.0.1-canary-20240228-7cee619"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240228-7cee619"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "cec5dbf93061f3c693f8a13f9cd173ad5bd56ff8", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240228-7cee619.tgz", "fileCount": 584, "integrity": "sha512-QKzlUfUCv0jwNdFNX0/+Lcx6TeWji09Gm7vY3SFAWiZDh+RjQN30Wik7/INlImeTR6V6mm1J3wbQx7t0gKq5vQ==", "signatures": [{"sig": "MEUCIGEQDJuThIpk3oSBKN8WlD4pLRTvjWUy5ERg5kD7vohOAiEAmDaul/pe8dLs/t2DTnB62uVV9vuSE7O2sQ1ixB9UH8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1317356}}, "0.0.1-canary-20240305-e60019e": {"name": "expo-notifications", "version": "0.0.1-canary-20240305-e60019e", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240305-e60019e", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240305-e60019e", "@expo/image-utils": "0.0.1-canary-20240305-e60019e"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240305-e60019e"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "d3ff05a2ed683f96466d92c83595d16ea80963b9", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240305-e60019e.tgz", "fileCount": 584, "integrity": "sha512-BM6D7yG57JL7pUO4zJ4BQG3L7puc+guycLRU0Xh1SjV5SQAtl6/MVCDqTFcPUuF/pXNA+aOa31pbAD7czJ1ORQ==", "signatures": [{"sig": "MEUCIAj0xAZJreQHWTDuzzMt/SblX4TTu3I42PAiKiwPyA1dAiEA/REoREy0EOcrGK4YZrQHQ6c4pkUbLa5gQhDJjl1D37w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1317496}}, "0.0.1-canary-20240308-6715875": {"name": "expo-notifications", "version": "0.0.1-canary-20240308-6715875", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240308-6715875", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240308-6715875", "@expo/image-utils": "0.0.1-canary-20240308-6715875"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240308-6715875"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "44ef416f1287c946fab750146cad38ffb0527f33", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240308-6715875.tgz", "fileCount": 584, "integrity": "sha512-XvuKJu2pcjjFEEJdz9FZp5mpI2G6ArcsRJTR7I1rSoPQ+E73KpD6L+7Kc3NMEDZmrBE2mfbhbKuFIRg/hIUCIg==", "signatures": [{"sig": "MEYCIQDPHxur24WfEX6qf0mY9ZzNYknw7mhrcSOIiYps1H5jzwIhAIN/RezB1GXn3glUcbSZua19o6zlkfDiHmqwZw35tG28", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1317596}}, "0.0.1-canary-20240315-ce71005": {"name": "expo-notifications", "version": "0.0.1-canary-20240315-ce71005", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240315-ce71005", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240315-ce71005", "@expo/image-utils": "0.0.1-canary-20240315-ce71005"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240315-ce71005"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "1147afa4c6c326cfcc0996e8025ef2d1333ed719", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240315-ce71005.tgz", "fileCount": 583, "integrity": "sha512-JHRPV0cAZkKXy4JDQbVy9is0StZOVpdCBADVDI7/AcKxZSdEc1vlKIIezc67OmmsabqFxG6yN9hfqaJHS1T0bA==", "signatures": [{"sig": "MEQCIEMBlVW4CtgDS1Jfvj1r4nVkXKuYXFxQ/uuHc4V6sZitAiB5iojt7tTXqaYDavF4FfNlaoKy+NE/sZeCqHbQw7JNnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093042}}, "0.0.1-canary-20240318-53194f5": {"name": "expo-notifications", "version": "0.0.1-canary-20240318-53194f5", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240318-53194f5", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240318-53194f5", "@expo/image-utils": "0.0.1-canary-20240318-53194f5"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240318-53194f5"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "18f4cafa9d5d7939134254fa9a6a58ca2da97179", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240318-53194f5.tgz", "fileCount": 583, "integrity": "sha512-2b3dTYPnJPC1H7uwpapAKYsJ6RBAgI725V2QhtJBdDMhZBXJIZ6F84jieHcts8wfIKV4XhbBHhKPnvI7+V4CzQ==", "signatures": [{"sig": "MEQCIC28q7umxlgfgkcisFV4/uXSOGMoGu/jABcuk6lZoeySAiBzEoYFwT7lT5RUasn+Douzl0zb58ZeOo8XZGoRmLUJvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093042}}, "0.0.1-canary-20240318-af634b1": {"name": "expo-notifications", "version": "0.0.1-canary-20240318-af634b1", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240318-af634b1", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240318-af634b1", "@expo/image-utils": "0.0.1-canary-20240318-af634b1"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240318-af634b1"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "f29a7e236b5bf9e97c72b4298df3be1608b18023", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240318-af634b1.tgz", "fileCount": 583, "integrity": "sha512-ezwS1PCaea5SpjEhzkpsI/okLNlzyM2t25C1HtzbTeudbuiPucWPkLFbzLDYz7l1AD0ZtKqs5FDz6k9p3+xl/w==", "signatures": [{"sig": "MEUCIQCZKJLDaT+Pc84shfl2fA1HdvYh/p78lUY01+XlUXqd+QIgSq8c4rksu6s3XfLowEigr0RSJ5zUqEqhPxtOiQavTiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093042}}, "0.0.1-canary-20240318-dd8f245": {"name": "expo-notifications", "version": "0.0.1-canary-20240318-dd8f245", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240318-dd8f245", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240318-dd8f245", "@expo/image-utils": "0.0.1-canary-20240318-dd8f245"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240318-dd8f245"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "ce6aa6df60aeab043f5d296adfa07bcfd4408b9e", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240318-dd8f245.tgz", "fileCount": 583, "integrity": "sha512-hTXvebfdr+JayVOHC9z0mkbcsJvXG9jBwSe4sZICJHJtbTJ9JXxXtCWERkVK5yVDxaVdofjc5K+YWKRf3BSj4g==", "signatures": [{"sig": "MEQCIC40a3IYogB4vIymoQ7nYu2I2iTDxhCc3kXvpGKknjZyAiA5ls1uEpgxonF1Y5UEb1H1H950CO3+wy/kWgMDJzNnMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093042}}, "0.0.1-canary-20240320-8a10e09": {"name": "expo-notifications", "version": "0.0.1-canary-20240320-8a10e09", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240320-8a10e09", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240320-8a10e09", "@expo/image-utils": "0.0.1-canary-20240320-8a10e09"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240320-8a10e09"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "faba39495178a5317ca407b37ccab43e20488132", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240320-8a10e09.tgz", "fileCount": 583, "integrity": "sha512-2QQCjGgm1tMH1QOdgvSaORI5qBidV8WzwsvlRsusKud/DXbUBsaEzRjmyFedDZ7Tdol2nCS/i+vBA82RRXVOkQ==", "signatures": [{"sig": "MEQCIAL6O4qv3D67QwwF2zIbjc+Ednv0uf2PxAMNDsBkwwkAAiBrjFZspC1C0Y34kU3Z0GmPzi63ywdlteOfBycREPy1qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093042}}, "0.0.1-canary-20240327-a7302d9": {"name": "expo-notifications", "version": "0.0.1-canary-20240327-a7302d9", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240327-a7302d9", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240327-a7302d9", "@expo/image-utils": "0.0.1-canary-20240327-a7302d9"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240327-a7302d9"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "d532457de251730086883d221775959053c0576d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240327-a7302d9.tgz", "fileCount": 584, "integrity": "sha512-TvtHbrnIMe8UBuMm0CjvKH66Zr1qeVeM+v7fOsmZLVDXRi2CjegUDGJKKQwgZHqnF2s4VmmCrb38koFmBr7qZw==", "signatures": [{"sig": "MEUCIQC4SqBjJnIHX+00Pk1Z7FLM2oYwsHk545d4qcslKdtKQwIgeIYyO7IqTiPYgyO2/fyLHrAnph/jW+FhxV/cYnSkf0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095561}}, "0.0.1-canary-20240328-24ecc5e": {"name": "expo-notifications", "version": "0.0.1-canary-20240328-24ecc5e", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240328-24ecc5e", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240328-24ecc5e", "@expo/image-utils": "0.0.1-canary-20240328-24ecc5e"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240328-24ecc5e"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "6739e7c8fabb55d18b867f36aa4adf8d27073936", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240328-24ecc5e.tgz", "fileCount": 584, "integrity": "sha512-CNidAh5x3VDnsKyWz77MIZ7A7+YsTiZaCtoZ88oSqhJS2AO0AsfJGEg8AVhjDCgLpyC1gF9RhsS4OnGUdXLmFA==", "signatures": [{"sig": "MEUCIEZNBVZTK4YDCX3MFmxcXp7u0kq1zyvsXguVb1O2Wn+OAiEA2SJSw66yfNMymicaH5kuFNauZFlB5pkIXJO3NmUYZyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095561}}, "0.0.1-canary-20240404-e2b8743": {"name": "expo-notifications", "version": "0.0.1-canary-20240404-e2b8743", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240404-e2b8743", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240404-e2b8743", "@expo/image-utils": "0.0.1-canary-20240404-e2b8743"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240404-e2b8743"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "62a149d2de284b2e8455eaa5120e7f3e10b9b3ea", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240404-e2b8743.tgz", "fileCount": 584, "integrity": "sha512-y7EwuMCPariM80Nbr1aQmTOa+R5iYy2z7IwHGBDxpYFgRQMN0LYKPd2jHW7uPVE+zQM4p8q7dky+/cbhjsBvLg==", "signatures": [{"sig": "MEUCIFxvQQpEHoEIHJWrzJXA1wIWSW0+SMMM9FdO0sP2u5KIAiEAtlDJt1w96CAOc3VxxEndx3kqh1XZvVeg2g00VuCWF9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095648}}, "0.0.1-canary-20240405-cdfd9c1": {"name": "expo-notifications", "version": "0.0.1-canary-20240405-cdfd9c1", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240405-cdfd9c1", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240405-cdfd9c1", "@expo/image-utils": "0.0.1-canary-20240405-cdfd9c1"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240405-cdfd9c1"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "92b9e61750d43006daa5feb1fdb29b8f4d68f80f", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240405-cdfd9c1.tgz", "fileCount": 584, "integrity": "sha512-FdaFMt6+DR0fjI1CHw2O4fC7wT2Foqk55mtu0gUrqdhOoefxCIW4liOug/rLJggZS0CGFThn+2tLg4yyTYzShw==", "signatures": [{"sig": "MEQCIDPmxEP5mReIWML579DEHayApwpWQGmJcbpvJ0E7o1BSAiA0YBg0MeovW0gZSbNtogXEKH8rS4bnCIG9DOD+7Q+25w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095648}}, "0.0.1-canary-20240406-a4950c7": {"name": "expo-notifications", "version": "0.0.1-canary-20240406-a4950c7", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240406-a4950c7", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240406-a4950c7", "@expo/image-utils": "0.0.1-canary-20240406-a4950c7"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240406-a4950c7"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "1a20e1e3c4e53a9ff908b6f29399750d768a6fe2", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240406-a4950c7.tgz", "fileCount": 584, "integrity": "sha512-v5IJfr2GhTN/8SZ6/MbkHnIalDMZc5U0bNhDYT4fCRCVLFe3lgLYl9TsKWpho4IizvVMv548qYRfKxkiZP3ZzQ==", "signatures": [{"sig": "MEQCIGDSPIBArd4lznw+8iWrxwr1L/B75Qx2hwZ4PsvNl1I3AiAWaQhsdKtxJaEbgScCtyj8qADm9MX4kKHyFTsUApJ0dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095648}}, "0.0.1-canary-20240411-33a7dc1": {"name": "expo-notifications", "version": "0.0.1-canary-20240411-33a7dc1", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240411-33a7dc1", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240411-33a7dc1", "@expo/image-utils": "0.0.1-canary-20240411-33a7dc1"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240411-33a7dc1"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "ad4d91464af4cbad7bf77fc981d887d45949d25d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240411-33a7dc1.tgz", "fileCount": 584, "integrity": "sha512-Wb/ouTapslVnC0OeNMuJmmHFmMxzTatg/NN8Q0QN7o3yXk0sL16FWq5p6oQKN2NmVMdZkzjmTRl01ocrSIL47A==", "signatures": [{"sig": "MEQCIHWgz5a3T3x0OiNMVjJ6fTO1h5DTLUiDWM+gLOGTjJ2WAiB0DNAjsx5tek/vRt1MHLj4QS+hHxOdMAPScIhrNFlzNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093567}}, "0.0.1-canary-20240411-55a0085": {"name": "expo-notifications", "version": "0.0.1-canary-20240411-55a0085", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240411-55a0085", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240411-55a0085", "@expo/image-utils": "0.0.1-canary-20240411-55a0085"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240411-55a0085"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "cfe0c5e57d27e93997c2b37318d75b86dc8c219c", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240411-55a0085.tgz", "fileCount": 584, "integrity": "sha512-VzUastS4DV4UPAeGdMS7B7yobmP3voBV7FQ6nPCJwzT4A8oylVS5rRnXxd1yzZlIG44RDh6NKAsev1uGzIcdTA==", "signatures": [{"sig": "MEQCIE1PSDLnCOtUGU2PMmqihvoPKTVT54ie4+FwCZlWWJEvAiBhv0NnLfMaLybakFkM96cXZLwgfBjm4IHGN0qEsuAcig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093567}}, "0.27.7": {"name": "expo-notifications", "version": "0.27.7", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "b44f93b92eff22b4333e589c4d1a5e12223cb866", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.7.tgz", "fileCount": 584, "integrity": "sha512-qcetBEQlmV3VHvsJrpbQYDWpxXxFLs9HgUCb75YlGNzLrLG9puCE5lBIVskgErrG5ph8LaJs3JmazuwgW4qpQA==", "signatures": [{"sig": "MEUCID7yLQ8jUOUsuexl4d2BfITZkgYNOrfpQaMOkdyXfAgPAiEAsJAjwh5vdVGIYGFeeaoVOMVkNFIHLA/K33py+IBCipI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1094183}}, "0.0.1-canary-20240415-0868fc4": {"name": "expo-notifications", "version": "0.0.1-canary-20240415-0868fc4", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240415-0868fc4", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240415-0868fc4", "@expo/image-utils": "0.0.1-canary-20240415-0868fc4"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240415-0868fc4"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "3b36bea05c576a2f94629a44faad014078f80c44", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240415-0868fc4.tgz", "fileCount": 584, "integrity": "sha512-Ijct1FCEI6lRkplsm1QOuR/RsuR1lUPpHCwYDVw3DRx48nKyuJUGpnzA6jQ6K0g8DS9CjbS9MpeMNlkUo1AY8Q==", "signatures": [{"sig": "MEYCIQDQubvPwjtbmle855lfPMH+s0nyIjyrRHcNt0Vrwf/o4AIhALwP6FQW9hag246SFw7GiMHJi69WiZe1ZdHSC3RkSeuN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093492}}, "0.0.1-canary-20240415-cca04d8": {"name": "expo-notifications", "version": "0.0.1-canary-20240415-cca04d8", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240415-cca04d8", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240415-cca04d8", "@expo/image-utils": "0.0.1-canary-20240415-cca04d8"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240415-cca04d8"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "e77db339b9870948a443fae0d86cf0e80d72f9b5", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240415-cca04d8.tgz", "fileCount": 584, "integrity": "sha512-X7/B/R49ekUtkOTZ4w/xuHFhcrvoStQld9n0PguReH+JN56wG7yO3awb0S57r0fwMpSBUFW4bw1NURBtVFSFhg==", "signatures": [{"sig": "MEUCIGBOXBaAhB1e0rKaqIo47j0LzfhLG3pmUVR5GtQHCPdgAiEA5nvHGKGz0N5HG4tMLtTaXf2q9hsY6XUoAq0q9C14wFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093492}}, "0.0.1-canary-20240418-8d74597": {"name": "expo-notifications", "version": "0.0.1-canary-20240418-8d74597", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "0.0.1-canary-20240418-8d74597", "abort-controller": "^3.0.0", "expo-application": "0.0.1-canary-20240418-8d74597", "@expo/image-utils": "0.0.1-canary-20240418-8d74597"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "0.0.1-canary-20240418-8d74597"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "8cd54abe3be47357dd5a279b0da16b2b72917a72", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.0.1-canary-20240418-8d74597.tgz", "fileCount": 584, "integrity": "sha512-NUtvHKk2GP0c/L6MlK4d+V/Bx9AJAdc8meE4hN7CA5ogJc2uOxPMm4dX0spO853/SoYwEV0AKrBOMxMNq550lw==", "signatures": [{"sig": "MEUCIQCWd1UpdwwoIXVEqH/J2rOgBmn2dSz+nmDuhD3ZTOvuUwIgeqYZpNA47jZzERBjQTwcoXNszAJjN/5qCST6jKQBboY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093492}}, "0.28.0": {"name": "expo-notifications", "version": "0.28.0", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "d1001d33440866b88337f3c6593cc2f3b5084cfe", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.0.tgz", "fileCount": 584, "integrity": "sha512-0xAZZxptJVrJ9Zw47b2VgMEJ/7AWChgpecLYEQUzE/V4tl7MzuAmCEd0IfuHipM6/8fCQx1LcVR3NK46egWLdw==", "signatures": [{"sig": "MEUCICNdTkMvMRGCHn/H3Lsc1RRrVdplCY0SmrS2PZBs4fX+AiEA5c57areRqN1zwB3CmYGnflRw8AX2MTEuQE2IXXqm2h4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093408}}, "0.28.1": {"name": "expo-notifications", "version": "0.28.1", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "9152cb17100ce72b66f2bf642fb097c3ae2d2019", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.1.tgz", "fileCount": 584, "integrity": "sha512-qBVcq3lc+FIvcYt/8M+JB1c60g0hVuyGY4MVGTY56ciU6nMOCiBiz4XPc3DeiZA16jVtfriooWA26wqBkQfkHg==", "signatures": [{"sig": "MEUCIQC3ehkTpexNjxUVhczKYA7YPVZXMXX+eNNmd+806v72WgIgM+RDYPCdzkYtaxJQ0tQn3Lms5NIyFKflDrLwsCWxPZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1091925}}, "0.28.2": {"name": "expo-notifications", "version": "0.28.2", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "23ff1447056c4247b16e46d87438b846077af9d3", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.2.tgz", "fileCount": 585, "integrity": "sha512-4ajJ68OP10v4IM1u4ecOxFM9K5ZutwcUvB/yKcebJjVjMqXTOqjdzDLACXaTx50ucGJp4v86EVuHtk8+0r26ng==", "signatures": [{"sig": "MEUCIQCNi68InLelfcZZ1jGFqY6/Qj/TMiM7dGu9Cxq1GHr2igIgVe8NyxEftSCEwUGTIeu99agfKcztkPwzPM4eqeq+FVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1100175}}, "0.28.3": {"name": "expo-notifications", "version": "0.28.3", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "9076c2bd69c3de3338a2e2161c8bd5f18cb440cb", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.3.tgz", "fileCount": 585, "integrity": "sha512-Xaj82eQUJzJXa8+giZr708ih86GGtkGS8N01epoiDkTKC8Z9783UJ8Pf8+PSFSfHsY3Sd8TJpQrD9n7QnGHwGQ==", "signatures": [{"sig": "MEYCIQDdv8OK6u15cblqYKQfGN3qtPOIy7khfdWEL2e6pmaOFwIhAJc1tTDOLIwv+LV3Py8OsMCk/NsFe+XaaDQmVw5nF5uE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1105205}}, "0.28.4": {"name": "expo-notifications", "version": "0.28.4", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "10104592d074d1349336a2c9accb2d8281c59ee1", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.4.tgz", "fileCount": 590, "integrity": "sha512-UVlxury96VsGVVZua4BOEA+ZYWSOqrY6FuAZmAM/eFzJOKm3kreRQwXZtd7+OnWqqcvu26shskWeAcHus2q5tA==", "signatures": [{"sig": "MEUCIQCTm54TCEbXhTNxq0PvxwWg+aJz6FXFBtZPgFrM7C5+3gIgZ9RmplgpT0Cgmf6w35IvyOpeRoFLSsDgSHpI+0cCEZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1109348}}, "0.28.5": {"name": "expo-notifications", "version": "0.28.5", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "7d827a693fd922124639c737acd4560c437b8953", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.5.tgz", "fileCount": 590, "integrity": "sha512-3Ry6VvVbQgLmmwjyEL174sKg8Q+xcnvCqGj4MWcAm0WeOqt+VxgZsAbRVGkNCvooy5MFOvab3K1XL0cptYR2Mg==", "signatures": [{"sig": "MEQCIFFSSoD/QH2HC5Pe5rY/+WbwVkUOfWUiN1/v2ikZLnY5AiBBhutQbmRbfVEGufQTpJGDss5okeIwu7kKG8IaeuUbyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1109657}}, "0.28.6": {"name": "expo-notifications", "version": "0.28.6", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "f484b7d3c990d7248af52a8bd66018f177ca25f8", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.6.tgz", "fileCount": 590, "integrity": "sha512-4kh1OM/NiHyPKCAim3lnWyuywJzQPsiwAdMpSXjaJGvTvrn6HgsRnOlvZAiAyZk6yxZNGWxSgCciWLFFPPCfBw==", "signatures": [{"sig": "MEQCIHIVbTpTAZWeeZZWlk1OUa2jjLLPErt1C4epWv2ImxUdAiAUBxp4VoNc2AFgu7d3Sa62c7VuVYGoHCaCNpKyeSNxsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1109220}}, "0.28.7": {"name": "expo-notifications", "version": "0.28.7", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "14826c10d126b1dd16d1604add2a4766aecf7591", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.7.tgz", "fileCount": 590, "integrity": "sha512-P68/IlnxTtKjfHy22nC+o/H4VSgBVrmTQcx42JCevTbVQB8JTITmR42hvpZh8GUecB7jNOsuVokTTDZDE6aSyg==", "signatures": [{"sig": "MEUCIQDUswmV1jPy0zFAF70qcx9eiEQUDTb8V6b7aNwFv7/dggIgNIEPD9wyzqXPu4KvTg33Aib/54jTv472X1vMXMY0N5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1109053}}, "0.28.8": {"name": "expo-notifications", "version": "0.28.8", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "787568092992cabba969fda6f6bc7954e56189a8", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.8.tgz", "fileCount": 590, "integrity": "sha512-WuNMaOwbyevlGFxZnOM9GiGuS/dKBL8IysYl2e7tBvO+eQj9HOtw61vS7otT33WXsubK7cunRCb0/iGkeScaLQ==", "signatures": [{"sig": "MEUCIBQYwStwUiSIr7f/eamqNzxpGjc3vyrjXOEoDRF2g08vAiEA3+nYhCrg4XMAL4qXxd3mbg3CN+S7gZ1bjqcr982JDiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1113363}}, "0.28.9": {"name": "expo-notifications", "version": "0.28.9", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "3ef49ed8931c0f871c3ffed5af9d09552d169df3", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.9.tgz", "fileCount": 590, "integrity": "sha512-lilBS+0n+MC71jU6R9kYr91nev2God/OyxGRtab37XBmr7OWKL+jrahwdIRc2fJXq3hkQoiVWr4i9COGVFF8sA==", "signatures": [{"sig": "MEUCIQDgQxiByUWVqY1ptVUVisZxrMhoT68aeBOhwp66jZlKHQIgZMjIk37U4UBXGnJ/7Z3b5WU5T2JYeNU1po0TIibaEHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1114270}}, "0.27.8": {"name": "expo-notifications", "version": "0.27.8", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~15.4.0", "abort-controller": "^3.0.0", "expo-application": "~5.8.0", "@expo/image-utils": "^0.4.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "2a04b6a17ac3a02a3bc524196c49d7184b9ecccd", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.27.8.tgz", "fileCount": 590, "integrity": "sha512-osGkKr+K3Qan4yPtGd5OrHi8nyYwoumfW//xozfYUnZrYcNt/nzLJj3BzvE0upU62zKf2MEmXg3bYjDP5l3cFA==", "signatures": [{"sig": "MEUCIFV5nBlRe/DNNcDSQ1wBjR9/7ZRGZcgDT3q1F/JKJiyAAiEA4uHQxEte1UqA2Vw453fzKdRYTKETBVlJZsPvNqZp/JQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1111461}}, "0.28.10-canary-20240625-2333e70": {"name": "expo-notifications", "version": "0.28.10-canary-20240625-2333e70", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "16.0.3-canary-20240625-2333e70", "abort-controller": "^3.0.0", "expo-application": "5.9.2-canary-20240625-2333e70", "@expo/image-utils": "0.5.2-canary-20240625-2333e70"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240625-2333e70"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "e38910706833ad64e5a7e1c15fb0336cfe65a4d2", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.10-canary-20240625-2333e70.tgz", "fileCount": 590, "integrity": "sha512-lzFVRF4WUNDXNthZS4PiVIeuXqLd6Th0wFCzSLE20AUZkaOAkA6eepvzvNH8VjB/1y40VD+HAEMXjulHzD5IxQ==", "signatures": [{"sig": "MEQCIENp4EXSOSRIv52ktIycgmjm3SWUaD0ujAb7uKOw1TsnAiAOk+jyKIxPdeqSrwiUf+eq2tgSi4HJ3lsO3+DK4BkTxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1115053}}, "0.28.10-canary-20240627-1402f4b": {"name": "expo-notifications", "version": "0.28.10-canary-20240627-1402f4b", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "16.0.3-canary-20240627-1402f4b", "abort-controller": "^3.0.0", "expo-application": "5.9.2-canary-20240627-1402f4b", "@expo/image-utils": "0.5.2-canary-20240627-1402f4b"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240627-1402f4b"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "c1ff3b06900d081f804b1e943b01dff009203b9c", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.10-canary-20240627-1402f4b.tgz", "fileCount": 590, "integrity": "sha512-kf9ezJGOM9WWZDS6x0ybNaVoRh/PuoeUyPrd5EN/WXHMTPUU5Mqn4MxvPf4UF2FAB+/KbIH9SQqfE0tOwU7xBA==", "signatures": [{"sig": "MEQCIAiJXqa/GpMJC+kd+dz34wslCtPx/vaRTx3BZqhczp1KAiAos3ScX0rqXf5hX9ua8IegPJ1Xl/Yjyl+qnR6iQ07EUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1115173}}, "0.28.10-canary-20240628-1ba8152": {"name": "expo-notifications", "version": "0.28.10-canary-20240628-1ba8152", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "16.0.3-canary-20240628-1ba8152", "abort-controller": "^3.0.0", "expo-application": "5.9.2-canary-20240628-1ba8152", "@expo/image-utils": "0.5.2-canary-20240628-1ba8152"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240628-1ba8152"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "919f3644a6efa2a4bed64c202d3c342aa01e9751", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.10-canary-20240628-1ba8152.tgz", "fileCount": 590, "integrity": "sha512-5R/Mi8GkU1GbIIu+Iqk0GmbkDJojq0r/MZfZixWMnp5KdzxXrY553RBrkoRKC/Qlc5QBaGkQ5hwv5rI/b+DvfQ==", "signatures": [{"sig": "MEUCIQCy33ybVbSjH6U5LMEO2kn35yHhMQFG6dKcOFNvUYieyAIgTDcC1+Hhvr+rMA4O+PqgDyomewKhhLR8zAQjU9JMq08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1115173}}, "0.28.10": {"name": "expo-notifications", "version": "0.28.10", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "5db8ba41f5fd09db7db8323997f2185feb1830cc", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.10.tgz", "fileCount": 590, "integrity": "sha512-<PERSON><PERSON>+Edc7lQajsD7//0ivP68bCijyQV7QEk7mCaORXMltM9Ec516CrWKP1v3rRX6RvXVP1s5t6K/aEooiz6AhM+A==", "signatures": [{"sig": "MEYCIQChQ1Nq8FWyx1v3icrFwMhnJOmym2PrcAhRx9iqvnPe7QIhAJizfuQHNaDMNaV6DjqDfETu3+636YOSjge1/hqiIy6/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1113735}}, "0.28.11-canary-20240719-83ee47b": {"name": "expo-notifications", "version": "0.28.11-canary-20240719-83ee47b", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "16.0.3-canary-20240719-83ee47b", "abort-controller": "^3.0.0", "expo-application": "5.9.2-canary-20240719-83ee47b", "@expo/image-utils": "0.5.2-canary-20240719-83ee47b"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240719-83ee47b"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "dist": {"shasum": "70d07ae55d7b4692a64248fbd5f673555c4a30ab", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.11-canary-20240719-83ee47b.tgz", "fileCount": 590, "integrity": "sha512-zgLmaXjG87YpA4JvHFKxcpaJzePGiLbFzwLft8MyX3yd/UZtOal0lurP00WdwRUJlNrtasvoN+kU4FweO9h21Q==", "signatures": [{"sig": "MEUCIErXLmnSaO+YGY4g73Cbdvw83+KBNTrhJjVno02XOOFHAiEAs+eWgId0XhvKddALmDCdf3y/er+VZZz9+J16C0t+9Pk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1113813}}, "0.28.11": {"name": "expo-notifications", "version": "0.28.11", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "70aad815ee0c81a39307c3daa44f08045bf29356", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.11.tgz", "fileCount": 590, "integrity": "sha512-fLW<PERSON>+wRcZxEHAJtM/4+bcLzgh5w6+PR9Hu4/BSf65M8ZA6QYX0ALeXi+XawCHWfgua/UOBopBY51OTPfTPei8Q==", "signatures": [{"sig": "MEQCIAriU7T+xYZZv69ZLIpwbQQnRrbHzd/RR4UvbxFRWUMaAiAVtqEuX60/vCHVy9iRchB01SrynDm6gvCbcGkbh1fRjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1114374}}, "0.28.12": {"name": "expo-notifications", "version": "0.28.12", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "ab7c427c1200c929c369b95398d2707fc746a543", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.12.tgz", "fileCount": 590, "integrity": "sha512-fQsnEay4Cy2K1haGMZ2fHfBvrAUerWkS74y33ljybJ2UBjxOJ/hL4A/4EuImw7rbpd79qSNJDWQmqSsBM2EkVQ==", "signatures": [{"sig": "MEYCIQD/1rHVycco61fXuyMjuw5AulXyAQMSwAG0mdEW/rs6TgIhAJTfK1Df6tj14VrrMYhn/NQX0v/wdISgdR+kx3JdB2Ii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1114806}}, "0.28.13": {"name": "expo-notifications", "version": "0.28.13", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "4dc27f034216fd65c7d0417217a28edd7c8ebb08", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.13.tgz", "fileCount": 591, "integrity": "sha512-fifu7BkgecpPDEVGKKN87XADzP3O+W9E3eyd7ocAdPZwC/MjLXGRCBsGgIiIV/3V5lbOYdyBvjAHlBbQQjtspQ==", "signatures": [{"sig": "MEYCIQDe25Q5vsMr52dWJgOQiwjSaeifEV3BmSqSqJ+ScVd/TAIhALEgn7BP8XOKQI9z6WwUwmBSWynOIJZgJBrEoCWc1iJN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1124117}}, "0.28.14": {"name": "expo-notifications", "version": "0.28.14", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "33c7dd24167cfcb507f267f6da30ed2ed761b0f7", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.14.tgz", "fileCount": 591, "integrity": "sha512-bRdJIuJiGTVPbcTVjeFgkT5qXvOaG4BEHcYU8yqMmWmKt4e4eInDjVzKf0axJ/dvwF3kyeoPENtRBiDbLCutjw==", "signatures": [{"sig": "MEUCIBmh+no/4cD6nIlefjdsevXJC2uxoaCqUpl3HsVOJfu8AiEAkB4Dk+G3udVZfmd+7B2283aTq4itpbJsB6OzC8bHMbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1126872}}, "0.28.15": {"name": "expo-notifications", "version": "0.28.15", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "41d43d518ee5be2b7d57f4472ba49402717d3c48", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.15.tgz", "fileCount": 591, "integrity": "sha512-T303dw2akcG4w8HBKI7+Y1v5kFxYy53DjlBSLBMo5f22vnLTHWaa8oOhmpwGRGU4uiX56Bw4q0wywCTSgbEamQ==", "signatures": [{"sig": "MEUCIBF/bpkmM9FN7DpYvY8FFvwMtoBRB4j8Ly9aBSN48WpcAiEAyJJGmYjfpk3ZFnvlROR6Y79X4Ndv2GGyru5b2wip1zA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1129567}}, "1.0.0-canary-20240814-ce0f7d5": {"name": "expo-notifications", "version": "1.0.0-canary-20240814-ce0f7d5", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "17.0.0-canary-20240814-ce0f7d5", "abort-controller": "^3.0.0", "expo-application": "6.0.0-canary-20240814-ce0f7d5", "@expo/image-utils": "0.5.2-canary-20240814-ce0f7d5"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240814-ce0f7d5"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "dist": {"shasum": "6ff6e7d73341407295c57666533b8eb5eca4d651", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-1.0.0-canary-20240814-ce0f7d5.tgz", "fileCount": 591, "integrity": "sha512-WT5NpTutZNZhaXYGtdSDhPZ82CU2d90hXwsxiHVJJVWBM/YHaOlYDazcW9aH1Vr2dnKdy85vs9olxL58uE1tPw==", "signatures": [{"sig": "MEUCIQD6wOJiN5XiPrX6FxY4uKBvgdxvCQR4kRtV6NW+oZUjBQIgVyocHHulK1kN0qs1MxCd7zmozthcvFAVqd+cFqpc/dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1126239}}, "0.28.16": {"name": "expo-notifications", "version": "0.28.16", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "2c712a1634c707945eb6c9f31af593c5939f87dc", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.16.tgz", "fileCount": 591, "integrity": "sha512-sj4oDip+uFNmxieGHkfS2Usrwbw2jfOTfQ22a7z5tdSo/vD6jWMlCHOnJifqYLjPxyqf9SLTsQWO3bmk7MY2Yg==", "signatures": [{"sig": "MEUCIQD+MXsjwvNKlR8TkQGL/2FiBOPOvQt6HKJBpZc8sp9hDAIgB38mUE/Tn4ls60FuIosGIdSzTffi/6lm/mdRgijCUdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1134844}}, "1.0.0-canary-20240904-69100c1": {"name": "expo-notifications", "version": "1.0.0-canary-20240904-69100c1", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "17.0.0-canary-20240904-69100c1", "abort-controller": "^3.0.0", "expo-application": "6.0.0-canary-20240904-69100c1", "@expo/image-utils": "0.5.2-canary-20240904-69100c1"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240904-69100c1"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "dist": {"shasum": "6146775dad4e9190b255f5a8018c9d4e141c149d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-1.0.0-canary-20240904-69100c1.tgz", "fileCount": 587, "integrity": "sha512-830jntiqLP0PNtTKcV/JFHEZXD2CYTkp6Brlu2egs1rqvYr94C44eiW2pQrcWQFjaCOgIToUBP6To0nnFxrwMQ==", "signatures": [{"sig": "MEQCIAR1GFVp2B+LeD1a7ORyljHoLC+8SNYDl+c7ieVlTB/KAiBEWQSd+MFE0Zocw63GDLemwzkVhnisBf+oEtWTkM/uqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1127449}}, "1.0.0-canary-20240912-1059f85": {"name": "expo-notifications", "version": "1.0.0-canary-20240912-1059f85", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "17.0.0-canary-20240912-1059f85", "abort-controller": "^3.0.0", "expo-application": "6.0.0-canary-20240912-1059f85", "@expo/image-utils": "0.5.2-canary-20240912-1059f85"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240912-1059f85"}, "peerDependencies": {"expo": "52.0.0-canary-20240912-1059f85", "react": "*", "react-native": "*"}, "dist": {"shasum": "243466f88d457f01c0ec9cdddca708a8ef130d9d", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-1.0.0-canary-20240912-1059f85.tgz", "fileCount": 587, "integrity": "sha512-nqnhKd9OQ2wNLUb3U0pTjSrr852adQTQGhzFz0XLvyxxRFZdj7D0z/kXQOeQE8+HOcyZHRm2nGgjKzYmt4atrg==", "signatures": [{"sig": "MEUCIQDAtJYtOqQ28KXRTNBnQwoQtXzMMgg6vcWxn45J4i99yQIgWulQe0db/Tqu+GwN74X5XBNmYsasIz3tdl3b9wFgXs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1127478}}, "0.28.17": {"name": "expo-notifications", "version": "0.28.17", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "e9cb4edb3159b4cd8504df27e9dfbe6e9d22ea89", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.17.tgz", "fileCount": 589, "integrity": "sha512-tuhc/X385O1gLSBEsPpXSqmmBK6Ve6zG8u6YFa1kXILbyy83DHJuHB5ELJKo/HZdstlYeFjkDCei4haOuxCLCQ==", "signatures": [{"sig": "MEYCIQDhD9Ou7g0QA0H2ZvepFOiD9VCS3kjWQbAPMhJ4ZeSFBAIhAPxa1mcJt6vCFPB2QllB/dXQH2iTr0EaPDOnDwcf5m1j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1130915}}, "1.0.0-canary-20240927-ab8a962": {"name": "expo-notifications", "version": "1.0.0-canary-20240927-ab8a962", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "17.0.0-canary-20240927-ab8a962", "abort-controller": "^3.0.0", "expo-application": "6.0.0-canary-20240927-ab8a962", "@expo/image-utils": "0.5.2-canary-20240927-ab8a962"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20240927-ab8a962"}, "peerDependencies": {"expo": "52.0.0-canary-20240927-ab8a962", "react": "*", "react-native": "*"}, "dist": {"shasum": "5757b58f901508c887b7fdb0264fa291f30fc0c6", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-1.0.0-canary-20240927-ab8a962.tgz", "fileCount": 589, "integrity": "sha512-n/OpNaL/j3xSvft8oNXo5tsBPq1KRPwgn+87L72MCd5DraNws0k4WtOxanW49Gu07AlwNqaBm6ly50kJ8uBrWg==", "signatures": [{"sig": "MEYCIQD+RUVZhcMDZ8ztRAnZsIXo6mgPaGBIJt0Nj2ug20vrtwIhAOWGma+ILly4O9GZbW+WanpVNjmhYmDhBfmAA9RsJmAE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1138357}}, "0.28.18": {"name": "expo-notifications", "version": "0.28.18", "dependencies": {"assert": "^2.0.0", "badgin": "^1.1.5", "fs-extra": "^9.1.0", "@ide/backoff": "^1.0.0", "expo-constants": "~16.0.0", "abort-controller": "^3.0.0", "expo-application": "~5.9.0", "@expo/image-utils": "^0.5.0"}, "devDependencies": {"memfs": "^3.2.0", "node-fetch": "^2.6.7", "@types/node-fetch": "^2.5.7", "expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "dist": {"shasum": "a3e5488429079d664885e975985dd2d6bdb52a5b", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-0.28.18.tgz", "fileCount": 589, "integrity": "sha512-oRvr8rYhbbKNhVgcO+fj5g5g6vS0umGcElpeMSWa0KudUfOOgV6nNLvv5M89393z2Ahd7wPK4bnK8lygc0nCPQ==", "signatures": [{"sig": "MEQCICPEJQy2wXGRjdoVnQmT1CwiJ7XCbeV2WRf1vC1WzPP0AiBL1ME5tUsK3KHVb+dTNy4bPjdt1KMemTuelV1Yu+qpVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1138982}}, "1.0.0-canary-20241008-90b13ad": {"name": "expo-notifications", "version": "1.0.0-canary-20241008-90b13ad", "dependencies": {"@expo/image-utils": "0.6.0-canary-20241008-90b13ad", "@ide/backoff": "^1.0.0", "abort-controller": "^3.0.0", "assert": "^2.0.0", "badgin": "^1.1.5", "expo-application": "6.0.0-canary-20241008-90b13ad", "expo-constants": "17.0.0-canary-20241008-90b13ad", "fs-extra": "^9.1.0"}, "devDependencies": {"@types/node-fetch": "^2.5.7", "expo-module-scripts": "3.6.0-canary-20241008-90b13ad", "memfs": "^3.2.0", "node-fetch": "^2.6.7"}, "peerDependencies": {"expo": "52.0.0-canary-20241008-90b13ad", "react": "*", "react-native": "*"}, "dist": {"integrity": "sha512-UVt1GPKsZ/b7ypHFtyfdS/yREc1GmKIoPlpluxeugt2vTQWA64Vxd3cNV1zkHLHiP1HYmwDL2teVc9ga9NdeWw==", "shasum": "5739d4f57941d3acf04a80d3103fccaea3077043", "tarball": "https://registry.npmjs.org/expo-notifications/-/expo-notifications-1.0.0-canary-20241008-90b13ad.tgz", "fileCount": 588, "unpackedSize": 1152679, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBATnYVYdflQRbQi6v+iA1iFzhvRzvD/yKM5lqIy/I9VAiEA9AQsDI3BhDU9kL6K9HzmlY3rgBSWxNOZomFsVC5DkpY="}]}}}, "modified": "2024-10-08T10:00:04.309Z"}