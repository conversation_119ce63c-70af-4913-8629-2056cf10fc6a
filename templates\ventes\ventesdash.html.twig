<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<style>
    .dashboard-container {
        max-width: 100%;
        padding: 20px;
        margin-left: 1.5%;
        z-index: 99999;
    }

    .row {
        display: flex;
        flex-wrap: nowrap; 
        margin-bottom: 18px;
    }

    .dashboard-container .row:first-child {
        gap: 18px; 
    }

    .map-analytics-vente {
        width: 490px; 
        border-radius: 10px;
            flex-direction: column; 
    background: linear-gradient(to bottom, var(--color-background-light), var(--color-background-dark)); 
        padding: 0; 

    }

    .topsecond-info {
        width: 242px;
        padding: 0; 
        border-radius: 10px;
    }

    .detailsmotif-section {
        width: 230px; 
        background: linear-gradient(to bottom, var(--color-background-lightdetailsmotif), var(--color-background-darkdetailsmotif)); 
        border-radius: 10px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        padding: 0; 
    }

    .historique-ventes-nord {
        width: 985px;
        height: 40px;
        background-color: #f9f9f9; 
        border-radius: 10px;
            flex-direction: column; 
    background: linear-gradient(to bottom, var(--color-background-light), var(--color-background-dark)); 
    display: flex;
    position: relative;
    }

         .par-techno,
     .par-gamme,
    .par-parcours, 
    .par-status-preco {
    border-radius: 15px;
    padding: 15px;
    background: linear-gradient(to bottom, var(--color-background-light4), var( --color-background-dark4)); 
    display: flex;
    margin-right:18px;
    width: 236px;
    position: relative;
  }

  
    :root {
        --color-background-light: #e6f7ff;
        --color-background-light4: #c0e8f7;
                --color-background-dark4: #e7f5fd;
        --color-background-lightdetailsmotif: #abdef2;
        --color-background-darkdetailsmotif: #e7f5fe;
                    --text-colors4: #1d4d6b;

    }

    [data-theme="dark"] {
        --color-background-light: #303238;
        --color-background-dark4: #2c2d30;
                --color-background-light4: #1e1f21;
         --color-background-lightdetailsmotif: #122e3a;
        --color-background-darkdetailsmotif: #2a2c30 ;
                    --text-colors4: #ffffff; 

    }
   
.text-header4s {
    font-size: 0.9em;
    color: var( --text-colors4);
    margin: 0;
}

</style>
<div class="dashboard-container">

  <div class="scrollable">
  <div class="row">
    <div class="map-analytics-vente"> {% include 'ventes/DashboardItems/mapanalytics.html.twig' %} </div>
    <div class="topsecond-info"> {% include 'ventes/DashboardItems/topsecond.html.twig' %} </div>
    <div class="detailsmotif-section"> {% include 'ventes/DashboardItems/detailsmotifs.html.twig' %} </div>
  </div>
  {# <div class="row">
    <div class="historique-ventes-nord"> {% include 'ventes/DashboardItems/HistoriqueVentesNord.html.twig' %} </div>
  </div> #}
  <div class="row">
    <div class="par-techno"> {% include 'ventes/DashboardItems/partechno.html.twig' %} </div>
    <div class="par-gamme"> {% include 'ventes/DashboardItems/pargamme.html.twig' %} </div>
    <div class="par-parcours"> {% include 'ventes/DashboardItems/parparcours.html.twig' %} </div>
    <div class="par-status-preco"> {% include 'ventes/DashboardItems/parstatuspreco.html.twig' %} </div>
  </div>
  <div class="row">
    <div class="full-tabledata"> {% include 'ventes/DashboardItems/table.html.twig' %} </div>
  </div>
</div>

   <div class="row">
    <div class="full-cmd-bottom"> {% include 'ventes/DashboardItems/cmdfooter.html.twig' %} </div>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/js/bootstrap.min.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>