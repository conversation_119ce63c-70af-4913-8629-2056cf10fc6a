{# content.html.twig #}
<style>

.checkbox {
    opacity: 0;
    position: absolute;
}

.label {
    background-color: #111;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    position: relative;
    height: 15px;
    width: 35px;
    transform: scale(1.5);
}

.label .ball {
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: -10px;
    height: 12px;
    width: 12px;
    transform: translateX(10px);
    transition: transform 0.2s linear;
}

.checkbox:checked + .label .ball {
    transform: translateX(30px);
}

.fa-moon {
    color: #f1c40f;
}

        .collapsed {
            width: 0 !important;
            overflow: hidden;
            transition: width 0.3s;
        }

  .nav-buttons {
        
        padding: 5px 10px;
        background-color: transparent;
        color: darkgrey;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      
        margin-left: 30px;
    }
    .nav-button {
        
        padding: 4px 8px;
        background-color: #47c2ae;
        color: #FFF;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        font-weight: bold;
        width: 100px;
        margin-left: 20px;
        
    }

    .nav-buttones {
    padding: 3px 15px;
    color: var( --color-buttons-nav);
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 12px; 
}

.nav-buttones.active {
    background-color: #007bff; 
    color: #fff;
}

.nav-buttones:hover {
    background-color: #555;
    color: #fff;
}
 .dark-mode .nav-buttons{
    color: darkgrey;
 }
  .dark-mode .nav-button{
    color: #000;
 }
.nav-buttons-group {
    display: flex;
}
 
</style>
<div class="left-side-topbar">

        
        {% if liste %}  
               <div class="nav-buttons-group">
         <a href="{{ path('liste') }}" class="nav-buttones active"> 
                        <i class="fas fa-home"></i> 
                        </a>
                        </div>
        {% elseif detailsliste %}  
        <div class="nav-buttons-group">
     <a href="{{ path('liste') }}" class="nav-buttones"> 
                        <i class="fas fa-home"></i> 
                        </a>
         <div class="nav-buttones active"> {{ clusterDetails.lbl_cluster }}</div>

        </div>
        {% elseif clusterDetailsRues %}
        <div class="nav-buttons-group">
     <a href="{{ path('liste') }}" class="nav-buttones"> 
                        <i class="fas fa-home"></i> 
                        </a>
        <a href="/prises/parc/migrables/ville/{{ code_cluster }}" class="nav-buttones">
            {{ clusterDetailsRuess.nom_voie[0]|first.data.lbl_cluster }}
        </a>
            <div class="nav-buttones active">
                {{ clusterDetailsRuess.nom_voie[0]|first.data.ville }}
            </div>
        </div>


            {% elseif showtimeline %}  
            {% elseif synthese %}
               <div class="nav-buttons-group">

                <a href="{{ path('synthese') }}" class="nav-buttones active"> 
                        <i class="fas fa-home"></i> 
                        </a>

        </div>  
             {% elseif synthesedetaille %}
                     <div class="nav-buttons-group">

        <a href="{{ path('synthese') }}" class="nav-buttones "> 
                            <i class="fas fa-home"></i> 
                            </a>
            <div id="libelleCluster" class="nav-buttones active"> libelle</div>
            </div>  
            
             {% elseif synthesedetaillerue %}
             <div class="nav-buttons-group">    
        <a href="{{ path('synthese') }}" class="nav-buttones "> 
                            <i class="fas fa-home"></i> 
                            </a>
            <a  href="/prises/synthese/detaille/{{ code_cluster }}" id="libelleCluster" class="nav-buttones "> libelle</a>
            <div id="selectedCity" class="nav-buttones active">ville </div>
            </div>  
            {% elseif showconsolidation %}
             {% elseif showconsolidationdetaille %}
          {% else %}
        <div class="nav-buttons-group">
        <div class="nav-button active" >Ventes Brutes</div>
        <div class="nav-button">Ventes Validés</div>
        <div class="nav-button">Ventes Raccordé</div>
    </div>
    {% endif %}
</div>

<div class="right-side-topbar">

    
{# 
    <div class="switch-container">
        <input type="checkbox" class="checkbox" id="chk" />
        <label class="label" for="chk">
            <div class="ball"></div>
        </label>
    </div> #}

    <div class="svg-topbar" id="sidebarToggleBtn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-chevron-left" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
        </svg>
    </div>

    <div class="svg-topbar" id="collapse-right-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-chevron-right" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
        </svg>
    </div>
    <div class="search-container">
        <input type="text" placeholder="Rechercher">
      
    </div>
</div>

<script>

    const libelle = localStorage.getItem("selectedCluster");
  
     const ville = localStorage.getItem("selectedCity");
  
    // Display retrieved values in the breadcrumb links
    document.getElementById("libelleCluster").innerText = libelle;
 document.getElementById("selectedCity").innerText = ville;

</script>