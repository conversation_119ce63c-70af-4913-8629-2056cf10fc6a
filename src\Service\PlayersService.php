<?php

namespace App\Service;

use Symfony\Contracts\HttpClient\HttpClientInterface;

class PlayersService
{

    private $client;

    public function __construct(HttpClientInterface $client)
    {
        $this->client = $client;
    }

    public function getPlayersByClubId($clubId){
        try {
            // Fetch user data
            $responsePlayers = $this->client->request('GET',
                'https://transfermarkt-api.fly.dev/clubs/'.$clubId.'/players', [
                'headers' => [
                    'accept' => 'application/json',
                ]
            ]);
            $playersData = json_decode($responsePlayers->getContent(), true);

            if(is_array($playersData) && array_key_exists('players', $playersData)){
                return $playersData['players'];
            }

            return [];
        } catch (\Exception $e) {
            return [];
        }
    }

}