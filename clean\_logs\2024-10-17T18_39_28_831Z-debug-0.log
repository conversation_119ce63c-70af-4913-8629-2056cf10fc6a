0 verbose cli C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@10.6.0
2 info using node@v20.14.0
3 silly config:load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config:load:file:C:\Users\<USER>\.npmrc
5 silly config:load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
6 verbose title npm install bootstrap
7 verbose argv "install" "bootstrap"
8 verbose logfile logs-max:10 dir:C:\Users\<USER>\OneDrive\Desktop\symfony\Dashboard\clean\_logs\2024-10-17T18_39_28_831Z-
9 verbose logfile C:\Users\<USER>\OneDrive\Desktop\symfony\Dashboard\clean\_logs\2024-10-17T18_39_28_831Z-debug-0.log
10 silly logfile done cleaning log files
11 silly idealTree buildDeps
12 silly fetch manifest bootstrap@^5.3.3
13 http fetch GET 200 https://registry.npmjs.org/bootstrap 269ms (cache miss)
14 silly placeDep ROOT bootstrap@5.3.3 REPLACE for:  want: ^5.3.3
15 silly reify moves {}
16 silly audit bulk request {
16 silly audit   '@babel/code-frame': [ '7.10.4', '7.24.7' ],
16 silly audit   '@babel/generator': [ '7.24.10' ],
16 silly audit   '@babel/helper-annotate-as-pure': [ '7.24.7' ],
16 silly audit   '@babel/helper-create-class-features-plugin': [ '7.24.8' ],
16 silly audit   semver: [ '6.3.1', '7.5.3', '7.3.2', '5.7.2', '7.6.0' ],
16 silly audit   '@babel/helper-environment-visitor': [ '7.24.7' ],
16 silly audit   '@babel/helper-function-name': [ '7.24.7' ],
16 silly audit   '@babel/helper-hoist-variables': [ '7.24.7' ],
16 silly audit   '@babel/helper-member-expression-to-functions': [ '7.24.8' ],
16 silly audit   '@babel/helper-module-imports': [ '7.24.7' ],
16 silly audit   '@babel/helper-module-transforms': [ '7.24.9' ],
16 silly audit   '@babel/helper-optimise-call-expression': [ '7.24.7' ],
16 silly audit   '@babel/helper-plugin-utils': [ '7.24.8' ],
16 silly audit   '@babel/helper-replace-supers': [ '7.24.7' ],
16 silly audit   '@babel/helper-simple-access': [ '7.24.7' ],
16 silly audit   '@babel/helper-skip-transparent-expression-wrappers': [ '7.24.7' ],
16 silly audit   '@babel/helper-split-export-declaration': [ '7.24.7' ],
16 silly audit   '@babel/helper-string-parser': [ '7.24.8' ],
16 silly audit   '@babel/helper-validator-identifier': [ '7.24.7' ],
16 silly audit   '@babel/helper-validator-option': [ '7.24.8' ],
16 silly audit   '@babel/highlight': [ '7.24.7' ],
16 silly audit   'ansi-styles': [ '3.2.1', '4.3.0' ],
16 silly audit   chalk: [ '2.4.2', '4.1.2' ],
16 silly audit   'color-convert': [ '1.9.3', '2.0.1' ],
16 silly audit   'color-name': [ '1.1.3', '1.1.4' ],
16 silly audit   'escape-string-regexp': [ '1.0.5' ],
16 silly audit   'has-flag': [ '3.0.0', '4.0.0' ],
16 silly audit   'supports-color': [ '5.5.0', '7.2.0' ],
16 silly audit   '@babel/parser': [ '7.24.8' ],
16 silly audit   '@babel/plugin-syntax-jsx': [ '7.24.7' ],
16 silly audit   '@babel/plugin-syntax-nullish-coalescing-operator': [ '7.8.3' ],
16 silly audit   '@babel/plugin-syntax-optional-chaining': [ '7.8.3' ],
16 silly audit   '@babel/plugin-syntax-typescript': [ '7.24.7' ],
16 silly audit   '@babel/plugin-transform-arrow-functions': [ '7.24.7' ],
16 silly audit   '@babel/plugin-transform-modules-commonjs': [ '7.24.8' ],
16 silly audit   '@babel/plugin-transform-nullish-coalescing-operator': [ '7.24.7' ],
16 silly audit   '@babel/plugin-transform-optional-chaining': [ '7.24.8' ],
16 silly audit   '@babel/plugin-transform-shorthand-properties': [ '7.24.7' ],
16 silly audit   '@babel/plugin-transform-template-literals': [ '7.24.7' ],
16 silly audit   '@babel/plugin-transform-typescript': [ '7.24.8' ],
16 silly audit   '@babel/preset-typescript': [ '7.24.7' ],
16 silly audit   '@babel/template': [ '7.24.7' ],
16 silly audit   '@babel/traverse': [ '7.24.8' ],
16 silly audit   '@babel/types': [ '7.24.9' ],
16 silly audit   '@egjs/hammerjs': [ '2.0.17' ],
16 silly audit   '@expo/config': [ '8.5.4' ],
16 silly audit   '@expo/config-plugins': [ '7.8.4' ],
16 silly audit   glob: [ '7.1.6' ],
16 silly audit   '@expo/config-types': [ '50.0.0' ],
16 silly audit   'lru-cache': [ '6.0.0' ],
16 silly audit   yallist: [ '4.0.0' ],
16 silly audit   '@expo/fingerprint': [ '0.6.0' ],
16 silly audit   '@expo/image-utils': [ '0.4.1' ],
16 silly audit   'crypto-random-string': [ '1.0.0' ],
16 silly audit   'fs-extra': [ '9.0.0', '9.1.0' ],
16 silly audit   jsonfile: [ '6.1.0' ],
16 silly audit   universalify: [ '2.0.1', '1.0.0' ],
16 silly audit   'temp-dir': [ '1.0.0' ],
16 silly audit   tempy: [ '0.3.0' ],
16 silly audit   'type-fest': [ '0.3.1' ],
16 silly audit   'unique-string': [ '1.0.0' ],
16 silly audit   '@expo/json-file': [ '8.3.0' ],
16 silly audit   '@expo/plist': [ '0.1.0' ],
16 silly audit   '@expo/sdk-runtime-versions': [ '1.0.0' ],
16 silly audit   '@expo/spawn-async': [ '1.5.0' ],
16 silly audit   '@ide/backoff': [ '1.0.0' ],
16 silly audit   '@jridgewell/gen-mapping': [ '0.3.5' ],
16 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
16 silly audit   '@jridgewell/set-array': [ '1.2.1' ],
16 silly audit   '@jridgewell/sourcemap-codec': [ '1.4.15' ],
16 silly audit   '@jridgewell/trace-mapping': [ '0.3.25' ],
16 silly audit   '@react-native-community/datetimepicker': [ '8.2.0' ],
16 silly audit   '@react-native-community/masked-view': [ '0.1.11' ],
16 silly audit   '@react-native/normalize-color': [ '2.1.0' ],
16 silly audit   '@types/hammerjs': [ '2.0.45' ],
16 silly audit   '@xmldom/xmldom': [ '0.7.13', '0.8.10' ],
16 silly audit   'abort-controller': [ '3.0.0' ],
16 silly audit   'any-promise': [ '1.3.0' ],
16 silly audit   assert: [ '2.1.0' ],
16 silly audit   asynckit: [ '0.4.0' ],
16 silly audit   'at-least-node': [ '1.0.0' ],
16 silly audit   'available-typed-arrays': [ '1.0.7' ],
16 silly audit   axios: [ '1.6.8' ],
16 silly audit   badgin: [ '1.2.3' ],
16 silly audit   'balanced-match': [ '1.0.2' ],
16 silly audit   'base64-js': [ '1.5.1' ],
16 silly audit   'big-integer': [ '1.6.52' ],
16 silly audit   'bplist-creator': [ '0.1.0' ],
16 silly audit   'brace-expansion': [ '1.1.11' ],
16 silly audit   'call-bind': [ '1.0.7' ],
16 silly audit   'combined-stream': [ '1.0.8' ],
16 silly audit   'concat-map': [ '0.0.1' ],
16 silly audit   'convert-source-map': [ '2.0.0' ],
16 silly audit   'cross-spawn': [ '6.0.5' ],
16 silly audit   debug: [ '4.3.4' ],
16 silly audit   'define-data-property': [ '1.1.4' ],
16 silly audit   'define-properties': [ '1.2.1' ],
16 silly audit   'delayed-stream': [ '1.0.0' ],
16 silly audit   'es-define-property': [ '1.0.0' ],
16 silly audit   'es-errors': [ '1.3.0' ],
16 silly audit   'event-target-shim': [ '5.0.1' ],
16 silly audit   'expo-application': [ '5.8.3' ],
16 silly audit   'expo-barcode-scanner': [ '12.9.3' ],
16 silly audit   'expo-constants': [ '15.4.5' ],
16 silly audit   'expo-image-loader': [ '4.6.0' ],
16 silly audit   'expo-notifications': [ '0.27.6' ],
16 silly audit   'find-up': [ '5.0.0' ],
16 silly audit   'follow-redirects': [ '1.15.6' ],
16 silly audit   'for-each': [ '0.3.3' ],
16 silly audit   'form-data': [ '4.0.0' ],
16 silly audit   'fs.realpath': [ '1.0.0' ],
16 silly audit   'function-bind': [ '1.1.2' ],
16 silly audit   'get-intrinsic': [ '1.2.4' ],
16 silly audit   getenv: [ '1.0.0' ],
16 silly audit   git: [ '0.1.5' ],
16 silly audit   globals: [ '11.12.0' ],
16 silly audit   gopd: [ '1.0.1' ],
16 silly audit   'graceful-fs': [ '4.2.11' ],
16 silly audit   'has-property-descriptors': [ '1.0.2' ],
16 silly audit   'has-proto': [ '1.0.3' ],
16 silly audit   'has-symbols': [ '1.0.3' ],
16 silly audit   'has-tostringtag': [ '1.0.2' ],
16 silly audit   hasown: [ '2.0.2' ],
16 silly audit   'hoist-non-react-statics': [ '3.3.2' ],
16 silly audit   imurmurhash: [ '0.1.4' ],
16 silly audit   inflight: [ '1.0.6' ],
16 silly audit   inherits: [ '2.0.4' ],
16 silly audit   invariant: [ '2.2.4' ],
16 silly audit   'is-arguments': [ '1.1.1' ],
16 silly audit   'is-callable': [ '1.2.7' ],
16 silly audit   'is-generator-function': [ '1.0.10' ],
16 silly audit   'is-nan': [ '1.3.2' ],
16 silly audit   'is-typed-array': [ '1.1.13' ],
16 silly audit   isexe: [ '2.0.0' ],
16 silly audit   'jimp-compact': [ '0.16.1' ],
16 silly audit   'js-tokens': [ '4.0.0' ],
16 silly audit   jsesc: [ '2.5.2' ],
16 silly audit   json5: [ '2.2.3' ],
16 silly audit   'lines-and-columns': [ '1.2.4' ],
16 silly audit   'locate-path': [ '6.0.0' ],
16 silly audit   'loose-envify': [ '1.4.0' ],
16 silly audit   mime: [ '1.2.9' ],
16 silly audit   'mime-db': [ '1.52.0' ],
16 silly audit   'mime-types': [ '2.1.35' ],
16 silly audit   minimatch: [ '3.1.2' ],
16 silly audit   ms: [ '2.1.2' ],
16 silly audit   mz: [ '2.7.0' ],
16 silly audit   'nice-try': [ '1.0.5' ],
16 silly audit   'node-fetch': [ '2.7.0' ],
16 silly audit   'object-assign': [ '4.1.1' ],
16 silly audit   'object-is': [ '1.1.6' ],
16 silly audit   'object-keys': [ '1.1.1' ],
16 silly audit   'object.assign': [ '4.1.5' ],
16 silly audit   once: [ '1.4.0' ],
16 silly audit   'p-limit': [ '3.1.0' ],
16 silly audit   'p-locate': [ '5.0.0' ],
16 silly audit   'parse-png': [ '2.1.0' ],
16 silly audit   'path-exists': [ '4.0.0' ],
16 silly audit   'path-is-absolute': [ '1.0.1' ],
16 silly audit   'path-key': [ '2.0.1' ],
16 silly audit   picocolors: [ '1.0.0' ],
16 silly audit   pirates: [ '4.0.6' ],
16 silly audit   plist: [ '3.1.0' ],
16 silly audit   xmlbuilder: [ '15.1.1', '11.0.1', '14.0.0' ],
16 silly audit   pngjs: [ '3.4.0' ],
16 silly audit   'possible-typed-array-names': [ '1.0.0' ],
16 silly audit   'prop-types': [ '15.8.1' ],
16 silly audit   'proxy-from-env': [ '1.1.0' ],
16 silly audit   'react-freeze': [ '1.0.4' ],
16 silly audit   'react-is': [ '16.13.1' ],
16 silly audit   'react-native-gesture-handler': [ '2.17.1' ],
16 silly audit   'react-native-reanimated': [ '3.14.0' ],
16 silly audit   'react-native-safe-area-context': [ '4.10.8' ],
16 silly audit   'react-native-screens': [ '3.32.0' ],
16 silly audit   'require-from-string': [ '2.0.2' ],
16 silly audit   'resolve-from': [ '5.0.0' ],
16 silly audit   sax: [ '1.3.0' ],
16 silly audit   'set-function-length': [ '1.2.2' ],
16 silly audit   'shebang-command': [ '1.2.0' ],
16 silly audit   'shebang-regex': [ '1.0.0' ],
16 silly audit   'signal-exit': [ '3.0.7' ],
16 silly audit   'simple-plist': [ '1.3.1' ],
16 silly audit   'bplist-parser': [ '0.3.1' ],
16 silly audit   slash: [ '3.0.0' ],
16 silly audit   slugify: [ '1.6.6' ],
16 silly audit   'stream-buffers': [ '2.2.0' ],
16 silly audit   sucrase: [ '3.34.0' ],
16 silly audit   commander: [ '4.1.1' ],
16 silly audit   thenify: [ '3.3.1' ],
16 silly audit   'thenify-all': [ '1.6.0' ],
16 silly audit   'to-fast-properties': [ '2.0.0' ],
16 silly audit   tr46: [ '0.0.3' ],
16 silly audit   'ts-interface-checker': [ '0.1.13' ],
16 silly audit   util: [ '0.12.5' ],
16 silly audit   'warn-once': [ '0.1.1' ],
16 silly audit   'webidl-conversions': [ '3.0.1' ],
16 silly audit   'whatwg-url': [ '5.0.0' ],
16 silly audit   which: [ '1.3.1' ],
16 silly audit   'which-typed-array': [ '1.1.15' ],
16 silly audit   wrappy: [ '1.0.2' ],
16 silly audit   'write-file-atomic': [ '2.4.3' ],
16 silly audit   xcode: [ '3.0.1' ],
16 silly audit   uuid: [ '7.0.3' ],
16 silly audit   xml2js: [ '0.6.0' ],
16 silly audit   'yocto-queue': [ '0.1.0' ],
16 silly audit   bootstrap: [ '5.3.3' ]
16 silly audit }
17 http fetch GET 200 https://registry.npmjs.org/npm 507ms
18 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 358ms
19 silly audit report {
19 silly audit report   semver: [
19 silly audit report     {
19 silly audit report       id: 1098562,
19 silly audit report       url: 'https://github.com/advisories/GHSA-c2qf-rxjj-qqgw',
19 silly audit report       title: 'semver vulnerable to Regular Expression Denial of Service',
19 silly audit report       severity: 'high',
19 silly audit report       vulnerable_versions: '>=7.0.0 <7.5.2',
19 silly audit report       cwe: [Array],
19 silly audit report       cvss: [Object]
19 silly audit report     }
19 silly audit report   ],
19 silly audit report   axios: [
19 silly audit report     {
19 silly audit report       id: 1098583,
19 silly audit report       url: 'https://github.com/advisories/GHSA-8hc4-vh64-cxmj',
19 silly audit report       title: 'Server-Side Request Forgery in axios',
19 silly audit report       severity: 'high',
19 silly audit report       vulnerable_versions: '>=1.3.2 <=1.7.3',
19 silly audit report       cwe: [Array],
19 silly audit report       cvss: [Object]
19 silly audit report     }
19 silly audit report   ],
19 silly audit report   git: [
19 silly audit report     {
19 silly audit report       id: 1088754,
19 silly audit report       url: 'https://github.com/advisories/GHSA-9gqr-xp86-f87h',
19 silly audit report       title: 'Code injection in npm git',
19 silly audit report       severity: 'moderate',
19 silly audit report       vulnerable_versions: '<=0.1.5',
19 silly audit report       cwe: [Array],
19 silly audit report       cvss: [Object]
19 silly audit report     }
19 silly audit report   ],
19 silly audit report   mime: [
19 silly audit report     {
19 silly audit report       id: 1093780,
19 silly audit report       url: 'https://github.com/advisories/GHSA-wrvr-8mpx-r7pp',
19 silly audit report       title: 'mime Regular Expression Denial of Service when MIME lookup performed on untrusted user input',
19 silly audit report       severity: 'high',
19 silly audit report       vulnerable_versions: '<1.4.1',
19 silly audit report       cwe: [Array],
19 silly audit report       cvss: [Object]
19 silly audit report     }
19 silly audit report   ]
19 silly audit report }
20 http fetch GET 200 https://registry.npmjs.org/axios 111ms (cache miss)
21 http fetch GET 200 https://registry.npmjs.org/semver 120ms (cache miss)
22 http fetch GET 200 https://registry.npmjs.org/mime 157ms (cache miss)
23 http fetch GET 200 https://registry.npmjs.org/git 658ms (cache miss)
24 http fetch GET 200 https://registry.npmjs.org/@expo%2fimage-utils 253ms (cache miss)
25 http fetch GET 200 https://registry.npmjs.org/expo-notifications 616ms (cache miss)
26 notice
26 notice New [33mminor[39m version of npm available! [33m10.6.0[39m -> [34m10.9.0[39m
26 notice Changelog: [34mhttps://github.com/npm/cli/releases/tag/v10.9.0[39m
26 notice To update run: [4mnpm install -g npm@10.9.0[24m
26 notice  { force: true, [Symbol(proc-log.meta)]: true }
27 verbose exit 0
28 info ok
