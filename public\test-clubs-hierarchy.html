<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Hiérarchie des Clubs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/clubs-hierarchy.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        
        .container {
            max-width: 1400px;
        }
        
        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .controls-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 0 5px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .btn-custom:active {
            transform: translateY(0);
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            min-height: 600px;
        }
        
        .status-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .status-info i {
            color: #2196f3;
            margin-right: 0.5rem;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            display: none;
        }
        
        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .loading-content i {
            font-size: 2rem;
            color: #007bff;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- En-tête -->
        <div class="header-card">
            <h1>
                <i class="fas fa-sitemap text-primary"></i>
                Test - Hiérarchie Complète des Clubs
            </h1>
            <p class="text-muted mb-0">
                Visualisation complète de tous les clubs organisés par continent et compétition<br>
                <small>Utilise <code>hierarchy-config.json</code> et charge automatiquement tous les clubs depuis les fichiers JSON</small>
            </p>
        </div>
        
        <!-- Contrôles -->
        <div class="controls-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <button class="btn btn-custom" onclick="clubsHierarchy.expandAll()">
                        <i class="fas fa-expand"></i> Tout Développer
                    </button>
                    <button class="btn btn-custom" onclick="clubsHierarchy.collapseAll()">
                        <i class="fas fa-compress"></i> Tout Réduire
                    </button>
                    <button class="btn btn-custom" onclick="clubsHierarchy.reload()">
                        <i class="fas fa-refresh"></i> Recharger
                    </button>
                    <button class="btn btn-custom" onclick="loadAllClubsAutomatically()">
                        <i class="fas fa-download"></i> Charger Tous les Clubs
                    </button>
                    <button class="btn btn-custom" onclick="showLoadedData()">
                        <i class="fas fa-database"></i> Statistiques
                    </button>
                    <button class="btn btn-custom" onclick="exportData()">
                        <i class="fas fa-file-export"></i> Exporter
                    </button>
                </div>
                <div class="col-md-4">
                    <div class="status-info">
                        <i class="fas fa-info-circle"></i>
                        <span id="status-text">Initialisation...</span>
                    </div>
                </div>
            </div>

            <!-- Informations de chargement automatique -->
            <div class="alert alert-info" id="auto-load-info" style="display: none; margin-top: 1rem;">
                <i class="fas fa-magic"></i>
                <strong>Chargement automatique activé :</strong> Les clubs seront chargés automatiquement pour toutes les compétitions disponibles.
            </div>
        </div>
        
        <!-- Contenu principal -->
        <div class="main-content">
            <div id="clubs-hierarchy-container">
                <!-- Le composant sera rendu ici -->
                <div class="text-center py-5">
                    <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                    <p class="mt-3 text-muted">Initialisation du composant...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Overlay de chargement -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
            <h5>Chargement en cours...</h5>
            <p class="text-muted mb-0" id="loading-text">Veuillez patienter</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="Data/clubs-hierarchy.js"></script>
    <script>
        let clubsHierarchy;
        let hierarchyConfig;
        let autoLoadEnabled = false;

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initialisation de la page de test complète');

            try {
                // Charger d'abord la configuration
                await loadHierarchyConfig();

                // Créer l'instance du composant avec options étendues
                clubsHierarchy = new ClubsHierarchy('clubs-hierarchy-container', {
                    showPlayerCount: true,
                    showMarketValue: true,
                    enableSearch: true,
                    animationDuration: 400,
                    loadOnClick: true
                });

                // Écouter les événements du composant
                setupEventListeners();

                updateStatus('Composant initialisé - Prêt à charger les clubs', 'success');

                // Proposer le chargement automatique
                setTimeout(() => {
                    if (confirm('Voulez-vous charger automatiquement tous les clubs disponibles ?')) {
                        loadAllClubsAutomatically();
                    }
                }, 2000);

            } catch (error) {
                console.error('❌ Erreur lors de l\'initialisation:', error);
                updateStatus('Erreur lors de l\'initialisation', 'error');
            }
        });

        // Chargement de la configuration de hiérarchie
        async function loadHierarchyConfig() {
            try {
                const response = await fetch('Data/hierarchy-config.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                hierarchyConfig = await response.json();
                console.log('📊 Configuration de hiérarchie chargée:', hierarchyConfig);
            } catch (error) {
                console.error('❌ Erreur lors du chargement de la configuration:', error);
                throw error;
            }
        }
        
        // Configuration des écouteurs d'événements
        function setupEventListeners() {
            const container = document.getElementById('clubs-hierarchy-container');

            // Écouter la sélection d'un club
            container.addEventListener('clubSelected', function(event) {
                const { clubId } = event.detail;
                console.log('🏟️ Club sélectionné:', clubId);

                // Trouver les informations du club
                const clubInfo = findClubInfo(clubId);
                if (clubInfo) {
                    showClubDetails(clubInfo);
                }

                updateStatus(`Club sélectionné: ${clubInfo?.club?.name || clubId}`, 'info');
            });

            // Écouter les changements d'état des compétitions
            container.addEventListener('click', function(event) {
                if (event.target.closest('.competition-toggle')) {
                    const competitionElement = event.target.closest('.competition-item');
                    const competitionId = competitionElement?.dataset.competitionId;
                    if (competitionId) {
                        updateStatus(`Compétition ${competitionId} basculée`, 'info');
                    }
                }
            });
        }

        // Chargement automatique de tous les clubs
        async function loadAllClubsAutomatically() {
            if (!hierarchyConfig) {
                updateStatus('Configuration non chargée', 'error');
                return;
            }

            autoLoadEnabled = true;
            document.getElementById('auto-load-info').style.display = 'block';
            showLoading(true, 'Chargement automatique de tous les clubs...');

            let totalCompetitions = 0;
            let loadedCompetitions = 0;
            let totalClubs = 0;

            try {
                // Compter le total de compétitions
                Object.values(hierarchyConfig.continents).forEach(continent => {
                    totalCompetitions += continent.competitions.length;
                });

                updateStatus(`Chargement de ${totalCompetitions} compétitions...`, 'info');

                // Charger chaque compétition
                for (const [continentId, continent] of Object.entries(hierarchyConfig.continents)) {
                    for (const competition of continent.competitions) {
                        try {
                            updateLoadingText(`Chargement ${competition.name} (${loadedCompetitions + 1}/${totalCompetitions})`);

                            // Simuler le clic pour développer la compétition
                            await simulateCompetitionClick(continentId, competition.id);

                            // Attendre un peu pour l'animation
                            await sleep(300);

                            loadedCompetitions++;

                            // Compter les clubs chargés
                            const clubsData = clubsHierarchy.getLoadedClubs()[competition.id];
                            if (clubsData) {
                                totalClubs += clubsData.clubs.length;
                            }

                        } catch (error) {
                            console.warn(`⚠️ Impossible de charger ${competition.name}:`, error);
                        }
                    }
                }

                showLoading(false);
                updateStatus(`✅ ${loadedCompetitions}/${totalCompetitions} compétitions chargées - ${totalClubs} clubs au total`, 'success');

                // Afficher un résumé
                showLoadingSummary(loadedCompetitions, totalCompetitions, totalClubs);

            } catch (error) {
                console.error('❌ Erreur lors du chargement automatique:', error);
                showLoading(false);
                updateStatus('Erreur lors du chargement automatique', 'error');
            }
        }
        
        // Mise à jour du statut
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status-text');
            const statusContainer = statusElement.closest('.status-info');
            
            statusElement.textContent = message;
            
            // Changer la couleur selon le type
            statusContainer.className = 'status-info';
            switch(type) {
                case 'success':
                    statusContainer.style.background = '#e8f5e8';
                    statusContainer.style.borderColor = '#4caf50';
                    statusContainer.querySelector('i').style.color = '#4caf50';
                    break;
                case 'error':
                    statusContainer.style.background = '#ffebee';
                    statusContainer.style.borderColor = '#f44336';
                    statusContainer.querySelector('i').style.color = '#f44336';
                    break;
                case 'warning':
                    statusContainer.style.background = '#fff3e0';
                    statusContainer.style.borderColor = '#ff9800';
                    statusContainer.querySelector('i').style.color = '#ff9800';
                    break;
                default:
                    statusContainer.style.background = '#e3f2fd';
                    statusContainer.style.borderColor = '#2196f3';
                    statusContainer.querySelector('i').style.color = '#2196f3';
            }
        }
        
        // Afficher les données chargées
        function showLoadedData() {
            const loadedClubs = clubsHierarchy.getLoadedClubs();
            const competitionCount = Object.keys(loadedClubs).length;
            const totalClubs = Object.values(loadedClubs).reduce((sum, data) => sum + data.clubs.length, 0);
            
            const message = `${competitionCount} compétitions chargées, ${totalClubs} clubs au total`;
            updateStatus(message, 'info');
            
            console.log('📊 Données chargées:', loadedClubs);
            
            // Afficher dans la console pour debug
            console.table(Object.keys(loadedClubs).map(competitionId => ({
                Competition: competitionId,
                Clubs: loadedClubs[competitionId].clubs.length,
                'Valeur Totale': loadedClubs[competitionId].statistics?.total_market_value || 'N/A'
            })));
        }
        
        // Afficher une notification
        function showNotification(message, type = 'info') {
            // Créer une notification toast simple
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'info' ? 'primary' : type} position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'info' ? 'info-circle' : type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Supprimer automatiquement après 5 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Simuler un clic sur une compétition
        async function simulateCompetitionClick(continentId, competitionId) {
            const competitionElement = document.querySelector(`[data-competition-id="${competitionId}"][data-continent-id="${continentId}"]`);
            if (competitionElement) {
                const toggleElement = competitionElement.querySelector('.competition-toggle');
                if (toggleElement) {
                    toggleElement.click();
                    return true;
                }
            }
            return false;
        }

        // Trouver les informations d'un club
        function findClubInfo(clubId) {
            const loadedClubs = clubsHierarchy.getLoadedClubs();

            for (const [competitionId, clubsData] of Object.entries(loadedClubs)) {
                const club = clubsData.clubs.find(c => c.id == clubId);
                if (club) {
                    return {
                        club,
                        competition: competitionId,
                        competitionData: clubsData
                    };
                }
            }
            return null;
        }

        // Afficher les détails d'un club
        function showClubDetails(clubInfo) {
            const { club, competition } = clubInfo;

            const details = `
                🏟️ ${club.name} (${club.short_name || club.name})
                📍 ${club.stadium} (${club.capacity?.toLocaleString()} places)
                📅 Fondé en ${club.founded}
                💰 Valeur: ${formatCurrency(club.market_value)}
                ⚽ ${club.players_count || 0} joueurs
                🏆 Compétition: ${competition}
            `;

            console.log('🏟️ Détails du club:', details);
            showNotification(`Détails de ${club.name}`, 'info');
        }

        // Afficher un résumé du chargement
        function showLoadingSummary(loaded, total, clubs) {
            const summary = `
                📊 Résumé du chargement automatique:
                ✅ ${loaded}/${total} compétitions chargées
                🏟️ ${clubs} clubs au total
                📈 Taux de réussite: ${((loaded/total)*100).toFixed(1)}%
            `;

            console.log(summary);

            // Afficher dans une notification
            setTimeout(() => {
                showNotification(`Chargement terminé: ${loaded}/${total} compétitions, ${clubs} clubs`, 'success');
            }, 1000);
        }

        // Fonction utilitaire pour attendre
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Mettre à jour le texte de chargement
        function updateLoadingText(text) {
            const loadingText = document.getElementById('loading-text');
            if (loadingText) {
                loadingText.textContent = text;
            }
        }

        // Formater la valeur marchande
        function formatCurrency(value) {
            if (!value || value === '0') return 'N/A';

            const num = parseInt(value);
            if (num >= 1000000000) {
                return `€${(num / 1000000000).toFixed(1)}B`;
            } else if (num >= 1000000) {
                return `€${(num / 1000000).toFixed(1)}M`;
            } else if (num >= 1000) {
                return `€${(num / 1000).toFixed(1)}K`;
            }
            return `€${num.toLocaleString()}`;
        }

        // Exporter les données
        function exportData() {
            const loadedClubs = clubsHierarchy.getLoadedClubs();
            const exportData = {
                timestamp: new Date().toISOString(),
                competitions: Object.keys(loadedClubs).length,
                totalClubs: Object.values(loadedClubs).reduce((sum, data) => sum + data.clubs.length, 0),
                data: loadedClubs
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `clubs-hierarchy-export-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            updateStatus(`Données exportées (${exportData.totalClubs} clubs)`, 'success');
        }

        // Afficher/masquer l'overlay de chargement
        function showLoading(show = true, text = 'Veuillez patienter') {
            const overlay = document.getElementById('loading-overlay');
            overlay.style.display = show ? 'flex' : 'none';
            if (show && text) {
                updateLoadingText(text);
            }
        }
        
        // Styles pour l'animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // Fonctions utilitaires pour les tests
        window.testFunctions = {
            // Simuler un clic sur une compétition spécifique
            clickCompetition: function(competitionId) {
                const element = document.querySelector(`[data-competition-id="${competitionId}"] .competition-toggle`);
                if (element) {
                    element.click();
                    updateStatus(`Clic simulé sur la compétition ${competitionId}`, 'info');
                } else {
                    updateStatus(`Compétition ${competitionId} non trouvée`, 'warning');
                }
            },

            // Rechercher un club
            searchClub: function(query) {
                const searchInput = document.querySelector('.clubs-search-input');
                if (searchInput) {
                    searchInput.value = query;
                    searchInput.dispatchEvent(new Event('keyup'));
                    updateStatus(`Recherche: "${query}"`, 'info');
                }
            },

            // Obtenir les statistiques
            getStats: function() {
                const stats = clubsHierarchy.getLoadedClubs();
                console.log('📊 Statistiques détaillées:', stats);
                return stats;
            },

            // Charger toutes les compétitions d'un continent
            loadContinent: async function(continentId) {
                if (!hierarchyConfig || !hierarchyConfig.continents[continentId]) {
                    console.error(`Continent ${continentId} non trouvé`);
                    return;
                }

                const continent = hierarchyConfig.continents[continentId];
                console.log(`🌍 Chargement du continent ${continent.name}...`);

                for (const competition of continent.competitions) {
                    await simulateCompetitionClick(continentId, competition.id);
                    await sleep(200);
                }

                updateStatus(`Continent ${continent.name} chargé`, 'success');
            },

            // Obtenir les clubs d'une compétition spécifique
            getClubsByCompetition: function(competitionId) {
                const loadedClubs = clubsHierarchy.getLoadedClubs();
                const clubsData = loadedClubs[competitionId];

                if (clubsData) {
                    console.log(`🏟️ Clubs de ${competitionId}:`, clubsData.clubs);
                    return clubsData.clubs;
                } else {
                    console.warn(`Compétition ${competitionId} non chargée`);
                    return [];
                }
            },

            // Rechercher un club par nom dans toutes les compétitions
            findClub: function(clubName) {
                const loadedClubs = clubsHierarchy.getLoadedClubs();
                const results = [];

                for (const [competitionId, clubsData] of Object.entries(loadedClubs)) {
                    const matchingClubs = clubsData.clubs.filter(club =>
                        club.name.toLowerCase().includes(clubName.toLowerCase())
                    );

                    matchingClubs.forEach(club => {
                        results.push({
                            club,
                            competition: competitionId
                        });
                    });
                }

                console.log(`🔍 Clubs trouvés pour "${clubName}":`, results);
                return results;
            },

            // Obtenir les statistiques par continent
            getStatsByContinent: function() {
                const loadedClubs = clubsHierarchy.getLoadedClubs();
                const stats = {};

                if (!hierarchyConfig) return stats;

                Object.entries(hierarchyConfig.continents).forEach(([continentId, continent]) => {
                    stats[continentId] = {
                        name: continent.name,
                        competitions: 0,
                        clubs: 0,
                        totalValue: 0
                    };

                    continent.competitions.forEach(competition => {
                        const clubsData = loadedClubs[competition.id];
                        if (clubsData) {
                            stats[continentId].competitions++;
                            stats[continentId].clubs += clubsData.clubs.length;
                            stats[continentId].totalValue += clubsData.clubs.reduce((sum, club) =>
                                sum + (parseInt(club.market_value) || 0), 0
                            );
                        }
                    });
                });

                console.table(stats);
                return stats;
            }
        };

        console.log('🛠️ Fonctions de test étendues disponibles dans window.testFunctions');
        console.log('📖 Exemples:');
        console.log('  - testFunctions.clickCompetition("GB1")');
        console.log('  - testFunctions.loadContinent("UEFA")');
        console.log('  - testFunctions.findClub("Manchester")');
        console.log('  - testFunctions.getStatsByContinent()');
        console.log('  - testFunctions.getClubsByCompetition("GB1")');
    </script>
</body>
</html>
