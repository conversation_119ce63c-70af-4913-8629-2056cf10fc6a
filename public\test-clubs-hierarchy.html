<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Hiérarchie des Clubs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/clubs-hierarchy.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        
        .container {
            max-width: 1400px;
        }
        
        .header-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .controls-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 0 5px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .btn-custom:active {
            transform: translateY(0);
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            min-height: 600px;
        }
        
        .status-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .status-info i {
            color: #2196f3;
            margin-right: 0.5rem;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            display: none;
        }
        
        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .loading-content i {
            font-size: 2rem;
            color: #007bff;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- En-tête -->
        <div class="header-card">
            <h1>
                <i class="fas fa-sitemap text-primary"></i>
                Test - Composant Hiérarchie des Clubs
            </h1>
            <p class="text-muted mb-0">
                Démonstration du composant <code>ClubsHierarchy.js</code> - 
                Hiérarchie des clubs organisés par compétition et continent
            </p>
        </div>
        
        <!-- Contrôles -->
        <div class="controls-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <button class="btn btn-custom" onclick="clubsHierarchy.expandAll()">
                        <i class="fas fa-expand"></i> Tout Développer
                    </button>
                    <button class="btn btn-custom" onclick="clubsHierarchy.collapseAll()">
                        <i class="fas fa-compress"></i> Tout Réduire
                    </button>
                    <button class="btn btn-custom" onclick="clubsHierarchy.reload()">
                        <i class="fas fa-refresh"></i> Recharger
                    </button>
                    <button class="btn btn-custom" onclick="showLoadedData()">
                        <i class="fas fa-database"></i> Données Chargées
                    </button>
                </div>
                <div class="col-md-4">
                    <div class="status-info">
                        <i class="fas fa-info-circle"></i>
                        <span id="status-text">Composant initialisé</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contenu principal -->
        <div class="main-content">
            <div id="clubs-hierarchy-container">
                <!-- Le composant sera rendu ici -->
                <div class="text-center py-5">
                    <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                    <p class="mt-3 text-muted">Initialisation du composant...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Overlay de chargement -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <i class="fas fa-spinner fa-spin"></i>
            <h5>Chargement en cours...</h5>
            <p class="text-muted mb-0">Veuillez patienter</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="Data/clubs-hierarchy.js"></script>
    <script>
        let clubsHierarchy;
        
        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initialisation de la page de test');
            
            try {
                // Créer l'instance du composant
                clubsHierarchy = new ClubsHierarchy('clubs-hierarchy-container', {
                    showPlayerCount: true,
                    showMarketValue: true,
                    enableSearch: true,
                    animationDuration: 300,
                    loadOnClick: true
                });
                
                // Écouter les événements du composant
                setupEventListeners();
                
                updateStatus('Composant initialisé avec succès', 'success');
                
            } catch (error) {
                console.error('❌ Erreur lors de l\'initialisation:', error);
                updateStatus('Erreur lors de l\'initialisation', 'error');
            }
        });
        
        // Configuration des écouteurs d'événements
        function setupEventListeners() {
            const container = document.getElementById('clubs-hierarchy-container');
            
            // Écouter la sélection d'un club
            container.addEventListener('clubSelected', function(event) {
                const { clubId } = event.detail;
                console.log('🏟️ Club sélectionné:', clubId);
                
                // Afficher une notification
                showNotification(`Club sélectionné: ${clubId}`, 'info');
                
                // Vous pouvez ajouter ici votre logique personnalisée
                // Par exemple: charger les joueurs du club, afficher des détails, etc.
            });
            
            // Écouter les changements d'état du composant
            container.addEventListener('competitionToggled', function(event) {
                const { competitionId, expanded } = event.detail;
                const action = expanded ? 'développée' : 'réduite';
                updateStatus(`Compétition ${competitionId} ${action}`, 'info');
            });
        }
        
        // Mise à jour du statut
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status-text');
            const statusContainer = statusElement.closest('.status-info');
            
            statusElement.textContent = message;
            
            // Changer la couleur selon le type
            statusContainer.className = 'status-info';
            switch(type) {
                case 'success':
                    statusContainer.style.background = '#e8f5e8';
                    statusContainer.style.borderColor = '#4caf50';
                    statusContainer.querySelector('i').style.color = '#4caf50';
                    break;
                case 'error':
                    statusContainer.style.background = '#ffebee';
                    statusContainer.style.borderColor = '#f44336';
                    statusContainer.querySelector('i').style.color = '#f44336';
                    break;
                case 'warning':
                    statusContainer.style.background = '#fff3e0';
                    statusContainer.style.borderColor = '#ff9800';
                    statusContainer.querySelector('i').style.color = '#ff9800';
                    break;
                default:
                    statusContainer.style.background = '#e3f2fd';
                    statusContainer.style.borderColor = '#2196f3';
                    statusContainer.querySelector('i').style.color = '#2196f3';
            }
        }
        
        // Afficher les données chargées
        function showLoadedData() {
            const loadedClubs = clubsHierarchy.getLoadedClubs();
            const competitionCount = Object.keys(loadedClubs).length;
            const totalClubs = Object.values(loadedClubs).reduce((sum, data) => sum + data.clubs.length, 0);
            
            const message = `${competitionCount} compétitions chargées, ${totalClubs} clubs au total`;
            updateStatus(message, 'info');
            
            console.log('📊 Données chargées:', loadedClubs);
            
            // Afficher dans la console pour debug
            console.table(Object.keys(loadedClubs).map(competitionId => ({
                Competition: competitionId,
                Clubs: loadedClubs[competitionId].clubs.length,
                'Valeur Totale': loadedClubs[competitionId].statistics?.total_market_value || 'N/A'
            })));
        }
        
        // Afficher une notification
        function showNotification(message, type = 'info') {
            // Créer une notification toast simple
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'info' ? 'primary' : type} position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'info' ? 'info-circle' : type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Supprimer automatiquement après 5 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Afficher/masquer l'overlay de chargement
        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            overlay.style.display = show ? 'flex' : 'none';
        }
        
        // Styles pour l'animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // Fonctions utilitaires pour les tests
        window.testFunctions = {
            // Simuler un clic sur une compétition spécifique
            clickCompetition: function(competitionId) {
                const element = document.querySelector(`[data-competition-id="${competitionId}"] .competition-toggle`);
                if (element) {
                    element.click();
                    updateStatus(`Clic simulé sur la compétition ${competitionId}`, 'info');
                } else {
                    updateStatus(`Compétition ${competitionId} non trouvée`, 'warning');
                }
            },
            
            // Rechercher un club
            searchClub: function(query) {
                const searchInput = document.querySelector('.clubs-search-input');
                if (searchInput) {
                    searchInput.value = query;
                    searchInput.dispatchEvent(new Event('keyup'));
                    updateStatus(`Recherche: "${query}"`, 'info');
                }
            },
            
            // Obtenir les statistiques
            getStats: function() {
                const stats = clubsHierarchy.getLoadedClubs();
                console.log('📊 Statistiques détaillées:', stats);
                return stats;
            }
        };
        
        console.log('🛠️ Fonctions de test disponibles dans window.testFunctions');
        console.log('📖 Exemples:');
        console.log('  - testFunctions.clickCompetition("GB1")');
        console.log('  - testFunctions.searchClub("Manchester")');
        console.log('  - testFunctions.getStats()');
    </script>
</body>
</html>
