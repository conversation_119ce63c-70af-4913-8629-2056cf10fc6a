
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
		<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
		<link rel="stylesheet" href="{{ asset('styles/declatifCrad.css') }}">
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

	<style>
    /* Light Theme (Default) */
    :root {
        --background-color: #fff;
        --text-color: #333;
        --button-background-color: #fff;
        --button-hover-color: #f0f0f0;
        --icon-color: #333;
        --map-background-color: #4d254d;
    }

    /* Dark Theme */
    [data-theme="dark"] {
        --background-color: #444;
        --text-color: #fff;
        --button-background-color: #444;
        --button-hover-color: #555;
        --icon-color: #fff;
        --map-background-color: linear-gradient(to top, transparent, #4d254d);
    }
    .Icon {
        color:var(--icon-color) ;
    }

    /* Map styles */
    .maps, #map {
        height: 850px;
        position: relative;
        background-size: cover;
        background-color: var(--background-color);
    }

    /* Button container and button styles */
    .button-container {
        position: absolute;
        top: 6%;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 3px;
        z-index: 1000; /* Ensures buttons are above the map */
    }

    .map-button, .input-icons {
        background-color: var(--button-background-color);
        border: none;
        border-radius: 15px;
        font-size: 11px;
        color: var(--text-color);
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 2px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        transition: background-color 0.3s;
    }

    .map-button {
        width: 140px;
    }

    .input-icons {
        width: 260px;
    }

    .map-button:hover, .input-icons:hover {
        background-color: var(--button-hover-color);
    }

    .input-icons input {
        border: none;
        background-color: transparent;
        color: var(--text-color);
        font-size: 16px;
        margin-left: 10px;
    }

    /* Dark mode specific styles */
    .map-button[data-theme="dark"], .input-icons[data-theme="dark"] {
        background-color: var(--button-background-color);
    }

    body.dark-mode .floating-title {
        color: var(--text-color);
        background-image: var(--map-background-color);
        background-blend-mode: overlay;
        border-radius: 20px 20px 0 0;
    }

    body.dark-mode .custom-icon i {
        color: var(--icon-color);
    }
</style>



		<!-- Button container with icons and input field -->
		<div class="button-container">
			<div class="input-icons but">
                <span >
				<input type="text" placeholder="Rechercher dans Google  ">
				<i style="margin-left: 3px; font-size: 15px;" class="bi bi-search"></i>
				<i style="margin-left: 10px;font-size: 20px; color: #0096FF;" class="bi bi-sign-turn-right-fill"></i>
                </span>
			</div>
			<button style="width: 85px;" class="map-button">
				<i class="fa-solid fa-utensils"></i>
				Restaurants
			</button>
			<button style="width: 65px;" class="map-button">
				<i class="fa-solid fa-bed"></i>
				Hôtels
			</button>
			<button style="width: 130px;" class="map-button">
				<i class="bi bi-camera"></i>
				Activités à découvrir
			</button>
			<button style="width: 65px;" class="map-button">
			<i class="bi bi-building-fill"></i>
				Musées
			</button>
			<button class="map-button">
				<i class="bi bi-bus-front-fill"></i>
				Transports en commun
			</button>
			<button style="width: 85px;" class="map-button">
			<i class="fa-solid fa-prescription-bottle-medical"></i>
				Pharmacies
			</button>
			<button class="map-button">
				<i class="fa-solid fa-train"></i>
				Distributeurs de billets
			</button>
		</div>

		<!-- Map container -->
		<div class="maps" id="map"></div>
		<div class="containers">
			<div style="bottom: 0; position: absolute; width: 100%;" class="containeres">
				<div class="top-bar">
					<div class="top-bar-left calculation">DESKTOP-ON5C84Q ></div>
					<div class="top-bar-icons">
						<div class="input-icon">
							<img src="https://img.icons8.com/ios-glyphs/30/4b484b/filter.png" alt="Filter" class="icon">
							<input type="text" style="background-color: #06262b;" placeholder="">
						</div>
						<i class="bi bi-download fs-5"></i>
						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/add-file.png" alt="Add File">
						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/settings.png" alt="Settings">
						<i class="bi bi-chevron-up"></i>
						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/microphone.png" alt="Microphone">
						<img src="https://img.icons8.com/?size=100&id=45&format=png&color=FFFFFF" alt="close">
					</div>
				</div>
				<div>
					<div id="command-line" class="command-line"></div>
				</div>
			</br>
		</div>
	

</div>



<!-- Bootstrap and other external libraries -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>

<!-- Local scripts -->
<script src="{{ asset('cmd/bundle.min.js') }}"></script>
<script src="{{ asset('cmd/app.min.js') }}"></script>
<script src="{{ asset('cmd/script.js') }}"></script>

<!-- Initialize map and production data script -->
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Initialize the map
    var map = L.map('map', {
        attributionControl: false,
        zoomControl: false
    }).setView([48.8214, 2.3522], 6);

    // Light and dark tile layers
    var tileLayerLight = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
        maxZoom: 20,
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
    });
    var tileLayerDark = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        maxZoom: 20,
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
    });

    tileLayerLight.addTo(map);

    // Toggle dark mode based on a checkbox
    function toggleDarkMode() {
        var isChecked = document.getElementById('chk').checked;
        if (isChecked) {
            map.removeLayer(tileLayerLight);
            tileLayerDark.addTo(map);
            document.body.classList.add('dark-mode');
        } else {
            map.removeLayer(tileLayerDark);
            tileLayerLight.addTo(map);
            document.body.classList.remove('dark-mode');
        }
    }
    document.getElementById('chk').addEventListener('change', toggleDarkMode);

    // Fetch production data from Twig variable and parse it
    const productionData = {{ productions|raw }};

    // Function to add markers using production data
    function addMarkersFromProductionData(productionData) {
        const coordinates = productionData.coordinates;
        coordinates.forEach((location) => {
            const {latitude: lat, longitude: lng} = location;
            const greenIcon = L.divIcon({
                className: 'custom-icon',
                html: '<i class="bi bi-geo-alt-fill" style="color: green; font-size: 20px;"></i>',
                iconSize: [40, 40],
                iconAnchor: [30, 20]
            });
            L.marker([lat, lng], {icon: greenIcon}).addTo(map);
        });
    }

    // Call the function with the production data
    if (productionData) {
        addMarkersFromProductionData(productionData);
    }
});
</script>

