<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">
<style>
:root {
    --sidebar-left-right-color: #2B2D31 !important; /* Replace #your_new_color with your desired color value */
    --rightblock-color:#313338!important;
}

	.main {
		padding: 40px;

	}
	.profile-header h1 {
		font-size: 1.5em;
		color: #fff;
		margin-bottom: 10px;

	}

	.tabs {
		display: flex;
		gap: 20px;
		margin-top: 10px;


	}

	.tabs span {
		cursor: pointer;
		color: #bbb;
		font-size: 1em;
	}

	.tabs .active-tab {
		color: #fff;
		border-bottom: 2px solid #00bfff;
		padding-bottom: 10px;
	}

	.profile-info {
		display: flex;
		gap: 20px;
		margin-top: 20px;
		flex-wrap: wrap;
	}

	.info-section {
		width: 100%;
		max-width: 400px;
	}

	.info-section label {
		color: #bbb;
		font-size: 14px;
		display: block;
		margin-top: 15px;
	}

	.info-section input {
		width: 100%;
		padding: 6px;
		font-size: 15px;
		background-color: #111;
		border: 1px solid #111;
		border-radius: 5px;
		color: #3e97e5;
	}

	/* Profile Card */
	.profile-card {
		padding: 20px;
		border-radius: 8px;
		background: linear-gradient(to top, #000,#1d2a40);
		color: #fff;
		width: 400px;
		height: 185px;
		margin-top: 40px;
	}

	.bi-person-circle {
		font-size: 35px; /* Adjust icon size */
		color: white; /* Icon color */
	}

	.avatar {
		width: 80px;
		height: 80px;
		background-color: #00bfff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24px; /* Optional: Add an icon or initial here */
	}

	.avatar-name {
		display: flex;
		align-items: center;
		margin-bottom: 20px;
		margin-top: 10px;
	}

	/* Additional Settings */
	.additional-settings {
		margin-top: 20px;
	}

	.profile-picture,
	.theme-color {
		margin-top: 10px;
	}

	.profile-picture button,
	.theme-color .color-box {
		padding: 6px;
		background-color: #00bfff;
		border: none;
		color: #fff;
		cursor: pointer;
		border-radius: 5px;
		font-size: 0.9em;

	}


	.color-box {
		width: 60px;
		height: 60px;
		border-radius: 5px;
		display: inline-block;
		background-color: #00bfff;
		padding: 0 !important;
	}
	.fa-pen {
		color: #111;
		margin-left: 40px;
		margin-top: -10px;
	}
</style>


<div
	class="main">
	<!-- Main Content -->
	<div
		class="main-content">
		<!-- Profile Header -->
		<div class="profile-header">
			<div style="display: flex; justify-content:space-between  ;width: 840px;
													">
				<h5 style="color: #fff; margin-right: 8px;">profils</h5>
				<i style="color: #fff; justify-content: end;" class="bi bi-x-circle justify-content-end"></i>
			</div>

			<div class="tabs">
				<span style="" class="active-tab">
					<i class="bi bi-person"></i>
					Profil d'utilisateur
				</span>
				<span style="border-bottom: 2px solid ;  padding-bottom: 10px; ">
					<i class="bi bi-cloud-arrow-down"></i>
					Mes documents
				</span>
			</div>

		</div>
	</div>
	<!-- Profile Info Section -->
	<div class=" profile-info">
		<div class="info-section">
			<label>NOM</label>
			<input type="text" value={{user.nom}}>

			<label>PRÉNOM</label>
			<input type="text" value={{user.prenom}}>

			<label>E-MAIL</label>
			<input type="text" value={{user.email}}>

			<label>TÉLÉPHONE</label>
			<input type="text" value="06 62 33 55 44">
		</div>

		<!-- Profile Card -->
		<div class="profile-card">
			<div class="avatar-name">
				<div class="avatar">
					<i style="size: 60px;"  class="bi bi-person-circle"></i>
				</div>
				<p style="font-weight: bold; color: #fff;margin-top: 10px; margin-left: 10px;">{{user.prenom}}</p>
			</div>
			<p style="margin: 0; color: #bbb;">{{user.email}}</p>
			<p style="margin: 0; color: #00bfff;">06 62 33 55 44</p>
		</div>


	</div>
</br><hr
style="color: white;">
<!-- Additional Settings -->
<div class="additional-settings">

	<div style="color: #fff;">photo profil</div>
	<div class="profile-picture">

		<button type="file">Importer une photo</button>
	</div>

	<hr
	style="color: white;">
	<!-- Theme Color Selection -->
	<span style="color: #fff;">Couleur Thème</span>
	<div class="theme-color">
		<div class="color-box">
			<i class="fas fa-pen"></i>
			<!-- Icône de crayon -->
		</div>
	</div>


</div></div></div><script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
