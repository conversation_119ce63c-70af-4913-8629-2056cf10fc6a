/**
 * Styles CSS pour le composant ClubsHierarchy
 * Hiérarchie des clubs organisés par compétition
 */

/* Container principal */
.clubs-hierarchy-container {
    width: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Barre de recherche */
.clubs-search-container {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 2;
}

.clubs-search-input {
    width: 100%;
    padding: 12px 40px 12px 40px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.clubs-search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.clear-search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: #6c757d;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-search-btn:hover {
    background: #495057;
}

/* Sections des continents */
.continent-section {
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.continent-header {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    gap: 1rem;
}

.continent-icon {
    font-size: 1.5rem;
}

.continent-name {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    flex-grow: 1;
}

.competitions-count {
    font-size: 0.9rem;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 12px;
}

/* Container des compétitions */
.competitions-container {
    padding: 1rem;
}

/* Items de compétition */
.competition-item {
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.competition-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.competition-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.competition-header:hover {
    background: #e9ecef;
}

.competition-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.competition-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #2d3436;
    font-size: 0.9rem;
}

.competition-details h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #2d3436;
}

.competition-country {
    font-size: 0.9rem;
    color: #6c757d;
}

.competition-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.clubs-count {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.expand-icon {
    color: #007bff;
    transition: transform 0.3s ease;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

/* Container des clubs */
.clubs-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #fff;
}

.clubs-container.expanded {
    max-height: 2000px; /* Valeur élevée pour l'animation */
}

/* Grille des clubs */
.clubs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

/* Items de club */
.club-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.club-item:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    border-color: #007bff;
}

.club-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.club-logo-placeholder {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 50%;
    flex-shrink: 0;
}

.club-info h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3436;
}

.club-short-name {
    font-size: 0.85rem;
    color: #6c757d;
}

.club-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.club-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #495057;
}

.club-detail i {
    width: 16px;
    color: #6c757d;
}

.club-detail.market-value {
    font-weight: 600;
    color: #28a745;
}

/* États de chargement et d'erreur */
.loading-clubs {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;
}

.loading-clubs i {
    margin-right: 0.5rem;
}

.clubs-error {
    text-align: center;
    padding: 2rem;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 8px;
    margin: 1rem;
}

.clubs-error i {
    margin-right: 0.5rem;
}

.no-clubs {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;
}

/* Statistiques de compétition */
.competition-stats-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem;
    border-top: 3px solid #007bff;
}

.competition-stats-summary h6 {
    margin: 0 0 0.75rem 0;
    color: #2d3436;
    font-weight: 600;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    text-align: center;
    padding: 0.5rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3436;
}

/* Erreur générale */
.clubs-hierarchy-error {
    text-align: center;
    padding: 3rem;
    color: #dc3545;
}

.clubs-hierarchy-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.clubs-hierarchy-error h4 {
    margin-bottom: 1rem;
}

.clubs-hierarchy-error p {
    margin-bottom: 2rem;
    color: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
    .clubs-grid {
        grid-template-columns: 1fr;
    }
    
    .competition-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .competition-stats {
        align-self: flex-end;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .continent-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
