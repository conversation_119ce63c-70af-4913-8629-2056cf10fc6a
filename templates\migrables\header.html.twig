    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/all.css" />
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v5.15.4/css/duotone.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

   
   <style>

 :root {
            --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 320px;
            --prisescardcard-height: 165px;
            --navbackground-color: #edecfc;
            --card-background-color: #edecfd;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #fcfbff;
            --icons-colors-i: #949fa9;
            --smaller-text-color-numbers: #c1c9ce;
            --bigtext-color: #394d5f;
            		--skeleton-bg:#e0e0e0
            
        }

        [data-theme="dark"] {
              --prisescardrow-gap: 18px;
            --prisescardcol-gap: 18px;
            --prisescardcard-width: 300px;
            --prisescardcard-height: 165px;
            --navbackground-color: #2b3942;
            --card-background-color:#2b3942;
            --card-border-color: #e4e1f2;
            --small-cards-bg-colors: #1f2c34;
            --icons-colors-i: #6e6f71;
            --smaller-text-color-numbers: #6e6f71;
            --bigtext-color: #e9ecf0;
            --bigtext-colors: #6e6f71;
            		--skeleton-bg:#e0e0e0;
        }

        .prisesdashcard-container {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            scrollbar-width: thin;
            scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
            overflow-y: auto; 
            max-height: 98vh; 
           
        }
@media screen and (min-width: 1400px) {
    #prisescard {
        display: grid;
       grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 10px;
    }
}

@media screen and (max-width: 1366px) and (min-width: 400px) {
    #prisescard {
        display: grid;
        grid-template-columns: repeat(3, minmax(300px, 340px)); /* Adjust values as needed */
        gap: 15px; /* Set explicit gap between cards */
        padding-left: 10px;
    }
}


        #prisescard .custom-card {
            background-color: var(--card-background-color);
            border-radius: 12px;
            padding: 10px;
            color: white;
            width: var(--prisescardcard-width);
            height: var(--prisescardcard-height);
            opacity: 0; /* Initial state for lazy loading */
            transition: opacity 0.3s ease-out;
        }

        #prisescard .card-header {
            background-color: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
         
            border: none;
            box-shadow: none;
        }

        #prisescard .card-header i {
            color: #bb86fc;
            margin-right: 5px;
        }

        #prisescard .card-title {
            font-weight: bold;
            color: var(--bigtext-color);
            font-size: 0.8em;
        }

        #prisescard .number {
            color: var(--bigtext-color);
            font-weight: bold;
            font-size: 0.8em;
            margin-top: -3%;
        }

        #prisescard .data-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 0.85em;
        }

        #prisescard .data-row {
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }

        #prisescard .data-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 12px;
            background-color: var(--small-cards-bg-colors);
            border-radius: 6px;
            width: 160px;
            font-size: 1.1em;
        }
            #prisescard .data-boxs {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 12px;
            background-color: var(--small-cards-bg-colors);
            border-radius: 6px;
            width: 100px;
            font-size: 1.1em;
        }

        #prisescard .data-box i {
            color: var(--icons-colors-i);
        }

        #prisescard .data-box .texticon {
            color: var(--icons-colors-i);
            font-weight: bold;
        }

        #prisescard .data-box .number {
            color: var(--smaller-text-color-numbers);
            font-size: 1em;
        }
          #prisescard .data-boxs .number {
            color: var(--smaller-text-color-numbers);
            font-size: 1em;
        }

        /* Navbar Styles */
        #smallprisesnav .navbar {
            display: flex;
            gap: 8px;
            padding: 20px;
            justify-content: flex-start;
            align-items: center;
            width: 100%;
        }

        #smallprisesnav .nav-button {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 1rem;
            font-weight: bold;
            color: var(--bigtext-colors);
            background-color: transparent;
            border: none;
            transition: all 0.3s;
            cursor: pointer;
        }

        #smallprisesnav .nav-button.active {
            background-color: var(--navbackground-color);
            color: var(--bigtext-color);
        }

        #smallprisesnav .nav-button .icon {
            width: 24px;
            height: 24px;
        }


</style>
 <style>
 :root {
    /* Default Variables */

    --skeleton-bg: #e0e0e0;
    --skeleton-highlight: #f0f0f0;
    --prisescardcard-width: 300px;
    --prisescardcard-height: 150px;
}

/* Styles for dark mode */
[data-theme="dark"] {

    --skeleton-bg: #1f2c34;
    --skeleton-highlight: #2b3942;
}

.skeleton-card {
    background-color: var(--card-background-color);
    border-radius: 12px;
    padding: 10px;
    width: var(--prisescardcard-width);
    height: var(--prisescardcard-height);
    display: flex;
    flex-direction: column;
    gap: 10px;
    animation: shimmer 1.5s infinite;
}

/* Header Skeleton */
.skeleton-card-header {
    height: 20px;
    width: 80%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
    margin-bottom: 8px;
}

/* Data Container Skeleton */
.skeleton-data-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.skeleton-data-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.skeleton-data-row div {
    height: 30px;
    width: 45%;
    background-color: var(--skeleton-bg);
    border-radius: 4px;
}

/* Animation for Shimmer Effect */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: 200px 0;
    }
}

.skeleton-card-header,
.skeleton-data-row div {
    background: linear-gradient(
        90deg,
        var(--skeleton-bg) 25%,
        var(--skeleton-highlight) 50%,
        var(--skeleton-bg) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Hide actual cards during loading */
.custom-card.loading {
    display: none;
}

.custom-card.loaded {
    opacity: 1;
    display: block;
}

/* Responsive Design */
@media (max-width: 600px) {
    :root {
        --prisescardcard-width: 100%;
        --prisescardcard-height: auto;
    }
    
    .skeleton-card {
        padding: 8px;
    }
    
    .skeleton-data-row div {
        height: 20px;
    }
    .icon {
    width: 20px; /* Ajustez la largeur selon vos besoins */
    height: auto; /* Garde les proportions de l'image */
    max-height: 20px; /* Fixe une hauteur maximale si nécessaire */
    object-fit: contain; /* S'assure que l'image s'ajuste sans être déformée */
}



}

</style>
<style>
[data-theme="dark"] #btn-adsl img {
    content: url("{{ asset('image/icon/adsl-gris.svg') }}");
}
[data-theme="dark"] #btn-fibre img {
    content: url("{{ asset('image/icon/Fibre-gris.svg') }}");
}
[data-theme="dark"] #btn-mob img {
    content: url("{{ asset('image/icon/Mobile-gris.svg') }}");
}
[data-theme="dark"] #btn-mob-fibre img {
    content: url("{{ asset('image/icon/mobile-fibre-gris.svg') }}");
}
[data-theme="dark"] #btn-mob-adsl img {
    content: url("{{ asset('image/icon/mobile-adsl-gris.svg') }}");
}
</style>

<div class="navbar">
    <button class="nav-button" id="btn-adsl" data-param="adsl" 
            data-src-light="{{ asset('image/icon/adsl-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/adsl-gris.svg') }}">
        <img src="{{ asset('image/icon/adsl-violet.svg') }}" alt="Router Icon" class="icon" />
        <span>{{ total_nb_fyr_adsl }}</span>
    </button>
    <button class="nav-button" id="btn-fibre" data-param="thd" 
            data-src-light="{{ asset('image/icon/Fibre-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/Fibre-gris.svg') }}">
        <img src="{{ asset('image/icon/Fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_thd }}</span>
    </button>
    <button class="nav-button" id="btn-mob" data-param="mobMono" 
            data-src-light="{{ asset('image/icon/Mobile-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/Mobile-gris.svg') }}">
        <img src="{{ asset('image/icon/Mobile-violet.svg') }}" alt="Mobile Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_mono }}</span>
    </button>
    <button class="nav-button" id="btn-mob-fibre" data-param="mobMultiThd" 
            data-src-light="{{ asset('image/icon/mobile-fibre-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/mobile-fibre-gris.svg') }}">
        <img src="{{ asset('image/icon/mobile-fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_multi_thd }}</span>
    </button>
    <button class="nav-button" id="btn-mob-adsl" data-param="mobMultiAdsl" 
            data-src-light="{{ asset('image/icon/mobile-adsl-violet.svg') }}" 
            data-src-dark="{{ asset('image/icon/mobile-adsl-gris.svg') }}">
        <img src="{{ asset('image/icon/mobile-adsl-violet.svg') }}" alt="Plug Icon" class="icon" />
        <span>{{ total_nb_fyr_mob_multi_adsl }}</span>
    </button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const buttons = document.querySelectorAll('.nav-button');
    const params = {
        adsl: localStorage.getItem('adsl') || 'no',
        thd: localStorage.getItem('thd') || 'no',
        mobMono: localStorage.getItem('mobMono') || 'no',
        mobMultiThd: localStorage.getItem('mobMultiThd') || 'no',
        mobMultiAdsl: localStorage.getItem('mobMultiAdsl') || 'no',
    };

    // Initialize button states
    buttons.forEach(button => {
        const param = button.dataset.param;
        if (params[param] === 'yes') button.classList.add('active');

        button.addEventListener('click', function () {
            const isActive = button.classList.toggle('active');
            params[param] = isActive ? 'yes' : 'no';
            localStorage.setItem(param, params[param]);

            const queryString = new URLSearchParams(params).toString();
            const apiUrl = `${window.location.pathname}?${queryString}&page=1`;
            window.location.href = apiUrl;
        });
    });

    // Update icon based on theme
    const updateIcons = theme => {
        buttons.forEach(button => {
            const img = button.querySelector('img');
            const src = theme === 'dark' ? button.dataset.srcDark : button.dataset.srcLight;
            img.src = src;
        });
    };

    // Detect theme and update icons
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    updateIcons(currentTheme);

    const observer = new MutationObserver(() => {
        const newTheme = document.documentElement.getAttribute('data-theme');
        updateIcons(newTheme);
    });

    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['data-theme'] });
});
</script>
