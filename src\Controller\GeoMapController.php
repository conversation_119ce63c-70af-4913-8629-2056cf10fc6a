<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\HttpFoundation\Request;

class GeoMapController extends AbstractController
{
    #[Route('/GeoMapp', name: 'geomap')]
    public function geomap(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
            $userId = $dataUser['user_id'] ?? null;
            $cpv = $dataUser['cpv'] ?? null;
            $pointOfSaleId  = $dataUser['point_of_sale_id'] ?? null;
            if (!$userId || !$cpv) {
                throw new \Exception('User ID or CPV not found in the response.');
            }
    
            $userData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
            $HierarchyData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/interventions-places-hierarchy/{$cpv}?page=1", $jwt);
            $effectifsData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}/tree/all?page=1", $jwt);
            $effectifs = $effectifsData['children'] ?? [];
            $migrable = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/{$cpv}?page=1", $jwt);

         
        } catch (\Exception $e) {
            $this->addFlash('error', $e->getMessage());
            return $this->redirectToRoute('app_dashboard');
        }
        $username = null;
        if ($jwt) {
            $decodedToken = $this->decodeJwt($jwt);
            $username = $decodedToken['username'] ?? 'Guest';
        }
    
        return $this->render('geomap/geomap.html.twig', [
            'cpv' => $cpv,
            'pointOfSaleId'=>$pointOfSaleId ,
            'HierarchyData' => $HierarchyData,
            'effectifs' => $effectifs,
            'userId' => $userId,
            'Migrable' => $migrable,
            'username' => $username,
            'jwt' => $jwt,
        ]);
    }
    private function decodeJwt(string $jwt): array
    {
        try {
            $parts = explode('.', $jwt);
            if (count($parts) !== 3) {
                return [];
            }

            return json_decode(base64_decode($parts[1]), true) ?? [];
        } catch (\Exception $e) {
            return [];
        }
    }
    #[Route('/GeoMapp/production', name: 'production')]
    public function production(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
            $userId = $dataUser['user_id'] ?? null;
            $cpv = $dataUser['cpv'] ?? null;
            $pointOfSaleId  = $dataUser['point_of_sale_id'] ?? null;
            if (!$userId || !$cpv) {
                throw new \Exception('User ID or CPV not found in the response.');
            }
    
            $userData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
            $HierarchyData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/productions-consolidation/{$userId}/V", $jwt);
            $effectifsData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}/tree/all?page=1", $jwt);
            $effectifs = $effectifsData['children'] ?? [];
            $migrableData = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/{$cpv}?page=1", $jwt);
            $arretcuivre = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/interventions-places-arret-cu/{$cpv}?page=1", $jwt);
            $response3 = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/productions-by-lib-motif-instance/3?debut=01-10-2024&fin=31-10-2024&page=1', [
                'headers' => ['Authorization' => 'Bearer ' . $jwt],
            ]);
            $productionsMotif = json_decode($response3->getContent(), true);
            
        } catch (\Exception $e) {
            $this->addFlash('error', $e->getMessage());
            return $this->redirectToRoute('app_dashboard');
        }
        $username = null;
        if ($jwt) {
            $decodedToken = $this->decodeJwt($jwt);
            $username = $decodedToken['username'] ?? 'Guest';
        }
    
        return $this->render('geomap/production/production.html.twig', [
            'cpv' => $cpv,
            'pointOfSaleId'=>$pointOfSaleId ,
            'HierarchyData' => $HierarchyData,
            'effectifs' => $effectifs,
            'migrableData' => $migrableData,
            'arretcuivre' => $arretcuivre,
            'jwt' => $jwt,
            'userId' => $userId,
            'username' => $username,
            'productionsMotif' => $productionsMotif,
        ]);
    }
    
#[Route('/GeoMapp/street-data', name: 'geomap_street_data', methods: ['GET'])]
 public function streetData(HttpClientInterface $httpClient, SessionInterface $session, Request $request): JsonResponse
 {
     // Retrieve JWT from session
     $jwt = $session->get('jwt');
     if (!$jwt) {
         return new JsonResponse(['error' => 'Unauthorized'], 401);
     }
 
     // Validate required query parameters
     $clusterCode = $request->query->get('clusterCode');
     $inseeCode = $request->query->get('inseeCode');
 
     if (!$clusterCode || !$inseeCode) {
         return new JsonResponse([
             'error' => 'Invalid parameters: clusterCode and inseeCode are required.'
         ], 400);
     }
 
     try {
         // Fetch user-connected data
         $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
         $userId = $dataUser['user_id'] ?? null;
 
         if (!$userId) {
             throw new \Exception('User ID not found in the user-connected data.');
         }
 
         // Fetch user details
         $userDetails = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
         $cpv = $dataUser['cpv'] ?? null;
 
         if (!$cpv) {
             throw new \Exception('CPV not found in the user data.');
         }
 
         // Fetch street data
         $url = "https://api.nomadcloud.fr/api/interventions-places-streets/{$cpv}/{$clusterCode}/{$inseeCode}?page=1";
         $streetData = $this->fetchData($httpClient, $url, $jwt);
 
     } catch (\Exception $e) {
         return new JsonResponse(['error' => $e->getMessage()], 500);
     }
 
     return new JsonResponse($streetData);
 }
 #[Route('/GeoMapp/distribue-data', name: 'geomap_distribue_data', methods: ['GET'])]
 public function DistribueData(HttpClientInterface $httpClient, SessionInterface $session, Request $request): JsonResponse
 {
     // Retrieve JWT from session
     $jwt = $session->get('jwt');
     if (!$jwt) {
         return new JsonResponse(['error' => 'Unauthorized'], 401);
     }
 
     // Validate required query parameters
     $clusterCode = $request->query->get('clusterCode');
     $inseeCode = $request->query->get('inseeCode');
 
     if (!$clusterCode || !$inseeCode) {
         return new JsonResponse([
             'error' => 'Invalid parameters: clusterCode and inseeCode are required.'
         ], 400);
     }
 
     try {
         // Fetch user-connected data
         $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
         $userId = $dataUser['user_id'] ?? null;
 
         if (!$userId) {
             throw new \Exception('User ID not found in the user-connected data.');
         }
 
         // Fetch user details
         $userDetails = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
         $cpv = $dataUser['cpv'] ?? null;
 
         if (!$cpv) {
             throw new \Exception('CPV not found in the user data.');
         }
 
         // Fetch street data
         $url = "https://api.nomadcloud.fr/api/interventions-places-distribution-hierarchy/{$cpv}/{$clusterCode}/{$inseeCode}?page=1";
         $DistribueData = $this->fetchData($httpClient, $url, $jwt);
 
     } catch (\Exception $e) {
         return new JsonResponse(['error' => $e->getMessage()], 500);
     }
 
     return new JsonResponse($DistribueData);
 }
 #[Route('/GeoMapp/migrable-data', name: 'migrableData', methods: ['GET'])]
 public function migrableData(HttpClientInterface $httpClient, SessionInterface $session, Request $request): JsonResponse
 {
     // Retrieve JWT from session
     $jwt = $session->get('jwt');
     if (!$jwt) {
         return new JsonResponse(['error' => 'Unauthorized'], 401);
     }
 
     // Validate required query parameters
     $clusterCode = $request->query->get('clusterCode');
     $inseeCode = $request->query->get('inseeCode');
 
     if (!$clusterCode || !$inseeCode) {
         return new JsonResponse([
             'error' => 'Invalid parameters: clusterCode and inseeCode are required.'
         ], 400);
     }
 
     try {
         // Fetch user-connected data
         $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
         $userId = $dataUser['user_id'] ?? null;
 
         if (!$userId) {
             throw new \Exception('User ID not found in the user-connected data.');
         }
 
         // Fetch user details
         $userDetails = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
         $cpv = $dataUser['cpv'] ?? null;
 
         if (!$cpv) {
             throw new \Exception('CPV not found in the user data.');
         }
 
         // Fetch street data
         $url = "https://api.nomadcloud.fr/api/interventions-places-streets-migrable/{$cpv}/{$clusterCode}/{$inseeCode}?page=1";
         $migrableData = $this->fetchData($httpClient, $url, $jwt);

     } catch (\Exception $e) {
         return new JsonResponse(['error' => $e->getMessage()], 500);
     }
 
     return new JsonResponse($migrableData);
 }
//  #[Route('/GeoMapp/vendu-data', name: 'migrableData', methods: ['GET'])]
//  public function VenduData(HttpClientInterface $httpClient, SessionInterface $session, Request $request): JsonResponse
//  {
//      // Retrieve JWT from session
//      $jwt = $session->get('jwt');
//      if (!$jwt) {
//          return new JsonResponse(['error' => 'Unauthorized'], 401);
//      }
 
//      // Validate required query parameters
//      $clusterCode = $request->query->get('clusterCode');
//      $inseeCode = $request->query->get('inseeCode');
 
//      if (!$clusterCode || !$inseeCode) {
//          return new JsonResponse([
//              'error' => 'Invalid parameters: clusterCode and inseeCode are required.'
//          ], 400);
//      }
 
//      try {
//          // Fetch user-connected data
//          $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
//          $userId = $dataUser['user_id'] ?? null;
 
//          if (!$userId) {
//              throw new \Exception('User ID not found in the user-connected data.');
//          }
 
//          // Fetch user details
//          $userDetails = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
//          $cpv = $dataUser['cpv'] ?? null;
 
//          if (!$cpv) {
//              throw new \Exception('CPV not found in the user data.');
//          }
 
//          // Fetch street data
//          $url = "https://api.nomadcloud.fr/api/interventions-places-hierarchy-eligible/{$cpv}?codeCluster={$clusterCode}&codeInsee={$inseeCode}?page=1";
//          $venduDAta = $this->fetchData($httpClient, $url, $jwt);

//      } catch (\Exception $e) {
//          return new JsonResponse(['error' => $e->getMessage()], 500);
//      }
 
//      return new JsonResponse($venduDAta);
//  }
 #[Route('/GeoMapp/arretcuivre-data', name: 'arretcuivreData', methods: ['GET'])]
 public function arretcuivreData(HttpClientInterface $httpClient, SessionInterface $session, Request $request): JsonResponse
 {
     // Retrieve JWT from session
     $jwt = $session->get('jwt');
     if (!$jwt) {
         return new JsonResponse(['error' => 'Unauthorized'], 401);
     }
 
     // Validate required query parameters
     $clusterCode = $request->query->get('clusterCode');
     $inseeCode = $request->query->get('inseeCode');
 
     if (!$clusterCode || !$inseeCode) {
         return new JsonResponse([
             'error' => 'Invalid parameters: clusterCode and inseeCode are required.'
         ], 400);
     }
 
     try {
         // Fetch user-connected data
         $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
         $userId = $dataUser['user_id'] ?? null;
 
         if (!$userId) {
             throw new \Exception('User ID not found in the user-connected data.');
         }
 
         // Fetch user details
         $userDetails = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
         $cpv = $dataUser['cpv'] ?? null;
 
         if (!$cpv) {
             throw new \Exception('CPV not found in the user data.');
         }
 
         // Fetch street data
         $url = "https://api.nomadcloud.fr/api/interventions-places-arret-cu-streets/{$cpv}/{$clusterCode}/{$inseeCode}?page=1";
         $arretcuivre = $this->fetchData($httpClient, $url, $jwt);
     
     } catch (\Exception $e) {
         return new JsonResponse(['error' => $e->getMessage()], 500);
     }
 
     return new JsonResponse($arretcuivre);
 }
 #[Route('/GeoMapp/anciennete-data', name: 'ancienneteData', methods: ['GET'])]
 public function ancienneteData(HttpClientInterface $httpClient, SessionInterface $session, Request $request): JsonResponse
 {
     // Retrieve JWT from session
     $jwt = $session->get('jwt');
     if (!$jwt) {
         return new JsonResponse(['error' => 'Unauthorized'], 401);
     }
 
     // Validate required query parameters
     $clusterCode = $request->query->get('clusterCode');
     $inseeCode = $request->query->get('inseeCode');
 
     if (!$clusterCode || !$inseeCode) {
         return new JsonResponse([
             'error' => 'Invalid parameters: clusterCode and inseeCode are required.'
         ], 400);
     }
 
     try {
         // Fetch user-connected data
         $dataUser = $this->fetchData($httpClient, 'https://api.nomadcloud.fr/api/user-connected', $jwt);
         $userId = $dataUser['user_id'] ?? null;
 
         if (!$userId) {
             throw new \Exception('User ID not found in the user-connected data.');
         }
 
         // Fetch user details
         $userDetails = $this->fetchData($httpClient, "https://api.nomadcloud.fr/api/users/{$userId}", $jwt);
         $cpv = $dataUser['cpv'] ?? null;
 
         if (!$cpv) {
             throw new \Exception('CPV not found in the user data.');
         }
        
         // Fetch street data
         $url = "https://api.nomadcloud.fr/api/interventions-places-streets-by-anciennete/{$cpv}/{$clusterCode}/{$inseeCode}?anciennete=-45&page=1";
         $anciennet = $this->fetchData($httpClient, $url, $jwt);
     
     } catch (\Exception $e) {
         return new JsonResponse(['error' => $e->getMessage()], 500);
     }
 
     return new JsonResponse($anciennet);
 }
 #[Route('/GeoMapp/effectifUser-data', name: 'effectifUser', methods: ['GET', 'PATCH'])]
 public function effectifUser(HttpClientInterface $httpClient, SessionInterface $session, Request $request): JsonResponse {
     $jwt = $session->get('jwt');
     if (!$jwt) {
         return new JsonResponse(['error' => 'Unauthorized'], 401);
     }
     $data = json_decode($request->getContent(), true);
     $selectedUserId = $data['selectedUserId'] ?? null;
     $codeClusterStr = $data['codeClusterStr']?? '';
     $codeInseeStr = $data['codeInseeStr']?? '';
     $nomsVoiesStr = $data['nomsVoiesStr'] ?? '';
     if ($request->isMethod('PATCH') && !$selectedUserId) {
         return new JsonResponse(['error' => 'Missing selectedUserId in request body'], 400);
     }
    $headers = [
        'Authorization' => "Bearer {$jwt}",
        'Content-Type' => 'application/json'
    ];
    $responseUser = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/user-connected', [
        'headers' => $headers
    ]);

    $dataUser = $responseUser->toArray();
    if (!$dataUser || !isset($dataUser['user_id'])) {
        return new JsonResponse(['error' => 'User data not found'], 404);
    }

    $userId = $dataUser['user_id'];
    $cpv = $dataUser['cpv'] ?? null;
    if (!$cpv) {
        return new JsonResponse(['error' => 'CPV not found in user data'], 400);
    }

    $url = "https://api.nomadcloud.fr/api/interventions-places-update-user/{$cpv}?codesClusters={$codeClusterStr}&codesInsees={$codeInseeStr}&nomsVoies={$nomsVoiesStr}";

    $response = $httpClient->request('PATCH', $url, [
        'headers' => $headers,
        'json' => ['userId' => $selectedUserId]
    ]);

    if ($response->getStatusCode() !== 200) {
        throw new \Exception('HTTP error! status: ' . $response->getStatusCode());
    }

    return new JsonResponse(['message' => 'User data updated successfully', 'data' => $response->toArray(),'url' => $url]);
}

private function fetchData(HttpClientInterface $httpClient, string $url, string $jwt): ?array {
    try {
        $response = $httpClient->request('GET', $url, [
            'headers' => ['Authorization' => 'Bearer ' . $jwt]
        ]);

        if ($response->getStatusCode() === 200) {
            return json_decode($response->getContent(), true);
        } else {
            error_log('Response: ' . $response->getContent(false));
            throw new \Exception('Failed to fetch data. Status: ' . $response->getStatusCode());
        }
    } catch (\Exception $e) {
        error_log('Error in fetchData: ' . $e->getMessage());
        return null;
    }
}


#[Route('/blanc_page', name: 'BlancPage')]
    public function BlancPAge(HttpClientInterface $httpClient, SessionInterface $session): Response
    {
        $jwt = $session->get('jwt');
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
        return $this->render('BlancPage/BlancPage.html.twig', [
            'jwt' => $jwt,
        ]);
    }
}
