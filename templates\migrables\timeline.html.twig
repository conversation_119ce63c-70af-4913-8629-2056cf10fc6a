<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>jQuery Dateline Timeline Example</title>
{# <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">
<link href='http://fonts.googleapis.com/css?family=Roboto+Condensed' rel='stylesheet' type='text/css'> #}
{# <link href="css/jquery.dateline.css" rel="stylesheet"> #}
<style>
  body { background-color: #fafafa; }
  .container { margin: 150px auto; max-width: 960px; font-family: 'Roboto Condensed'; }
  </style>
</head>

<body>

  <div class="container">
   
  {# <div id="dl"></div> #}
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.9.1/underscore-min.js"></script>
<script src="https://code.jquery.com/jquery-1.12.4.min.js" integrity="sha384-nvAa0+6Qg9clwYCGGPpDQLVpLNn0fRaROjHqs13t4Ggj3Ez50XnGQqc/r8MhnRDZ" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script src="js/jquery.dateline.js"></script>
<script>
  jQuery(function ($) {
    $('#dl').dateline({
        "start": "1900-01-01",
        "end": "2150-12-31",
        "cursor": "2025-02-01",
        "bands": [
            {
                "width": "60%",
                "scale": 3, // HOUR scale
                "interval": 30 // Modified to 30 minutes instead of 60
            },
            {
                "width": "24%",
                "layout": "overview",
                "scale": 4, // DAY scale
                "interval": 50 // Modified to 50 days instead of 100
            },
            {
                "width": "16%",
                "layout": "overview",
                "scale": 5, // MONTH scale
                "interval": 31, // Modified to 30 months instead of 40
                "multiple": 5 // Modified to 3 months interval instead of 2
            }
        ],
        "events": [
            {"id": 2, "start": "2008-01-01", "text": "YII project started"},
            {"id": 3, "start": "2008-12-03", "text": "YII 1.0 released"},
            {"id": 4, "start": "2014-10-12", "text": "YII 2.0 released"},
            {"id": 5, "start": "1995-06-08", "description": "Known as 'Personal Home Page Tools'", "text": "PHP 1.0 released"},
            {"id": 6, "start": "1997-11-01", "description": "Officially called PHP/FI, the first 'real' PHP", "text": "PHP 2.0 released"},
            {"id": 7, "start": "1998-06-06", "text": "PHP 3.0 released"},
            {"id": 8, "start": "2000-10-20", "text": "Support PHP 3.0 stops"},
            {"id": 9, "start": "2000-05-22", "text": "PHP 4.0 released"},
            {"id": 11, "start": "2001-06-23", "text": "Support PHP 4.0 stops"},
            {"id": 12, "start": "2004-07-13", "text": "PHP 5.0 released"},
            {"id": 13, "start": "2005-09-05", "text": "Support PHP 5.0 stops"},
            {"id": 14, "start": "1968-11-22", "description": "Founder of PHP", "text": "Rasmus Lerdorf born", "caption": "Founder of PHP"},
            {"id": 15, "start": "1912-06-23", "text": "Alan Turing born"},
            {"id": 16, "start": "1954-06-07", "text": "Alan Turing dies"},
            {"id": 17, "start": "1903-12-28", "text": "John von Neumann born"},
            {"id": 18, "start": "1957-02-08", "text": "John von Neumann dies"},
            {"id": 19, "start": "1925-09-28", "text": "Seymour Cray born"},
            {"id": 20, "start": "1996-10-05", "text": "Seymour Cray dies"},
            {"id": 21, "start": "1923-11-08", "text": "Jack Kilby born", "caption": "Inventor of the chip"},
            {"id": 22, "start": "2005-06-20", "text": "Jack Kilby dies"},
            {"id": 23, "start": "1927-12-12", "text": "Robert Noyce born"},
            {"id": 24, "start": "1990-06-03", "text": "Robert Noyce dies"},
            {"id": 25, "start": "1955-02-24", "text": "Steve Jobs born"},
            {"id": 26, "start": "2011-10-05", "text": "Steve Jobs dies"},
            {"id": 27, "start": "1950-08-11", "text": "Steve Wozniak born"},
            {"id": 28, "start": "1976-04-01", "text": "Apple founded"},
            {"id": 29, "start": "1980-12-12", "text": "Apple goes public"},
            {"id": 30, "start": "2007-01-09", "text": "Apple Computer, Inc. renamed to Apple, Inc."},
            {"id": 31, "start": "1984-01-22", "text": "Apple Macintosh launched"},
            {"id": 32, "start": "1977-06-10", "text": "Apple ][ launched"},
            {"id": 33, "start": "2001-03-24", "text": "Mac OS X released"},
            {"id": 34, "start": "2006-01-10", "text": "MacBook Pro released"},
            {"id": 35, "start": "2007-01-09", "text": "Apple iPhone introduced"},
            {"id": 36, "start": "1955-10-28", "text": "Bill Gates born"},
            {"id": 37, "start": "1953-01-21", "text": "Paul Allen born"},
            {"id": 38, "start": "1975-04-04", "text": "Microsoft founded"},
            {"id": 39, "start": "1985-11-20", "text": "Windows 1.0 released"}
        ]
    });
});
</script>


</body>
<script type="text/javascript">

  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', 'UA-********-1']);
  _gaq.push(['_setDomainName', 'jqueryscript.net']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();

</script>
</html>
