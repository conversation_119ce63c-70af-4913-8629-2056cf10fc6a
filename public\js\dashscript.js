document.addEventListener('DOMContentLoaded', function () {
    const themeSwitcher = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const themeListItems = document.querySelectorAll('.theme-options li');

    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.body.setAttribute('data-theme', savedTheme);
        updateActiveTheme(savedTheme);
    }

    themeSwitcher.addEventListener('click', function () {
        if (themeOptions.style.display === 'block') {
            themeOptions.style.display = 'none';
        } else {
            themeOptions.style.display = 'block';
        }
    });

    themeListItems.forEach(item => {
        item.addEventListener('click', function () {
            const selectedTheme = this.getAttribute('data-theme');
            document.body.setAttribute('data-theme', selectedTheme);
            localStorage.setItem('theme', selectedTheme); 
            themeOptions.style.display = 'none'; 
            updateActiveTheme(selectedTheme); 
        });
    });

    document.addEventListener('click', function (event) {
        if (!themeSwitcher.contains(event.target) && !themeOptions.contains(event.target)) {
            themeOptions.style.display = 'none';
        }
    });

    function updateActiveTheme(theme) {
        themeListItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('data-theme') === theme) {
                item.classList.add('active');
            }
        });
    }
});






document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    const leftSidebar = document.getElementById('leftSidebar');

    sidebarToggleBtn.addEventListener('click', function() {
        if (leftSidebar.classList.contains('collapsed')) {
            leftSidebar.classList.remove('collapsed');
            leftSidebar.style.width = '250px'; 
        } else {
            leftSidebar.classList.add('collapsed');
            leftSidebar.style.width = '0'; 
        }
    });
});


