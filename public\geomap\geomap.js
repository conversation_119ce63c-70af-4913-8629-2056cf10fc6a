
const currentDate = new Date();

// Format the current date as 'dd-mm-yyyy'
const day = String(currentDate.getDate()).padStart(2, '0');
//const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are zero-based
const month ='01';
const year = currentDate.getFullYear();
let Type='V';


const formattedDate = `${day}-${month}-${year}`;
function formatDateRange(month, year) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    const formatStartDate = `${String(startDate.getDate()).padStart(2, '0')}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;
    const formatEndDate = `${String(endDate.getDate()).padStart(2, '0')}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${endDate.getFullYear()}`;
    return `debut=${formatStartDate}&fin=${formatEndDate}`;
}

// Example usage:
const dateString = formatDateRange(2, 2025); // For February 2025
console.log(dateString); // Outputs: "debut=01-02-2025&fin=28-02-2025"

document.addEventListener('DOMContentLoaded', () => {
    localStorage.setItem("clusterInseeDatag", JSON.stringify([]));
    localStorage.setItem("clusterInseeDatag", JSON.stringify([]));
    localStorage.setItem("ville", '');
    localStorage.setItem("libelleCluster", '');
    localStorage.setItem("clusterCode", '');
    localStorage.setItem("CodeInsee", '');
    localStorage.setItem("ClusterCode", '');
    localStorage.setItem("lenghthsavedVoies",0);
});
document.addEventListener('DOMContentLoaded', () => {
    const currentThemeDisplay = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const currentThemeIcon = currentThemeDisplay.querySelector('img');
    const currentThemeText = currentThemeDisplay.childNodes[1];

    let currentTheme = localStorage.getItem('theme') || 'dark'||'darkblue'||'lightsand'||'darklight'||"darkpurple";
    document.body.setAttribute('data-theme', currentTheme);

    const currentThemeElement = document.querySelector(`li[data-theme="${currentTheme}"]`);
    if (currentThemeElement) {
      updateThemeDisplay(currentThemeElement);
    }

    currentThemeDisplay.addEventListener('click', () => {
      themeOptions.style.display = themeOptions.style.display === 'block' ? 'none' : 'block';
    });

    themeOptions.addEventListener('click', event => {
      const themeChoice = event.target.closest('li');
      if (themeChoice) {
        const selectedTheme = themeChoice.getAttribute('data-theme');
        const imgSrc = themeChoice.querySelector('img').src;
        const themeName = themeChoice.textContent.trim();

        currentThemeIcon.src = imgSrc;
        currentThemeText.nodeValue = " " + themeName + " ";

        document.body.setAttribute('data-theme', selectedTheme);
        localStorage.setItem('theme', selectedTheme);

        themeOptions.style.display = 'none';

        document.querySelectorAll('.theme-options li').forEach(li => li.classList.remove('active'));
        themeChoice.classList.add('active');
      }
    });
  });
  
  function updateThemeDisplay(themeElement) {
    const iconSrc = themeElement.querySelector('img').src;
    const iconName = themeElement.textContent.trim();
    document.querySelector('.current-theme img').src = iconSrc;
    document.querySelector('.current-theme').childNodes[1].nodeValue = " " + iconName + " ";
  }

const IcontoggleChart = document.querySelector('.IcontoggleChart');
let isTableVisible = false;
function toggleChartPanelDown() {

    const TablePanel = document.getElementById('displayChartPanel');
    if (isTableVisible) {
        TablePanel.style.bottom = '-330px';
        setTimeout(() => IcontoggleChart.style.transform = 'rotate(0deg)', 300);
        //setTimeout(() => TablePanel.style.display = 'none', 300);
    } else {
        //TablePanel.style.display = 'block';
        setTimeout(() => TablePanel.style.bottom = '0', 10);
        IcontoggleChart.style.transform = 'rotate(180deg)';
    }
    isTableVisible = !isTableVisible;
}
  document.addEventListener('DOMContentLoaded', function () {
    const resizers = document.querySelectorAll('.resizer');

    resizers.forEach((resizer) => {
        let startY, startHeightPrev, startHeightNext;

        resizer.addEventListener('mousedown', function (e) {
            const prevDiv = resizer.previousElementSibling;
            const nextDiv = resizer.nextElementSibling;

            // Store the initial mouse position and heights
            startY = e.pageY;
            startHeightPrev = prevDiv.offsetHeight;
            startHeightNext = nextDiv.offsetHeight;

            // Add mousemove and mouseup event listeners
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);

            function mouseMoveHandler(e) {
                const dy = e.pageY - startY;

                // Calculate new heights
                const newHeightPrev = Math.max(50, startHeightPrev + dy);
                const newHeightNext = Math.max(50, startHeightNext - dy);

                // Apply new heights to the sections
                prevDiv.style.height = `${newHeightPrev}px`;
                nextDiv.style.height = `${newHeightNext}px`;
            }

            function mouseUpHandler() {
                // Remove the event listeners when mouse is released
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            }
        });
    });
});

document.getElementById('HandleRightPanel').addEventListener('click', function() {
    var sidebar = document.querySelector('.right-sidebar-concepteur');
    var sidebarStyle = window.getComputedStyle(sidebar);
    var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');

    if (sidebarStyle.right === '0px') {
        sidebar.style.right = '-319px';
        IconRightPanelToggle.style.transform = 'rotate(-90deg)';
    } else {
        sidebar.style.right = '0px';
        IconRightPanelToggle.style.transform = 'rotate(90deg)';
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const panel = document.querySelector('.panel');

    if (panel) {
        panel.addEventListener('click', function(event) {
            const target = event.target.closest('.icon');
            if (target) {
                document.querySelectorAll('.panel .icon').forEach(icon => {
                    icon.classList.remove('active-field');
                    icon.classList.remove('brighticon');
                });

                target.classList.add('active-field');
                target.classList.add('brighticon');
            }
        });
    }
});


    // Select all buttons with the class "day"
    const dayButtons = document.querySelectorAll('.day');

    // Loop through each button and add a click event listener
    dayButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Toggle the "activeday" class when clicked
            this.classList.toggle('activeday');
        });
    });



       // Récupérer tous les boutons et sections
       const spans = document.querySelectorAll('.cardCan .headers span');
       const sections = document.querySelectorAll('.cardCan > div[class^="content-"]');
   
       // Ajouter un événement de clic à chaque bouton
       spans.forEach(span => {
           span.addEventListener('click', () => { // Retirer la classe active de tous les boutons
               spans.forEach(s => s.classList.remove('activejourne'));
               // Ajouter la classe active au bouton cliqué
               span.classList.add('activejourne');
   
               // Masquer toutes les sections
               sections.forEach(section => section.classList.remove('active-section'));
               // Afficher la section correspondante
               const targetId = span.id.replace('-palettes', '-contents');
               document.getElementById(targetId).classList.add('active-section');
           });
       }); 

       let debounceTimer;

       document.getElementById('TreeSearchs').addEventListener('input', function () {
        clearTimeout(debounceTimer);
    
        const treeSearchInput = this.value.trim();
        const treeRoot = document.getElementById('tree-root');
    
        debounceTimer = setTimeout(async function () {
            try {
                let data = treeSearchInput === "" 
                    ? await fetchdataHierarchy() 
                    : await fetchdatalanciennete(treeSearchInput);
    
                if (data) {
                    const htmlContent = treeSearchInput === "" 
                        ? generateHierarchyHtml(data, false) 
                        : generateHierarchyHtmlanciennete(data);
    
                    if (htmlContent) {
                        treeRoot.innerHTML = htmlContent;
                        setupCaretListeners('tree-root');
                       treeSearchInput === ""?setupFieldListeners(false, false, false): setupFieldListenersDistribue("non");
                    }
                }
            } catch (error) {
                console.error("Erreur lors du chargement des données :", error);
            }
        }, 500);
});

       async function fetchdatalanciennete() {
        try {
            // Récupérer la valeur de l'input
            const treeSearchsInput = document.getElementById('TreeSearchs');
            const inputValue = treeSearchsInput ? treeSearchsInput.value.trim() : '';
    
            let debutAnciennete, finAnciennete;
    
            // Si l'input contient " a ", extraire les deux valeurs
            if (inputValue.includes(' a ')) {
                [debutAnciennete, finAnciennete] = inputValue.split(' a ').map(val => val.trim());
            } else if(inputValue.includes(' à ')){
                [debutAnciennete, finAnciennete] = inputValue.split(' à ').map(val => val.trim());

            }
            else {
                // Sinon, considérer un seul chiffre avec finAnciennete = 0
                debutAnciennete = inputValue;
                finAnciennete = "0";
            }
    
            // Vérifier si les valeurs sont bien des nombres
            if (isNaN(debutAnciennete) || isNaN(finAnciennete)) {
                return;
                //throw new Error("Les valeurs 'debutAnciennete' et 'finAnciennete' doivent être des nombres valides.");
            }
    
            // Vérification des paramètres requis
            if (!cpv) {
                throw new Error("Le paramètre 'cpv' est manquant.");
            }
    
            if (!jwtToken) {
                throw new Error("Le token JWT est manquant.");
            }
    
            // Construire l'URL avec les valeurs extraites
            const url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-by-anciennete/${cpv}?debutAnciennete=${debutAnciennete}&finAnciennete=${finAnciennete}&page=1`;
    
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            return await response.json();
    
        } catch (error) {
            console.error('Fetch error:', error);
            return null;
        }
    }
    async function fetchdataVenduForMenuSelection() {
        try {
            if (!cpv) {
                throw new Error("Le paramètre 'cpv' est manquant.");
            }
            if (!jwtToken) {
                throw new Error("Le token JWT est manquant.");
            }
    
            let url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-eligible/${cpv}?page=1`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            const data = await response.json();

    
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
            return null;
        }
    }
    async function fetchdataArretcuivreForMenuSelection() {
        try {
            if (!cpv) {
                throw new Error("Le paramètre 'cpv' est manquant.");
            }
            if (!jwtToken) {
                throw new Error("Le token JWT est manquant.");
            }
    
            let url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu/${cpv}?page=1`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            const data = await response.json();

    
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
            return null;
        }
    }
    let Interventionsmigrable;
    async function fetchdataMigrable(clusterCode) {
        try {
            if (!cpv) {
                throw new Error("Le paramètre 'cpv' est manquant.");
            }
            if (!jwtToken) {
                throw new Error("Le token JWT est manquant.");
            }
    
            let url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/${cpv}?codeCluster=${clusterCode}&page=1`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            const data = await response.json();
            displayTP(data.taux_penetration);
            //console.log('Response fetchdataMigrable:', data);
            Interventionsmigrable = data;
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
            return null;
        }
    }
    function displayTP(data){
        console.log('displayTP',data);
        document.querySelector('.TauxPenetration').textContent =data ;
    }
    async function fetchdataHierarchy() {
        try {

    
            let url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy/${cpv}?page=1`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            const data = await response.json();
            // console.log('Response fetchdataHierarchy:', data);
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
            return null;
        }
    }
    async function fetchdataDistrubue(option,optionisMigrable, clusterCode = '', inseeCode = '',) {
        try {
            let url = `https://api.nomadcloud.fr/api/interventions-places-distribution-hierarchy/${cpv}/${option}/${optionisMigrable}?page=1`;
    
            // Ajout des paramètres optionnels si disponibles
            if (clusterCode) url += `&codeCluster=${clusterCode}`;
            if (inseeCode) url += `&codeInsee=${inseeCode}`;
            console.log(url);
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            const data = await response.json();
            console.log('Response fetchdataDistrubue:', data);
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
            return null;
        }
    }

    document.addEventListener("DOMContentLoaded", async function () {
        const treeRoot = document.getElementById('tree-root');
        const menuItems = document.querySelectorAll('.dropdown-content a');
        const itemChoosed = document.querySelector('.itemChoosed');
        const ClusterFilter = document.querySelector('.ClusterFilter');
    
     
        if (!treeRoot || !itemChoosed) {
            console.error("Required elements not found in the DOM.");
            return;
        }

        menuItems.forEach(item => {
            item.addEventListener('click', async function (event) {
                event.preventDefault();
                ClusterFilter.style.display = 'none';
    
                const selectedMenu = this.textContent.trim();
                let dataToUse;
                let isMigrable = false, isArretCuivre = false, isAnciennete = false,isVendu = false;
                let htmlContent = '';
                let Type = localStorage.getItem("clickedType") || "T"; // Valeur par défaut
    
                localStorage.setItem("isMigrable", isMigrable.toString());
                itemChoosed.textContent = selectedMenu;
                
                // Fonction pour nettoyer le localStorage
                const clearLocalStorage = () => {
                    const keysToRemove = [
                        'savedClusterslibelle',
                        'nomberprisesVille',
                        'savedVilleslibelle',
                        'savedRueslibelle',
                        'totalGeneral',
                        'nomberprisesclutser',
                        'isCheckedsomme'
                    ];
                    keysToRemove.forEach(key => localStorage.removeItem(key));
                };
    
                clearLocalStorage();
    
                switch (selectedMenu) {
                    case "Parc":
                        if (Type === "T") {
                            dataToUse = HierarchyData;
                        } else if (Type === "N") {
                            dataToUse = await fetchdataDistrubue("oui", "non");
                        } else if (Type === "D") {
                            dataToUse = await fetchdataDistrubue("non", "non");
                        }
                        localStorage.setItem("isMigrable", "false");
                        htmlContent = generateHierarchyHtml(dataToUse, isMigrable,isparc=true);
                        break;
    
                    case "Migrables":
                        if (Type === "T") {
                            dataToUse = Migrable;
                        } else if (Type === "N") {
                            dataToUse = await fetchdataDistrubue("oui", "oui");
                        } else if (Type === "D") {
                            dataToUse = await fetchdataDistrubue("non", "oui");
                        }
                        isMigrable = true;
                        localStorage.setItem("isMigrable", isMigrable.toString());
                        htmlContent = generateHierarchyHtml(dataToUse, isMigrable);
                        break;
    
                    case "Arret cuivre":
                        dataToUse = await fetchdataArretcuivreForMenuSelection();
                        isArretCuivre = true;
                        localStorage.setItem("isMigrable", "false");
                        htmlContent = generateHierarchyHtmlCuivre(dataToUse);
                        break;
    
                    case "Anciennete":
                        dataToUse = await fetchdatalanciennete();
                        isAnciennete = true;
                        localStorage.setItem("isMigrable", "false");
                        htmlContent = generateHierarchyHtmlanciennete(dataToUse);
                        break;

                    case "Vendu":
                        dataToUse = await fetchdataVenduForMenuSelection();
                        isVendu = true;
                        localStorage.setItem("isMigrable", "false");
                        htmlContent = generateHierarchyHtml(dataToUse);
                        break;
    
                    default:
                        console.warn("Menu non pris en charge :", selectedMenu);
                        return;
                }
                
                if (htmlContent) {
                    treeRoot.innerHTML = htmlContent;
                    setupCaretListeners('tree-root');
                    setupFieldListeners(isMigrable, isArretCuivre, isAnciennete,isVendu);
                }
            });
        });
    
        // Chargement par défaut
        let initialData = HierarchyData;
        if (!initialData) {
            initialData = await fetchdataHierarchy(); // Récupérer les données si nécessaire
        }
        const htmlContent = generateHierarchyHtml(initialData);
        treeRoot.innerHTML = htmlContent;
        setupCaretListeners('tree-root');
        AllClusterRue = await setupFieldListeners(false, false, false);
        setupSearchFunctionality();
    });



    function generateHierarchyHtml(data, isMigrable = false,isparc=false) {
        console.log('Data:', data,isparc);
        
        let htmlContent = '';
        const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
        const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    
        data.forEach(cluster => {
            let placesContent = '';
    
            cluster.villes.forEach(place => {

                const dejaClientInseeFn = isparc
                ? `fetchdataElligible('${cluster.clusterCode}','${place.cod_insee}');`
                : '';

                placesContent += `
                    <li class="field-list-item">
                        <div onclick=" ${dejaClientInseeFn}findTotalPriceville('${place.total_prises}', '${place.ville}','${place.cod_insee}','${cluster.clusterCode}');saveClusterInsee('${cluster.clusterCode}', '${place.cod_insee}','${cluster.libelle_cluster}','${place.ville}');"class="caret fieldLiSpan StreetSpan" style="display: flex; align-items: center; width: 100%;">
                            <div  style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                                <div class="fieldLiSpan" style="flex-grow: 1; display: flex; align-items: center;"
                                    data-cluster-code="${cluster.clusterCode}" data-insee-code="${place.cod_insee}"
                                    onclick=" findClusterDataForUser('${cluster.clusterCode}', '${place.cod_insee}'); findInseeMeteo('${place.cod_insee}'); fetchCityData('${place.cod_insee}');displayInterventionsMigrable('${place.cod_insee}',true);sendRightdataforChartDAys('${place.cod_insee}',true); cuivreV2('${cluster.clusterCode}','${place.cod_insee}');">
                                    <div style="display: flex; align-items: center;" >
                                        <svg style="height: 12px; width: 12px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d=M12.7,1.1c0-0.6-0.5-1.1-1.1-1.1H1.1C0.5,0,0,0.5,0,1.1v4.7h12.7V1.1z /><path d=M12.7,11.6V6.9H0v4.7c0,0.6,0.5,1.1,1.1,1.1h10.4C12.2,12.7,12.7,12.2,12.7,11.6z /></g></svg>
                                    </div>
                                    ${place.ville}
                                </div>
                                <div class="total-place" onclick="CallHiarchyRue('${cluster.clusterCode}',false);" style="margin-right: 15px; color: #326E78;"
                                data-insee-code="${place.cod_insee}"
                                data-nb-fyr-adsl="${place.nb_fyr_adsl}"
                                    data-nb-fyr-fttb="${place.nb_fyr_fttb}"
                                    data-nb-fyr-thd="${place.nb_fyr_thd}"
                                    data-nb-fyr-mob-mono="${place.nb_fyr_mob_mono}"
                                    data-nb-fyr-mob-multi-thd="${place.nb_fyr_mob_multi_thd}"
                                    data-nb-fyr-mob-multi-adsl="${place.nb_fyr_mob_multi_adsl}"
                                    data-total-prises="${place.total_prises}">
                                    ${ 
                                        (
                                            (document.getElementById('nb_fyr_adsl').checked ? place.nb_fyr_adsl : 0) +
                                            (document.getElementById('nb_fyr_fttb').checked ? place.nb_fyr_fttb : 0) +
                                            (document.getElementById('nb_fyr_thd').checked ? place.nb_fyr_thd : 0) +
                                            (document.getElementById('nb_fyr_mob_mono').checked ? place.nb_fyr_mob_mono : 0) +
                                            (document.getElementById('nb_fyr_mob_multi_thd').checked ? place.nb_fyr_mob_multi_thd : 0) +
                                            (document.getElementById('nb_fyr_mob_multi_adsl').checked ? place.nb_fyr_mob_multi_adsl : 0)
                                        ) || place.total_prises || 0
                                    }
                                </div>
                            </div>
                        </div>
                    </li>
                `;
            });
    
            const total = isMigrable 
                ? (
                    (document.getElementById('nb_fyr_adsl').checked ? cluster.nb_fyr_adsl : 0) +
                    (document.getElementById('nb_fyr_fttb').checked ? cluster.nb_fyr_fttb : 0) +
                    (document.getElementById('nb_fyr_thd').checked ? cluster.nb_fyr_thd : 0) +
                    (document.getElementById('nb_fyr_mob_mono').checked ? cluster.nb_fyr_mob_mono : 0) +
                    (document.getElementById('nb_fyr_mob_multi_thd').checked ? cluster.nb_fyr_mob_multi_thd : 0) +
                    (document.getElementById('nb_fyr_mob_multi_adsl').checked ? cluster.nb_fyr_mob_multi_adsl : 0)
                )
                : cluster.total_prises;
                const dejaClientClusterFn = isparc
                ? `fetchdataElligible('${cluster.clusterCode}');`
                : '';
            htmlContent += `
                <li class="formListItem">
                    <div class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center; width: 100%;" 
                    onclick="${dejaClientClusterFn}findTotalPrice('${total}', '${cluster.libelle_cluster}','${cluster.clusterCode}'); fetchDataJachere('${cluster.clusterCode}'); OpenRightPanel('${cluster.clusterCode}',' ${cluster.libelle_cluster}'); displayInterventionsMigrable('${cluster.clusterCode}',false);findClusterDataForUser('${cluster.clusterCode}') ; cuivreV2('${cluster.clusterCode}',''); sendRightdataforChartDAys('${cluster.clusterCode}',false);simulateClickByClusterCode('${cluster.clusterCode}'); "> 
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;" >
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                                </div>
                                ${cluster.libelle_cluster}
                            </div>
                            <div class="total-cluster" onclick="CallHiarchyRue('${cluster.clusterCode}',true)"style="display: flex; align-items: center; margin-right: 5px; font-size: 16px;"
                                data-nb-fyr-adsl="${cluster.nb_fyr_adsl}"
                                data-nb-fyr-fttb="${cluster.nb_fyr_fttb}"
                                data-nb-fyr-thd="${cluster.nb_fyr_thd}"
                                data-nb-fyr-mob-mono="${cluster.nb_fyr_mob_mono}"
                                data-nb-fyr-mob-multi-thd="${cluster.nb_fyr_mob_multi_thd}"
                                data-nb-fyr-mob-multi-adsl="${cluster.nb_fyr_mob_multi_adsl}"
                                data-total-prises="${cluster.total_prises}"
                                data-is-migrable="${isMigrable}">
                                ${total}
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-left: 6px; border-radius: 2px;" version=1.1 viewBox="0 0 15.8 14.1"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d="M1.8,0h3c1.7,0,3.1,1.4,3.1,3.1S6.5,6.2,4.8,6.2H3.5c-0.9,0-1.7,0.8-1.7,1.7c0,1,0.8,1.8,1.7,1.8h1.8v1.8H3.5C1.6,11.4,0,9.8,0,7.9s1.6-3.5,3.5-3.5h1.3c0.7,0,1.3-0.6,1.3-1.3S5.5,1.8,4.8,1.8h-3c-0.5,0-0.9-0.4-0.9-0.9C0.9,0.4,1.3,0,1.8,0z M12.3,7v7H9.7c-1,0-1.8-0.8-1.8-1.8H6.1V8.8h1.8c0-1,0.8-1.8,1.8-1.8H12.3zM15.8,8.8c0-0.5-0.4-0.9-0.9-0.9h-1.8v1.8h1.8C15.4,9.7,15.8,9.2,15.8,8.8z M15.8,12.3c0-0.5-0.4-0.9-0.9-0.9h-1.8v1.7h1.8C15.4,13.2,15.8,12.8,15.8,12.3z"/></g></svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nested">${placesContent}</ul>
                </li>
            `;
        });
    
        return htmlContent;
    }
    
    // Fonction pour mettre à jour les totaux
    function updateTotals() {
        // Mise à jour des totaux des lieux
        document.querySelectorAll('.total-place').forEach(element => {
            const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
            const fttb = parseInt(element.dataset.nbFyrFttb) || 0;
            const thd = parseInt(element.dataset.nbFyrThd) || 0;
            const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
            const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
            const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
            const totalPrises = parseInt(element.dataset.totalPrises) || 0;
    
            let sum = 0;
            if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
            if (document.getElementById('nb_fyr_fttb').checked) sum += fttb;
            if (document.getElementById('nb_fyr_thd').checked) sum += thd;
            if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
            if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
            if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;
    
            element.textContent = sum || totalPrises || 0;
        });
    
        // Mise à jour des totaux des clusters
        document.querySelectorAll('.total-cluster').forEach(element => {
            const isMigrable = element.dataset.isMigrable === 'true';
            const totalPrises = parseInt(element.dataset.totalPrises) || 0;
    
            if (isMigrable) {
                const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
                const fttb = parseInt(element.dataset.nbFyrFttb) || 0;
                const thd = parseInt(element.dataset.nbFyrThd) || 0;
                const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
                const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
                const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
    
                let sum = 0;
                if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
                if (document.getElementById('nb_fyr_fttb').checked) sum += fttb;
                if (document.getElementById('nb_fyr_thd').checked) sum += thd;
                if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
                if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
                if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;
    
                element.textContent = sum || totalPrises || 0;
            }
        });
    }
    document.querySelectorAll('#fileUploadForm input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateTotals);
    });
    function saveClusterInsee(clusterCode, codInsee,libelleCluster,ville) {
        // Retrieve existing data from localStorage or create an empty array
        let savedData = JSON.parse(localStorage.getItem("clusterInseeData")) || [];
        
        // Push the new cluster and place into the saved data
        savedData.push({ clusterCode, codInsee });
    
        // Save the updated data back to localStorage
        localStorage.setItem("clusterInseeData", JSON.stringify(savedData));
        localStorage.setItem("libelleCluster", libelleCluster);
        localStorage.setItem("ville", ville);
        localStorage.removeItem('savedVoiestotal');
        localStorage.removeItem('RueHiarchyvoie');
       // console.log("Saved data:", savedData);
    }
    let sommecluster = 0;
    let st = 0;
    let savedClusters = [];
    
    function findTotalPrice(total, libelle_cluster,clusterCode) {
        console.log("Finding total price", total);
        // Vérifier si le cluster existe déjà
        let existingCluster = savedClusters.find(cluster => cluster.libelle === libelle_cluster);
        
        if (!existingCluster) {
            st++;
            sommecluster += Number(total);  
    
            // Ajouter le nouveau cluster
            savedClusters.push({ libelle: libelle_cluster, total: Number(total) });
    
            localStorage.setItem('totalGeneral', sommecluster);
            localStorage.setItem("nomberprisesclutser", st);
            localStorage.setItem("savedClusterslibelle", JSON.stringify(savedClusters)); // Stocker en JSON
        }
        localStorage.setItem('totalGeneralCluster', total);
       // fetchdataMigrable(clusterCode);
    }
    
    let sommeVille = 0;
    let stville = 0;
    let savedVilles = [];
    
    async function findTotalPriceville(total, libelle_ville,codeInsee,codeCluster) {
        // Vérifier si la ville existe déjà
        localStorage.setItem('CodeInsee', codeInsee);
        let existingVille = savedVilles.find(ville => ville.libelle === libelle_ville);
    
        if (!existingVille) {
            stville++;
            sommeVille += Number(total);
    
            // Ajouter la nouvelle ville
            savedVilles.push({ libelle: libelle_ville, total: Number(total),codeInsee: codeInsee ,codeCluster: codeCluster});
    
            localStorage.setItem('totalGeneral', sommeVille);
            localStorage.setItem("nomberprisesVille", stville);
            localStorage.setItem("savedVilleslibelle", JSON.stringify(savedVilles));
        }
        localStorage.setItem('totalGeneralVille', total);

    }
    

    function generateHierarchyHtmlCuivre(data) {
        const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
        const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
        let htmlContent = '';
    
        const years = Object.keys(data).sort((a, b) => b - a);
    
        years.forEach(year => {
            const yearData = data[year][1];
            const clusters = yearData.clusters;
    
            let clustersHtml = '';
            clusters.forEach(cluster => {
                let villesHtml = '';
                if (cluster.villes) {
                    cluster.villes.forEach(ville => {
                        villesHtml += `
                            <li class="field-list-item">
                                <span onclick="findTotalPriceville('${ville.totalPrises}', '${ville.ville}','${ville.cod_insee}','${cluster.code_cluster}');saveClusterInsee('${cluster.code_cluster}', '${ville.cod_insee}','${cluster.libelle_cluster}','${ville.ville}'););"class="caret fieldLiSpan StreetSpan" style="display: flex; align-items: center; width: 100%;">
                                    <span class="fieldLiSpan"  onclick=" findClusterDataForUser('${cluster.code_cluster}', '${ville.cod_insee}'); findInseeMeteo('${ville.cod_insee}'); fetchCityData('${ville.cod_insee}');displayInterventionsMigrable('${ville.cod_insee}',true);sendRightdataforChartDAys('${ville.cod_insee}',true); cuivreV2('${cluster.code_cluster}','${ville.cod_insee}');"
                                    style="flex-grow: 1; display: flex; align-items: center;" 
                                          data-cluster-code="${cluster.code_cluster}" 
                                          data-insee-code="${ville.cod_insee}">
                                    <img src="${flecheFields}" style="width: 12px; margin-right: 6px;">
                                    <div style="height: 13px; width: 13px; background: #015D92; border-radius: 2px; margin-right: 5px; display: flex; align-items: center;">
                                        <div style="height: 2px; width: 13px; background: #000;"></div>
                                    </div>
                                        ${ville.ville}
                                    </span>
                                    <span  class="total-place" onclick="findTotalPriceville('${ville.totalPrises}', '${ville.ville}','${ville.cod_insee}','${cluster.code_cluster}');CallHiarchyRue('${cluster.clusterCode}',false);" 
                                     data-insee-code="${ville.cod_insee}"
                                     style="margin-right: 15px; color: #326E78;">${ville.totalPrises}</span>
                                </span>
                            </li>
                        `;
                    });
                }
    
                clustersHtml += `
                    <li class="formListItem"  >
                        <div class="caret formSpan" onclick="findTotalPrice('${cluster.totalPrises}', '${cluster.libelle_cluster}','${cluster.clusterCode}');fetchDataJachere('${cluster.clusterCode}'); OpenRightPanel('${cluster.clusterCode}', '${cluster.libelle_cluster}'); cuivreV2('${cluster.code_cluster}','') ;displayInterventionsMigrable('${cluster.code_cluster}',false);findClusterDataForUser('${cluster.code_cluster}') ;sendRightdataforChartDAys('${cluster.code_cluster}',false);" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                            <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                                <div style="display: flex; align-items: center;"  >
                                    <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                        <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                    </div>
                                    <div style="display: flex; align-items: center;" >
                                        <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                                    </div>
                                    ${cluster.libelle_cluster}
                                </div>
                                <div  class="total-cluster"    onclick="CallHiarchyRue('${cluster.clusterCode}',true)" style="display: flex; align-items: center;margin-right: 5px;font-size: 16px;">
                                    ${cluster.totalPrises}
                                    <i class="bi bi-plugin" style="color: var(--coloredTextPrimary); margin-left: 6px;border-radius: 2px;"></i>
                                </div>
                            </div>
                        </div>
                        <ul class="nested">${villesHtml}</ul>
                    </li>
                `;
            });
    
            htmlContent += clustersHtml;
        });
    
        return htmlContent;
    }

    function generateHierarchyHtmlanciennete(data) {  
        let htmlContent = '';
        const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
        const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    
        data.clusters.forEach(cluster => {
            let placesContent = '';
    
            if (Array.isArray(cluster.villes)) {
                cluster.villes.forEach(place => {
                    placesContent += `
                        <li class="field-list-item">
                            <span onclick="findTotalPriceville('${place.totalPrises}', '${place.ville}','${place.cod_insee}','${cluster.code_cluster}');" class="caret fieldLiSpan StreetSpan" style="display: flex; align-items: center; width: 100%;">
                                <span class="fieldLiSpan" onclick="cuivreV2('${cluster.code_cluster}','${place.cod_insee}')" style="flex-grow: 1; display: flex; align-items: center;"
                                    data-cluster-code="${cluster.code_cluster}" 
                                    data-insee-code="${place.cod_insee || ''}">
                                    <img src="${flecheFields}" style="width: 12px; margin-right: 6px;">
                                    <div style="height: 13px; width: 13px; background: #015D92; border-radius: 2px; margin-right: 5px; display: flex; align-items: center;">
                                        <div style="height: 2px; width: 13px; background: #000;"></div>
                                    </div>
                                    ${place.ville || 'Inconnu'}
                                </span>
                                <span class="total-place" onclick="CallHiarchyRue('${cluster.clusterCode}',false);"  style="margin-right: 15px; color: #326E78;"data-insee-code="${place.cod_insee}">
                                    ${place.totalPrises || 0}
                                </span>
                            </>
                        </li>
                    `;
                });
            }
    
            htmlContent += `
                <li class="formListItem">
                    <div onclick="findTotalPrice('${cluster.totalPrises}', '${cluster.libelle_cluster}','${cluster.clusterCode}');"class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center; width: 100%;" >
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;">
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                                </div>
                                ${cluster.libelle_cluster || 'Inconnu'}
                            </div>
                            <div class="total-cluster" onclick="CallHiarchyRue('${cluster.clusterCode}',true);" style="display: flex; align-items: center; margin-right: 5px; font-size: 16px;" 
                                data-total-prises="${cluster.totalPrises || 0}">${cluster.totalPrises || 0}
                                <i class="bi bi-plugin" style="color: var(--coloredTextPrimary); margin-left: 6px; border-radius: 2px;"></i>
                            </div>
                        </div>
                    </div>
                    <ul class="nested">${placesContent}</ul>
                </li>
            `;
        });
    
        return htmlContent;
    }

    function setupCaretListeners(rootElementId) {
        const rootElement = document.getElementById(rootElementId);
        const togglers = rootElement.querySelectorAll(".caret, .formSpan");
        
        togglers.forEach(caret => {
            caret.addEventListener("click", function(event) {
                event.stopPropagation();
        
                let nestedUl = this.closest('li').querySelector('.nested');
                if (nestedUl) {
                    nestedUl.classList.toggle("active");
                }
        
                document.querySelectorAll('.active-field').forEach(el => {
                    el.classList.remove('active-field');
                });
                this.classList.add('active-field');
        
                this.classList.toggle("caret-down");
            });
        });
    }
    
    function setupSearchFunctionality() {
        const searchInput = document.getElementById('TreeSearch');
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase().trim();
            filterFormListItems(searchText);
        });
    }
    
    function filterFormListItems(searchText) {
        const formListItems = document.querySelectorAll('.formListItem');
    
        if (searchText.length < 3) {
            // Show all items if the search text length is less than 3
            formListItems.forEach(item => {
                item.style.display = '';
                item.querySelectorAll('.field-list-item').forEach(subItem => subItem.style.display = '');
            });
        } else {
            formListItems.forEach(item => {
                let hasMatch = false;
                const placeItems = item.querySelectorAll('.field-list-item');
    
                // Check each place within the cluster for a match
                placeItems.forEach(subItem => {
                    const placeName = subItem.querySelector('.fieldLiSpan').textContent.toLowerCase();
                    if (placeName.includes(searchText)) {
                        subItem.style.display = '';
                        hasMatch = true; // Mark as match if any place matches
                    } else {
                        subItem.style.display = 'none';
                    }
                });
    
                // Check the cluster name itself
                const clusterName = item.querySelector('.formSpan').textContent.toLowerCase();
                if (clusterName.includes(searchText) || hasMatch) {
                    item.style.display = ''; // Show the cluster if it or any place matches
                } else {
                    item.style.display = 'none'; // Hide the cluster if no places match
                }
            });
        }
    }
let AllClusterRue ;
// Fonction de réattachement des écouteurs de clic pour les Rues
// async function setupFieldListeners(isMigrable = false, isArretCuivre = false, isAnciennete = false) {
//     AllClusterRue ='';
//     document.querySelectorAll('.fieldLiSpan').forEach(element => {
//         element.addEventListener('click', function () {
//             const clusterCode = localStorage.getItem('clusterCode');
//             const inseeCode = this.getAttribute('data-insee-code')||localStorage.getItem('CodeInsee');
//             console.log('test', inseeCode, clusterCode);
//             if (!clusterCode || !inseeCode) {
//                 console.warn('Cluster Code or INSEE Code is null. Skipping fetch.');
//                 return;
//             }

//             let url;

//             if (isAnciennete) {
//                 // URL pour l'ancienneté
            
//                 url = `/GeoMapp/anciennete-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
//             } else if (isArretCuivre) {
//                 // URL pour l'arrêt cuivre
//                 url = `/GeoMapp/arretcuivre-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
//             } else if (isMigrable) {
//                 // URL pour migrable
//                 url = `/GeoMapp/migrable-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
//             }  
//             else {
//                 // URL par défaut
//                 url = `/GeoMapp/street-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
//             }

//             fetch(url)
//                 .then(response => {
//                     if (!response.ok) {
//                         throw new Error(`HTTP error! status: ${response.status}`);
//                     }
//                     return response.json();
//                 })
//                 .then(data => {
//                     const treeRoot = document.getElementById('tree-root-roles');
//                     treeRoot.innerHTML = '';
//                     const htmlContent = isAnciennete ? generateHierarchyHtmlsanciennetedata(data) : (isArretCuivre ? generateHierarchyHtmlscuivre(data) : generateHierarchyHtmls(data));
//                     treeRoot.innerHTML = htmlContent;
//                     setupCaretListeners('tree-root-roles');
//                     setupSearchFunctionalityRues();
//                     return data;
//                 })
//                 .catch(error => {
//                     console.error('Error fetching data:', error);
//                 });
//         });
//     });
// }
async function setupFieldListeners(isMigrable = false, isArretCuivre = false, isAnciennete = false, isVendu = false) {
    AllClusterRue = '';

    document.querySelectorAll('.fieldLiSpan').forEach(element => {
      const newElement = element.cloneNode(true);
      element.replaceWith(newElement);

      newElement.addEventListener('click', async function () {
        const clusterCode = localStorage.getItem('clusterCode');
        const inseeCode = this.getAttribute('data-insee-code') || localStorage.getItem('CodeInsee');

        console.log('📌 Clicked:', { clusterCode, inseeCode });

        if (!clusterCode || !inseeCode) {
          console.warn('⚠️ Cluster Code or INSEE Code is missing.');
          return;
        }

        let url = '';
        let options = {};

        if (isAnciennete) {
          url = `/GeoMapp/anciennete-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
        } else if (isArretCuivre) {
          url = `/GeoMapp/arretcuivre-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
        } else if (isMigrable) {
          url = `/GeoMapp/migrable-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
        } else if (isVendu) {
          url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-eligible/${cpv}?codeCluster=${clusterCode}&codeInsee=${inseeCode}&page=1`;
          options = {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${jwtToken}`,
              'Content-Type': 'application/json'
            }
          };
        } else {
          url = `/GeoMapp/street-data?clusterCode=${clusterCode}&inseeCode=${inseeCode}`;
        }

        try {
          const response = await fetch(url, options);
          if (!response.ok) throw new Error(`HTTP ${response.status}`);

          const data = await response.json();

          const treeRoot = document.getElementById('tree-root-roles');
          treeRoot.innerHTML = ''; // Clear previous content

          console.log('📦 Data received:', data);

          const htmlContent = isAnciennete
            ? generateHierarchyHtmlsanciennetedata(data)
            : isArretCuivre
              ? generateHierarchyHtmlscuivre(data)
              : generateHierarchyHtmls(data);

          treeRoot.innerHTML = htmlContent;
          setupCaretListeners('tree-root-roles');
          setupSearchFunctionalityRues();
        } catch (err) {
          console.error('❌ Error fetching data:', err);
        }
      });
    });
  }

// Stocker la couleur de chaque rue
const colorMaps = new Map();

function getColorForStreets(streetName) {
    if (!colorMaps.has(streetName)) {
        const color = colorMaps.size % 2 === 0 ? '#A074C4' : '#55C5D0';
        colorMaps.set(streetName, color);
    }
    return colorMaps.get(streetName);
}



function generateHierarchyHtmls(data) {
   console.log('Generatingdata',data);
    let htmlContent = '';
    const groupedData = {};

    // Group streets by their code_iris
    data.forEach(street => {
        const codeIris = street.code_iris;
        if (!groupedData[codeIris]) {
            groupedData[codeIris] = [];
        }
        groupedData[codeIris].push(street);
    });

    // Loop through each group of streets and generate HTML
    for (const codeIris in groupedData) {
        const streetsInIris = groupedData[codeIris];
        let irisSum = 0;
        const streetsData = [];

        // HTML content for streets within each code_iris
        let streetsHtml = '';
        streetsInIris.forEach(street => {
            const total =
                (document.getElementById('nb_fyr_adsl').checked ? street.nb_fyr_adsl : 0) +
                (document.getElementById('nb_fyr_thd').checked ? street.nb_fyr_thd : 0) +
                (document.getElementById('nb_fyr_mob_mono').checked ? street.nb_fyr_mob_mono : 0) +
                (document.getElementById('nb_fyr_mob_multi_thd').checked ? street.nb_fyr_mob_multi_thd : 0) +
                (document.getElementById('nb_fyr_mob_multi_adsl').checked ? street.nb_fyr_mob_multi_adsl : 0) +
                (document.getElementById('nb_fyr_fttb').checked ? street.nb_fyr_fttb : 0) ||
                street.total_prises || 0;

            irisSum += total;
            streetsData.push({ nomVoie: street.nom_voie, total: total });

            if (total > 0) {
                streetsHtml += `
                    <li class="formListItem" style=" border-right: 2px solid ${getColorForStreets(codeIris)}; ">
                        <div class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                            <div onclick="saveToLocalStorages('${street.nom_voie || 'N/A'}', '${street.complement || 'N/A'}', '${getColorForStreets(codeIris)}', '${total}', this, '${codeIris}',' ${total}');" style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;">
                                <div class="formSpanvoie" id="formSpanvoiedata" style="display: flex; align-items: center; font-size: 13px;" >                                    <div style="margin-left: 15px;">
                                        <input class="checkbox-voie" data-nomvoie="${street.nom_voie}" onclick="handleClick('${total}', this)" type="checkbox">
                                    </div>
                                    <div style="height: 12px; width: 12px; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                                    ${truncateText(street.nom_voie || 'N/A', 25)}
                                </div>
                                <div id="totalePrises" class="total-street" 
                            
                                    style="display: flex; color: ${getColorForStreets(codeIris)}; align-items: center; margin-right: 15px; font-size: 14px;"
                                    data-nb-fyr-adsl="${street.nb_fyr_adsl}"
                                    data-nb-fyr-thd="${street.nb_fyr_thd}"
                                    data-nb-fyr-mob-mono="${street.nb_fyr_mob_mono}"
                                    data-nb-fyr-mob-multi-thd="${street.nb_fyr_mob_multi_thd}"
                                    data-nb-fyr-mob-multi-adsl="${street.nb_fyr_mob_multi_adsl}"
                                    data-nb-fyr-fttb="${street.nb_fyr_fttb}"
                                    data-total-prises="${street.total_prises}">
                                    ${total}
                                </div>
                            </div>
                        </div>
                    </li>
                `;
            }
        });
        // onclick="findTotalPriceRues('${total}', ' ${truncateText(street.nom_voie || 'N/A', 25)}')" 
        htmlContent += `
        <div id="${codeIris}" 
            class="code-iris-container" 
            data-sum="${irisSum}" 
            data-streets='${JSON.stringify(streetsData)}'
            style="background-color: var(--nested-bg, #f9f9f9); transition: background-color 0.3s ease;"
            onclick="toggleAllStreets('${codeIris}', this)">
            ${streetsHtml}
        </div>
    `;
    
    }

    return htmlContent;
}
async function fetchdataNumero(num) {
    try {
        const clusterCode = localStorage.getItem("clusterCode");
        const codeInsee = localStorage.getItem("CodeInsee");



        let url = `https://api.nomadcloud.fr/api/interventions-places/streets/${cpv}/${codeInsee}?nomVoie=${num}&&page=1`;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
}

async function handleBuildingsMatching(map, nomvoie, lon, lat) {
    if (typeof turf === 'undefined') {
      console.error("❌ Turf.js is not loaded.");
      return;
    }
  
    const dataNumero = await fetchdataNumero(nomvoie);
    console.log("📦 dataNumero:", dataNumero);
  
    if (!Array.isArray(dataNumero) || !dataNumero[0]?.data) {
      console.warn("⚠️ dataNumero structure is unexpected:", dataNumero);
      return;
    }
  
    // ✅ Normalize & flip coordinates
    const allBuildings = dataNumero.flatMap(entry =>
      (entry.data || []).map(building => {
        const numVoie = building.NUMR_VOIE?.toString() || '';
        const ssNum = building.SS_NUMR_VOIE?.toString() || '';
        building.numeroComplet = [numVoie, ssNum].filter(Boolean).join(',');
  
        // 🧠 FLIPPED coordinates fix
        const lat = Number(building.Longitude); // should be latitude
        const lon = Number(building.Latitude);  // should be longitude
  
        building.normLat = lat;
        building.normLon = lon;
  
        return building;
      })
    );
  
    // ✅ Create Turf points
    const turfPoints = turf.featureCollection(
      allBuildings.map(b =>
        turf.point(
          [parseFloat(b.normLon), parseFloat(b.normLat)],
          { numeroComplet: b.numeroComplet, TECH: b.TECH }
        )
      )
    );
  
    // console.table(allBuildings.slice(0, 5), ['Latitude', 'Longitude', 'normLat', 'normLon']);
  
    await map.once('idle');
  
    const point = map.project([lon, lat]);
    const bbox = [[point.x - 50, point.y - 50], [point.x + 50, point.y + 50]];
  
    const buildings = map.queryRenderedFeatures(bbox, {
      layers: ['3d-buildings'],
      filter: ['==', '$type', 'Polygon']
    });
  
    console.log("🏢 Buildings from queryRenderedFeatures:", buildings);
  
    if (buildings.length === 0) {
      alert("Aucun bâtiment visible dans la zone ciblée.");
      return;
    }
  
    // 🧠 Match by nearest point (spatial proximity)
    const matchedFeatures = buildings.filter(feature => {
      try {
        const centroid = turf.centerOfMass(feature.geometry);
        const nearest = turf.nearestPoint(centroid, turfPoints);
        const distance = turf.distance(centroid, nearest, { units: 'meters' });
  
        // console.log(`📏 Distance to nearest: ${distance.toFixed(2)}m`);
        return distance <= 50; // Allow 50m matching threshold
      } catch (error) {
        console.warn("❌ Erreur matching par distance:", error);
        return false;
      }
    });
  
    console.log("✅ Matched buildings (by distance):", matchedFeatures.length);
  
    if (matchedFeatures.length === 0) {
      alert("Bâtiments trouvés mais aucun appariement spatial.");
      return;
    }
  
    // ✅ Highlight matched buildings using setFeatureState (correct sourceLayer)
    const featureIds = matchedFeatures
      .map(f => f.id)
      .filter(id => id !== undefined);
  
    featureIds.forEach(id => {
      map.setFeatureState({
        source: 'composite',
        sourceLayer: 'building', // ✅ REQUIRED for vector source
        id: id
      }, { highlight: true });
    });
  
    // 🎨 Paint highlighting logic
    map.setPaintProperty('3d-buildings', 'fill-extrusion-color', [
      'case',
      ['boolean', ['feature-state', 'highlight'], false],
      '#ff0000',
      '#888'
    ]);
  
    // 💬 Popups for matched features
    // matchedFeatures.forEach(f => {
    //   try {
    //     const centroid = turf.centerOfMass(f.geometry).geometry.coordinates;
    //     const nearest = turf.nearestPoint(turf.point(centroid), turfPoints);
    //     const props = nearest.properties;
  
    //     new mapboxgl.Popup({ offset: 25 })
    //       .setLngLat(centroid)
    //       .setHTML(`
    //         <div class="popup-building">
    //           <h4>${props.numeroComplet}</h4>
    //           <p>Techno: ${props.TECH}</p>
    //         </div>
    //       `)
    //       .addTo(map);
    //   } catch (e) {
    //     console.warn("Erreur popup:", e);
    //   }
    // });
  }
  
function toggleAllStreets(codeIris, container) {
    const checkboxes = container.querySelectorAll('.checkbox-voie');
    const streetsData = container.getAttribute('data-streets');
    const isChecked = !Array.from(checkboxes).every(cb => cb.checked);
    localStorage.setItem('isChecked', isChecked);
    checkboxes.forEach(cb => cb.checked = isChecked);
    localStorage.setItem('codeIris', codeIris);
    if (isChecked && streetsData) {
        localStorage.setItem("RueHiarchyvoie", streetsData);
        createRueHiarchy(); 
    } else {
        localStorage.setItem("RueHiarchyvoie", []);
        createRueHiarchy(); 
    }

    // You should probably only refresh UI or state instead of re-invoking createRueHiarchy
    // 
}

function updateTotales() {
    // Mise à jour des totaux des lieux
    document.querySelectorAll('.total-place').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const fttb = parseInt(element.dataset.nbFyrFttb) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;
        if (document.getElementById('nb_fyr_fttb').checked) sum += fttb;

        element.textContent = sum || totalPrises || 0;
    });

    // Mise à jour des totaux des rues
    document.querySelectorAll('.total-street').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const fttb = parseInt(element.dataset.nbFyrFttb) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;
        if (document.getElementById('nb_fyr_fttb').checked) sum += fttb;

        element.textContent = sum || totalPrises || 0;
    });
}

function generateHierarchyHtmlsanciennetedata(data) {
    let htmlContent = '';
    const groupedData = {};

    // Assuming data.voies holds the actual street data
    const streets = data.voies || [];  // Safely access 'voies' array

    // Trier et regrouper les rues par code_iris
    streets.forEach(street => {
        const codeIris = street.code_iris;
        if (!groupedData[codeIris]) {
            groupedData[codeIris] = [];
        }
        groupedData[codeIris].push(street);
    });

    // Générer l'affichage HTML pour chaque code_iris
    for (const codeIris in groupedData) {
        const backgroundColor = getColorForStreets(codeIris);

        htmlContent += `<div id="${codeIris}"  style="transition: border-left 0.3s ease;">
            <style>
                #${codeIris}:hover {
                    border-left: 2px solid ${backgroundColor};
                }
            </style>
          `; // Add <ul> tag to ensure list items are inside an unordered list

        groupedData[codeIris].forEach(street => {
            const total = street.total_prises; // Access total_prises correctly

            htmlContent += `
                <li class="formListItem" style="background-color: var(--nested-bg, #f9f9f9); border-right: 2px solid ${backgroundColor};">
                    <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span class="formSpanvoie" id="formSpanvoiedata" style="display: flex; align-items: center; font-size: 13px;" 
                            onclick="saveToLocalStorages('${street.nom_voie || 'N/A'}', '${street.complement || 'N/A'}', '${backgroundColor}', '${total}', this,'${codeIris}')">
                            <div style="margin-left: 15px;">
                                <input class="checkbox-voie" data-nomvoie="${street.nom_voie}" onclick="handleClick('${total}', this)" type="checkbox">
                            </div>
                            <div style="height: 12px; width: 12px; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                            ${truncateText(street.nom_voie || 'N/A', 25)}
                        </span>
                        <div id="totalePrises" class="total-street" style="display: flex; color:${backgroundColor}; align-items: center; margin-right: 15px; font-size: 14px;">
                            ${total}
                        </div>
                    </span>
                </li>`;
        });

        htmlContent += `</ul></div>`; // Correct closing of <ul> and div
    }

    return htmlContent;
}

function truncateText(text, maxLength) {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
}


let totalGeneral = 0;
let totalDistanceSum = 0;
let somme = 0;
let decouche = false;

function handleClick(totalprises, checkbox) {
    // Vérifie si la checkbox est cochée
    const isChecked = checkbox.checked;
    const totalDistance = parseFloat(localStorage.getItem("totalDistance")) || 0; // Convertir en nombre (si non défini, valeur par défaut 0)

    // Ajoute ou soustrait la valeur de la checkbox au total général
    if (isChecked) {
        totalGeneral += parseInt(totalprises); // Ajoute si la checkbox est cochée
        totalDistanceSum += totalDistance;
        somme++;
        decouche = true;
    } else {
        totalGeneral -= parseInt(totalprises); // Soustrait si la checkbox est décochée
        totalDistanceSum -= totalDistance;
        somme--;
        decouche = false;
    }

    // Sauvegarde les valeurs dans le localStorage
    localStorage.setItem('isCheckedsomme', somme);
    localStorage.setItem('totalGeneral', totalGeneral);
    localStorage.setItem('totalDistanceSum', totalDistanceSum.toFixed(2));

    // Sauvegarde isChecked et decouche dans localStorage
    localStorage.setItem('isChecked', isChecked); 
    localStorage.setItem('decouche', decouche);
}

let savedRues = [];
function saveToLocalStorages(nomVoie, complement, backgroundColor, totalprises, spanElement,codeIris, total) { 
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;

    // Sauvegarde des informations dans localStorage
    localStorage.setItem('codeIris', codeIris);
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises);
    localStorage.setItem('isChecked', isChecked);
    // Récupération des voies sauvegardées
    let savedVoies = JSON.parse(localStorage.getItem('savedVoies')) || [];
    let savedVoiestotal = JSON.parse(localStorage.getItem('savedVoiestotal')) || [];
    let existingRue = savedRues.find(rue => rue.libelle === nomVoie);
    if (!existingRue) {
        // Ajouter la nouvelle rue
        savedRues.push({ libelle: nomVoie, total: Number(totalprises) });
        localStorage.setItem("savedRueslibelle", JSON.stringify(savedRues));
    }
    // Vérifier si la voie est déjà enregistrée pour éviter les doublons
    if (!savedVoies.includes(nomVoie)) {
        savedVoies.push(nomVoie);
        localStorage.setItem('savedVoies', JSON.stringify(savedVoies));
    }
     // Stocker correctement les voies avec leurs totaux
    if (!savedVoiestotal.some(item => item.nomVoie === nomVoie)) {
        savedVoiestotal.push({ nomVoie, total: Number(total) });
        localStorage.setItem('savedVoiestotal', JSON.stringify(savedVoiestotal));
    }
    if ( isChecked){
        createRueHiarchy();
    }
    // 
}

document.querySelectorAll('#fileUploadForm input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', updateTotales);
});

const colorMap = new Map();

function getColorForCodeIris(code_iris) {
    if (!colorMap.has(code_iris)) {
        const color = colorMap.size % 2 === 0 ? '#094044' : '#094044';
        colorMap.set(code_iris, color);
    }
    return colorMap.get(code_iris);
}

function generateHierarchyHtmlscuivre(data) {
    let htmlContent = '';
    const years = Object.keys(data).sort((a, b) => b - a); // Trier les années en ordre décroissant

    years.forEach(year => {
        const yearData = data[year][1]; // Vérifiez que cette structure est correcte
        if (!yearData || !yearData.voies) return; // Vérification de l'existence des données

        yearData.voies.forEach(voie => {
            // Obtenir une couleur unique pour chaque code_iris
            const backgroundColor = getColorForCodeIris(voie.code_iris);

            // Construire le HTML pour chaque 'voie'
            htmlContent += `
                <li class="formListItem" style="background-color: var(--nested-bg, #f9f9f9); border-right: 2px solid ${backgroundColor};">
                    <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span class="formSpanvoie" data-nom-voie="${voie.nom_voie}" style="display: flex; align-items: center;"
                              onclick="saveToLocalStorages('${voie.nom_voie || 'N/A'}', '${voie.complement || 'N/A'}', '${backgroundColor}', '${voie.total_prises}', this,''); ">
                            <div style="margin-left: 15px;">
                                <input class="checkbox-voie" data-nomvoie="${voie.nom_voie}" onclick="handleClick('${voie.total_prises || 0}', this)" id="checkbox8" type="checkbox">
                            </div>
                            <div style="height: 12px; width: 12px; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                            ${voie.nom_voie || 'N/A'}
                        </span>
                        <div style="display: flex; color: ${backgroundColor}; align-items: center; margin-right: 15px; font-size: 14px;">
                            ${voie.total_prises || 0}
                        </div>
                    </span>
                </li>
            `;
        });
    });

    return htmlContent; // Retourne le HTML généré
}



function selectVoieAndSearchStreet(nomVoie,complement,backgroundColor,totalprises,spanElement) {
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;

    // Sauvegarde des informations dans localStorage
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises);
    localStorage.setItem('isChecked', isChecked);
    // Récupération des voies sauvegardées
    let savedVoies = JSON.parse(localStorage.getItem('savedVoies')) || [];

    // Vérifier si la voie est déjà enregistrée pour éviter les doublons
    if (!savedVoies.includes(nomVoie)) {
        savedVoies.push(nomVoie);
        localStorage.setItem('savedVoies', JSON.stringify(savedVoies));
    }
}
function toggleStyle(element, isChecked) {
    const span = element.querySelector('span');

    // Toggle background color and border
    if (span.style.backgroundColor === 'rgb(45, 140, 235)') {
        span.style.backgroundColor = 'var(--nav-link-active-bg)';
        span.style.border = '4px solid var(--bg-table-section)';

    } else {
        span.style.backgroundColor = '#2D8ceb';
        span.style.border = '4px solid var(--bg-table-section)';
  
    }
}


    
    function setupCaretListeners(rootElementId) {
        const rootElement = document.getElementById(rootElementId);
        const togglers = rootElement.querySelectorAll(".caret, .formSpan");
        
        togglers.forEach(caret => {
            caret.addEventListener("click", function(event) {
                event.stopPropagation();
        
                let nestedUl = this.closest('li').querySelector('.nested');
                if (nestedUl) {
                    nestedUl.classList.toggle("active");
                }
        
                document.querySelectorAll('.active-field').forEach(el => {
                    el.classList.remove('active-field');
                });
                this.classList.add('active-field');
        
                this.classList.toggle("caret-down");
            });
        });
    }
    
    function setupSearchFunctionalityRues() {
        const searchInput = document.getElementById('TreeSearchRues');
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase().trim();
            filterFormListItemsRues(searchText);
        });
    }
    
    function filterFormListItemsRues(searchText) {
        const formListItems = document.querySelectorAll('.formListItem');
    
        if (searchText.length < 3) {
            // Afficher tous les éléments si la recherche contient moins de 3 caractères
            formListItems.forEach(item => {
                item.style.display = '';
            });
        } else {
            formListItems.forEach(item => {
                const streetNameElement = item.querySelector('.formSpanvoie');
                if (streetNameElement) {
                    const streetName = streetNameElement.textContent.toLowerCase();
                    if (streetName.includes(searchText)) {
                        item.style.display = ''; // Afficher l'élément si le nom correspond
                    } else {
                        item.style.display = 'none'; // Cacher sinon
                    }
                }
            });
        }
    }
    
    document.addEventListener('DOMContentLoaded', function () {
        const container = document.querySelector('.interface .cards');
        if (!container) return;
    
        container.innerHTML = '';
    
        // Ajouter le style CSS pour la classe .active
        const style = document.createElement('style');
        style.innerHTML = `
            .active {
                padding: 0;
                display: block;
                padding-left:10px;
                
            }
         
        `;
        document.head.appendChild(style);
    
        // Création des en-têtes
        const headers = document.createElement('div');
        headers.className = 'headers';
        headers.innerHTML = `
            <!-- Optionally you can add some header content here -->
        `;
        container.appendChild(headers);
    
    
    
                // Ajouter un délai pour améliorer les performances
            let searchTimeout;
            const searchInput = document.getElementById('TreeSearchinterface');
            searchInput.addEventListener('input', function () {
                clearTimeout(searchTimeout); // Annuler le délai précédent
                searchTimeout = setTimeout(() => {
                    const searchText = this.value.toLowerCase().trim();
                    if(searchText){
                        filterSearcher(searchText);
                    }else{
                        resetTreeStructure();
                    }
                    
                }, 300); // Délai de 300 ms
            });
            function resetTreeStructure() {
                const nodes = document.querySelectorAll('.tree-node');
                
                nodes.forEach(node => {
                    node.style.display = 'block'; // Réafficher tous les nœuds
                });
            
                const nestedElements = document.querySelectorAll('.resetTreeStructure');
                nestedElements.forEach(nested => {
                    nested.style.display = 'none'; // Réinitialiser les sous-niveaux
                });
            
                const carets = document.querySelectorAll('.caret');
                carets.forEach(caret => {
                    caret.classList.remove('caret-down'); // Réinitialiser les flèches
                });
            }
            
            function filterSearcher(query) {
                const nodes = document.querySelectorAll('.tree-node');

                nodes.forEach(node => {
                    // Vérifier si ce nœud ou ses enfants correspondent à la recherche
                    const isMatch = searchNode(node, query);

                    if (isMatch) {
                        node.style.display = 'block'; // Afficher ce nœud
                        openParentNodes(node); // Ouvrir les parents
                        openAllChildrenNodes(node); // Ouvrir tous les enfants
                    } else {
                        node.style.display = 'none'; // Masquer ce nœud
                    }
                });
            }

            /**
             * 🔹 Recherche récursive pour vérifier si un nœud ou ses enfants correspondent à la recherche.
             */
            function searchNode(node, query) {
                // Vérifier le texte du nœud dans les différents niveaux
                const level0 = node.querySelector('.caret:not(.child-img)');
                const level1 = node.querySelector('.caret.child-img');
                const level2 = node.querySelector('.formSpanvoie');

                const level0Text = level0 ? level0.textContent.toLowerCase().trim() : '';
                const level1Text = level1 ? level1.textContent.toLowerCase().trim() : '';
                const level2Text = level2 ? level2.textContent.toLowerCase().trim() : '';

                // Vérifier si le nœud correspond à la recherche
                if (level0Text.includes(query) || level1Text.includes(query) || level2Text.includes(query)) {
                    return true;
                }

                // Rechercher récursivement dans les enfants
                const nestedNodes = node.querySelectorAll('.nested .tree-node');
                for (let nestedNode of nestedNodes) {
                    if (searchNode(nestedNode, query)) {
                        return true; // Retourner true dès qu'une correspondance est trouvée
                    }
                }

                return false; // Aucune correspondance trouvée
            }

            /**
             * 🔹 Ouvre tous les parents du nœud pour qu'ils soient visibles.
             */
            function openParentNodes(node) {
                let parent = node.closest('.nested');

                while (parent) {
                    const parentNode = parent.closest('.tree-node');
                    if (!parentNode) break;

                    parent.style.display = 'block'; // Afficher le parent

                    const caret = parentNode.querySelector('.caret');
                    if (caret) {
                        caret.classList.add('caret-down'); // Ouvrir le parent
                    }

                    parent = parentNode.closest('.nested'); // Remonter au parent suivant
                }
            }

            /**
             * 🔹 Ouvre tous les enfants du nœud (récursivement).
             */
            function openAllChildrenNodes(node) {
                const nested = node.querySelector('.nested');
                if (nested) {
                    nested.style.display = 'block'; // Afficher les enfants
                    const childrenNodes = nested.querySelectorAll('.tree-node');
                    childrenNodes.forEach(child => openAllChildrenNodes(child)); // Ouvrir les enfants récursivement
                }
            }
        
        // Rendering the effectifs
        if (typeof effectifs !== 'undefined' && Array.isArray(effectifs)) {
            const mainList = document.createElement('div');
            mainList.innerHTML = effectifs.map(effectif => generateTree(effectif)).join('');
            container.appendChild(mainList);
        } else {
            console.error("Erreur: `effectifs` n'est pas défini ou n'est pas un tableau.");
        }
    
        // Gestion des événements
        container.addEventListener('click', function (event) {
            const target = event.target.closest('.caret');
            if (target) {
                target.classList.toggle('caret-down');
                const nested = target.parentElement.querySelector('.nested');
                if (nested) {
                    nested.classList.toggle('active');
                    nested.style.display = (nested.style.display === 'none') ? 'block' : 'none';
    
                    // Mise à jour des enfants
                    const childImgs = nested.querySelectorAll('.child-img');
                    childImgs.forEach(img => {
                        img.classList.toggle('caret-down', target.classList.contains('caret-down'));
                    });
                }
            }
        });
    });
    const treeViewIcon = document.getElementById('TreeViewIcon');
    const fleche = '/discord/treeview/Fleche.svg';
    const flecheFields = treeViewIcon ? treeViewIcon.getAttribute('flecheFields') : '';

    function generateTree(user, level = 0) {
        if (!user) return '';

        let childrenContent = '';
        if (user.children && user.children.length > 0) {
            childrenContent = `
                <div class="nested resetTreeStructure" style="display: none;">
                    ${user.children.map(child => generateTree(child, level + 1)).join('')}
                </div>
            `;
        }

        const colors = ['#54C5d0', '#015D92', '#015D92'];
        const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];

        // Separate HTML structure for each level
        const baseStyle = {
            level0: `
                <div class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center;font-size: 14px;">
                    <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                        <div style="display: flex; align-items: center;">
                            <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                            </div>
                            <div style="display: flex; align-items: center;" >
                                <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M8.25 6.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM15.75 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM2.25 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM6.31 15.117A6.745 6.745 0 0 1 12 12a6.745 6.745 0 0 1 6.709 7.498.75.75 0 0 1-.372.568A12.696 12.696 0 0 1 12 21.75c-2.305 0-4.47-.612-6.337-1.684a.75.75 0 0 1-.372-.568 6.787 6.787 0 0 1 1.019-4.38Z" clip-rule="evenodd" /><path d="M5.082 14.254a8.287 8.287 0 0 0-1.308 5.135 9.687 9.687 0 0 1-1.764-.44l-.115-.04a.563.563 0 0 1-.373-.487l-.01-.121a3.75 3.75 0 0 1 3.57-4.047ZM20.226 19.389a8.287 8.287 0 0 0-1.308-5.135 3.75 3.75 0 0 1 3.57 4.047l-.01.121a.563.563 0 0 1-.373.486l-.115.04c-.567.2-1.156.349-1.764.441Z" /></svg>
                            </div>
                            ${user.nom}
                        </div>
                        <div style="display: flex; align-items: center;">
                            <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                            <i class="bi bi-plugin" style="color: var(--coloredTextPrimary); margin-left: 6px; border-radius: 2px;"></i>
                        </div>
                    </div>
                </div>
            `,
            level1: `
                <div class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center;font-size: 14px;">
                    <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                        <div style="display: flex; align-items: center;">
                            <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                            </div>
                            <div style="display: flex; align-items: center;" >
                                <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path d="M4.5 6.375a4.125 4.125 0 1 1 8.25 0 4.125 4.125 0 0 1-8.25 0ZM14.25 8.625a3.375 3.375 0 1 1 6.75 0 3.375 3.375 0 0 1-6.75 0ZM1.5 19.125a7.125 7.125 0 0 1 14.25 0v.003l-.001.119a.75.75 0 0 1-.363.63 13.067 13.067 0 0 1-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 0 1-.364-.63l-.001-.122ZM17.25 19.128l-.001.144a2.25 2.25 0 0 1-.233.96 10.088 10.088 0 0 0 5.06-1.01.75.75 0 0 0 .42-.643 4.875 4.875 0 0 0-6.957-4.611 8.586 8.586 0 0 1 1.71 5.157v.003Z" /></svg>
                            </div>
                            ${user.nom}
                        </div>
                        <div style="display: flex; align-items: center;">
                            <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                        </div>
                    </div>
                </div>
            `,
            level2: `
            <div class="formListItem">
                <span class="caret formSpan formSpanvoie" data-userId="${user.id}" 
                    onclick="handleSelectNomPrenom('${user.nom}', '${user.prenom}', ${level}, ${user.id}); toggleContent('${user.id}'); "
                        style="display: flex; justify-content: space-between; align-items: center; font-size: 14px;">
                    <div class="ViewCard" style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                        <div style="display: flex; align-items: center;">
                            <div style="display: flex; align-items: center;" >
                                <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd" /></svg>
                            </div>
                            ${user.nom}
                        </div>
                        <div style="display: flex; align-items: center;">
                            <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                        </div>
                    </div>
                </span>
            </div>
            `
        };

        // Return the appropriate structure based on the level
        let nodeContent = '';
        if (level === 0) {
            nodeContent = baseStyle.level0;
        } else if (level === 1) {
            nodeContent = baseStyle.level1;
        } else if (level === 2) {
            nodeContent = baseStyle.level2;
        }

        return `
            <div class="tree-node" style="color: var( --tree-view-color);">
                ${nodeContent}
                ${childrenContent}
            </div>
        `;
    }
  
    function renderEffectifes(effectifs, container) {
        if (typeof effectifs !== 'undefined' && Array.isArray(effectifs)) {
            const mainList = document.createElement('div');
            mainList.className = 'effectifs-container'; // Adding a class name
            mainList.innerHTML = effectifs.map(effectif => generateTree(effectif)).join('');
            container.appendChild(mainList);
           
        } else {
            console.error("Erreur: `effectifs` n'est pas défini ou n'est pas un tableau.");
        }
    }
    

    
    function generateTreefilter(user, level = 0) {
        if (!user || typeof user !== "object") {
            console.warn("Invalid user data:", user);
            return '';
        }

        const colors = ['#54C5D0', '#015D92', '#015D92', '#015D92'];
        const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0', '#b0b0b0'];

        const color = colors[level] || '#000';  
        const prenomColor = prenomColors[level] || '#666'; 

        const isLastLevel = !Array.isArray(user.children) || user.children.length === 0;
        const onClickAttribute = isLastLevel ? `onclick="toggleNesteds(${user.id})"` : '';

        const flecheImg = isLastLevel ? '' : `<img src="${level === 0 ? fleche : flecheFields}" style="margin-right:10px;" class="flecheimg">`;

        const fullName = `${user.nom} ${user.prenom}`;

        let totalPrises = 0;

        if (level !== 0) {
            // Accumuler la somme pour les villes
            if (Array.isArray(user.villes)) {
                totalPrises += user.villes.reduce((sum, ville) => sum + (Number(ville.total_prises) || 0), 0);
            } else if (user.villes && typeof user.villes.total_prises === 'number') {
                totalPrises += Number(user.villes.total_prises);
            }

            // Accumuler la somme pour les voies
            if (Array.isArray(user.voies)) {
                totalPrises += user.voies.reduce((sum, voie) => sum + (Number(voie.total_prises) || 0), 0);
            } else if (user.voies && typeof user.voies.total_prises === 'number') {
                totalPrises += Number(user.voies.total_prises);
            }
        }

       // console.log('generateTreefilter', user);
        let childrenHTML = '';
        if (Array.isArray(user.children) && user.children.length > 0) {
            childrenHTML = `
                <div class="nested" style="display: none;">
                    ${user.children.map(child => generateTreefilter(child, level + 1)).join('')}
                </div>
            `;
        }
    
        let levelHTML = '';
        switch (level) {
            case 0:
                levelHTML = `
                    <div class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center; font-size: 14px;">
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;">
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M8.25 6.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM15.75 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM2.25 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM6.31 15.117A6.745 6.745 0 0 1 12 12a6.745 6.745 0 0 1 6.709 7.498.75.75 0 0 1-.372.568A12.696 12.696 0 0 1 12 21.75c-2.305 0-4.47-.612-6.337-1.684a.75.75 0 0 1-.372-.568 6.787 6.787 0 0 1 1.019-4.38Z" clip-rule="evenodd" /><path d="M5.082 14.254a8.287 8.287 0 0 0-1.308 5.135 9.687 9.687 0 0 1-1.764-.44l-.115-.04a.563.563 0 0 1-.373-.487l-.01-.121a3.75 3.75 0 0 1 3.57-4.047ZM20.226 19.389a8.287 8.287 0 0 0-1.308-5.135 3.75 3.75 0 0 1 3.57 4.047l-.01.121a.563.563 0 0 1-.373.486l-.115.04c-.567.2-1.156.349-1.764.441Z" /></svg>
                                </div>
                                ${fullName}
                            </div>
                            <div style="display: flex; align-items: center; ">
                                <span style="color: ${prenomColor}">${totalPrises}</span>
                            </div>
                        </div>
                    </div>
                `;
                break;
            case 1:
                levelHTML = `
                    <div class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center; font-size: 14px;">
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;">
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path d="M4.5 6.375a4.125 4.125 0 1 1 8.25 0 4.125 4.125 0 0 1-8.25 0ZM14.25 8.625a3.375 3.375 0 1 1 6.75 0 3.375 3.375 0 0 1-6.75 0ZM1.5 19.125a7.125 7.125 0 0 1 14.25 0v.003l-.001.119a.75.75 0 0 1-.363.63 13.067 13.067 0 0 1-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 0 1-.364-.63l-.001-.122ZM17.25 19.128l-.001.144a2.25 2.25 0 0 1-.233.96 10.088 10.088 0 0 0 5.06-1.01.75.75 0 0 0 .42-.643 4.875 4.875 0 0 0-6.957-4.611 8.586 8.586 0 0 1 1.71 5.157v.003Z" /></svg>
                                </div>
                                ${fullName}
                            </div>
                            <div style="display: flex; align-items: center; ">
                                <span style="color: ${prenomColor}">${totalPrises}</span>
                            </div>
                        </div>
                    </div>
                `;
                break;
            case 2:
                case 2:
                    levelHTML = `
                    <div class="caret formSpan" style="display: flex; justify-content: space-between; align-items: center; font-size: 14px;">
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;">
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd" /></svg>
                                </div>
                                ${fullName}
                            </div>
                            <div style="display: flex; align-items: center; ">
                                <span style="color: ${prenomColor}">${totalPrises}</span>
                            </div>
                        </div>
                    </div>
                    `;
                    break;
                
            case 3:
                levelHTML = `
                    <div class="caret formListItem formSpan">
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div class="" data-userId="${user.id}" onclick=" toggleNesteds('${user.id}');" style="display: flex; justify-content: space-between; align-items: center; font-size: 14px; margin-left: 15px; width: 100%;">
                                <div style="display: flex; align-items: center;">
                                    <div style="display: flex; align-items: center;" >
                                        <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px;"xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M18.685 19.097A9.723 9.723 0 0 0 21.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 0 0 3.065 7.097A9.716 9.716 0 0 0 12 21.75a9.716 9.716 0 0 0 6.685-2.653Zm-12.54-1.285A7.486 7.486 0 0 1 12 15a7.486 7.486 0 0 1 5.855 2.812A8.224 8.224 0 0 1 12 20.25a8.224 8.224 0 0 1-5.855-2.438ZM15.75 9a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" clip-rule="evenodd" /></svg>

                                    </div>
                                    ${fullName}
                                </div>
                                <div style="display: flex; align-items: center; ">
                                    <span style="color: ${prenomColor}">${totalPrises}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                break;

            default:
                levelHTML = '';
                break;
        }
    
        return `
            <div class="tree-node" style="color:var( --tree-view-color);">
                ${levelHTML}
                ${childrenHTML}
            </div>
        `;
    }
    function OpenRightPanel(clusterCode,libelleCluster){
        localStorage.setItem("clusterCode", clusterCode);
        localStorage.setItem("libelleCluster", libelleCluster);
        //     var sidebar = document.querySelector('.right-sidebar-concepteur');
        //     var sidebarStyle = window.getComputedStyle(sidebar);
        
        //     if (sidebarStyle.right === '0px') {
        //         sidebar.style.right = '-317px';
        //     } else {
        //         sidebar.style.right = '0px';
        // }
    }

    async function findClusterDataForUser(codecluster,codeInsee) { 
       
        try {
            // Récupérer les données
            const data = await fetchtotalClusterUser(codecluster,codeInsee);
    
            // Sélection du conteneur dans lequel injecter l'arborescence
            const container = document.querySelector('.interface .cards');
            if (!container) {
                console.error("Container element not found");
                return;
            }
    
            // Vider le contenu précédent
            container.innerHTML = '';
    
            if (!Array.isArray(data) || data.length === 0) {
                console.warn("Aucune donnée trouvée, affichage des effectifs par défaut.");
    
                renderEffectifes(effectifs, container) ;
    
                return;
            }
    
           // console.log("Données reçues:", data);
            document.getElementById('MiseEnpageCard').style.display = 'block';
    
            // Générer le HTML pour chaque utilisateur
            const mainList = document.createElement('div');
            mainList.innerHTML = data.map(user => generateTreefilter(user)).join('');
         
            container.appendChild(mainList);
    
        } catch (error) {
            console.error('Erreur lors du rendu des effectifs:', error);
        }
    }
  
    async function fetchtotalClusterUser(codecluster, codeInsee) {
        try {
            // Construire l'URL avec codeInsee si fourni, sinon sans
            let url = `https://api.nomadcloud.fr/api/interventions-places-total-by-user/${cpv}/${codecluster}?page=1`;
            if (codeInsee) {
                url += `&codeInsee=${codeInsee}`;
            }
    
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
        }
    }
    async function fetchdataElligible(codecluster, codeInsee) {
        try {
            let url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-eligible/${cpv}?codeCluster=${codecluster}&page=1`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                console.error('HTTP error!', response.status, response.statusText);
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            const data = await response.json();
            let clusterData = data.find(item => item.clusterCode === codecluster);

            let totalPrises=clusterData.total_prises;
            if (codeInsee) {
                const villeData = clusterData.villes.find(item => item.cod_insee == codeInsee);
                totalPrises = villeData ? villeData.total_prises : 0;
            }
            document.getElementById('dejaClient').innerHTML =totalPrises ;
            return data;
        } catch (error) {
            console.error('Fetch error:', error);
        }
    }

    async function toggleNesteds(userId) {
        try {
            const data = await fetchtotalforUser(userId);
           // console.log(data);
            if (!data || data.length === 0) {
                document.getElementById('structureCard').style.display = 'none';
                return;
            }
            document.getElementById('structureCard').style.display = 'block';
            let mainList = document.querySelector('.main-list-back');
            if (!mainList) {
                mainList = document.createElement('div');
                mainList.classList.add('main-list-back'); // Add the class
                document.getElementById('structureCard').appendChild(mainList);
            }
            mainList.innerHTML = generateClusterNodes(data);
        } catch (error) {
            console.error('Error fetching data:', error);
            document.getElementById('structureCard').style.display = 'none';
        }
    }

    async function toggleContent(userId) {
        try {

            const data = await fetchtotalforUser(userId);
           // console.log(data);

            if (!data || data.length === 0) {
                // Hide structureCard if no data
                document.getElementById('structureCard').style.display = 'none';
                return; // Exit the function early
            }
            document.getElementById('structureCard').style.display = 'block';
            // Check if mainList already exists
            let mainList = document.querySelector('.main-list-back');

            // If it doesn't exist, create a new one
            if (!mainList) {
                mainList = document.createElement('div');
                mainList.classList.add('main-list-back'); // Add the class
                document.getElementById('structureCard').appendChild(mainList);
            }

            mainList.innerHTML = generateClusterNodes(data);

        } catch (error) {
            console.error('Error fetching data:', error);
            // Hide structureCard on error
            document.getElementById('structureCard').style.display = 'none';
        }
    }

// Click handler for expanding/collapsing nodes
document.addEventListener('DOMContentLoaded', function () {
    const container = document.querySelector('.interface .cardsSturct');
    if (!container) return;

    container.innerHTML = '';

    // Styles for different levels
    const style = document.createElement('style');
    style.innerHTML = `
        .active { /*display: block !important;*/ }
        .nested { display: none; }
      
    `;
    document.head.appendChild(style);
    // setupSearchFunctionalityforme();
    // Event listener for the search input
    const searchInput = document.getElementById('TreeSearchinterface2');
    searchInput.addEventListener('input', function () {
        filterSearcher(searchInput.value);
    });

    // Search filter function
    function filterSearcher(query) {
        const users = document.querySelectorAll('.tree-node');
        users.forEach(userNode => {
            const userName = userNode.querySelector('.caret').textContent.toLowerCase();
            if (userName.includes(query.toLowerCase())) {
                userNode.style.display = ''; // Show matching node
            } else {
                userNode.style.display = 'none'; // Hide non-matching node
            }
        });
    }

    // Click handler for expanding/collapsing nodes
    container.addEventListener('click', async function (event) {
        const target = event.target.closest('.caret');
        if (!target) return;

        // Toggle caret icon
        target.classList.toggle('caret-down');
        const nested = target.parentElement.querySelector('.nested');
        if (nested) nested.classList.toggle('active');

        // If it's a user node and it's not already loaded
        if (target.classList.contains('user-node') && !target.dataset.loaded) {
            const userId = target.dataset.userId;
            target.dataset.loaded = 'true';

            try {
                const data = await fetchtotalforUser(userId);
                const nestedContainer = target.parentElement.querySelector('.nested') || createNestedContainer(target.parentElement);
                nestedContainer.innerHTML = generateClusterNodes(data);
            } catch (error) {
                document.getElementById('structureCard').style.display = 'none';
                console.error('Error:', error);
            }
        }
    });
});

// Function to generate HTML for the clusters
function generateClusterNodes(clusters) {
    const level = 0;
    const fleche = '/discord/treeview/Fleche.svg';
    const colors = ['#54C5d0', '#015D92', '#015D92'];
    const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    
    return clusters.map(cluster => `
        <div class="tree-node">
            <div class="caret formSpan cluster-node user-node" style="display: flex; justify-content: space-between; align-items: center; width: 93%; font-size: 14px; ">
                <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                    <div style="display: flex; align-items: center;color: var( --tree-view-color);">
                        <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                            <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                        </div>
                        <div style="display: flex; align-items: center;" >
                            <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                        </div>
                        ${cluster.libelle_cluster}
                    </div>
                    <div style="display: flex; align-items: center;">
                        <span style="color: ${prenomColors[level]}">${cluster.total_prises}</span>
                        <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-left: 6px; border-radius: 2px;" version=1.1 viewBox="0 0 15.8 14.1"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d="M1.8,0h3c1.7,0,3.1,1.4,3.1,3.1S6.5,6.2,4.8,6.2H3.5c-0.9,0-1.7,0.8-1.7,1.7c0,1,0.8,1.8,1.7,1.8h1.8v1.8H3.5C1.6,11.4,0,9.8,0,7.9s1.6-3.5,3.5-3.5h1.3c0.7,0,1.3-0.6,1.3-1.3S5.5,1.8,4.8,1.8h-3c-0.5,0-0.9-0.4-0.9-0.9C0.9,0.4,1.3,0,1.8,0z M12.3,7v7H9.7c-1,0-1.8-0.8-1.8-1.8H6.1V8.8h1.8c0-1,0.8-1.8,1.8-1.8H12.3zM15.8,8.8c0-0.5-0.4-0.9-0.9-0.9h-1.8v1.8h1.8C15.4,9.7,15.8,9.2,15.8,8.8z M15.8,12.3c0-0.5-0.4-0.9-0.9-0.9h-1.8v1.7h1.8C15.4,13.2,15.8,12.8,15.8,12.3z"/></g></svg>
                    </div>
                </div>
            </div>

            <div class="nested">
                ${generateVilleNodes(Object.values(cluster.villes))}
            </div>
        </div>
    `).join('');
}

function generateVilleNodes(villes) {
    const level = 1;
    const fleche = '/discord/treeview/Fleche.svg';
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    const colors = ['#54C5d0', '#015D92', '#015D92'];
    const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    return villes.map(ville => `
        <div class="tree-node">
            <div  class="caret formSpan ville-node cluster-node user-node" style="display: flex; justify-content: space-between; align-items: center; width: 88%; font-size: 14px;">
                <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                    <div style="display: flex; align-items: center;color: var( --tree-view-color);">
                        <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                            <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color);opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                        </div>
                        <div style="display: flex; align-items: center;" >
                            <svg style="height: 12px; width: 12px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d=M12.7,1.1c0-0.6-0.5-1.1-1.1-1.1H1.1C0.5,0,0,0.5,0,1.1v4.7h12.7V1.1z /><path d=M12.7,11.6V6.9H0v4.7c0,0.6,0.5,1.1,1.1,1.1h10.4C12.2,12.7,12.7,12.2,12.7,11.6z /></g></svg>
                        </div>
                        ${ville.ville}
                    </div>
                    <div style="display: flex; align-items: center; ">
                        <span style="color: ${prenomColors[level]}">${ville.total_prises}</span>
                    </div>
                </div>
            </div>
            <div class="nested">
                ${generateVoieNodes(Object.values(ville.voies))}
            </div>
        </div>
    `).join('');
}

// Génération des voies
function generateVoieNodes(voies) {
    const level = 2;
    const fleche = '/discord/treeview/Fleche.svg';
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    const colors = ['#54C5d0', '#015D92', '#015D92'];
    const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    return voies.map(voie => `
        <div class="tree-node">
            <div class="caret formSpan  ville-node cluster-node user-node" style="display: flex; justify-content: space-between; align-items: center; width: 88%; font-size: 14px;">
                <div style="display: flex; align-items: center;color: var( --tree-view-color); padding-left: 23px;">
                    <div style="display: flex; align-items: center;" >
                        <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d=M5.9,0H1.1C0.5,0,0,0.5,0,1.1v4.7h5.9V0z /><path d=M12.7,5.9V1.1c0-0.6-0.5-1.1-1.1-1.1H6.9v5.9H12.7z /><path d=M6.9,12.7h4.7c0.6,0,1.1-0.5,1.1-1.1V6.9H6.9V12.7z /><path d=M0,6.9v4.7c0,0.6,0.5,1.1,1.1,1.1h4.7V6.9H0z /></g></svg>
                    </div>
                    ${ truncateTexts(voie.nom_voie,20)} 
                </div>
                <div style="display: flex; align-items: center; ">
                    <span style="color: ${prenomColors[level]}">${voie.total_prises}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// Helper pour créer un conteneur nested
function createNestedContainer(parent) {
    const nested = document.createElement('div');
    nested.className = 'nested';
    parent.appendChild(nested);
    return nested;
}

// Fonction fetch avec paramètre dynamique
async function fetchtotalforUser(userId) {
    try {
        // Construct the URL with the correct cpv and userId
        const url = `https://api.nomadcloud.fr/api/interventions-places-total-for-a-user/${cpv}/${userId}?page=1`;
      
    
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,  // Ensure jwtToken is defined
                'Content-Type': 'application/json'
            }
        });
    
        // Check if the response is OK
        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
    
        const data = await response.json();
     //   console.log('Response data:', data);  // Log the fetched data
        return data;
    } catch (error) {
        document.getElementById('structureCard').style.display = 'none';
        console.error('Fetch error:', error);
        throw error;
    }
}


function generateUserNode(user) {
    const level = 0;
    const fleche = '/discord/treeview/Fleche.svg';
    const colors = ['#54C5d0', '#015D92', '#015D92'];
    const prenomColors = ['#b0b0b0', '#326E78', '#b0b0b0'];
    return `
        <div class="tree-node">
            <span data-user-id="${user.id}" class="caret formSpan user-node caret-down" 
                    style="display: flex; justify-content: space-between; align-items: center; width: 88%; font-size: 12px;">
                <span style="display: flex; align-items: center;">
                            <div style="margin-left: 15px;">
                            <input class="checkbox-voie"  type="checkbox">
                        </div>
                    <div style="margin-right:5px;border-radius:2px;"></div>
                    ${truncateTexts(user.nom,20)}   
                </span>
                <div style="display: flex; align-items: center; ">
                    <span style="color: ${prenomColors[level]}">${user.prenom}</span>
                </div>
            </span>
            <div class="nested"></div>
        </div>
    `;
}
function truncateTexts(text, maxLength) {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
}
    // Handle selection only for sub-child (level 2)
function handleSelectNomPrenom(nom, prenom, level, id) {
    if (level === 2) {  // Only save for sous-enfant (sub-child)
        localStorage.setItem('selectednom', nom);
        localStorage.setItem('selectedprenom', prenom);
        localStorage.setItem('selecteduserid', id);
        createRueHiarchy();
    }
}
document.addEventListener("DOMContentLoaded", () => {
    localStorage.removeItem('clusterInseeData');
    localStorage.removeItem('savedVoies');
    localStorage.removeItem('selectedUserId');
    localStorage.removeItem('selectednom');
    localStorage.removeItem('selectedprenom');
    localStorage.removeItem('totalDistanceSum'); 
    localStorage.removeItem('totalDistance');
    localStorage.removeItem('sommeprisesclutser');
    localStorage.removeItem('totalGeneral');
    localStorage.removeItem('nomberprisesclutser');
    localStorage.removeItem('selectedComplement');
    localStorage.removeItem('selectedTotalprises');
    localStorage.removeItem('selectedVoie');
    localStorage.removeItem('savedClusterslibelle');
    localStorage.removeItem('nomberprisesVille');
    localStorage.removeItem('savedVilleslibelle');
    localStorage.removeItem('savedRueslibelle');
    localStorage.removeItem('isCheckedsomme');
    localStorage.removeItem('codeIris');
    localStorage.removeItem('isChecked');
    localStorage.removeItem('isMigrable');
    localStorage.removeItem('clickedType');
    localStorage.removeItem('savedVoiestotal');
});
document.addEventListener('DOMContentLoaded', function () {

    document.getElementById('MiseEnpageCard').style.display = "block";
    document.getElementById('structureCard').style.display = "none";

});
document.addEventListener("DOMContentLoaded", () => {
    const dropdownIcon = document.querySelector(".icon-dropdown");
    const dropdownContent = document.querySelector(".content-dropdown");

    // Show/Hide the menu and change the icon on click
    dropdownIcon.addEventListener("click", () => {
        const isDisplayed = dropdownContent.style.display === "block";
        dropdownContent.style.display = isDisplayed ? "none" : "block";

        // Toggle icon between "chevron-down" and "x-lg"
        if (isDisplayed) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });

    // Handle custom navigation on link click
    const dropdownLinks = document.querySelectorAll(".content-dropdown a");
    dropdownLinks.forEach(link => {
        link.addEventListener("click", (event) => {
            event.preventDefault();  // Prevent default anchor behavior
            const path = link.getAttribute("data-path");
            window.location.href = path;  // Manually navigate to the path
        });
    });
});

async function findInseeMeteo(codeInsee) {
    //  console.log("codeInsee",codeInsee);
    await getWeather(codeInsee);
}


async function getWeather(inseeCode) {
    if (!inseeCode) {
        alert("Veuillez entrer un code INSEE.");
        return;
    }

    try {
        // Étape 1 : Obtenir les coordonnées de la commune via API Geo
        const geoResponse = await fetch(`https://geo.api.gouv.fr/communes/${inseeCode}?fields=nom,centre`);
        if (!geoResponse.ok) throw new Error("Commune non trouvée.");
        const geoData = await geoResponse.json();

        const { nom, centre } = geoData;
        const latitude = centre.coordinates[1];
        const longitude = centre.coordinates[0];

        // Étape 2 : Obtenir la météo via Open-Meteo
        const meteoResponse = await fetch(`https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current_weather=true`);
        if (!meteoResponse.ok) throw new Error("Météo non disponible.");
        const meteoData = await meteoResponse.json();

        const temperature = meteoData.current_weather.temperature;
        const weatherCode = meteoData.current_weather.weathercode;

        // Choisir l'icône en fonction du code météo
        const icons = {
            0: "<img src='/image/Weather/0.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Soleil
            1: "<img src='/image/Weather/1.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Peu nuageux
            2: "<img src='/image/Weather/2.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",   // Partiellement nuageux
            3: "<img src='/image/Weather/3.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Couvert
            45: "<img src='/image/Weather/45.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Brouillard
            51: "<img src='/image/Weather/51.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Pluie légère
            61: "<img src='/image/Weather/61.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Pluie modérée
            63: "<img src='/image/Weather/63.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Pluie forte
            71: "<img src='/image/Weather/71.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Neige légère
            73: "<img src='/image/Weather/73.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Neige modérée
            75: "<img src='/image/Weather/75.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Neige forte
            80: "<img src='/image/Weather/80.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Averses légères
            81: "<img src='/image/Weather/81.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Averses modérées
            82: "<img src='/image/Weather/82.svg' alt='Peu nuageux' style='width:40px' class='weather-icon'>",  // Averses fortes
        };
        const weatherIcon = icons[weatherCode] || "❓";

            // Fonction pour tronquer le texte si nécessaire
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
        }

        const maxTitleLength = 15; // Ajuste la longueur maximale selon tes besoins
        const truncatedNom = truncateText(nom, maxTitleLength);

        // Affichage des résultats
        document.getElementById('weather').innerHTML = `
            <span style='font-size: 14px'>${truncatedNom}</span>
            <span style='display: flex;justify-content: space-between; font-size: 20px;align-items: center;' >${weatherIcon} <span>${temperature}°C</span></span>
        `;
    } catch (error) {
        alert("Erreur : " + error.message);
    }
}

// async function fetchInterventionsMigrable(clusterCode) {
//   //  console.log('Fetching data for cluster code:', clusterCode);
//     const url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/${cpv}?codeCluster=${clusterCode}&page=1`;
//     try {
//         const response = await fetch(url, {
//             method: 'GET',
//             headers: {
//                 'Authorization': `Bearer ${jwtToken}`, // Ensure jwtToken is defined and valid
//                 'Content-Type': 'application/json'
//             }
//         });
//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }
//         Interventionsmigrable = await response.json();
//     } catch (error) {
//         console.error('Error fetching interventions migrable:', error);
//         return null;
//     }
// }

async function displayInterventionsMigrable(Code, isVille) {
    let dataToDisplay;
    localStorage.setItem('ClusterCode', Code);
    if (!isVille) {
        await fetchdataMigrable(Code);
        dataToDisplay = Interventionsmigrable;
    } else {
        if (!Interventionsmigrable || !Interventionsmigrable.villes) {
            console.error("Missing 'villes' data or 'Interventionsmigrable' is not loaded");
            return;
        }
        const codeNumeric = parseInt(Code, 10);
        dataToDisplay = Interventionsmigrable.villes.find(v => v.cod_insee === codeNumeric);
        if (!dataToDisplay) {
            console.error("No ville found with the INSEE code:", Code);
            return;
        }
    }
    updateDisplayValues(dataToDisplay);
}
document.addEventListener("DOMContentLoaded", async function () {
    const card = document.getElementById("info-card");

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    // Assurez-vous que la carte est positionnée en `absolute` pour pouvoir la déplacer
    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifier si le bouton gauche de la souris est utilisé

        isDragging = true;

        // Calculer l'offset entre la position de la souris et le coin supérieur gauche de la carte
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;

        card.style.transition = "none"; // Désactiver la transition pendant le déplacement
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;

        // Déplacer la carte en mettant à jour `left` et `top`
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Réactiver la transition
    });
});
document.addEventListener("DOMContentLoaded", function () {
    const card = document.getElementById("info-card-iris");

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    // Assurez-vous que la carte est positionnée en `absolute` pour pouvoir la déplacer
    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifier si le bouton gauche de la souris est utilisé

        isDragging = true;

        // Calculer l'offset entre la position de la souris et le coin supérieur gauche de la carte
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;

        card.style.transition = "none"; // Désactiver la transition pendant le déplacement
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;

        // Déplacer la carte en mettant à jour `left` et `top`
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Réactiver la transition
    });
});
document.addEventListener("DOMContentLoaded", function () {
    const card = document.getElementById("info-card-Cluster");

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    // Assurez-vous que la carte est positionnée en `absolute` pour pouvoir la déplacer
    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifier si le bouton gauche de la souris est utilisé

        isDragging = true;

        // Calculer l'offset entre la position de la souris et le coin supérieur gauche de la carte
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;

        card.style.transition = "none"; // Désactiver la transition pendant le déplacement
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;

        // Déplacer la carte en mettant à jour `left` et `top`
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Réactiver la transition
    });
});
document.addEventListener("DOMContentLoaded", function () {
    const card = document.getElementById("info-card-Rues");

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    // Assurez-vous que la carte est positionnée en `absolute` pour pouvoir la déplacer
    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifier si le bouton gauche de la souris est utilisé

        isDragging = true;

        // Calculer l'offset entre la position de la souris et le coin supérieur gauche de la carte
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;

        card.style.transition = "none"; // Désactiver la transition pendant le déplacement
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;

        // Déplacer la carte en mettant à jour `left` et `top`
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Réactiver la transition
    });
});
document.addEventListener("DOMContentLoaded", function () {
    const card = document.getElementById("info-card-Villes");

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    // Assurez-vous que la carte est positionnée en `absolute` pour pouvoir la déplacer
    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifier si le bouton gauche de la souris est utilisé

        isDragging = true;

        // Calculer l'offset entre la position de la souris et le coin supérieur gauche de la carte
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;

        card.style.transition = "none"; // Désactiver la transition pendant le déplacement
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;

        // Déplacer la carte en mettant à jour `left` et `top`
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Réactiver la transition
    });
});

document.addEventListener("DOMContentLoaded", function () {
    let openCard = null; // Stocke la carte actuellement ouverte

    function toggleInfoCard(toggleId, cardId) {
        const toggleElement = document.getElementById(toggleId);
        const infoCard = document.getElementById(cardId);

        if (toggleElement && infoCard) {
            toggleElement.addEventListener("click", function () {
                // Ferme la carte actuellement ouverte si elle est différente de celle cliquée
                if (openCard && openCard !== infoCard) {
                    openCard.style.display = "none";
                }

                // Ouvre la nouvelle carte si elle était fermée, sinon la ferme
                if (infoCard.style.display === "none" || infoCard.style.display === "") {
                    infoCard.style.display = "block";
                    openCard = infoCard; // Mettre à jour la carte ouverte
                } else {
                    infoCard.style.display = "none";
                    openCard = null; // Aucune carte ouverte
                }
            });
        }
    }

    // Initialiser les cartes
    toggleInfoCard("toggleInfoCardIris", "info-card-iris");
    toggleInfoCard("toggleInfoRues", "info-card-Rues");
    toggleInfoCard("toggleInfoVille", "info-card-Villes");
    toggleInfoCard("toggleInfoCluster", "info-card-Cluster");
});

document.addEventListener("DOMContentLoaded", function () {
    // Sélectionne tous les éléments qui ont l'ID 'triggerAddGroupe'
    document.querySelectorAll("#triggerAddGroupe").forEach(function (element) {
        element.addEventListener("click", function () {
            // Récupère le texte du <span> à l'intérieur du <a> cliqué
            let selectedText = this.querySelector("span").textContent;

            // Cible la div TypeOptionSelector
            let typeOption = document.querySelector(".TypeOptionSelectortest");

            if (typeOption) {
                // Change le texte du <span> à l'intérieur de .TypeOptionSelector
                typeOption.querySelector("span").textContent = selectedText;

                // Affiche la div en changeant son style
                typeOption.style.display = "block";
            }
        });
    });
});

document.addEventListener("DOMContentLoaded", function () {
    // Sélectionne tous les éléments qui ont l'ID 'triggerAddGroupe'
    document.querySelectorAll("#OptionSelector").forEach(function (element) {
        element.addEventListener("click", function () {
            // Récupère le texte du <span> à l'intérieur du <a> cliqué
            let selectedText = this.querySelector("span").textContent;

            // Cible la div TypeOptionSelector
            let typeOption = document.querySelector(".TypeOptionSelectorOption");

            if (typeOption) {
                // Change le texte du <span> à l'intérieur de .TypeOptionSelector
                typeOption.querySelector("span").textContent = selectedText;

                // Affiche la div en changeant son style
                typeOption.style.display = "block";
            }
        });
    });
});
document.addEventListener('click', function(event) {
    const irisContainer = event.target.closest('.code-iris-container');
    
    if (irisContainer) {
      
        const totalSum = irisContainer.dataset.sum;
        const streets = irisContainer.dataset.streets ? JSON.parse(irisContainer.dataset.streets) : [];



        // Vérifier si l'élément irisTotalprise existe avant d'essayer de le modifier
        const totalElement = document.getElementById('irisTotalprise');
        if (totalElement) {
            totalElement.textContent = totalSum || '0';
        } else {
            console.error("⚠️ Élément #irisTotalprise introuvable !");
        }

        // Vérifier si l'élément irisList existe avant d'ajouter les rues
        const irisList = document.getElementById('irisList');
        if (irisList) {
            irisList.innerHTML = streets.map(street => 
                `               	<div class="info-item">
								<span style="align-items: center;display: flex;" >${street.name}</span> 
								<span class="isCheckedsomme" id="totalGeneral">${street.total}</span>
							</div>`
            ).join('');
            irisList.style.display = 'block';
        } else {
            console.error("⚠️ Élément #irisList introuvable !");
        }
    }
});

// Fonction pour basculer l'affichage de la liste des rues
function toggleListiris() {
    const irisList = document.getElementById('irisList');
    if (irisList) {
        irisList.style.display = irisList.style.display === 'none' ? 'block' : 'none';
    }
}

 async function updateTypeOptionTout(selectedElement) {
    var selectors = document.querySelectorAll('.TypeOptionSelector');

    selectors.forEach(function(selector) {
        selector.classList.remove('clickedType');
    });

    selectedElement.classList.add('clickedType');
    let optionisMigrable; 
    var Type = selectedElement.getAttribute('dataType'); 
    localStorage.setItem("clickedType",Type);
    if (Type === "T") { // Assurez-vous que T est défini quelque part
        const treeRoot = document.getElementById('tree-root');
        const menuItems = document.querySelectorAll('.dropdown-content a');
        const itemChoosed = document.querySelector('.itemChoosed');
        const isMigrable = localStorage.getItem("isMigrable") === "true"; 
    
        
        let dataToUse;
        
        if (isMigrable) {
            dataToUse = Migrable;
        } else {
            dataToUse = await fetchdataHierarchy(); // Ensure dataToUse is assigned
        }
        
    
        const htmlContent = generateHierarchyHtml(dataToUse, isMigrable);
        
         
     if (htmlContent) {
        treeRoot.innerHTML = htmlContent;
        setupCaretListeners('tree-root');
        setupFieldListeners(isMigrable, false, false);
        }
    }else if (Type === "N"){
        const treeRoot = document.getElementById('tree-root');
        const menuItems = document.querySelectorAll('.dropdown-content a');
        const itemChoosed = document.querySelector('.itemChoosed');
        const isMigrable= localStorage.getItem("isMigrable")||false;
        optionisMigrable = isMigrable ? "oui" : "non";

        dataToUse = await fetchdataDistrubue("oui",optionisMigrable);
        htmlContent = generateHierarchyHtml(dataToUse, isMigrable);
             // Mise à jour du contenu si disponible
     if (htmlContent) {
        treeRoot.innerHTML = htmlContent;
        setupCaretListeners('tree-root');
        setupFieldListenersDistribue("oui");
        }
    }else if (Type === "D"){
        const treeRoot = document.getElementById('tree-root');
        const menuItems = document.querySelectorAll('.dropdown-content a');
        const itemChoosed = document.querySelector('.itemChoosed');
        const isMigrable= localStorage.getItem("isMigrable")||false;
        optionisMigrable = isMigrable ? "oui" : "non";

        dataToUse = await fetchdataDistrubue("non",optionisMigrable);
        htmlContent = generateHierarchyHtml(dataToUse, isMigrable);
             // Mise à jour du contenu si disponible
     if (htmlContent) {
        treeRoot.innerHTML = htmlContent;
        setupCaretListeners('tree-root');
        setupFieldListenersDistribue("non");
        }
    }
}

 
function setupFieldListenersDistribue(option) {
    document.querySelectorAll('.fieldLiSpan').forEach(element => {
        element.addEventListener('click', async function () {
            const clusterCode = this.getAttribute('data-cluster-code');
            const inseeCode = this.getAttribute('data-insee-code');

            if (!clusterCode || !inseeCode) {
                console.warn('Cluster Code or INSEE Code is null. Skipping fetch.');
                return;
            }

            try {
                const isMigrable= localStorage.getItem("isMigrable")||false;
                optionisMigrable = isMigrable ? "oui" : "non";
                const data = await fetchdataDistrubue(option,optionisMigrable, clusterCode, inseeCode,);
                
                if (data) {
                    const treeRoot = document.getElementById('tree-root-roles');
                    treeRoot.innerHTML = '';
                    const htmlContent = generateHierarchyHtmls(data);
                    treeRoot.innerHTML = htmlContent;
                    setupCaretListeners('tree-root-roles');
                    setupSearchFunctionalityRues();
                }
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        });
    });
}


function updateDisplayValues(data) {
    const keys = ['nb_fyr_fttb', 'nb_fyr_adsl', 'nb_fyr_mob_mono', 'nb_fyr_mob_multi_thd', 'nb_fyr_mob_multi_adsl', 'nb_fyr_thd'];
    keys.forEach(key => {
        const element = document.querySelector(`.${key}`);
        if (element) {
            element.textContent = data[key] || 'N/A';  // Providing a fallback if data[key] is undefined
        } else {
            console.error(`Element with class ${key} not found.`);
        }
    });
}

setInterval(() => {
    const isMigrable = localStorage.getItem("isMigrable") || false;

    if (isMigrable) {
        // Show both "MIGRABLE" and "DISTRIBUÉ"
        document.querySelectorAll('.TypeOptionSelector[dataType="D"]').forEach(item => {
            item.style.display = 'block';
        });
    }

}, 500);

function breadcrumbsd(){
    document.querySelectorAll('.TypeOptionSelector[dataType="N"]').forEach(item => {
        item.style.display = 'block';
    });
}

async function fetchDistributionHierarchy(option,clusterCode,codeInsee){
    var url =`'https://api.nomadcloud.fr/api/interventions-places-distribution-hierarchy/${cpv}/${option}?page=1`;
    if(clusterCode){
        url += `&codeCluster=${clusterCode}`;
    }
    if(codeInsee){
        url += `&codInsee=${codeInsee}`;
    }
    try {
        const response = await fetch(url,{
            method:"GET",
            headers:{
                "Authorization": `Bearer ${jwtToken}`,
                "Content-Type": "application/json"
            }
        });
        if (response.status === 404) {
            return [];
        }

        const data = await response.json();
        return data;
    }catch (error) {
        console.error('Failed to fetch or parse data:', error);
        return [];
    }
}



document.addEventListener("DOMContentLoaded", async function () {
    const card = document.getElementById("RueHiarchy");

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    // Assurez-vous que la carte est positionnée en `absolute` pour pouvoir la déplacer
    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Vérifier si le bouton gauche de la souris est utilisé

        isDragging = true;

        // Calculer l'offset entre la position de la souris et le coin supérieur gauche de la carte
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;

        card.style.transition = "none"; // Désactiver la transition pendant le déplacement
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;

        // Déplacer la carte en mettant à jour `left` et `top`
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Réactiver la transition
    });
    //let data = await createRueHiarchy();
    
});



/*************************Handle Distribution USer Update */
let clusterInseeDatag = [];
let closterslenghLocalestorage=0;
function CallHiarchyRue(clickedCluster, ByClickingCluster) {
    setTimeout(() => createRueHiarchy(clickedCluster, ByClickingCluster), 500);
}
let lastNom = localStorage.getItem("selectednom");
let lastPrenom = localStorage.getItem("selectedprenom");

setInterval(() => {
    const nom = localStorage.getItem("selectednom");
    const prenom = localStorage.getItem("selectedprenom");

    if (nom !== lastNom || prenom !== lastPrenom) {
        CallHiarchyRue();
        lastNom = nom;
        lastPrenom = prenom;
        createRueHiarchy();
    }
   
}, 500); // Vérifie toutes les 500 ms si les valeurs ont changé
// document.querySelectorAll('.ViewCard').forEach(function (element) {
//     element.addEventListener('click', function () {
//         createRueHiarchy();
//     });
// });

const openPanelButton = document.getElementById('openPanel');
const closePanelButton = document.getElementById('closePanel');
const productionPanel = document.getElementById('displayproductionPanel');

openPanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '0';
});

closePanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '-100%';
    closePanelButton.style.top='39px';
});
const openPanelConsoleButton = document.getElementById('openPanelConsole');
const closePanelButtonConsole = document.getElementById('closePanelConsole');
const productionPanelConsole = document.getElementById('displayproductionPanelConsole');

openPanelConsoleButton.addEventListener('click', function () {
    productionPanelConsole.style.bottom = '0';
});

closePanelButtonConsole.addEventListener('click', function () {
    productionPanelConsole.style.bottom = '-100%';
    closePanelButtonConsole.style.top='39px';
});

function attachToggleEvents() {
    document.querySelectorAll('.toggle-node').forEach(node => {
        node.addEventListener('click', function () {
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.style.display = targetElement.style.display === 'none' ? 'block' : 'none';
            }
        });
    });
}


document.addEventListener("DOMContentLoaded", function () {
    const inputField = document.getElementById("TreeSearchs");

    if (!inputField) {
        console.error("Input field #TreeSearchs not found!");
        return;
    }

    inputField.addEventListener("keydown", function (event) {
        if (event.key === "Enter") {
            event.preventDefault(); // Prevent form submission if inside a form
            
            const text = inputField.value.trim();
            let debutAnciennete = null, finAnciennete = null, commandText = "";
            let extractedCommands = [];

            // Extract the range if present
            let rangeMatch = text.match(/(-?\d+)\s*(?:a|à)\s*(-?\d+)/i);
            if (rangeMatch) {
                debutAnciennete = rangeMatch[1];
                finAnciennete = rangeMatch[2];
                commandText = text.replace(rangeMatch[0], "").trim(); // Remove the range from text
            } else {
                commandText = text; // No range found, keep the text as is
            }

            // Extract words starting with "c:" or "v:"
            let commandMatches = commandText.match(/\b[cv]:\S+/gi); // Matches words starting with "c:" or "v:"
            if (commandMatches) {
                extractedCommands = commandMatches;
            }

            // Remove extracted commands from commandText
            commandText = commandText.replace(/\b[cv]:\S+/gi, "").trim();

            // Trigger fetch when Enter is pressed
            fetchInterventionPlacesCmdLine(extractedCommands, debutAnciennete, finAnciennete);
        }
    });
});



async function fetchInterventionPlacesCmdLine(extractedCommands, debutAnciennete, finAnciennete) {
    const formattedQuery = extractedCommands.join(" ").replace(/:/g, "%3A");
    var place = formattedQuery[0];
    try {
        if (!cpv) {
            throw new Error("Le paramètre 'cpv' est manquant.");
        }
        if (!jwtToken) {
            throw new Error("Le token JWT est manquant.");
        }

        let url = `https://api.nomadcloud.fr/api/interventions-places-cmd-line/${cpv}`;
        if(debutAnciennete&&finAnciennete){
            url +=`${debutAnciennete,finAnciennete}/`;
        }
        if(formattedQuery){
            url += `?place=${extractedCommands}&page=1`;
        }
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        //alert('fetched data')
        DisplayCmdInterventionTable(data[0].data,place);

        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
}


function closingBigPanel() {
    console.log('Closing big panel');
    const panel = document.getElementById("displayPanelSearching");
    const panelContent = document.querySelector(".SearchContainerINPanel");

    if (panel) {
        panel.style.bottom = "-100%";
        panelContent.innerHTML = ""; // Clear panel content
    }
}


// function syncInputs(element) {
//     var inputValue = element.value;  // Get the current value of the first input
//     var targetInput = document.querySelector('.forminputSearchPanel');  // Select the second input
//     targetInput.value = inputValue; 
//     var searchInput = document.querySelector(".searchContainerTopbarInput");
//     searchInput.addEventListener("input", displaySearchResults);
// }
// async function displaySearchResults() {

//     //var SearchContainerINPanel =document.querySelector('.SearchContainerINSearchPanel');
//     //SearchContainerINPanel.style.display = 'block';
//     var ClosingBigPanel =document.querySelector('.ClosingBigPanel');
//     ClosingBigPanel.style.top="73px";
    
//     const sidebar = document.querySelector('.CmdInterventionTable');
//     if (!sidebar) {
//         console.error("Sidebar element not found.");
//         return;
//     }
//     sidebar.innerHTML = '';
// }
document.querySelector(".distribution-scripts").addEventListener("click", async function () {
    await distributionScripts();
});

async function distributionScripts() {
    console.log('call distributionScripts');
    try {
        
        let url = `https://api.nomadcloud.fr/api/distribution-scripts?cpv=${cpv}`;
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Données reçues:", data);
    } catch (error) {
        console.error('Erreur lors de la requête:', error);
    }
}



async function DisplayCmdInterventionTable(data, place) {
    console.log("DisplayCmdInterventionTable",place);
    const productionPanel = document.getElementById('displayPanelSearching');
    productionPanel.style.bottom = '-40px';
    try {
        const sidebar = document.querySelector('.CmdInterventionTable');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = ''; // Clear previous content

        let tableId = "productionTable";
        let table = document.getElementById(tableId);
        
        if (!table) {
            let tableHeaders = '';
            if (place === 'c') {
                tableHeaders = `
                    <th style="width: 13%;">Code Insee</th>
                    <th style="width: 20%;">Libelle Ville</th>
                    <th style="width: 13%;">Total Prises</th>
                    <th></th>
                `;
            } else if (place === 'v') {
                tableHeaders = `
                    <th style="width: 20%;">Nom Voie</th>
                    <th style="width: 13%;">Total Prises</th>
                    <th></th>
                `;
            }

            sidebar.innerHTML = `
                <table id="${tableId}" class="productionTable">
                    <thead>
                        <tr>${tableHeaders}</tr>
                    </thead>
                    <tbody id="productionTableBody"></tbody>
                </table>
            `;
            table = document.getElementById(tableId);
        }

        await populateTableData(data, place);
    } catch (error) {
        console.error('Error fetching production data:', error);
        document.querySelector('.DetailsCAlenderdata').innerHTML = '<p>Failed to load data.</p>';
    }
}

async function populateTableData(data, place) {
    console.log('Loading production', data);
    const tbody = document.getElementById("productionTableBody");
    if (!tbody) {
        console.error("Table body not found.");
        return;
    }
    tbody.innerHTML = ''; // Clear previous content

    if (data && data.length > 0) {
        const fragment = document.createDocumentFragment();
        data.forEach(item => {
            const row = document.createElement('tr');
            if (place === 'c') {
                row.innerHTML = `
                    <td>${item.cod_insee}</td>
                    <td>${item.ville}</td>
                    <td>${item.total_prises}</td>
                    <td></td>
                `;
            } else if (place === 'v') {
                row.innerHTML = `
                    <td>${item.nom_voie}</td>
                    <td>${item.total_prises}</td>
                    <td></td>
                `;
            }
            fragment.appendChild(row);
        });
        tbody.appendChild(fragment);
    } else {
        tbody.innerHTML = '<tr><td colspan="4">No data found for the selected date.</td></tr>';
    }
}


let map; // Declare map variable globally

let rotationInterval = null;


let theme = localStorage.getItem("theme") || "light"; 
mapboxgl.accessToken = 'pk.eyJ1IjoicmdvdW50aXRpIiwiYSI6ImNtMnA1bHJ5NDBuczcycnNieGsyamVjOTMifQ.FjXmzR2E_Di8YWn8nfTPog';

let currentBearing = 0; // Initial rotation angle
let is3D = true; // Start with 3D view enabled

// Function to initialize the map
function initializeMap(theme) {
    return map = new mapboxgl.Map({
        container: 'map',
        style: theme === "darkblue" 
            ? 'mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw' 
            : 'mapbox://styles/mapbox/standard',
            //: 'mapbox://styles/mapbox/light-v10',
        center: [2.2945, 48.8584],
        zoom: 16, 
        pitch: 60,  // Set initial pitch for 3D view
        bearing: -20 // Optional: Adjust the initial bearing if needed
    });
     map.addControl(new mapboxgl.NavigationControl());
}

// Function to switch to the 3D view
function setTiltedView() {
    map.easeTo({
        pitch: 60,  // Tilt the view to 60°
        bearing: -20,
        duration: 1000
    });
}

// Function to switch to the flat 2D view
function setFlatView() {
    map.easeTo({
        pitch: 0,  // No tilt for the 2D view
        bearing: 0,
        duration: 1000
    });
}

// Function to rotate the map by 45°
function rotateMap() {
    currentBearing += 45; // Add 45° on each click
    if (currentBearing >= 360) {
        currentBearing = 0; // Reset angle to 0 after 360°
    }
    map.easeTo({
        bearing: currentBearing,
        duration: 1000
    });
}

initializeMap(theme);


map.on('load', () => {
    map.addLayer({
        id: '3d-buildings',
        source: 'composite',
        'source-layer': 'building',
        type: 'fill-extrusion',
        paint: {
            'fill-extrusion-color': '#89b9ed',
            'fill-extrusion-height': ['get', 'height'],
            'fill-extrusion-base': ['get', 'min_height'],
            'fill-extrusion-opacity': 0.6
        }
    });
});

// Toggle button event
document.getElementById("toggleView").onclick = function() {
    if (is3D) {
        setFlatView(); // Switch to 2D view
        this.textContent = '3D'; // Update button text
    } else {
        setTiltedView(); // Switch to 3D view
        this.textContent = '2D'; // Update button text
    }
    is3D = !is3D; // Toggle the state
};

document.getElementById("rotateMap").onclick = rotateMap;

// Configuration du clustering
const CLUSTER_SOURCE_ID = 'clusters-source';
let markers = [];

function initializeClusterSource() {
    if (!map.getSource(CLUSTER_SOURCE_ID)) {
        map.addSource(CLUSTER_SOURCE_ID, {
            type: 'geojson',
            data: { type: 'FeatureCollection', features: [] },
            cluster: true,
            clusterMaxZoom: 14,
            clusterRadius: 50
        });
    }

    // Couche des clusters
    if (!map.getLayer('clusters-layer')) {
        map.addLayer({
            id: 'clusters-layer',
            type: 'circle',
            source: CLUSTER_SOURCE_ID,
            filter: ['has', 'point_count'],
            paint: {
                'circle-color': [
                    'step',
                    ['get', 'point_count'],
                    '#51bbd6',
                    10,
                    '#51bbd6',
                    100,
                    '#51bbd6'
                ],
                'circle-radius': [
                    'step',
                    ['get', 'point_count'],
                    20,
                    10,
                    30,
                    100,
                    40
                ]
            }
        });
    }

    // Texte des clusters
    if (!map.getLayer('cluster-count')) {
        map.addLayer({
            id: 'cluster-count',
            type: 'symbol',
            source: CLUSTER_SOURCE_ID,
            filter: ['has', 'point_count'],
            layout: {
                'text-field': '{point_count_abbreviated}',
                'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
                'text-size': 12
            }
        });
    }

    // Gestion des marqueurs individuels
    updateMarkers();
}

function updateMarkers() {
    markers.forEach(marker => marker.remove());
    markers = [];

    const features = map.querySourceFeatures(CLUSTER_SOURCE_ID, {
        filter: ['!', ['has', 'point_count']]
    });

    features.forEach(feature => {
        const el = document.createElement('div');
        el.className = 'custom-marker';
        el.style.width = '30px';
        el.style.height = '40px';
        el.style.borderRadius = '50%';
        el.style.display = 'flex';
        el.style.alignItems = 'center';
        el.style.justifyContent = 'center';
        // el.style.backgroundColor = 'white'; // Optional: to ensure visibility

        const icon = document.createElement('i');
        icon.className = 'bi bi-geo-alt-fill';
        icon.style.fontSize = '20px';
        icon.style.color = '#51bbd6'; // Adjust color if needed
        
        el.appendChild(icon);

        const marker = new mapboxgl.Marker(el)
            .setLngLat(feature.geometry.coordinates)
            .addTo(map);

        markers.push(marker);
    });
}



// Récupérer les données GeoJSON et mettre à jour la carte
async function fetchAndDisplayEarthquakes(codecluster, codeinsee) {
    let url = `https://api.nomadcloud.fr/fileAttached/geo_map/prises_parc/${cpv}/coordinates/${codecluster}`;
    
    if (codeinsee) {
        url += `/${codeinsee}.geojson`;
    }else{
        url += `/coordinates.geojson`;
    }
    
 

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Erreur HTTP ! Statut : ${response.status}`);
        }
        const data = await response.json();
        console.log(data);
        if (data) {
            initializeClusterSource();
            updateClusterData(data);
          
        }
    } catch (error) {
        console.error("❌ Erreur lors du chargement des données GeoJSON :", error);
    }
}


// Mettre à jour la source des clusters avec les nouvelles données
function updateClusterData(data) {
    const source = map.getSource(CLUSTER_SOURCE_ID);
    if (source) {
        source.setData({
            type: 'FeatureCollection',
            features: data.features.map(feature => ({
                ...feature,
                geometry: {
                    ...feature.geometry,
                    coordinates: feature.geometry.coordinates.slice(0, 2).reverse() // Inverser les coordonnées (longitude, latitude)
                }
            }))
        });

        map.once('idle', () => {
            updateMarkers();
            map.triggerRepaint();
        });
    } else {
        console.error("⚠️ Source de clusters non trouvée !");
    }
}
function updateClusterDatacuivre(data) {
    const source = map.getSource(CLUSTER_SOURCE_ID);
    if (source) {
        // Conversion des données brutes en FeatureCollection GeoJSON
        const geojsonData = {
            type: 'FeatureCollection',
            features: data.map(item => ({
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [
                        parseFloat(item.longitude),
                        parseFloat(item.latitude)
                    ]
                },
                properties: {
                    nom_voie: item.nom_voie,
                    numr_voie: item.numr_voie,
                    total_prises: item.total_prises
                    // Ajouter d'autres propriétés si nécessaire
                }
            }))
        };

        source.setData(geojsonData);

        map.once('idle', () => {
            updateMarkers();
            map.triggerRepaint();
        });
    } else {
        console.error("⚠️ Source de clusters non trouvée !");
    }
}

// Mettre à jour les marqueurs quand la carte bouge ou zoome
map.on('move', updateMarkers);
map.on('zoom', updateMarkers);


// Charger les données et afficher la carte


window.findClusterCoordinatestime = async function(codecluster, codeinsee) {
 console.log("codecluster", codecluster);
      
    fetchAndDisplayEarthquakes(codecluster, codeinsee);
  
};

async function fetchdataCoordinates(codecluster, codeinsee ) {  // default to empty string if null
    try {
        // If codeinsee is null or empty, it will be left out from the URL
        let url = `https://api.nomadcloud.fr/api/interventions-places-coordinates-v2/${cpv}/${codecluster}?page=1`;

        // Only append codeInsee parameter if codeinsee is not null or empty
        if (codeinsee) {
            url += `&codeInsee=${codeinsee}`;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`, // Assurez-vous que jwtToken est défini
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Response fetchdataCoordinates:",data)
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
}


map.on('data', (e) => {
    if (e.sourceId === CLUSTER_SOURCE_ID && e.isSourceLoaded) {
  
        map.triggerRepaint();
    }
});

// Événements de mouvement de la carte
map.on('move', updateMarkers);
map.on('zoom', updateMarkers);

// Fonction principale


    // Vérification régulière du changement de thème
    setInterval(() => {
        let newTheme = localStorage.getItem("theme");
        
        if (newTheme !== theme) {
            theme = newTheme; // Mise à jour du thème actuel
            
            if (theme === "light") {
                location.reload(); // Recharger pour appliquer le style light correctement
            } else {
                map.setStyle('mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw'); // Mise à jour du style
            }
        }
    }, 500); // Vérifie toutes les secondes





 // Variables globales pour la rotation


let rotationBound = false;
let irisPolygons = []; // Stocke les IDs des polygones IRIS sauvegardés

// Fonction pour charger les données IRIS et afficher le polygone
async function loadIrisData(codeIris) {
  if (!codeIris) return;

  const url = `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/georef-france-iris/records?where=iris_code='${codeIris}'`;

  try {
    const response = await fetch(url);
    const data = await response.json();
    if (!data.results || data.results.length === 0) {
      console.error('Aucune donnée trouvée pour ce code IRIS');
      return;
    }
    const geoShape = data.results[0].geo_shape;
    addIrisPolygonToMap(geoShape);
  } catch (error) {
    console.error('Erreur lors de la récupération des données', error);
  }
}

// Ajoute le polygone IRIS sur la carte
function addIrisPolygonToMap(geoShape) {
  if (!geoShape || !geoShape.geometry || geoShape.geometry.type !== 'Polygon') {
    console.error('La géométrie IRIS est mal formée');
    return;
  }

  const isChecked = localStorage.getItem("isChecked") === "true";
  const polygonIndex = irisPolygons.length;
  const polygonId = `iris-shape-${polygonIndex}`;

  // Ajout de la source GeoJSON
  map.addSource(polygonId, {
    type: 'geojson',
    data: {
      type: 'Feature',
      geometry: geoShape.geometry
    }
  });

  // Ajout de la couche de remplissage
  map.addLayer({
    id: `iris-fill-${polygonIndex}`,
    type: 'fill',
    source: polygonId,
    paint: {
      'fill-color': '#ff9800',
      'fill-opacity': 0.3
    }
  });

  // En fonction de la valeur de isChecked, on conserve le polygone ou on le retire après 5 secondes
  if (isChecked) {
    irisPolygons.push(polygonId);
  } else {
    setTimeout(() => {
      if (map.getSource(polygonId)) {
        map.removeLayer(`iris-fill-${polygonIndex}`);
        map.removeSource(polygonId);
      }
    }, 5000);
  }

  // Centrage de la carte sur le polygone
  const coordinates = geoShape.geometry.coordinates[0];
  const bounds = coordinates.reduce((b, coord) => b.extend(coord), new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));
  map.fitBounds(bounds, { padding: 20, pitch: 60, zoom: 16, bearing: -20 });

  // Démarrer la rotation après le zoom
  map.once('moveend', startRotationAfterZoom);

  // Ajout de la couche 3D des bâtiments si elle n'existe pas déjà
  if (!map.getLayer('3d-buildings')) {
    map.addLayer({
      id: '3d-buildings',
      source: 'composite',
      'source-layer': 'building',
      type: 'fill-extrusion',
      paint: {
        'fill-extrusion-color': '#888',
        'fill-extrusion-height': ['get', 'height'],
        'fill-extrusion-base': ['get', 'min_height'],
        'fill-extrusion-opacity': 0.7
      }
    });
    if (map.getSource('iris-shape')) {
            map.addLayer({
                id: 'iris-border',
                type: 'line',
                source: 'iris-shape', // Même source que le fill
                layout: {},
                paint: {
                    'line-color': '#ff9800', // Rouge
                    'line-width': 2, // Épaisseur
                    'line-opacity': 1, // Opacité
                }
            });
    }

  }

  // Ajout des écouteurs d'évènements pour la rotation (une seule fois)
  if (!rotationBound) {
    rotationBound = true;
    map.on('zoomend', startRotationAfterZoom);
    map.on('click', stopRotation);
  }
}

// Supprime tous les polygones IRIS ajoutés
function removeAllIrisPolygons() {
  irisPolygons.forEach((polygonId, index) => {
    if (map.getLayer(`iris-fill-${index}`)) {
      map.removeLayer(`iris-fill-${index}`);
    }
    if (map.getSource(polygonId)) {
      map.removeSource(polygonId);
    }
  });
  irisPolygons = [];
}

// Démarre la rotation de la carte
function startRotation() {
  if (!rotationInterval) {
    rotationInterval = setInterval(() => {
      currentBearing = (currentBearing + 1) % 360;
      map.easeTo({ bearing: currentBearing, duration: 100 });
    }, 80);
  }
}

// Arrête la rotation de la carte
function stopRotation() {
  if (rotationInterval) {
    clearInterval(rotationInterval);
    rotationInterval = null;
  }
}

// Démarre la rotation après la fin du zoom
function startRotationAfterZoom() {
  if (!rotationInterval) {
   // setTimeout(startRotation, 80);
  }
}

// Fonctions pour activer la vue 3D (inclinaison) et revenir à la vue 2D
function setTiltedView() {
  map.easeTo({ pitch: 60, duration: 1000 });
}

function setFlatView() {
  map.easeTo({ pitch: 0, duration: 1000 });
}

// Vérification périodique de la valeur de codeIris dans localStorage (même onglet)
let previousCodeIris = localStorage.getItem("codeIris");
setInterval(() => {
  const currentCodeIris = localStorage.getItem("codeIris");
  if (currentCodeIris && currentCodeIris !== previousCodeIris) {
    previousCodeIris = currentCodeIris;
    loadIrisData(currentCodeIris);
  }
}, 1000);

// Écoute des changements de codeIris dans d'autres onglets
window.addEventListener("storage", (event) => {
  if (event.key === "codeIris") {
    loadIrisData(event.newValue);
  }
});

// Charger les données IRIS au démarrage de la page
document.addEventListener("DOMContentLoaded", () => {
  const initialCodeIris = localStorage.getItem("codeIris");
  if (initialCodeIris) {
    loadIrisData(initialCodeIris);
  }
});

// --- Fonctions liées à la recherche de rue ---

// Géocodage d'une adresse via Nominatim
async function geocode(address) {
  const geocodeUrl = `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(address)}&format=json&addressdetails=1&limit=1`;
  const response = await fetch(geocodeUrl);
  if (!response.ok) {
    throw new Error("Erreur de géocodage (Nominatim inaccessible).");
  }
  return response.json();
}

// Formule de Haversine pour calculer la distance entre deux points
function haversine(coord1, coord2) {
  const R = 6371;
  const toRad = x => x * Math.PI / 180;
  const dLat = toRad(coord2[1] - coord1[1]);
  const dLon = toRad(coord2[0] - coord1[0]);
  const lat1 = toRad(coord1[1]);
  const lat2 = toRad(coord2[1]);
  const a = Math.sin(dLat / 2) ** 2 + Math.sin(dLon / 2) ** 2 * Math.cos(lat1) * Math.cos(lat2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Calcul du temps de marche (en minutes) à partir d'une distance en km
function calculateWalkingTime(distance) {
  const walkingSpeed = 5; // km/h
  return (distance / walkingSpeed) * 60;
}

/**
 * Supprime les features correspondant à une rue donnée en filtrant par la clé.
 */
function removeStreet() {
  const nomvoie = localStorage.getItem('selectedVoie');
  const selectedComplement = localStorage.getItem('selectedComplement');
  if (!nomvoie || !selectedComplement) {
    console.warn('Aucune adresse trouvée dans localStorage.');
    return;
  }
  const addressKey = `${nomvoie}, ${selectedComplement}`;

  const streetSource = map.getSource('streetLayer');
  if (streetSource) {
    // Récupérer les données actuelles de la source via getSource()._data n'est pas idéal,
    // il serait préférable de stocker la FeatureCollection dans une variable globale.
    const currentData = streetSource._data;
    const newFeatures = currentData.features.filter(feature => feature.properties.key !== addressKey);

    // Mise à jour de la source avec les nouvelles features
    streetSource.setData({
      type: 'FeatureCollection',
      features: newFeatures
    });

    // Si aucune rue n'est présente, supprimer aussi les polygones IRIS
    if (newFeatures.length === 0) {
      removeAllIrisPolygons();
    }
  }
}

/**
 * Recherche une rue et ajoute ses features sans supprimer celles déjà affichées.
 * Si une rue pour la même adresse existe déjà, elle est d'abord supprimée.
 */
async function searchStreet() {
  const nomvoie = localStorage.getItem('selectedVoie');
  const selectedComplement = localStorage.getItem('selectedComplement');

  if (!nomvoie || !selectedComplement) {
    console.warn('Aucune adresse trouvée dans localStorage.');
    return;
  }

  const address = `${nomvoie}, ${selectedComplement}`;
  console.log('Adresse:', address);

  // Supprime d'éventuelles features existantes pour éviter les doublons
  removeStreet();

  try {
    const results = await geocode(address);
    if (!results || results.length === 0) {
      throw new Error("Adresse introuvable. Veuillez vérifier l'orthographe.");
    }
    const { lat, lon, display_name } = results[0];
    const radius = 1000;
    // Extraction simplifiée du nom de la rue depuis display_name
    const streetName = display_name ? display_name.split(',')[0] : nomvoie;

    // Requête Overpass pour récupérer la géométrie de la rue
    const overpassQuery = `
      [out:json][timeout:25];
      (
        way["name"="${streetName}"](around:${radius},${lat},${lon});
      );
      out geom;
    `;
    const overpassUrl = `https://overpass-api.de/api/interpreter?data=${encodeURIComponent(overpassQuery)}`;
    const response = await fetch(overpassUrl);
    if (!response.ok) {
      throw new Error("Erreur réseau ou serveur Overpass non accessible.");
    }
    const data = await response.json();
    if (!data.elements || data.elements.length === 0) {
      alert(`La rue "${address}" est introuvable à proximité.`);
      return;
    }

    let totalDistance = 0;
    const voieColor = localStorage.getItem('selectedVoieColor') || '#539AF8';
    const features = [];
    const addressKey = `${nomvoie}, ${selectedComplement}`;

    data.elements.forEach(way => {
      if (way.geometry) {
        const coordinates = way.geometry.map(coord => [coord.lon, coord.lat]);
        // Calcul de la distance pour ce segment
        const distance = coordinates.reduce((acc, curr, idx, arr) => {
          if (idx === 0) return 0;
          return acc + haversine(arr[idx - 1], curr);
        }, 0);
        totalDistance += distance;
        const walkingTime = calculateWalkingTime(totalDistance);

        features.push({
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: coordinates
          },
          properties: {
            name: way.tags && way.tags.name ? way.tags.name : streetName,
            color: voieColor,
            totalDistance: totalDistance.toFixed(2), // Chaîne de caractères
            walkingTime: walkingTime.toFixed(0),       // Chaîne de caractères
            key: addressKey
          }
        });
        // Enregistrement de la distance totale dans localStorage
        localStorage.setItem('totalDistance', totalDistance.toFixed(2));
      }
    });

    // Prépare la nouvelle FeatureCollection
    const newGeojson = {
      type: 'FeatureCollection',
      features: features
    };

    // Mise à jour ou création de la source "streetLayer"
    if (map.getSource('streetLayer')) {
      // Récupération des données existantes
      const currentData = map.getSource('streetLayer')._data;
      const mergedFeatures = currentData.features.concat(features);
      map.getSource('streetLayer').setData({
        type: 'FeatureCollection',
        features: mergedFeatures
      });
    } else {
      map.addSource('streetLayer', {
        type: 'geojson',
        data: newGeojson
      });
      map.addLayer({
        id: 'streetLayer',
        type: 'line',
        source: 'streetLayer',
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': ['get', 'color'],
          'line-width': 8,
          'line-opacity': 0.8
        }
      });
      if (map.getSource('iris-shape')) {
            map.addLayer({
                id: 'iris-border',
                type: 'line',
                source: 'iris-shape', // Même source que le fill
                layout: {},
                paint: {
                    'line-color': '#ff9800', // Rouge
                    'line-width': 2, // Épaisseur
                    'line-opacity': 1, // Opacité
                }
            });
    }

    }
    



    // Calcul et ajustement du zoom pour englober toutes les features
    let allCoords = [];
    const streetSource = map.getSource('streetLayer');
    if (streetSource && streetSource._data && streetSource._data.features) {
      streetSource._data.features.forEach(feature => {
        allCoords = allCoords.concat(feature.geometry.coordinates);
      });
    }
    if (allCoords.length > 0) {
      const bounds = allCoords.reduce((b, coord) => b.extend(coord), new mapboxgl.LngLatBounds(allCoords[0], allCoords[0]));
      map.fitBounds(bounds, { padding: 20, pitch: 60, bearing: -20 });
    }
    handleBuildingsMatching(map, nomvoie, lon, lat);
  } catch (error) {
    console.error('Erreur :', error);
    alert(error.message || 'Une erreur est survenue.');
  }
}

map.on('click', 'streetLayer', function (e) {
  if (!e.features || !e.features.length) return;
  const feature = e.features[0];
  const properties = feature.properties;
  // Note : properties.totalDistance et walkingTime sont des chaînes de caractères déjà formatées
  localStorage.setItem('totalDistance', properties.totalDistance);
  const popupContent = `
    <div class="custom-popup">
      <div class="popup-title">
        <span class="location-icon"><i class="bi bi-geo-alt-fill"></i></span>
        <span style="color:${properties.color}">${properties.name}</span>
      </div>
      <div class="popup-details">
        <div>
          <span class="walk-icon"><i class="bi bi-person-walking"></i></span> Km: 
          <b style="color:${properties.color}; padding-left: 144px;">${properties.totalDistance}</b>
        </div>
        <div>
          <span class="clock-icon"><img class="icon" src="/image/picto-prises.svg" alt="Icon"></span> Prises 
          <b style="color:${properties.color}; padding-left: 144px;">${properties.walkingTime}</b>
        </div>
      </div>
    </div>
  `;
  new mapboxgl.Popup()
    .setLngLat(e.lngLat)
    .setHTML(popupContent)
    .addTo(map);
});

// Changement du curseur lorsque la souris survole la couche des rues
map.on('mouseenter', 'streetLayer', function () {
  map.getCanvas().style.cursor = 'pointer';
});
map.on('mouseleave', 'streetLayer', function () {
  map.getCanvas().style.cursor = '';
});

// Surveillance des changements de "selectedVoie" dans localStorage pour déclencher une recherche de rue
let lastSelectedVoie = localStorage.getItem('selectedVoie');
setInterval(() => {
  const currentSelectedVoie = localStorage.getItem('selectedVoie');
  if (currentSelectedVoie !== lastSelectedVoie) {
    lastSelectedVoie = currentSelectedVoie;
    searchStreet();
  }
}, 500);

// Surveillance d'un second flag ("decouche") pour ajouter ou supprimer une rue en fonction de la valeur de "isChecked"
let lastDecouche = localStorage.getItem("decouche");
setInterval(() => {
  const currentDecouche = localStorage.getItem("decouche");
  const isChecked = localStorage.getItem("isChecked");
  if (currentDecouche !== lastDecouche) {
    lastDecouche = currentDecouche;
    if (currentDecouche && isChecked === 'false') {
      removeStreet();
    } 
  }
}, 500);



// document.addEventListener('DOMContentLoaded', () => {
//     const canvas = document.getElementById('canvas');
//     const ctx = canvas.getContext('2d');
//     const infoCard = document.getElementById('RueHiarchy');
//     const treeNodes = document.querySelectorAll('.tree-node');
//     let startX = null, startY = null;
//     let isDrawing = false;

//     function resizeCanvas() {
//         canvas.width = window.innerWidth;
//         canvas.height = window.innerHeight;
//     }

//     infoCard.addEventListener('click', (event) => {
//         let rect = infoCard.getBoundingClientRect();
//         startX = rect.left + rect.width / 2;
//         startY = rect.top + rect.height / 2;
//         isDrawing = true;
//     });

//     document.addEventListener('mousemove', (event) => {
//         if (!isDrawing) return;
//         ctx.clearRect(0, 0, canvas.width, canvas.height);
//         drawLine(startX, startY, event.clientX, event.clientY);
//     });

//     document.addEventListener('click', (event) => {
//         if (isDrawing && event.target !== infoCard) {
//             drawLine(startX, startY, event.clientX, event.clientY);
//             isDrawing = false;
//         }
//     });

//     treeNodes.forEach(node => {
//         node.addEventListener('click', (event) => {
//             if (isDrawing) {
//                 drawLine(startX, startY, event.clientX, event.clientY);
//                 isDrawing = false;
//             }
//         });
//     });

//     treeNodes.forEach(node => {
//         node.addEventListener('mouseenter', () => {
//             node.style.border = '2px solid blue';
//         });
//         node.addEventListener('mouseleave', () => {
//             node.style.border = '';
//         });
//     });

//     function drawLine(x1, y1, x2, y2) {
//         const canvasRect = canvas.getBoundingClientRect();
//         const adjustedX1 = x1 - canvasRect.left;
//         const adjustedY1 = y1 - canvasRect.top;
//         const adjustedX2 = x2 - canvasRect.left;
//         const adjustedY2 = y2 - canvasRect.top;
//         const rootStyles = getComputedStyle(document.documentElement);
//         const primaryColor = rootStyles.getPropertyValue('--d-band-border').trim();

//         ctx.beginPath();
//         ctx.moveTo(adjustedX1, adjustedY1);
//         ctx.lineTo(adjustedX2, adjustedY2);
//         ctx.strokeStyle = primaryColor;
//         ctx.lineWidth = 1;
//         ctx.setLineDash([1, 2]);
//         ctx.stroke();
//         ctx.setLineDash([]);
//     }

//     window.addEventListener('resize', resizeCanvas);
//     resizeCanvas();
// });
async function fetchDataJachere(ClusterCode) {
    try {
         if (!jwtToken) {
             throw new Error("JWT Token is missing");
         }
 
         var url = `https://api.nomadcloud.fr/api/prospections-cycles-list/${pointOfSaleId}?codeCluster=${ClusterCode}&page=1`;
 
         const response = await fetch(url, {
             method: 'GET',
             headers: {
                 'Authorization': `Bearer ${jwtToken}`,
                 'Content-Type': 'application/json'
             }
         });
 
         if (!response.ok) {
             throw new Error(`HTTP error! Status: ${response.status}`);
         }
 
         var data = await response.json();
         document.getElementById("jachere").innerHTML = data[0].nbre_jours_jachere;
 
     } catch (error) {
         console.error(`Fetch error for ${url}:`, error);
         document.getElementById("jachere").innerHTML = "Error"; // Display "Error" in case of failure
     }
 }

async function fetchprospection(nbr) {
    //var nbr = document.getElementById("InputNumber").value;
    let cluster=localStorage.getItem("ClusterCode");
    try {
        let url = `https://api.nomadcloud.fr/api/prospections-cycles/${pointOfSaleId}?codeCluster=${cluster}`;
        const response = await fetch(url, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                nbreJoursJachere: Number(nbr)
            })
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        //alert('fetched data')
        console.log('Response fetchprospection:', data);
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
}


let MissingCities;
let allHiearchyData = []; // Ensure it's an array

async function fetchHiearchicalData() {
    const url = `https://api.nomadcloud.fr/api/productions-consolidation/${userIdLogIn}/V?page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        allHiearchyData = data[0][Number(year)][Number(month)];
        
    } catch (error) {
        console.error('Error fetching hierarchical data:', error);
        allHiearchyData = []; // Fallback to an empty array in case of an error
    }
}


async function fetchMissingcities(clusterCode) {
    const url = `https://api.nomadcloud.fr/api/productions-missing-cities/${pointOfSaleId}/${clusterCode}/${year}/${month}?optionSelect=${Type}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        MissingCities = await response.json();
        console.log(MissingCities);
        getClusterByCode(clusterCode);
    } catch (error) {
        console.error('Error fetching interventions migrable:', error);
        return null;
    }
}

function getClusterByCode(code) {
    const cluster = allHiearchyData.find(cluster => cluster.clusterCode === code);
    console.log(code);
    updateMapWithVilles(code);
    updateMapWithVillesWithoutProduction();
}
function updateMapWithVillesWithoutProduction() {
    if (typeof map === 'undefined' || !map) {
        console.error("Map is not initialized or accessible.");
        return;
    }

    if (!MissingCities || !MissingCities.villes || !Array.isArray(MissingCities.villes)) {
        console.error("Invalid data: MissingCities is undefined or missing 'villes'");
        return;
    }

    const sourceId = 'villes-missing';
    const geojsonData = createGeoJSONData(MissingCities);

    if (!geojsonData) {
        console.error("GeoJSON data creation failed.");
        return;
    }

    if (map.getSource(sourceId)) {
        map.getSource(sourceId).setData(geojsonData);
    } else {
        setupNewSourceAndLayers(sourceId, geojsonData, '#ffb5b5');
    }
    updateBoundsAndEvents(sourceId, 'villes-missing-fill');
}

function createGeoJSONData(data) {
    if (!data || !Array.isArray(data.villes)) {
        console.error('Data is undefined or not correctly structured:', data);
        return null; // Return null or an empty geoJSON structure if data is not available
    }

    return {
        type: "FeatureCollection",
        features: data.villes.map(ville => {
            if (!ville.polygon || !Array.isArray(ville.polygon.coordinates) || ville.polygon.coordinates.length === 0) {
                console.error('Malformed or missing coordinates for ville:', ville.ville);
                return null;
            }
            return {
                type: "Feature",
                id: ville.code_insee,
                properties: {
                    ville: ville.code_insee,
                    villeName: ville.ville
                },
                geometry: {
                    type: "Polygon",
                    coordinates: ville.polygon.coordinates
                }
            };
        }).filter(feature => feature !== null)
    };
}

function updateMapWithVilles(data) {
    if (!data.villes || !Array.isArray(data.villes)) {
        //console.error("Invalid data: missing 'villes'");
        return;
    }

    const sourceId = 'villes-production';

    const geojsonData = {
        type: "FeatureCollection",
        features: data.villes.map(ville => ({
            type: "Feature",
            id: ville.code_insee,
            properties: {
                ville: ville.code_insee,
                villeName: ville.ville
            },
            geometry: {
                type: "Polygon",
                coordinates: [ville.polygon.coordinates[0]]
            }
        }))
    };

    if (map.getSource(sourceId)) {
        map.getSource(sourceId).setData(geojsonData);
    } else {
        setupNewSourceAndLayers(sourceId, geojsonData, '#088');
    }
    updateBoundsAndEvents(sourceId, 'villes-production-fill');
}

function updateBoundsAndEvents(sourceId, layerIdFill) {
    const bounds = new mapboxgl.LngLatBounds();
    const source = map.getSource(sourceId);

    if (source && source._data && source._data.features) {
        source._data.features.forEach(feature => {
            feature.geometry.coordinates.forEach(polygon => {
                polygon.forEach(coord => {
                    if (coord && coord.length >= 2) {
                        bounds.extend(coord);
                    } else {
                        console.error('Invalid coordinate:', coord);
                    }
                });
            });
        });

        try {
            map.fitBounds(bounds, { padding: 20, maxZoom: 9 });
        } catch (error) {
            console.error('Error fitting bounds:', error);
        }

        map.on('click', layerIdFill, function (e) {
            if (e.features.length > 0) {
                const feature = e.features[0];
            }
        });
    } else {
        console.error('Source data is not loaded or is invalid:', sourceId);
    }
}

async function initializetest() {
    await fetchHiearchicalData();

}
function setupNewSourceAndLayers(sourceId, geojsonData, fillColor) {
    if (!map.getSource(sourceId)) {
        map.addSource(sourceId, {
            type: "geojson",
            data: geojsonData
        });
    }

    if (!map.getLayer(`${sourceId}-fill`)) {
        map.addLayer({
            id: `${sourceId}-fill`,
            type: "fill",
            source: sourceId,
            layout: {},
            paint: {
                'fill-color': fillColor,
                'fill-opacity': 0.5
            }
        });
    }

    if (!map.getLayer(`${sourceId}-outline`)) {
        map.addLayer({
            id: `${sourceId}-outline`,
            type: "line",
            source: sourceId,
            layout: {},
            paint: {
                'line-color': [
                    'case',
                    ['boolean', ['feature-state', 'selected'], false],
                    '#55C5D0', // Color when selected
                    'transparent' // Default (non-selected) color
                ],
                'line-width': [
                    'case',
                    ['boolean', ['feature-state', 'selected'], false],
                    3, // Width when selected
                    0  // Default (non-selected) width
                ]
            }
        });
    }

    //setupClickHandling(sourceId, `${sourceId}-fill`);
}

function simulateClickByClusterCode(ClusterCode) {
    // removeAllVilles();
    findClusterDataForUser(ClusterCode);
    //infoCardByPolygonCluster(ClusterCode, 'villeName', 'coordinates');
}
function simulateClickByVilleCode(villeCode) {

    const sourceId = 'villes-production';
    const layerIdFill = `${sourceId}-fill`;

    const source = map.getSource(sourceId);
    if (!source) {
        console.error('Source not found:', sourceId);
        return;
    }

    const feature = source._data.features.find(f => f.properties.ville === villeCode);
    if (!feature) {
        console.error('Ville not found with code:', villeCode);
        return;
    }

    resetAllSelections();

    map.setFeatureState(
        { source: sourceId, id: feature.id },
        { selected: true }
    );

    const coordinates = feature.geometry.coordinates[0];
    const villeName = feature.properties.villeName;
    console.log('Call infoCardByPolygon 1887');

    infoCardByPolygon(villeCode, villeName, coordinates);
}
async function findClusterDataForUser(codecluster, codeInsee) {


 
    var data = await fetchtotalClusterUsertest(codecluster, codeInsee);

    try {
        var totalPrisesDistribuer = sumTotalPrises(data);
        var totalPrisesByVille = await getTotalPriseVenteByVille(codecluster, codeInsee);

        var distribuerPercentage = (totalPrisesDistribuer / totalPrisesByVille.total_prises) * 100;
        var remainingPercentage = 100 - distribuerPercentage;

        var chartData = [
            {
                categorie: 'Attribuer',
                totalVentes: distribuerPercentage,
                label: `Attribuer (${totalPrisesDistribuer})`
            },
            {
                categorie: 'Prises',
                totalVentes: remainingPercentage,
                label: `Prises (${totalPrisesByVille.total_prises})`
            }
        ];

       

    } catch (error) {
        console.error('Error fetching or rendering data:', error);
    }
}
function sumTotalPrises(data) {
    let total = 0;

    data.forEach(entry => {
        if (Array.isArray(entry.voies)) {
            total += entry.voies.reduce((sum, voie) => sum + parseInt(voie.total_prises, 10), 0);
        }

        if (Array.isArray(entry.children) && entry.children.length > 0) {
            total += sumTotalPrises(entry.children);
        }
    });

    return total;
}

async function getTotalPriseVenteByVille(codeCluster,codeInsee){
    const baseURL= `https://api.nomadcloud.fr/api/interventions-places-hierarchy/${cpv}?codeCluster=${codeCluster}&page=1`;
    let url = new URL(baseURL);
    if (codeInsee) {
        url.searchParams.set('codeInsee', codeInsee);
    }
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();

        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return;
    }
}
async function fetchtotalClusterUsertest(codecluster, codeInsee) {
    //document.getElementById('MiseEnpageCard').innerHTML = '';
    try {
        let url = `https://api.nomadcloud.fr/api/interventions-places-total-by-user/${cpv}/${codecluster}?page=1`;
        if (codeInsee) {
            url += `&codeInsee=${codeInsee}`;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 404) {
                // Handle 404 specifically
                const responseData = await response.json();  // Parse the JSON to check the message
                if (responseData.error && responseData.error === "Aucun résultat trouvé pour les critères donnés") {
                    // If the specific error message is found, exit quietly
                    document.getElementById('MiseEnpageCard').innerHTML = '';  // Optionally update the UI silently
                    return;  // Exit without logging
                }
                // If another error message or not the specific one, you could log or handle differently here
            }
            return;  // Exit for all other non-OK responses if you decide not to handle them specifically
        }

        const data = await response.json();
        return data;  // Continue with normal processing if response is OK
    } catch (error) {
        document.getElementById('MiseEnpageCard').innerHTML = '';  // Handle network or parsing errors quietly
        return;  // Exit function quietly on catch
    }
}
function infoCardByPolygon(ville,villesName, coordinates) {

    findClusterDataForUser(localStorage.getItem('clusterCode'),ville);
    //getTotalPriseVenteByVille(localStorage.getItem('clusterCode'),ville);

    const card = document.getElementById("info-card-ByPolygon");
    card.style.display = "block";
    var villesTest=document.querySelector('.villesTest');
    villesTest.textContent=villesName;

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return;
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
    });
    displayInterventionsMigrable(ville);
}

initializetest();
