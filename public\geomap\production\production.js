var currentDate = new Date();
// Format the current date as 'dd-mm-yyyy'
var day = String(currentDate.getDate()).padStart(2, '0');
var month = String(currentDate.getMonth() + 1).padStart(2, '0');

var year = currentDate.getFullYear();
let Type='V';
const formattedDate = `${day}-${month}-${year}`;
function formatDateRange(month, year) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    const formatStartDate = `${String(startDate.getDate()).padStart(2, '0')}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;
    const formatEndDate = `${String(endDate.getDate()).padStart(2, '0')}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${endDate.getFullYear()}`;
    return `debut=${formatStartDate}&fin=${formatEndDate}`;
}
document.addEventListener('DOMContentLoaded', function() {
    setCurrentMonthName();

    var dropdownButton = document.getElementById('MonthSelector');
    var dropdownMenu = document.querySelector('.MonthSelectorDropdown');
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
       
    dropdownButton.addEventListener('click', function() {
        var isExpanded = this.getAttribute('aria-expanded') === 'true';
        this.setAttribute('aria-expanded', !isExpanded);
        dropdownMenu.style.display = isExpanded ? 'none' : 'flex';
        if (isExpanded) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });
});
function setCurrentMonthName() {
    const date = new Date();
    const monthIndex = date.getMonth();
    const monthNames = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
        "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"];

    const currentMonthName = monthNames[monthIndex];
    document.querySelector('.CurrentlyMonthApplyed').textContent = currentMonthName;
}
function updateMonth(selectedMonth,monthName) {
    document.querySelector('.CurrentlyMonthApplyed').textContent = monthName;
    month = selectedMonth;

    document.querySelector('.MonthSelectorDropdown').style.display = 'none';
    recallFunctionUpdatedParam();
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
    dropdownIcon.classList.remove("bi-chevron-down");
    dropdownIcon.classList.add("bi-x-lg");
}
function updateTypeOption(selectedElement) {
    var selectors = document.querySelectorAll('.TypeOptionSelector');

    selectors.forEach(function(selector) {
        selector.classList.remove('clickedType');
    });

    selectedElement.classList.add('clickedType');
    Type = selectedElement.getAttribute('dataType');
    localStorage.setItem('clusterForOnedays', '');
    localStorage.setItem('codeinseeForOnedays', '');

    recallFunctionUpdatedParam();
}


let isProductionsUsersNested;
let firstLoading;
async function fetchUserHiarchy(clustercode,codeInsee) {

    var container= document.getElementById('UsersByVente');
    container.innerHTML = '';
    var date =formatDateRange(month, year);
    const baseURL  = `https://api.nomadcloud.fr/api/productions-kpi-descendante/${pointOfSaleId}?userId=${userIdLogIn}&${date}&optionSelect=${Type}&page=1`;
    let url = new URL(baseURL);
    if (clustercode) {
        url.searchParams.set('codeCluster', clustercode);
    }
    if (codeInsee) {
        url.searchParams.set('codeInsee', codeInsee);
    }
    if (!clustercode&&!codeInsee) {
        firstLoading=true;
    }
    try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${jwtToken}`,
                "Content-Type": "application/json"
            }
        });

        if (response.status === 404) {
            return [];
        }

        const data = await response.json();
        
        if (data.message === "Aucun résultat") {
            return [];
        }
       if (typeof(data) == 'object') {
        container.innerHTML = generateHierarchyHtmlForUsers(data,true,firstLoading);
       }
    } catch (error) {
        console.error('Failed to fetch or parse data:', error);
        return [];
    }
}

function generateHierarchyHtmlForUsers(data,isProductionsUsers,firstLoading) {
    isProductionsUsersNested=isProductionsUsers;

    let htmlContent = '<ul style="padding-left: 0;">';  // Ensure the outermost list is always there
    const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');

    data.forEach(user => {
        let totalPrises = 0;

        if(isProductionsUsers==true){
            if (Array.isArray(user.productions) && user.productions.length > 0){ totalPrises= user.productions[0].totalVentes;}

        }else{
            if (Array.isArray(user.villes)) {
                totalPrises += user.villes.reduce((sum, ville) => sum + (Number(ville.total_prises) || 0), 0);
            }
            if (Array.isArray(user.voies)) {
                totalPrises += user.voies.reduce((sum, voie) => sum + (Number(voie.total_prises) || 0), 0);
            }
        }
        let childrenContent = user.children && user.children.length > 0 
            ? generateNestedUsers(user.children, flecheFields) 
            : '';
        const fullName = `${user.nom} ${user.prenom}`;
        let activeClass='';
        if(firstLoading==false){
         activeClass = totalPrises > 0 ? 'active-field blink_me' : '';
        }
        const onClickAction = isProductionsUsers ? `handleUserClick(this, '${fullName}',${user.id});` :'';
        htmlContent += `
            <li class="formListItem" style="font-size:14px;">
                <span class=" usersProductionKpi caret formSpan ${activeClass}" onclick="toggleVisibility(this);${onClickAction}" >
                   <div style="display: flex; align-items: center; width: 90%;">
                        <img src="${fleche}" class="flecheimg child-img" style="width: 12px; margin-right: 6px;">
                        <i class="bi bi-person-fill" style="color:var(--coloredTextPrimary); margin-right: 5px;"></i>
                        <div style="flex-grow: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${fullName}</div>
                    </div>

                    <span class="TotalByUser" >${totalPrises}</span>
                </span>
                ${childrenContent}
            </li>

        `;
    });

    htmlContent += '</ul>';
    return htmlContent;
}
function generateNestedUsers(children, flecheFields, depth = 0) {
    let content = '<ul class="nested" style="display: block; font-size:14px; color: #b0b0b0;">';
    children.forEach(child => {
        let totalPrises = 0;
        if (isProductionsUsersNested == true) {
            if (Array.isArray(child.productions) && child.productions.length > 0) { 
                totalPrises = child.productions[0].totalVentes; 
            }
        } else {
            if (Array.isArray(child.villes)) {
                totalPrises += child.villes.reduce((sum, ville) => sum + (Number(ville.total_prises) || 0), 0);
            }
            if (Array.isArray(child.voies)) {
                totalPrises += child.voies.reduce((sum, voie) => sum + (Number(voie.total_prises) || 0), 0);
            }
        }

        // Determine if the current user has children to decide on including the image
        let hasChildren = child.children && child.children.length > 0;
        let subChildrenContent = hasChildren 
            ? generateNestedUsers(child.children, flecheFields, depth + 1)
            : '';
        let paddingLeft = 10 + depth * 10;
        const fullName = `${child.nom} ${child.prenom}`;
        let activeClass = '';
        if (firstLoading == false) {
            activeClass = totalPrises > 0 ? 'active-field blink_me' : '';
        }
        const onClickAction = isProductionsUsersNested ? `handleUserClick(this, '${fullName}',${child.id});` : '';

        const imageHtml = hasChildren ? `<img src="${flecheFields}" class="flecheimg child-img" style="width: 12px; margin-right: 6px;">` : '';

        content += `
            <li>
                <span class="usersProductionKpi fieldLiSpan nestedSpan ${activeClass}" onclick="toggleVisibility(this);${onClickAction}" style="padding-left: ${paddingLeft}px;">
                    <div style="display: flex; align-items: center; width: 90%;">
                        ${imageHtml}
                        <i class="bi bi-person-fill" style="color:var(--coloredTextPrimary); margin-right: 5px;"></i>
                        <div class="FullNameUsersKpi">${fullName}</div>
                    </div>
                    <span class="TotalByUser">${totalPrises}</span>
                </span>
                ${subChildrenContent}
            </li>
        `;
    });
    content += '</ul>';
    return content;
}




function toggleVisibility(element) {
    const nestedList = element.nextElementSibling;
    const icon = element.querySelector('.flecheimg'); // Assumes .flecheimg is a child of the clicked element

    // Toggle display
    if (nestedList) {
        nestedList.style.display = (nestedList.style.display === 'block') ? 'none' : 'block';

        // Rotate icon based on visibility
        if (nestedList.style.display === 'block') {
            icon.classList.add('rotated'); // This should be the initial state
        } else {
            icon.classList.remove('rotated');
        }
    }
}



async function handleUserClick(element, fullName,userID) {

    var userDetails =await fetchUserDetailsVente(userID);

    const detailsDiv = document.getElementById('UsersVenteDetails');
    const isClosed = detailsDiv.style.right === '-318px' || detailsDiv.style.right === '';
    if(isClosed){ detailsDiv.style.right = '314px';}
    var UserNameForPanleDetails= document.querySelector('.UserNameForPanleDetails');
    UserNameForPanleDetails.innerHTML=fullName;
    var UserDetailsForPanleDetails=document.querySelector('.UserDetailsForPanleDetails');
    UserDetailsForPanleDetails.innerHTML='';
    UserDetailsForPanleDetails.innerHTML=generateUserDetailsHTML(userDetails) ;
    document.getElementById('RightPanelDetailsVente').addEventListener('click', function() {
        var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
        detailsDiv.style.right = '-318px';
        IconRightPanelToggle.style.transform = 'rotate(-90deg)';
    });
}

function generateUserDetailsHTML(userDetails) {
    let htmlContent = '<ul>';
    console.log(userDetails);
    if (userDetails==="Aucun productions pour cet utilisateur") {
        htmlContent += '';
        return htmlContent;
    }

    userDetails.forEach(user => {
        //var borderColor =getBorderColor(user.etat.code);
        htmlContent += `
            <li id="user-${user.id}" class="user-detail ">
                <span style="" class="userLiDetails fieldLiSpan"tabindex="0" aria-controls="details-${user.id}" aria-expanded="false">
                <img src="${fleche}" class="flecheimg flechuser-detail child-img" style="width: 12px; margin-right: 6px;">
                ${user.numCommande}</span>
                <ul class="detail-list" id="details-${user.id}" style="display: none;font-size: 14px;color: #b0b0b0;    background-color: var(--nested-bg);">
                    <li><span class="userLiDetailsSpan">
                    <img src="${flecheFields}" class="flecheimg child-img" style="width: 12px; margin-right: 6px;">
                    ${user.dateCmdA}</span></li>
                    <li><span class="userLiDetailsSpan" >
                    <img src="${flecheFields}" class="flecheimg child-img" style="width: 12px; margin-right: 6px;">
                    ${user.product.name}</span></li>
                    <li><span class="userLiDetailsSpan">
                    <img src="${flecheFields}" class="flecheimg child-img" style="width: 12px; margin-right: 6px;">
                    ${user.product.category.name}</span></li>
                    <li><span class="userLiDetailsSpan">
                    <img src="${flecheFields}" class="flecheimg child-img" style="width: 12px; margin-right: 6px;">
                    ${user.etat.description}</span></li>
                </ul>
            </li>
        `;
    });

    htmlContent += '</ul>';
    return htmlContent;
}
function getBorderColor(etat) {
    let borderColor;
    switch (etat) {
        case 1:
            borderColor = 'green';
            break;
        case 2:
            borderColor = 'red';
            break;
        case 3:
            borderColor = 'yellow';
            break;
        case 4:
            borderColor = 'orange';
            break;
        default:
            borderColor = 'gray';
    }
    return borderColor;
}

document.addEventListener('DOMContentLoaded', function() {
    document.addEventListener('click', function(e) {
        const target = e.target;
        if (target.classList.contains('user-detail') || target.parentNode.classList.contains('user-detail')) {
            const userDetail = target.classList.contains('user-detail') ? target : target.parentNode;
            const detailList = userDetail.querySelector('.detail-list');
            const isVisible = detailList.style.display === 'block';
            detailList.style.display = isVisible ? 'none' : 'block';
            userDetail.querySelector('span').setAttribute('aria-expanded', !isVisible);
        }
    });
});





async function fetchUserDetailsVente(userID){
    var url =`https://api.nomadcloud.fr/api/productions-details/users/${userID}?mois=${month}&annee=${year}&optionSelect=${Type}&page=1`;
    if(localStorage.getItem("clusterCode")!==null){
        url += `&codeCluster=${localStorage.getItem("clusterCode")}`;
    }
    if(localStorage.getItem("codInsee")!==null){
        url += `&codInsee=${localStorage.getItem("codInsee")}`;
    }
    try {
        const response = await fetch(url,{
            method:"GET",
            headers:{
                "Authorization": `Bearer ${jwtToken}`,
                "Content-Type": "application/json"
            }
        });
        if (response.status === 404) {
            return [];
        }

        const data = await response.json();
        return data;
    }catch (error) {
        console.error('Failed to fetch or parse data:', error);
        return [];
    }
}



async function createUserHiarchy(clustercode) {
    var treeUser = document.getElementById('tree-user');
    //treeUser.innerHTML = generateHierarchyHtmlForUsers(jsonData);
    // fetchUserHiarchy(clustercode).then(data => {
    //     treeUser.innerHTML = generateHierarchyHtmlForUsers(data);
    // });
}





document.addEventListener('DOMContentLoaded', function () {
    const resizers = document.querySelectorAll('.resizer');

    resizers.forEach((resizer) => {
        let startY, startHeightPrev, startHeightNext;

        resizer.addEventListener('mousedown', function (e) {
            const prevDiv = resizer.previousElementSibling;
            const nextDiv = resizer.nextElementSibling;

            // Store the initial mouse position and heights
            startY = e.pageY;
            startHeightPrev = prevDiv.offsetHeight;
            startHeightNext = nextDiv.offsetHeight;

            // Add mousemove and mouseup event listeners
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);

            function mouseMoveHandler(e) {
                const dy = e.pageY - startY;

                // Calculate new heights
                const newHeightPrev = Math.max(50, startHeightPrev + dy);
                const newHeightNext = Math.max(50, startHeightNext - dy);

                // Apply new heights to the sections
                prevDiv.style.height = `${newHeightPrev}px`;
                nextDiv.style.height = `${newHeightNext}px`;
            }

            function mouseUpHandler() {
                // Remove the event listeners when mouse is released
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            }
        });
    });
    document.getElementById('HandleRightPanel').addEventListener('click', function() {
        var sidebar = document.querySelector('.right-sidebar-concepteur');
        var sidebarStyle = window.getComputedStyle(sidebar);
        var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
        const detailsDiv = document.getElementById('UsersVenteDetails');
        if (sidebarStyle.right === '0px') {
            sidebar.style.right = '-319px';
            detailsDiv.style.right = '-318px';
            IconRightPanelToggle.style.transform = 'rotate(-90deg)';
        } else {
            sidebar.style.right = '0px';
            IconRightPanelToggle.style.transform = 'rotate(90deg)';
        }
    });
});


document.addEventListener('DOMContentLoaded', () => {
    const currentThemeDisplay = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const currentThemeIcon = currentThemeDisplay.querySelector('img');
    const currentThemeText = currentThemeDisplay.childNodes[1];

    let currentTheme = localStorage.getItem('theme') || 'dark'||'darkblue'||'lightsand'||'darklight'||"darkpurple";
    document.body.setAttribute('data-theme', currentTheme);

    const currentThemeElement = document.querySelector(`li[data-theme="${currentTheme}"]`);
    if (currentThemeElement) {
      updateThemeDisplay(currentThemeElement);
    }

    currentThemeDisplay.addEventListener('click', () => {
      themeOptions.style.display = themeOptions.style.display === 'block' ? 'none' : 'block';
    });

    themeOptions.addEventListener('click', event => {
      const themeChoice = event.target.closest('li');
      if (themeChoice) {
        const selectedTheme = themeChoice.getAttribute('data-theme');
        const imgSrc = themeChoice.querySelector('img').src;
        const themeName = themeChoice.textContent.trim();

        currentThemeIcon.src = imgSrc;
        currentThemeText.nodeValue = " " + themeName + " ";

        document.body.setAttribute('data-theme', selectedTheme);
        localStorage.setItem('theme', selectedTheme);

        themeOptions.style.display = 'none';

        document.querySelectorAll('.theme-options li').forEach(li => li.classList.remove('active'));
        themeChoice.classList.add('active');
      }
    });
  });
  
function updateThemeDisplay(themeElement) {
    const iconSrc = themeElement.querySelector('img').src;
    const iconName = themeElement.textContent.trim();
    document.querySelector('.current-theme img').src = iconSrc;
    document.querySelector('.current-theme').childNodes[1].nodeValue = " " + iconName + " ";
}
  
const toggleButtonTable = document.getElementById('toggle-Chart');
const TablePanel = document.getElementById('displayChartPanel');
let isTableVisible = false;

// Set the panel to be closed by default
TablePanel.style.bottom = '-330px';

toggleButtonTable.addEventListener('click', function () {
    TablePanel.style.transition='bottom 0.3s ease-in-out';
    TablePanel.style.height = '350px';
    var IcontoggleChart = document.querySelector('.IcontoggleChart');
    if (isTableVisible) {
        TablePanel.style.bottom = '-330px';
        setTimeout(() => IcontoggleChart.style.transform = 'rotate(0deg)', 300);
    } else {
        setTimeout(() => TablePanel.style.bottom = '0', 10);
        IcontoggleChart.style.transform = 'rotate(180deg)';
    }
    ResponsivePanelChart();

    isTableVisible = !isTableVisible;
    document.querySelectorAll('.ChartsByType').forEach(element => {
        if (TablePanel.style.height === '350px' || TablePanel.style.height === '') { 
           //element.classList.remove('ResponsivePanelChart');
            element.style.height = '100%';
        } else {
            element.style.height = '157px';
        }
    });
    drawAudienceChart();
    changeCanvaschartHeight(TablePanel);
});

function FullScreenPanelBottom() {
    applyResponsiveStyles();
    var blocMotifEchec=document.querySelector('.blocMotifEchec');
    var charts = document.querySelectorAll('.ChartsByType');
    const TablePanel = document.getElementById('displayChartPanel');
    TablePanel.style.transition='height 0.4s ease-in-out';
    if (TablePanel.style.height === '350px' || TablePanel.style.height === '') {
        TablePanel.style.height = '98%';
        blocMotifEchec.style.flexDirection='column';
    } else {
        TablePanel.style.height = '350px'; 
        blocMotifEchec.style.flexDirection='row';
    }
    ResponsivePanelChart();
    document.querySelectorAll('.ChartsByType').forEach(element => {
        if (TablePanel.style.height === '350px' || TablePanel.style.height === '') {            element.classList.remove('ResponsivePanelChart');
            element.style.height = '100%';
        } else {
            element.style.height = '157px';
        }
    });
    drawAudienceChart();
    changeCanvaschartHeight(TablePanel);
}
function ResponsivePanelChart() {
    var ContainerCohorteCards=document.querySelector('.ContainerCohorteCards');
    if (TablePanel.style.height === '350px' || TablePanel.style.height === '') {
        document.querySelectorAll('.ResponsivePanelChart').forEach(element => {
            element.classList.replace('ResponsivePanelChart', 'PanelChartUpDown');
        });
        ContainerCohorteCards.classList.remove('cohorteClass');
    } else {
        document.querySelectorAll('.PanelChartUpDown').forEach(element => {
            element.classList.replace('PanelChartUpDown', 'ResponsivePanelChart');
        });
        ContainerCohorteCards.classList.add('cohorteClass');
    }
    
}
function changeCanvaschartHeight(TablePanel) {
    var chartContent=document.querySelector('.chart-content')
    var chartR=document.getElementById('audienceChartStatcohorter');
    var chartV=document.getElementById('audienceChartStatcohortev');
    document.querySelectorAll('.chart-content').forEach(element => {
    if (TablePanel.style.height === '350px' || TablePanel.style.height === '') {
        // chartV.style.height = '90px';
        // chartR.style.height = '90px';
        element.style.height = '90px';
    } else {
        element.style.height ='120px';
        // chartV.style.height = '120px';
        // chartR.style.height = '120px';

    }
});
}

const openPanelButton = document.getElementById('openPanel');
const closePanelButton = document.getElementById('closePanel');
const productionPanel = document.getElementById('displayproductionPanel');

openPanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '0';
});

closePanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '-100%';
    closePanelButton.style.top='39px';
});

const openPanelConsoleButton = document.getElementById('openPanelConsole');
const closePanelButtonConsole = document.getElementById('closePanelConsole');
const productionPanelConsole = document.getElementById('displayproductionPanelConsole');

openPanelConsoleButton.addEventListener('click', function () {
    productionPanelConsole.style.bottom = '0';
});

closePanelButtonConsole.addEventListener('click', function () {
    productionPanelConsole.style.bottom = '-100%';
    closePanelButtonConsole.style.top='39px';
});
async function fetchHiearchicalData(){
    var url =`https://api.nomadcloud.fr/api/productions-consolidation/${userIdLogIn}/${Type}`;
    try {
        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        HierarchyData=data;
    } catch (error) {
        console.error('Error:', error);
        return null;
    }
}
async function recallFunctionUpdatedParam(){
    productionsByDay();
    localStorage.setItem('clusterCode', '');
    try {
        await fetchHiearchicalData();
        await fetchingProductionKPI();
        removeAllVilles();
        const treeRoot = document.getElementById('tree-root');
        let data = HierarchyData["0"][Number(year)][Number(month)];
        var itemChoosed=document.querySelector('.itemChoosed');
        var categoryFilter = itemChoosed.getAttribute('categoryFilter');
        if (categoryFilter==='Tous'||categoryFilter===''){categoryFilter=null;}
        const htmlContent = generateHierarchyHtml(data,categoryFilter);
        treeRoot.innerHTML = htmlContent;
        updateMapWithAllData(data);
        setupCaretListeners('tree-root');
        setupSearchFunctionality();
        setupDropdownListeners();
        fetchdataMonthweekdayhour();
        fetchProductionsIntervention();
        fetchProductionsAnnulation();
        applyBorderColors();
        fetchAllDatatest();
        fetchProductionKPI();
        fetchData();
        productionsTopFlop();
        fetchAndDrawChartsCohorte();
        fetchUserHiarchy();

    } catch (error) {
        console.error('Error in fetchingProductionKPI:', error);
    }
    drawChartproductionsMotif();
    fetchAnalystic();
    infoCardSatisfaction();
}


let allHiearchyData;
document.addEventListener("DOMContentLoaded", async function () {
    productionsByDay();
    localStorage.setItem('clusterCode', '');
    try {
        await fetchingProductionKPI(); // Wait for fetchProductionKPI to complete

        const treeRoot = document.getElementById('tree-root');
        //console.log("HierarchyData",HierarchyData);
        data = HierarchyData["0"][Number(year)][Number(month)];
        //console.log("HierarchyData",data);
        const htmlContent = generateHierarchyHtml(data);
        treeRoot.innerHTML = htmlContent;
        updateMapWithAllData(data);
        setupCaretListeners('tree-root');
        setupSearchFunctionality();
        setupDropdownListeners();
        fetchProductionsAnnulation();
        applyBorderColors();
        fetchUserHiarchy();
    } catch (error) {
        console.error('Error in fetchingProductionKPI:', error);
    }
    fetchAnalystic();
    fetchAndDrawChartsCohorte();
    infoCardSatisfaction();
});


function setupDropdownListeners() {
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    var ClusterFilter = document.querySelector('.ClusterFilter');
    const itemChoosed = document.querySelector('.itemChoosed');
    dropdownItems.forEach(item => {
        item.addEventListener('click', function () {
            var categoryFilter = this.classList[1];
            if (categoryFilter==='TousCluster'){categoryFilter=null;}
            data = HierarchyData[0][Number(year)][Number(month)];
            const htmlContent = generateHierarchyHtml(data, categoryFilter);
            itemChoosed.textContent = this.textContent;
            itemChoosed.setAttribute('categoryFilter', categoryFilter);
            ClusterFilter.style.display = 'none';
            const treeRoot = document.getElementById('tree-root');
            treeRoot.innerHTML = htmlContent;
            setupCaretListeners('tree-root');
            setupSearchFunctionality();
            setupDropdownListeners();
            applyBorderColors();
        });
    });
}


function generateHierarchyHtml(data, categoryFilter = null,FilterByEtatCluster = false) {
    allHiearchyData=data;
    let htmlContent = '';
    const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    
    data.forEach(cluster => {
        let clusterTotal;
        if(categoryFilter!==null){
            clusterTotal=cluster.categories[categoryFilter];
        } else{
            clusterTotal = Object.values(cluster.categories).reduce((sum, current) => sum + current, 0);
        }
        let placesContent = '';

        cluster.villes.forEach(place => {

            let totalVenteVille;
            if(categoryFilter!==null){
                totalVenteVille=place.categories[categoryFilter];
            } else{
                totalVenteVille =  Object.values(place.categories).reduce((sum, current) => sum + current, 0);
            }
            if (!categoryFilter || (place.categories[categoryFilter] && place.categories[categoryFilter] > 0)) {
                placesContent += `
                    <li class="field-list-item">
                        <div class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
                            <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                                <div class="fieldLiSpan" style="flex-grow: 1; display: flex; align-items: center;"
                                    data-cluster-code="${cluster.clusterCode}" data-insee-code="${place.code_insee}"
                                    onclick="saveClusterInsee('${cluster.clusterCode}', '${place.code_insee}','${place.ville}');fetchdataMonthweekdayhour('${cluster.clusterCode}', '${place.code_insee}'); fetchAllDatatest('${place.code_insee}');fetchproductionsMotif('${cluster.clusterCode}','${place.code_insee}');fetchProductionsIntervention('${cluster.clusterCode}', '${place.code_insee}');   fetchData('${cluster.clusterCode}', '${place.code_insee}');culsterinseeForOnedays('${cluster.clusterCode}','${place.code_insee}'); sendRightdataforChartDAys('${place.code_insee}',true); "
                                >
                                    <div style="display: flex; align-items: center;" >
                                        <svg style="height: 12px; width: 12px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d=M12.7,1.1c0-0.6-0.5-1.1-1.1-1.1H1.1C0.5,0,0,0.5,0,1.1v4.7h12.7V1.1z /><path d=M12.7,11.6V6.9H0v4.7c0,0.6,0.5,1.1,1.1,1.1h10.4C12.2,12.7,12.7,12.2,12.7,11.6z /></g></svg>
                                    </div>
                                    ${place.ville}
                                </div>
                                <div class="total-place" style="margin-right: 15px; color: #326E78;">
                                    ${totalVenteVille || 0}
                                </div>
                            </div>
                        </div>
                    </li>
                `;
            }
        });

        if (placesContent !== '') {
            htmlContent += `
                <li class="formListItem">
                    <div class="caret formSpan ClusterSpan" data-CLusterCode="${cluster.clusterCode}"style="display: flex; justify-content: space-between; align-items: center; width: 100%;" 
                    onclick=" simulateClickByClusterCode('${cluster.clusterCode}');handleClusterClick('${cluster.clusterCode}','${cluster.libelleCluster}');fetchAllDatatest('${cluster.clusterCode}');fetchdataMonthweekdayhour('${cluster.clusterCode}'); culsterinseeForOnedays('${cluster.clusterCode}','');fetchproductionsMotif('${cluster.clusterCode}') ; "> 
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;">
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color); opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                                </div>
                                ${cluster.libelleCluster}
                            </div>
                            <div class="total-cluster" style="display: flex; align-items: center; margin-right: 5px; font-size: 16px;"
                                data-CLusterID="${cluster.cluster_id}" data-CLusterCode="${cluster.clusterCode}" data-CLusterName="${cluster.libelleCluster}">
                                ${clusterTotal}
                            </div>                            
                        </div>
                    </div>
                    <ul class="nested">${placesContent}</ul>
                </li>
            `;
        }
    });
    return htmlContent;
}
function culsterinseeForOnedays(cluster, codeinsee) {
    // Set the values in localStorage
    localStorage.setItem('clusterForOnedays', cluster);
    localStorage.setItem('codeinseeForOnedays', codeinsee);
  }
  
function saveClusterInsee(clusterCode, codInsee,clusterName) {
    simulateClickByVilleCode(codInsee);
    fetchUserHiarchy(clusterCode, codInsee); 
    productionsByDay(clusterCode, codInsee);
    let savedData = JSON.parse(localStorage.getItem("clusterInseeData")) || [];
    savedData.push({ clusterCode, codInsee });
    localStorage.setItem("clusterInseeData", JSON.stringify(savedData));
    localStorage.setItem("codInsee",codInsee);
    SetClusterName(clusterName);

}

function SetClusterName(Name) {
    document.querySelector('.NameClickedPlace span').textContent = Name;
}
async function handleClusterClick(clusterCode,name){
    // List of all asynchronous functions to execute
    SetClusterName(name);
    const tasks = [
        productionsByDay(clusterCode),
        fetchAnalystic(clusterCode),
        fetchMissingcities(clusterCode),
        createUserHiarchy(clusterCode),
        sendRightdataforChartDAys(clusterCode,false),
        //ProductionKPIByPointOfSaleId(clusterCode),
        fetchdataCluster(clusterCode),
        findClusterData(clusterCode),
        fetchInterventionsMigrable(clusterCode)
    ];

    try {
        // Wait for all tasks to complete
        const results = await Promise.all(tasks);
    } catch (error) {
        console.error('Error during operations:', error);
    }
}



function toggleClusterVisibility(event, clusterCode) {
    event.preventDefault();
    const clusterList = document.getElementById(`cluster-${clusterCode}`);
    const isVisible = clusterList.style.display === 'block';
    clusterList.style.display = isVisible ? 'none' : 'block';

    // Optional: Change icon or styles if needed
    const icon = event.currentTarget.querySelector('i.bi-chevron-down');
    if (icon) {
        icon.className = isVisible ? 'bi bi-chevron-down' : 'bi bi-chevron-up';
    }
}



async function fetchAnalystic(clusterCode=null) {
    //const date = `month=${month}&year=${year}`; // Ensure `month` and `year` are defined
    var baseUrl = `https://api.nomadcloud.fr/api/productions-analytics/${pointOfSaleId}?month=${month}&year=${year}&optionSelect=${Type}`;
    var url = new URL(baseUrl);

    url.searchParams.append('month', month);
    url.searchParams.append('year', year);
    url.searchParams.append('optionSelect', Type);
    url.searchParams.append('page', '1');

    if (clusterCode !== null) {
        url.searchParams.append("codeCluster", clusterCode);
    }

    try {
        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        displayAnalystic(data);
    } catch (error) {
        console.error('Error:', error);
        return null;
    }
}

function displayAnalystic(data) {
    var ventes =document.querySelector('.ventes');

    if(data){
       // prises.style.display="none";
        ventes.style.display="flex";
        var totalVentas = document.querySelector('.totalventes');
        if (totalVentas) totalVentas.textContent = data.total_ventes;
    
        var moyGeneral = document.querySelector('.MoyGeneralVente');
        if (moyGeneral) moyGeneral.textContent = parseFloat(data.note_satisfaction).toFixed(2);
    
        var nbVendeur = document.querySelector('.Nbvendeur');
        if (nbVendeur) nbVendeur.textContent = data.nombre_vendeurs;
    
        var etpElement = document.querySelector('.etp');
        if (etpElement) etpElement.textContent = data.etp;
    
        var ttVentesKo = document.querySelector('.ttventesko');
        if (ttVentesKo) ttVentesKo.textContent = data.total_ventes_ko;
    
        var ttObjectif = document.querySelector('.ttobjectif');
        ttObjectifNumber=parseFloat(data.total_objectif);
        if (ttObjectif) ttObjectif.textContent = ttObjectifNumber;
    
        var projectionElement = document.querySelector('.projection');
        var projection=parseFloat(data.projection).toFixed(0);
        if (projectionElement) projectionElement.textContent = projection;
        document.querySelector('.productionsByDayProd').textContent = (Math.round(data.moyenne_journalliere)|| '0')+'/J';
        if(data.total_ventes===0 ||ttObjectifNumber===0){
            document.querySelector('.VenteObjectif').textContent ='0%';
        }else{
            document.querySelector('.VenteObjectif').textContent = parseFloat((data.total_ventes/ttObjectifNumber)*100).toFixed(2) + '%';
        }
        
    }
}

    function setupSearchFunctionality() {
        const searchInput = document.getElementById('TreeSearch');
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase().trim();
            filterFormListItems(searchText);
        });
    }
    
    function filterFormListItems(searchText) {
        const formListItems = document.querySelectorAll('.formListItem');
    
        if (searchText.length < 3) {
            // Show all items if the search text length is less than 3
            formListItems.forEach(item => {
                item.style.display = '';
                item.querySelectorAll('.field-list-item').forEach(subItem => subItem.style.display = '');
            });
        } else {
            formListItems.forEach(item => {
                let hasMatch = false;
                const placeItems = item.querySelectorAll('.field-list-item');
    
                // Check each place within the cluster for a match
                placeItems.forEach(subItem => {
                    const placeName = subItem.querySelector('.fieldLiSpan').textContent.toLowerCase();
                    if (placeName.includes(searchText)) {
                        subItem.style.display = '';
                        hasMatch = true; // Mark as match if any place matches
                    } else {
                        subItem.style.display = 'none';
                    }
                });
    
                // Check the cluster name itself
                const clusterName = item.querySelector('.formSpan').textContent.toLowerCase();
                if (clusterName.includes(searchText) || hasMatch) {
                    item.style.display = ''; // Show the cluster if it or any place matches
                } else {
                    item.style.display = 'none'; // Hide the cluster if no places match
                }
            });
        }
    }

let totalGeneral = 0;
let totalDistanceSum = 0;
let somme = 0;
let decouche =false ;
function handleClick(totalprises, checkbox) {
    // Vérifie si la checkbox est cochée
    const isChecked = checkbox.checked;
    const totalDistance = parseFloat(localStorage.getItem("totalDistance"));

    // Ajoute ou soustrait la valeur de la checkbox au total général
    if (isChecked) {
        totalGeneral += parseInt(totalprises); // Ajoute si la checkbox est cochée
        totalDistanceSum += totalDistance;
        somme++;
        decouche = true;
    } else {
        totalGeneral -= parseInt(totalprises); // Soustrait si la checkbox est décochée
        totalDistanceSum -= totalDistance;
        somme--;
        decouche = false
    }

    // Sauvegarde les valeurs dans le localStorage
    localStorage.setItem('isCheckedsomme', somme);
    localStorage.setItem('totalGeneral', totalGeneral);
    localStorage.setItem('totalDistanceSum', totalDistanceSum.toFixed(2));
    
    // Sauvegarde isChecked dans localStorage
    localStorage.setItem('isChecked', isChecked ); // Si isChecked est vrai, enregistre "true", sinon "false"
    localStorage.setItem('decouche', decouche );
}


function saveToLocalStorages(nomVoie, complement, backgroundColor, totalprises, spanElement) { 
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;

    // Sauvegarde des informations dans localStorage
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises);
    localStorage.setItem('isChecked', isChecked);
    // Récupération des voies sauvegardées
    let savedVoies = JSON.parse(localStorage.getItem('savedVoies')) || [];

    // Vérifier si la voie est déjà enregistrée pour éviter les doublons
    if (!savedVoies.includes(nomVoie)) {
        savedVoies.push(nomVoie);
        localStorage.setItem('savedVoies', JSON.stringify(savedVoies));
    }
}


/**** */


function updateTotales() {
      // Mise à jour des totaux des lieux
      document.querySelectorAll('.total-place').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;

        element.textContent = sum || totalPrises || 0;
    });
    // Mise à jour des totaux des rues
    document.querySelectorAll('.total-street').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;

        element.textContent = sum || totalPrises || 0;
    });
}

document.querySelectorAll('#fileUploadForm input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', updateTotales);
});

const colorMap = new Map();

function getColorForCodeIris(code_iris) {
    if (!colorMap.has(code_iris)) {
        // Alterne entre rouge et bleu de manière cohérente
        const color = colorMap.size % 2 === 0 ? '#094044' : '#094044';
        colorMap.set(code_iris, color);
    }
    return colorMap.get(code_iris);
}

function generateHierarchyHtmlscuivre(data) {
    let htmlContent = '';
    const years = Object.keys(data).sort((a, b) => b - a); // Tri des années en ordre décroissant

    years.forEach(year => {
        const yearData = data[year][1]; // Accès aux données de l'année
        const voies = yearData.voies; // Liste des 'voies' pour cette année

        voies.forEach(voie => {
            // Obtenir une couleur unique pour chaque code_iris
            const backgroundColor = getColorForCodeIris(voie.code_iris);

            // Construire le HTML pour chaque 'voie'
            htmlContent += `
                <li class="formListItem" style="background-color:  var(--nested-bg, #f9f9f9); border-right: 2px ${backgroundColor}; ">
                    <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span class="formSpanvoie" data-nom-voie="${voie.nom_voie}" style="display: flex; align-items: center;"
                            onclick="selectVoieAndSearchStreet('${voie.nom_voie || 'N/A'},${voie.complement}, ${backgroundColor}, ${voie.total_prises || 0},this')">
                             <div class="" style="margin-left: 15px;">
                            <div style="margin-left: 15px;">
                            <input class="checkbox-voie" data-nomvoie= "${street.nom_voie }"onclick="handleClick( '${total}',this)"  id="checkbox8" type="checkbox">
                            </div>
                            </div>
                            <div style="height: 12px; width: 12px; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                            ${voie.nom_voie || 'N/A'}
                        </span>
                        <div style="display: flex; color: ${backgroundColor}; align-items: center; margin-right: 15px; font-size: 14px;">
                            ${voie.total_prises || 0} <!-- Afficher totalPrises ou 0 si non défini -->
                        </div>
                    </span>
                </li>
            `;
        });
    });

    return htmlContent; // Retourne le HTML généré
}


function selectVoieAndSearchStreet(nomVoie,complement,backgroundColor,totalprises,spanElement) {
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises); 
    localStorage.setItem('isChecked', isChecked);
}
function toggleStyle(element, isChecked) {
    const span = element.querySelector('span');

    // Toggle background color and border
    if (span.style.backgroundColor === 'rgb(45, 140, 235)') {
        span.style.backgroundColor = 'var(--nav-link-active-bg)';
        span.style.border = '4px solid var(--bg-table-section)';

    } else {
        span.style.backgroundColor = '#2D8ceb';
        span.style.border = '4px solid var(--bg-table-section)';
  
    }
}


    
function setupCaretListeners(rootElementId) {
    const rootElement = document.getElementById(rootElementId);
    const togglers = rootElement.querySelectorAll(".caret, .formSpan");
    
    togglers.forEach(caret => {
        caret.addEventListener("click", function(event) {
            event.stopPropagation();
    
            let nestedUl = this.closest('li').querySelector('.nested');
            if (nestedUl) {
                nestedUl.classList.toggle("active");
            }
    
            document.querySelectorAll('.active-field').forEach(el => {
                el.classList.remove('active-field');
            });
            this.classList.add('active-field');
    
            this.classList.toggle("caret-down");
        });
    });
}

function setupSearchFunctionalityRues() {
    const searchInput = document.getElementById('TreeSearchRues');
    searchInput.addEventListener('input', function() {
        const searchText = this.value.toLowerCase().trim();
        filterFormListItemsRues(searchText);
    });
}
    
function filterFormListItemsRues(searchText) {
    const formListItems = document.querySelectorAll('.formListItem');

    if (searchText.length < 3) {
        // Afficher tous les éléments si la recherche contient moins de 3 caractères
        formListItems.forEach(item => {
            item.style.display = '';
        });
    } else {
        formListItems.forEach(item => {
            const streetNameElement = item.querySelector('.formSpanvoie');
            if (streetNameElement) {
                const streetName = streetNameElement.textContent.toLowerCase();
                if (streetName.includes(searchText)) {
                    item.style.display = ''; // Afficher l'élément si le nom correspond
                } else {
                    item.style.display = 'none'; // Cacher sinon
                }
            }
        });
    }
}

const treeViewIcon = document.getElementById('TreeViewIcon');
const fleche = '/discord/treeview/Fleche.svg';
const flecheFields = treeViewIcon ? treeViewIcon.getAttribute('flecheFields') : '';





let map;

document.addEventListener("DOMContentLoaded", function () {
    let theme = localStorage.getItem("theme") || "darkpurple";
    mapboxgl.accessToken = 'pk.eyJ1IjoicmdvdW50aXRpIiwiYSI6ImNtMnA1bHJ5NDBuczcycnNieGsyamVjOTMifQ.FjXmzR2E_Di8YWn8nfTPog';

    function initializeMap(theme) {
        let MapTheme;
        if (theme === "darkpurple") {
            MapTheme = 'mapbox://styles/rgountiti/cm70iodpa01iu01sa8ihf5wch';
        } else if (theme === "darkblue") {
            MapTheme = 'mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw';
        } else {
            MapTheme = 'mapbox://styles/mapbox/light-v10';
        }

        const mapContainer = document.getElementById('map');
        mapContainer.innerHTML = '';

        map = new mapboxgl.Map({
            container: 'map',
            style: MapTheme,
            center: [2.3522, 48.8566],
            zoom: 6
        });
        map.addControl(new mapboxgl.NavigationControl());
    }


    initializeMap(theme);

    setInterval(() => {
        let newTheme = localStorage.getItem("theme");
        if (newTheme !== theme) {
            theme = newTheme;
            if (theme === "darkpurple") {
                location.reload();
            } else if (theme === "light") {
                location.reload();
            }
            else if (theme === "darkblue") {
                location.reload();
            }
            else {
                map.setStyle('mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw'); // Update the map style
            }
        }
    }, 500);
});

function infoCardByPolygon(ville,villesName, coordinates) {

    findClusterDataForUser(localStorage.getItem('clusterCode'),ville);
    //getTotalPriseVenteByVille(localStorage.getItem('clusterCode'),ville);

    const card = document.getElementById("info-card-ByPolygon");
    card.style.display = "block";
    var villesTest=document.querySelector('.villesTest');
    villesTest.textContent=villesName;

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return;
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
    });
    displayInterventionsMigrable(ville);
}
async function infoCardByPolygonCluster(cluster,villesName, coordinates) {
    //getTotalPriseVenteByVille(localStorage.getItem('clusterCode'),ville);
    const card = document.getElementById("info-card-ByPolygon");
    card.style.display = "block";
    var villesTest=document.querySelector('.villesTest');
    villesTest.textContent=villesName;

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return;
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
    });
    displayInterventionsMigrable(ville);
}

async function infoCardSatisfaction() {
    const card = document.getElementById("info-card-statisfaction");
    const satisfactionCard = document.querySelector(".statisfactionCard");
    const data = await displaySatisfactionDetails();

    satisfactionCard.innerHTML = '';
    adjustContainerHeight(satisfactionCard);  // Set initial container properties

    for (const item of data) {
        const orderElement = document.createElement('div');
        orderElement.textContent = `Order Number: ${item.orderNumber}`;
        orderElement.style.padding = '10px';
        satisfactionCard.appendChild(orderElement);

        const verbatimElement = document.createElement('div');
        verbatimElement.style.padding = '10px';
        satisfactionCard.appendChild(verbatimElement);

        await typeText(verbatimElement, item.verbatim);
    }
    setupDraggableCard(card);
}
function adjustContainerHeight(container) {
    const maxHeight = 200;
    container.style.maxHeight = `${maxHeight}px`;
    container.style.overflowY = 'auto';
}



function typeText(element, text) {
    return new Promise((resolve) => {
        if (!text) {
            element.textContent = "aucune note";
            checkAndScrollIntoView(element);
            resolve();
            return;
        }

        let index = 0;
        function typeChar() {
            if (index < text.length) {
                element.textContent += text.charAt(index);
                index++;
                if (index === text.length || index % 20 === 0) {
                    checkAndScrollIntoView(element);
                }
                setTimeout(typeChar, 40);
            } else {
                resolve();
            }
        }

        typeChar();
    });
}

function checkAndScrollIntoView(element) {
    setTimeout(() => {
        const container = element.parentElement;  // Reference to the parent container
        container.scrollTop = container.scrollHeight;  // Scroll the container to the bottom
    }, 0);
}






function setupDraggableCard(card) {
    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";
    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Only react to left-clicks
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none"; // Remove transitions during drag
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Reapply transitions
    });
}

async function displaySatisfactionDetails() {
    const date = `${year}-${String(month).padStart(2, '0')}`;
    const url=`https://api.nomadcloud.fr/api/satisfaction-clients-details/${pointOfSaleId}?yearMonth=${date}&note=3&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json(); // Make sure to await the parsing of the JSON


        return data; // You might return this if needed elsewhere
    } catch (error) {
        console.error("Error in API fetchingProductionKPI:", error.message);
    }
}
function getClusterByCode(code) {
    const cluster = allHiearchyData.find(cluster => cluster.clusterCode === code);
    updateMapWithVilles(cluster);updateMapWithVillesWithoutProduction();
}



function addVilleLayer(ville) {
    if (map.getLayer(ville.ville)) {
        console.warn('Layer already exists:', ville.ville);
        return;
    }
    map.addLayer({
        'id': ville.ville,
        'type': 'fill',
        'source': {
            'type': 'geojson',
            'data': {
                'type': 'Feature',
                'geometry': ville.polygon
            }
        },
        'layout': {},
        'paint': {
            'fill-color': '#088',
            'fill-opacity': 0.5
        }
    });
}


let dataByCPVProduction;

async function fetchingProductionKPI() {
    const url = `https://api.nomadcloud.fr/api/objectifs-clusters-productions?mois=${month}&annee=${year}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json(); // Make sure to await the parsing of the JSON
        dataByCPVProduction = data.find(item => item.cpv === cpv);

        // You can continue processing here or call another function
        // getClustersWithoutSales(); // Uncomment if needed

        return data; // You might return this if needed elsewhere
    } catch (error) {
        console.error("Error in API fetchingProductionKPI:", error.message);
    }
}

function getClustersWithoutSales() {
    if (dataByCPVProduction && HierarchyData) {
        console.log(' getClustersWithoutSales Data is available');
    } else {
        console.log(' getClustersWithoutSales Data is not available');
        return;
    }

    const ClustersWithoutSales = dataByCPVProduction.clusters.filter(cluster => {
        return cluster.objectifCluster.pourcentageVente === '0%';
    });

    const ClustersWithout = ClustersWithoutSales.map(cluster => {
        const match = HierarchyData.find(hierarchy => hierarchy.clusterCode === cluster.codeCluster);
        return match;
    }).filter(cluster => cluster !== undefined);

}


function applyBorderColors() {
    const clusters = document.querySelectorAll('.ClusterSpan');

    clusters.forEach(cluster => {
        const clusterCode = cluster.getAttribute('data-CLusterCode');
        const dataCluster = dataByCPVProduction.clusters.find(c => c.codeCluster === clusterCode);
        if (dataCluster && dataCluster.objectifCluster && dataCluster.objectifCluster.pourcentageVente) {
            const pourcentageVente = parseFloat(dataCluster.objectifCluster.pourcentageVente.replace('%', ''));
            let borderColor = '';

            if (pourcentageVente >= 100) {
                borderColor = 'green';
            } else if (pourcentageVente >= 80) {
                borderColor = 'yellow';
            } else if (pourcentageVente >= 51) {
                borderColor = 'orange';
            } else if (pourcentageVente <= 49) {
                borderColor = 'red';
            }

            if (borderColor) {
                cluster.style.borderRight = `3px solid ${borderColor}`; // Apply the border color

            }
        }
    });
}
document.addEventListener("DOMContentLoaded", () => {
    const dropdownIcon = document.querySelector(".icon-dropdown");
    const dropdownContent = document.querySelector(".content-dropdown");

    // Show/Hide the menu and change the icon on click
    dropdownIcon.addEventListener("click", () => {
        const isDisplayed = dropdownContent.style.display === "block";
        dropdownContent.style.display = isDisplayed ? "none" : "block";

        // Toggle icon between "chevron-down" and "x-lg"
        if (isDisplayed) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });

    // Handle custom navigation on link click
    const dropdownLinks = document.querySelectorAll(".content-dropdown a");
    dropdownLinks.forEach(link => {
        link.addEventListener("click", (event) => {
            event.preventDefault();  // Prevent default anchor behavior
            const path = link.getAttribute("data-path");
            window.location.href = path;  // Manually navigate to the path
        });
    });
});

// async function fetchdataMonthweekdayhour() {
//     try {
//         if (!cpv) {
//             throw new Error("Le paramètre 'cpv' est manquant.");
//         }
//         if (!jwtToken) {
//             throw new Error("Le token JWT est manquant.");
//         }

//         let url = `https://api.nomadcloud.fr/api/productions-by-month-week-day-hour/${pointOfSaleId}?year=${year}&optionSelect=${Type}&page=1`;
//         const response = await fetch(url, {
//             method: 'GET',
//             headers: {
//                 'Authorization': `Bearer ${jwtToken}`,
//                 'Content-Type': 'application/json'
//             }
//         });

//         if (!response.ok) {
//             console.error('HTTP error!', response.status, response.statusText);
//             throw new Error(`HTTP error! Status: ${response.status}`);
//         }

//         const data = await response.json();


//         return data;
//     } catch (error) {
//         console.error('Fetch error:', error);
//         return null;
//     }
// }

// fetchdataMonthweekdayhour();
let Interventionsmigrable;

async function fetchInterventionsMigrable(clusterCode) {
    const url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/${cpv}?codeCluster=${clusterCode}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`, // Ensure jwtToken is defined and valid
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        Interventionsmigrable = await response.json();
        displayTP(Interventionsmigrable.taux_penetration);
        return Interventionsmigrable;
    } catch (error) {
        console.error('Error fetching interventions migrable:', error);
        return null;
    }
}
function displayTP(data){
    console.log('displayTP',data);
    document.querySelector('.TauxPenetration').textContent =data ;
}

async function displayInterventionsMigrable(Code) {
    let dataToDisplay;
    let data = Interventionsmigrable;
    const codeNumeric = parseInt(Code, 10);

    dataToDisplay = data.villes.find(v => v.cod_insee === codeNumeric);
    updateDisplayValues(dataToDisplay);
    displayTP(dataToDisplay.taux_penetration);
}

function updateDisplayValues(data) {

    const keys = ['nb_fyr_fttb', 'nb_fyr_adsl', 'nb_fyr_mob_mono', 'nb_fyr_mob_multi_thd', 'nb_fyr_mob_multi_adsl', 'nb_fyr_thd'];
    keys.forEach(key => {
        const element = document.querySelector(`.${key}`);
        if (element) {
            element.textContent = data && data.hasOwnProperty(key) ? data[key] : '-';
        }
    });
}

/******handling map clicking */
let MissingCities;
async function fetchMissingcities(clusterCode) {
    const url = `https://api.nomadcloud.fr/api/productions-missing-cities/${pointOfSaleId}/${clusterCode}/${year}/${month}?optionSelect=${Type}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        MissingCities = await response.json();

        getClusterByCode(clusterCode);
    } catch (error) {
        console.error('Error fetching interventions migrable:', error);
        return null;
    }
}

function updateMapWithVilles(data) {

    if (!data.villes || !Array.isArray(data.villes)) {
        console.error("Invalid data: missing 'villes'");
        return;
    }

    const sourceId = 'villes-production';
    const layerIdFill = 'villes-production-fill';
    const layerIdOutline = 'villes-production-outline';

    const geojsonData = {
        type: "FeatureCollection",
        features: data.villes.map(ville => ({
            type: "Feature",
            id: ville.code_insee,
            properties: {
                ville: ville.code_insee,
                villeName: ville.ville
            },
            geometry: {
                type: "Polygon",
                coordinates: [ville.polygon.coordinates[0]]
            }
        }))
    };

    if (map.getSource(sourceId)) {
        map.getSource(sourceId).setData(geojsonData);
    } else {
        setupNewSourceAndLayers(sourceId, geojsonData, '#088'); // Assuming setupNewSourceAndLayers abstracts layer setup
    }
    updateBoundsAndEvents(sourceId, 'villes-production-fill');
}

function updateMapWithVillesWithoutProduction() {

    if (!MissingCities || !MissingCities.villes || !Array.isArray(MissingCities.villes)) {
        console.error("Invalid data: MissingCities is undefined or missing 'villes'");
        return;
    }

    const sourceId = 'villes-missing';
    const layerIdFill = 'villes-missing-fill';

    const geojsonData = createGeoJSONData(MissingCities);

    if (map.getSource(sourceId)) {
        map.getSource(sourceId).setData(geojsonData);
    } else {
        setupNewSourceAndLayers(sourceId, geojsonData, '#ffb5b5'); // Color for missing production
    }
    updateBoundsAndEvents(sourceId, 'villes-missing-fill');
}

function updateBoundsAndEvents(sourceId, layerIdFill) {
    const bounds = new mapboxgl.LngLatBounds();
    const source = map.getSource(sourceId);

    if (source && source._data && source._data.features) {
        source._data.features.forEach(feature => {
            feature.geometry.coordinates.forEach(polygon => {
                polygon.forEach(coord => {
                    if (coord && coord.length >= 2) {
                        bounds.extend(coord);
                    } else {
                        console.error('Invalid coordinate:', coord);
                    }
                });
            });
        });

        try {
            map.fitBounds(bounds, { padding: 20, maxZoom: 9 });
        } catch (error) {
            console.error('Error fitting bounds:', error);
        }

        map.on('click', layerIdFill, function(e) {
            if (e.features.length > 0) {
                const feature = e.features[0];
            }
        });
    } else {
        console.error('Source data is not loaded or is invalid:', sourceId);
    }
}


function createGeoJSONData(data) {
    if (!data || !Array.isArray(data.villes)) {
        console.error('Data is undefined or not correctly structured:', data);
        return null; // Return null or an empty geoJSON structure if data is not available
    }

    return {
        type: "FeatureCollection",
        features: data.villes.map(ville => {
            if (!ville.polygon || !Array.isArray(ville.polygon.coordinates) || ville.polygon.coordinates.length === 0) {
                console.error('Malformed or missing coordinates for ville:', ville.ville);
                return null;
            }
            return {
                type: "Feature",
                id: ville.code_insee,
                properties: {
                    ville: ville.code_insee,
                    villeName: ville.ville
                },
                geometry: {
                    type: "Polygon",
                    coordinates: ville.polygon.coordinates
                }
            };
        }).filter(feature => feature !== null)
    };
}

function setupNewSourceAndLayers(sourceId, geojsonData, fillColor) {
    if (!map.getSource(sourceId)) {
        map.addSource(sourceId, {
            type: "geojson",
            data: geojsonData
        });
    }

    if (!map.getLayer(`${sourceId}-fill`)) {
        map.addLayer({
            id: `${sourceId}-fill`,
            type: "fill",
            source: sourceId,
            layout: {},
            paint: {
                'fill-color': fillColor,
                'fill-opacity': 0.5
            }
        });
    }

    if (!map.getLayer(`${sourceId}-outline`)) {
        map.addLayer({
            id: `${sourceId}-outline`,
            type: "line",
            source: sourceId,
            layout: {},
            paint: {
                'line-color': [
                    'case',
                    ['boolean', ['feature-state', 'selected'], false],
                    '#55C5D0', // Color when selected
                    'transparent' // Default (non-selected) color
                ],
                'line-width': [
                    'case',
                    ['boolean', ['feature-state', 'selected'], false],
                    3, // Width when selected
                    0  // Default (non-selected) width
                ]
            }
        });
    }

    setupClickHandling(sourceId, `${sourceId}-fill`);
}

function setupClickHandling(sourceId, layerIdFill) {
    map.on('click', layerIdFill, function(e) {
        if (e.features.length > 0) {
            const feature = e.features[0];

            resetAllSelections();

            map.setFeatureState(
                { source: sourceId, id: feature.id },
                { selected: true }
            );

            const coordinates = feature.geometry.coordinates[0];
            const villeCode = feature.properties.ville;
            const villeName = feature.properties.villeName;
            console.log('Call infoCardByPolygon 1841');
            infoCardByPolygon(villeCode, villeName, coordinates);
        }
    });
}

function resetAllSelections() {
    // Reset states for all sources that might have selected features
    ['villes-production', 'villes-missing'].forEach(source => {
        const sourceData = map.getSource(source);
        if (sourceData) {
            sourceData._data.features.forEach(feature => {
                map.setFeatureState(
                    { source, id: feature.id },
                    { selected: false }
                );
            });
        }
    });
}
function simulateClickByVilleCode(villeCode) {

    const sourceId = 'villes-production';
    const layerIdFill = `${sourceId}-fill`;

    const source = map.getSource(sourceId);
    if (!source) {
        console.error('Source not found:', sourceId);
        return;
    }

    const feature = source._data.features.find(f => f.properties.ville === villeCode);
    if (!feature) {
        console.error('Ville not found with code:', villeCode);
        return;
    }

    resetAllSelections();

    map.setFeatureState(
        { source: sourceId, id: feature.id },
        { selected: true }
    );

    const coordinates = feature.geometry.coordinates[0];
    const villeName = feature.properties.villeName;
    console.log('Call infoCardByPolygon 1887');

    infoCardByPolygon(villeCode, villeName, coordinates);
}
function simulateClickByClusterCode(ClusterCode) {
    removeAllVilles();
    findClusterDataForUser(ClusterCode);
    //infoCardByPolygonCluster(ClusterCode, 'villeName', 'coordinates');
}
/******end handling map clicking */
async function fetchdataMonthweekdayhour(clusterData,codeInsee) {
    try {
        let url = `https://api.nomadcloud.fr/api/productions-by-month-week-day-hour/${pointOfSaleId}?year=${year}&optionSelect=${Type}&page=1`;
        if (clusterData) {
            url += `&codeCluster=${clusterData}`;
        }
        if (codeInsee) {
            url += `&codeInsee=${codeInsee}`;
        }
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        // 🎯 Filtrer les heures pour exclure 0h à 7h
        const ventes_par_heure_filtrées = Object.fromEntries(
            Object.entries(data.ventes_par_heure)
                .filter(([heure, _]) => !(heure >= 0 && heure < 8)) // Exclut 0h à 7h
        );
        const selectedTheme = localStorage.getItem("theme") ;
        const chartColor = selectedTheme === "light" ? "#145177" : "#54C5d0";

        // 📊 Display charts with the filtered data
        afficherGraphique('graph_ventes_par_mois', '', 'Mois', data.ventes_par_mois || {}, chartColor);
        afficherGraphique('graph_ventes_par_semaine', '', 'Semaine', data.ventes_par_semaine || {}, chartColor);
        afficherGraphique('graph_ventes_par_jour', '', 'Jour', data.ventes_par_jour || {}, chartColor);
        afficherGraphique('graph_ventes_par_heure', '', 'Heure', ventes_par_heure_filtrées, chartColor);

        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
}

function afficherGraphique(graphId, label, xLabel, data, color) {
    const canvas = document.getElementById(graphId);
    const ctx = canvas.getContext('2d');

    if (canvas.chartInstance) {
        canvas.chartInstance.destroy();
    }

    // 🏷️ Définition des labels pour les mois et les jours
    const labelsMois = ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'];
    const labelsJours = ['S','D','L', 'M', 'M', 'J', 'V',  ]; // Corrigé : Commence par "L"

    // 📊 Déterminer le type de graphique et adapter les labels
    let labels = Object.keys(data);
    
    if (graphId.includes('mois')) {
        labels = labels.map(num => labelsMois[parseInt(num) - 1] || num);
    } else if (graphId.includes('semaine')) {
        labels = labels.map(num => labelsJours[parseInt(num) % 7] || num);
    }
    const newChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,       // Labels formatés
            datasets: [{
                label: label,
                data: Object.values(data),
                borderColor: color,
                borderWidth: 2,
                fill: true,
                tension: 0.2,
                pointRadius: 0,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    grid: { display: false },
                    title: { display: true, text: xLabel, color: '#909090' }
                },
                y: {
                    title: { display: true, text: label, color: '#909090' },
                    grid: { color: 'rgba(255,255,255,0.1)' },
                    beginAtZero: true
                }
            },
            plugins: {
                legend: { display: false },
                tooltip: { enabled: true, intersect: false }
            }
        }
    });

    canvas.chartInstance = newChart;
}

fetchdataMonthweekdayhour();

/*************************************** renderEffectifs */
function createTreeElementEffectifs (user, level = 0) {
    const nodeElement = document.createElement('div');
    nodeElement.style.paddingLeft = `${level * 20}px`;
    nodeElement.style.cursor = 'pointer';
    nodeElement.textContent = `${user.prenom} ${user.nom}`;
    nodeElement.setAttribute('data-id', user.id);


    nodeElement.onclick = function(event) {
        event.stopPropagation();
        const childContainer = nodeElement.nextSibling;
        if (childContainer.style.display === 'none') {
            childContainer.style.display = 'block';
        } else {
            childContainer.style.display = 'none';
        }
    };

    const childrenContainer = document.createElement('div');
    childrenContainer.style.display = 'none';
    user.children.forEach(child => {
        childrenContainer.appendChild(createTreeElementEffectifs(child, level + 1));
    });

    const elementContainer = document.createElement('div');
    elementContainer.appendChild(nodeElement);
    elementContainer.appendChild(childrenContainer);

    return elementContainer;
}

async function findClusterDataForUser(codecluster, codeInsee) {

    fetchUserHiarchy(codecluster, codeInsee);  // Assuming fetchUserHierarchy doesn't need to be awaited or its result used here.
    var sidebar = document.querySelector('.right-sidebar-concepteur');
    var sidebarStyle = window.getComputedStyle(sidebar);
    var IconRightPanelToggle = document.querySelector('.IconRightPanelToggle');

    if (sidebarStyle.right !== '0px') {
        sidebar.style.right = '0px';
        IconRightPanelToggle.style.transform = 'rotate(90deg)';
    }

    var data = await fetchtotalClusterUser(codecluster, codeInsee);
    // Correct check for undefined data
    if (!data) {  // This will be true if data is undefined, null, or empty array
        return;  // Exit function if no data
    }

    try {
        var totalPrisesDistribuer = sumTotalPrises(data);
        var totalPrisesByVille = await getTotalPriseVenteByVille(codecluster, codeInsee);

        var distribuerPercentage = (totalPrisesDistribuer / totalPrisesByVille.total_prises) * 100;
        var remainingPercentage = 100 - distribuerPercentage;

        var chartData = [
            {
                categorie: 'Attribuer',
                totalVentes: distribuerPercentage,
                label: `Attribuer (${totalPrisesDistribuer})`
            },
            {
                categorie: 'Prises',
                totalVentes: remainingPercentage,
                label: `Prises (${totalPrisesByVille.total_prises})`
            }
        ];

        ChartPriseTotalDistrubuer(chartData);
        const container = document.getElementById('MiseEnpageCard');
        container.innerHTML = ''; // Clear previous content

        if (!Array.isArray(data) || data.length === 0) {
            console.warn("No data found.");
            container.textContent = 'Aucune donnée trouvée.';
            return;
        }

        container.innerHTML = generateHierarchyHtmlForUsers(data, false);
    } catch (error) {
        console.error('Error fetching or rendering data:', error);
    }
}

async function findClusterDataForUserCluster(codecluster, codeInsee) {
    fetchUserHiarchy(codecluster);
    var sidebar = document.querySelector('.right-sidebar-concepteur');
    var sidebarStyle = window.getComputedStyle(sidebar);
    var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
    if (sidebarStyle.right !== '0px'){
        sidebar.style.right = '0px';
        IconRightPanelToggle.style.transform = 'rotate(90deg)';
    }
    try {
        var data = await fetchtotalClusterUser(codecluster, codeInsee);
        var totalPrisesDistrubuer = sumTotalPrises(data);
        var totalPrisesByVille = await getTotalPriseVenteByVille(codecluster, codeInsee);
        
        // Example data
        // var totalPrisesByVille = [{ total_prises: 171186 }]; // Assumed to be an array with an object
        // var totalPrisesDistrubuer = 14250; // Example value for Distribuer

        var distribuerPercentage = (totalPrisesDistrubuer / totalPrisesByVille[0].total_prises) * 100;
        var remainingPercentage = 100 - distribuerPercentage;

        var chartData = [
            {
                categorie: 'Attribuer',
                totalVentes: distribuerPercentage, // This will be the percentage of the total
                label: `Attribuer (${totalPrisesDistrubuer})`
            },
            {
                categorie: 'Prises ',
                totalVentes: remainingPercentage, // Remaining percentage
                label: `Prises (${totalPrisesByVille[0].total_prises})`
            }
        ];

        ChartPriseTotalDistrubuer(chartData);
        const container = document.getElementById('MiseEnpageCard');
        container.innerHTML = ''; // Clear previous content

        if (!Array.isArray(data) || data.length === 0) {
            console.warn("No data found.");
            container.textContent = 'Aucune donnée trouvée.';
            return;
        }

        container.innerHTML = generateHierarchyHtmlForUsers(data,false);
    } catch (error) {
        console.error('Error fetching or rendering data:', error);
    }
}
function sumTotalPrises(data) {
    let total = 0;

    data.forEach(entry => {
        if (Array.isArray(entry.voies)) {
            total += entry.voies.reduce((sum, voie) => sum + parseInt(voie.total_prises, 10), 0);
        }

        if (Array.isArray(entry.children) && entry.children.length > 0) {
            total += sumTotalPrises(entry.children);
        }
    });

    return total;
}

async function fetchtotalClusterUser(codecluster, codeInsee) {
    //document.getElementById('MiseEnpageCard').innerHTML = '';
    try {
        let url = `https://api.nomadcloud.fr/api/interventions-places-total-by-user/${cpv}/${codecluster}?page=1`;
        if (codeInsee) {
            url += `&codeInsee=${codeInsee}`;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 404) {
                // Handle 404 specifically
                const responseData = await response.json();  // Parse the JSON to check the message
                if (responseData.error && responseData.error === "Aucun résultat trouvé pour les critères donnés") {
                    // If the specific error message is found, exit quietly
                    document.getElementById('MiseEnpageCard').innerHTML = '';  // Optionally update the UI silently
                    return;  // Exit without logging
                }
                // If another error message or not the specific one, you could log or handle differently here
            }
            return;  // Exit for all other non-OK responses if you decide not to handle them specifically
        }

        const data = await response.json();
        return data;  // Continue with normal processing if response is OK
    } catch (error) {
        document.getElementById('MiseEnpageCard').innerHTML = '';  // Handle network or parsing errors quietly
        return;  // Exit function quietly on catch
    }
}





async function getTotalPriseVenteByVille(codeCluster,codeInsee){
    const baseURL= `https://api.nomadcloud.fr/api/interventions-places-hierarchy/${cpv}?codeCluster=${codeCluster}&page=1`;
    let url = new URL(baseURL);
    if (codeInsee) {
        url.searchParams.set('codeInsee', codeInsee);
    }
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();

        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return;
    }
}

var PriseTotalDistrubuer;
function ChartPriseTotalDistrubuer(chartData) {
    function createChartPrise() {
        var bonustechnoCtx = document.getElementById('PriseTotalByDistrubuerChart').getContext('2d');

        if (PriseTotalDistrubuer) {
            PriseTotalDistrubuer.destroy();
        }

        var labels = chartData.map(item => item.label);
        var data = chartData.map(item => item.totalVentes);
        var backgroundColors = [ '#62eade','#145277']; // Colors for each segment

        PriseTotalDistrubuer = new Chart(bonustechnoCtx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'right'
                    }
                },
                cutout: '80%',
                animation: {
                    animateScale: true
                }
            }
        });
    }

    function createLegendPrise() {
        const legendContainer = document.querySelector('.Prises-legendChart');
        legendContainer.innerHTML = '';
        chartData.forEach(function(item, index) {
            const legendItem = document.createElement('li');

            const legendColor = document.createElement('span');
            legendColor.classList.add('bonustechno-legend-color');
            legendColor.style.background = index % 2 === 0 ?  '#62eade':'#145277' ;

            legendItem.appendChild(legendColor);
            legendItem.appendChild(document.createTextNode(item.categorie + ": " + item.totalVentes + ' (' + item.percentage + ')'));
            legendContainer.appendChild(legendItem);
        });
    }

    createChartPrise();
    createLegendPrise();
}

function FindClustersByEtat() {
    const icon = document.getElementById('FilterByEtatCluster');
    const modal = document.getElementById('modalByEtatCluster');

    icon.addEventListener('click', function () {
        modal.style.display = modal.style.display === 'flex' ? 'none' : 'flex';

        if (modal.style.display === 'flex') {
            const iconRect = icon.getBoundingClientRect();
            modal.style.top = `${iconRect.top + window.scrollY}px`;
            modal.style.left = `${iconRect.right + 20}px`;
        }
    });

    window.addEventListener('click', function (event) {
        if (!icon.contains(event.target) && !modal.contains(event.target)) {
            modal.style.display = 'none';
        }
    });

    modal.addEventListener('click', function (event) {
        if (event.target.classList.contains('FilterClusterByEtat')) {
            let categoryData = event.target.getAttribute('data');
            let dataClusters = HierarchyData[0][Number(year)][Number(month)]; // Define here
            let filteredData;

            if (categoryData === "CategoryAll") {
                filteredData = dataClusters;
            } else {
                const ClustersFiltered = filterClustersByCategory(dataClusters, dataByCPVProduction.clusters, categoryData);
                filteredData = getDataForFilteredClusters(dataClusters, ClustersFiltered);
            }

            const htmlContent = generateHierarchyHtml(filteredData);
            document.getElementById('tree-root').innerHTML = htmlContent;

            setupCaretListeners('tree-root');
            setupSearchFunctionality();
            setupDropdownListeners();
            applyBorderColors();
        }
    });
}

function filterClustersByCategory(dataClusters, clusters, categoryData) {
    console.log("Filtering clusters by category:", categoryData);

    if (categoryData === "CategoryHorsZone") {
        const existingClusterCodes = clusters.map(cluster => cluster.codeCluster);
        
        return dataClusters.filter(cluster => !existingClusterCodes.includes(cluster.clusterCode))
                           .map(cluster => cluster.clusterCode);
    }

    const { min, max } = getThresholdBounds(categoryData);
    if (min === 0 && max === 0) {
        return [];
    }

    return clusters.filter(cluster => {
        const percentage = parseFloat(cluster.objectifCluster.pourcentageVente.replace('%', ''));
        return percentage >= min && percentage < max;
    }).map(cluster => cluster.codeCluster);
}

function getThresholdBounds(categoryData) {
    const thresholds = {
        "Category100": { min: 100, max: Infinity },
        "Category80": { min: 80, max: 100 },
        "Category51": { min: 51, max: 80 },
        "Category49": { min: 0, max: 49 }
    };

    return thresholds[categoryData] || { min: 0, max: 0 };
}

function getDataForFilteredClusters(allClusters, filteredClusterCodes) {
    return allClusters.filter(cluster => filteredClusterCodes.includes(cluster.clusterCode));
}


// function FindClustersByEtat() {
//     const icon = document.getElementById('FilterByEtatCluster');
//     const modal = document.getElementById('modalByEtatCluster');

//     icon.addEventListener('click', function(event) {
//         modal.style.display = modal.style.display === 'flex' ? 'none' : 'flex';
//         if (modal.style.display === 'flex') {
//             const iconRect = icon.getBoundingClientRect();
//             modal.style.top = `${iconRect.top + window.scrollY}px`;
//             modal.style.left = `${iconRect.right + 20}px`;
//         }
//     });

//     window.addEventListener('click', function(event) {
//         if (!icon.contains(event.target) && !modal.contains(event.target)) {
//             modal.style.display = 'none';
//         }
//     });
//     let dataClusters = HierarchyData[0][Number(year)][Number(month)];
//     modal.addEventListener('click', function(event) {
//         if (event.target.classList.contains('FilterClusterByEtat')) {
//             let categoryData = event.target.getAttribute('data');

//             let filteredData;
//             if (categoryData==="CategoryAll"){
//                 filteredData=dataClusters;
//             }else if(categoryData==="CategoryHorsZone"){
//                 const ClustersFiltered = filterClustersByPercentage(dataByCPVProduction.clusters, categoryData);
//                 filteredData = getDataForFilteredClusters(dataClusters, ClustersFiltered);
//             }
//             else{
//                 const bounds = getThresholdBounds(categoryData);
    
//                 if (bounds.min === 0 && bounds.max === 0) {
//                     return;
//                 }
//                 const ClustersFiltered = filterClustersByPercentage(dataByCPVProduction.clusters, categoryData);
//                 filteredData = getDataForFilteredClusters(dataClusters, ClustersFiltered);

//             }
//             const htmlContent = generateHierarchyHtml(filteredData);
//             const treeRoot = document.getElementById('tree-root');
//             treeRoot.innerHTML = htmlContent;

//             setupCaretListeners('tree-root');
//             setupSearchFunctionality();
//             setupDropdownListeners();
//             applyBorderColors();
//         }
//     });
// }

// function getThresholdBounds(categoryData) {
//     switch (categoryData) {
//         case "Category100":
//             return { min: 100, max: Infinity };
//         case "Category80":
//             return { min: 80, max: 100 };
//         case "Category51":
//             return { min: 51, max: 80 };
//         case "Category49":
//             return { min: 0, max: 49 };
//         default:
//             console.error("Invalid category data: ", categoryData);
//             return { min: 0, max: 0 };
//     }
// }
// function filterClustersByPercentage(clusters, categoryData) {
//     console.log("Filtering clusters by percentage:", categoryData);
//     if (categoryData === "CategoryHorsZone") {
//         // Get all cluster codes that exist in dataByCPVProduction.clusters
//         const existingClusterCodes = clusters.map(cluster => cluster.codeCluster);
        
//         // Filter clusters that are NOT in existingClusterCodes
//         return dataClusters.filter(cluster => !existingClusterCodes.includes(cluster.clusterCode))
//                            .map(cluster => cluster.clusterCode);
//     } else {
//         const { min, max } = getThresholdBounds(categoryData);

//         return clusters.filter(cluster => {
//             const percentage = parseFloat(cluster.objectifCluster.pourcentageVente.replace('%', ''));
//             return percentage >= min && percentage < max;
//         }).map(cluster => cluster.codeCluster);
//     }
// }


// function filterClustersByPercentage(clusters, categoryData) {
//     const { min, max } = getThresholdBounds(categoryData);

//     return clusters.filter(cluster => {
//         const percentage = parseFloat(cluster.objectifCluster.pourcentageVente.replace('%', ''));
//         return percentage >= min && percentage < max;
//     }).map(cluster => cluster.codeCluster);
// }

// function getDataForFilteredClusters(allClusters, filteredClusterCodes) {
//     return allClusters.filter(cluster => filteredClusterCodes.includes(cluster.clusterCode));
// }
document.addEventListener("DOMContentLoaded", () => {
    localStorage.removeItem('clusterForOnedays');
    localStorage.removeItem('codeinseeForOnedays');
})

function infoCardOpneAi() {
    const card = document.getElementById("info-card-OpenAi");
    card.style.display = "block";

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return;
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
    });
}


const closePanelButtones = document.getElementById('closePanel');
const productionPaneles = document.getElementById('displayproductionPanel');


closePanelButtones.addEventListener('click', function () {
    productionPaneles.style.bottom = '-100%';
});








function updateMapWithAllData(allData) {
    if (!Array.isArray(allData) || allData.length === 0) {
        console.error("Invalid data: allData should be an array with elements");
        return;
    }

    allData.forEach(cluster => {
        if (!cluster.villes || !Array.isArray(cluster.villes)) {
            console.error(`Invalid data: missing 'villes' for cluster ${cluster.libelleCluster}`);
            return;
        }

        cluster.villes.forEach(ville => {
            if (!ville.polygon || !Array.isArray(ville.polygon.coordinates) || ville.polygon.coordinates.length === 0 || !ville.polygon.coordinates[0]) {
                // Skip this ville if coordinates are not properly formatted
                return;
            }

            const sourceId = `villes-${cluster.cluster_id}-${ville.code_insee}`;
            const layerIdFill = `${sourceId}-fill`;
            const layerIdOutline = `${sourceId}-outline`;

            const geojsonData = {
                type: "FeatureCollection",
                features: [{
                    type: "Feature",
                    id: ville.code_insee,
                    properties: {
                        ville: ville.code_insee,
                        villeName: ville.ville
                    },
                    geometry: {
                        type: "Polygon",
                        coordinates: [ville.polygon.coordinates[0]]  // Ensure correct structure
                    }
                }]
            };

            if (!map.getSource(sourceId)) {
                map.addSource(sourceId, {
                    type: "geojson",
                    data: geojsonData
                });
            } else {
                map.getSource(sourceId).setData(geojsonData);
            }

            if (!map.getLayer(layerIdFill)) {
                map.addLayer({
                    id: layerIdFill,
                    type: "fill",
                    source: sourceId,
                    layout: {},
                    paint: {
                        'fill-color': '#54C5d0',  // Adjust color as needed
                        'fill-opacity': 0.5
                    }
                });

                // Bind click event to the layer to handle clicking on villes
                map.on('click', layerIdFill, function(e) {
                    if (e.features.length > 0) {
                        const villeCode = e.features[0].properties.ville;
                        TestVilleCode(villeCode, cluster.cluster_id); // Pass cluster ID as well
                    }
                });
            }

            if (!map.getLayer(layerIdOutline)) {
                map.addLayer({
                    id: layerIdOutline,
                    type: "line",
                    source: sourceId,
                    layout: {},
                    paint: {
                        'line-color': [
                            'case',
                            ['boolean', ['feature-state', 'selected'], false],
                            '#55C5D0', // Color when selected
                            'transparent' // Default (non-selected) color
                        ],
                        'line-width': [
                            'case',
                            ['boolean', ['feature-state', 'selected'], false],
                            3, // Width when selected
                            0  // Default (non-selected) width
                        ]
                    }
                });
            }
        });
    });
}
function TestVilleCode(villeCode, clusterId) { // Accept clusterId as a parameter
    const sourceId = `villes-${clusterId}-${villeCode}`; // Use clusterId here
    const layerIdFill = `${sourceId}-fill`;

    const source = map.getSource(sourceId);
    if (!source) {
        console.error('Source not found:', sourceId);
        return;
    }

    const feature = source._data.features.find(f => f.properties.ville === villeCode);
    if (!feature) {
        console.error('Ville not found with code:', villeCode);
        return;
    }

    resetAllSelectionsVilles();

    map.setFeatureState(
        { source: sourceId, id: feature.id },
        { selected: true }
    );

    const coordinates = feature.geometry.coordinates[0];
    const villeName = feature.properties.villeName;
    console.log('Call infoCardByPolygon 2626');

    infoCardByPolygon(villeCode, villeName, coordinates);
}

function resetAllSelectionsVilles() {
    const sources = map.getStyle().sources;
    Object.keys(sources).forEach(sourceId => {
        if (sourceId.startsWith('villes-')) {
            const source = map.getSource(sourceId);
            if (source && source._data && source._data.features) {
                source._data.features.forEach(feature => {
                    map.setFeatureState(
                        { source: sourceId, id: feature.id },
                        { selected: false }
                    );
                });
            }
        }
    });
}





function removeAllVilles() {
    const mapSources = map.getStyle().sources;

    for (let sourceId in mapSources) {
        if (sourceId.startsWith('villes-')) {
            const layerIdFill = `${sourceId}-fill`;
            const layerIdOutline = `${sourceId}-outline`;

            if (map.getLayer(layerIdFill)) {
                map.removeLayer(layerIdFill);
            }
            if (map.getLayer(layerIdOutline)) {
                map.removeLayer(layerIdOutline);
            }

            map.removeSource(sourceId);
        }
    }
}




async function fetchproductionsMotif(codeCluster, codeInsee) {
    const date = formatDateRange(month, year); // Ensure this function correctly formats the date range as a string
    let url = `https://api.nomadcloud.fr/api/productions-by-lib-motif-instance/${pointOfSaleId}?${date}&page=1`;
    let params = '';

    // Append additional parameters only if they are provided and not undefined
    if (codeCluster) params += `&codeCluster=${codeCluster}`;
    if (codeInsee) params += `&codeInsee=${codeInsee}`;

    // Append parameters to the URL
    url += params;

    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error:', error);
        return null;
    }
}
function syncInputs(element) {
    var inputValue = element.value;  // Get the current value of the first input
    var targetInput = document.querySelector('.forminputPanel');  // Select the second input
    targetInput.value = inputValue; 
    var searchInput = document.querySelector(".searchContainerTopbarInput");
searchInput.addEventListener("input", displaySearchResults);
 // Set the second input's value to match the first
}


google.charts.load('current', {'packages':['corechart']});
google.charts.setOnLoadCallback(drawChartproductionsMotif);

var globalChartInstance = null;

async function drawChartproductionsMotif() {
    var productionsMotif = await fetchproductionsMotif();
    if (!productionsMotif || !productionsMotif.details) {
        console.error("No data or details found.");
        return;
    }

    const sortedItems = productionsMotif.details
        .filter(item => item.libelle_motif_instance)
        .sort((a, b) => b.totalVentes - a.totalVentes);

    var dataArray = [['Motif', 'Ventes', { role: 'style' }]];
    
    sortedItems.forEach((item, index) => {
        dataArray.push([item.libelle_motif_instance, item.totalVentes, generateColor(index)]);
    });

    var data = google.visualization.arrayToDataTable(dataArray);

    var options = {
        title: '',
        width: '100%',
        height: '100%',
        legend: { position: 'none' },
        bars: 'horizontal',
        bar: { groupWidth: '75%' },
        isStacked: false,
        hAxis: {
            title: '',
            minValue: 0,
            gridlines: { color: 'transparent' },
            textStyle: { color: 'rgba(255,255,255,0.3)', fontSize: 10 }
        },
        vAxis: {
            title: '',
            textStyle: { color: '#b0b0b0', fontSize: 10 },
            gridlines: { color: 'rgba(255,255,255,0.1)' }
        },
        backgroundColor: 'transparent',
        chartArea: { width: '80%', height: '85%', left: '30%', top: 13 },
        tooltip: { trigger: 'selection' } // Désactive les tooltips automatiques
    };

    if (globalChartInstance) {
        globalChartInstance.clearChart();
    }

    globalChartInstance = new google.visualization.BarChart(document.getElementById('chart_div_motif_instance'));
    globalChartInstance.draw(data, options);

    // Écouteur d'événements pour les tooltips
    google.visualization.events.addListener(globalChartInstance, 'onmouseover', function (event) {
        let rowIndex = event.row;
        if (rowIndex !== null) {
            let item = sortedItems[rowIndex];
            showCustomTooltip(item.libelle_motif_instance, item.totalVentes);
        }
    });

    google.visualization.events.addListener(globalChartInstance, 'onmouseout', function () {
        hideCustomTooltip();
    });
}

// Définition de la couleur avec opacité
function generateColor(index) {
    const selectedTheme = localStorage.getItem("theme");
    const baseColor = selectedTheme === "light" ? "145, 177, 235" : "84, 197, 208"; // Updated RGB values
    const opacity = Math.min(1, 0.3 + (index * 0.1));
    return `rgba(${baseColor}, ${opacity})`;
}


// Tooltip personnalisé
function showCustomTooltip(title, value) {
    let tooltipDiv = document.getElementById('custom-tooltip');
    if (!tooltipDiv) {
        tooltipDiv = document.createElement('div');
        tooltipDiv.id = 'custom-tooltip';
        tooltipDiv.style.position = 'absolute';
        tooltipDiv.style.background = 'rgba(0, 0, 0, 0.8)';
        tooltipDiv.style.color = 'white';
        tooltipDiv.style.padding = '5px 10px';
        tooltipDiv.style.borderRadius = '5px';
        tooltipDiv.style.fontSize = '12px';
        tooltipDiv.style.pointerEvents = 'none';
        tooltipDiv.style.zIndex = '1000';
        document.body.appendChild(tooltipDiv);
    }
    tooltipDiv.innerHTML = `<b>${title}</b><br>Ventes: ${value}`;
    document.onmousemove = (e) => {
        tooltipDiv.style.left = e.pageX + 10 + 'px';
        tooltipDiv.style.top = e.pageY + 10 + 'px';
    };
}

// Masquer le tooltip
function hideCustomTooltip() {
    let tooltipDiv = document.getElementById('custom-tooltip');
    if (tooltipDiv) {
        tooltipDiv.remove();
    }
}

function applyResponsiveStyles() {
    document.querySelectorAll('.ResponsivePanelChart').forEach(el => {
        if (window.innerWidth >= 1920 && window.innerHeight >= 1080) {
            el.style.width = '315px !important';
            el.style.height = '195px !important';
            el.style.fontSize = '17px !important';
        } else {
            el.style.width = '340px !important';  // Reset to CSS default
            el.style.height = '175px !important';
            el.style.fontSize = '16px !important';
        }
    });
}

// Run when the page loads
document.addEventListener('DOMContentLoaded', applyResponsiveStyles);

// Run when the window resizes
window.addEventListener('resize', applyResponsiveStyles);

document.addEventListener("DOMContentLoaded", function () {
    const searchInput = document.querySelector(".searchContainerTopbarInput");
    const panel = document.getElementById("displayproductionPanel");
    const panelContent = document.querySelector(".SearchContainerINPanel");
    const closePanel = document.getElementById("closePanel");

    async function fetchProductionsContrat(contractNumber) {
        const url = `https://api.nomadcloud.fr/api/productions-search/${pointOfSaleId}?search=${contractNumber}&page=1`;
    
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`, // Assure-toi que jwtToken est défini
                    'Content-Type': 'application/json'
                }
            });
    
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
    
            const data = await response.json();
            console.log("fetchProductionsContrat", data);
            return data; 
        } catch (error) {
            console.error('Error fetching data:', error);
            return null;
        }
    }
    
    function generateTableRow(data) {
        return data.map(item => {
            const values = [
                item.numCommande || 'N/A',
                item.product ? item.product.name : 'N/A',
                item.category ? item.category.name : 'N/A',
                item.ville || 'N/A',
                item.streetName || 'N/A',
                item.nomClient || 'N/A',
                item.seller || 'N/A',
    
                item.etat ? item.etat.nom : 'N/A'
            ];
    
            const allNA = values.every(value => !value || value === 'N/A' || value.trim() === '');
            if (allNA) {
                return `<tr><td colspan="9">Aucun résultat pour cette commande</td></tr>`;
            }
    
            return `<tr data-id="${item.id}">
                ${values.map(value => `<td>${value}</td>`).join('')}
            </tr>`;
        }).join('');
    }
    
    
    async function displayProductionsData(contractNumber) {
        var data = await fetchProductionsContrat(contractNumber);
    
        if (!data || data.length === 0) {
            panelContent.innerHTML = `<p class="text-danger">Aucune donnée trouvée pour ce contrat.</p>`;
            panel.style.bottom = "0"; 
            document.getElementById("productionTableBody").innerHTML = ""; // Vide le tableau
            return;
        }
    
        panel.style.bottom = "0";
        panel.style.zIndex = "1000000";
        panelContent.style.display = "block";
        panelContent.innerHTML = `
            <div class="DetailsCAlenderdata scrollbar-custom">
                <table id="productionTable" class="productionTable">
                    <thead>
                        <tr>
                            <th>Commande</th>
                            <th  style="width: 13%;">Produit</th>
                            <th>Catégorie</th>
                            <th style="width: 9%;">Ville</th>
                            <th style="width: 13%;">Adresse</th>
                            <th style="width: 9%;">Nom du client</th>
                            <th style="width: 9%;">Vendeur</th>
                            <th>État</th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody">
                        ${generateTableRow(data)}
                    </tbody>
                </table>
            </div>
        `;
    
        document.getElementById("productionTableBody").addEventListener("click", async function (e) {
            const clickedRow = e.target.closest("tr");
            if (!clickedRow) return;
    
            const id = clickedRow.dataset.id;
            if (id) {
                const selectedData = data.find(item => item.id == id);
                if (selectedData) {
                    showPopup(selectedData);
                } else {
                    alert("Impossible de récupérer les données.");
                }
            }
        });
    }
    
    
    
    

    function showPopup(data) {
        const popup = document.createElement("div");
        popup.className = "popup-container-contrat";
    
        popup.innerHTML = `
            <div class="popupcontrat">
                <div class="popup-close-contrat">x</div>
                <span class="formListItem">
                    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
                        <span style='color:#88969F'>Commande:</span> ${data.numCommande || 'N/A'}
                    </span>
                    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
                        <span style='color:#88969F'>Date Commande:</span> ${data.dateCmdA || 'N/A'}
                    </span>
                    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
                        <span style='color:#88969F'>Date Validation:</span> ${data.dateVenteValidB || 'N/A'}
                    </span>
                    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
                        <span style='color:#88969F'>Catégorie:</span> ${data.category ? data.category.name : 'N/A'}
                    </span>
                    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
                        <span style='color:#88969F'>État:</span> ${data.etat ? data.etat.nom : 'N/A'}
                    </span>
                    <span class="caret formSpan" style='font-weight:bold; margin-top:10px;'>Vendeur Principal:</span>
                    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
                        <span style='color:#88969F'>Nom Client:</span> ${data.nomClient || 'N/A'}
                    </span>
                </span>
            </div>
        `;
    
        document.body.appendChild(popup);
        document.querySelector(".popup-close-contrat").addEventListener("click", () => {
            popup.remove();
        });
    }
    
    
    
    

    searchInput.addEventListener("input", async function () {
        const contractNumber = this.value.trim();

        if (contractNumber.length > 5) {
            await displayProductionsData(contractNumber);
        } else {
            panel.style.bottom = "-100%";
        }
    });

    if (closePanel) {
        closePanel.addEventListener("click", function () {
            panel.style.bottom = "-100%";
            panelContent.innerHTML = ""; // Vide le contenu du panneau
            document.getElementById("productionTableBody").innerHTML = ""; // Vide le tableau
        });
    }
    
    
});

