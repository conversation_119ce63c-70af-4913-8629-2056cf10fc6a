<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Compact Motifs Échec</title>

  <style>
    :root {
        --background-color: #1a1a1a;
        --text-color: #ffffff;
        --bar-background: #8a8889;
        --progress-complete-background: linear-gradient(90deg, #28b1f4, #63c9f2);
        --legend-color: #555;
        --text-colors4: #1d4d6b;
    }

    [data-theme="dark"] {
        --text-colorlabel: #cbd8e3;
        --color-background-light: #1e1e1e;
        --color-background-dark: #2a2a2a;
        --text-colors4: #ffffff;
    }

    body {
        font-family: 'Ubuntu', sans-serif;
        background-color: var(--background-color);
        color: var(--text-color);
    }

    .row3dashsales-progress-container {
        width: 100%;
        margin: 2px;
    }

    .row3dashsales-progress-bar-wrapper {
        width: 100%;
        background-color: var(--bar-background);
        height: 18px;
        border-radius: 10px;
        position: relative;
        overflow: hidden;
        align-items: center;
        margin: 0 auto;
    }

    .row3dashsales-progress-bar-complete {
        height: 100%;
        background: var(--progress-complete-background);
        border-radius: 10px 0 0 10px;
        position: relative;
    }

    .row3dashsales-legend {
        display: flex;
        justify-content: center;
        margin-top: 5px;
    }

    .row3dashsales-legend-item {
        font-size: 10px;
        color: var(--text-colors4);
        display: flex;
        font-weight: bold;
        align-items: center;
        margin: 0 5px;
    }

    .row3dashsales-legend-color {
        width: 18px;
        height: 18px;
        border-radius: 25%;
        display: inline-block;
        margin-right: 3px;
    }

    .row3dashsales-legend-blue { background-color: #63c9f2; }
    .row3dashsales-legend-gray { background-color: #8a8889; }

    .motifs-echec-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 5px;
        text-align: left;
        color: var(--text-colors4);
    }

    /* Style for the numbers inside the progress bar */
    .progress-bar-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        z-index: 1;
    }

    .percentage {
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        z-index: 1;
    }
  </style>
</head>
<body>

{% set progress = (productionsAnnulation[0].totalReseau > 0) 
    ? (productionsAnnulation[0].totalVentes / productionsAnnulation[0].totalReseau) * 100 
    : 0 %}
  
<div class="containermotifechec">
    <div class="motifs-echec-title">Motifs échec</div> 

    <!-- Progress Bar for Pb Vente/Client -->
    <div class="row3dashsales-progress-container">
        <div class="row3dashsales-progress-bar-wrapper">
            <div class="row3dashsales-progress-bar-complete" 
                 role="progressbar" 
                 aria-valuenow="{{ progress }}" 
                 aria-valuemin="0" 
                 aria-valuemax="100" 
                 style="width: {{ progress }}%;">
                <!-- Display totalVentes inside the progress bar -->
                <span class="progress-bar-text percentage" style="margin-left: 100px;"> {{ productionsAnnulation[0].totalVentes }}</span>
                <span class="progress-bar-text percentage">{{ productionsAnnulation[0].totalReseau }}</span>
            </div>
        </div>
        <div class="row3dashsales-legend">
            <div class="row3dashsales-legend-item">
                <span class="row3dashsales-legend-color row3dashsales-legend-blue"></span> 
                Pb Réseau/STIT
            </div>
            <div class="row3dashsales-legend-item">
                <span class="row3dashsales-legend-color row3dashsales-legend-gray"></span> 
                Pb Vente/Client 
            </div>
        </div>
    </div>
</div>

</body>
</html>
