:root
{
--background-color: #fff;
--text-color: #989898;
--border-color: rgba(225, 224, 225, 0.7);
--alert-success-bg: #b5afce;
--alert-pending-bg: #b7cb8b;
--alert-cancelled-bg: #dc3545;
--status-fulfilled-color: #c794ad;
--status-unfulfilled-color: #dc3545;
--status-cancelled-color: #ffc107;
--nav-active-bg: #e1e1e0;
--nav-active-text-color: #3a3b3b ;
--table-th-color: #989898;
--table-td-color: #989898;
--strong-text: #5b5a5b;
--bg-checkbox:#e4e4e4 ;
--background-table-erea:#ffffff ;
--bg-checkbox-border: none;
}
[data-theme="dark"] {
--background-color: #333;
--text-color: #9ea1a1;
--border-color: rgba(225, 224, 225, 0.1);
--alert-success-bg: #575e3f;
--alert-pending-bg: #584388;
--alert-cancelled-bg: #ff4c4c;
--status-fulfilled-color: #537d6d;
--status-unfulfilled-color: #fff;
--status-cancelled-color: #fff;
--nav-active-bg: #202020;
--nav-active-text-color:#abaaaa ;
--table-th-color: #989898;
--table-td-color: #989898;
--strong-text: #afb0b0;
--bg-checkbox:#1b1b1b ;
--background-table-erea: #0c0e0d ;
--bg-checkbox-border: #4a4a4a;
}
table,
th,
td {
padding: 8px;
}
body {
font-family: 'Source Sans Pro', sans-serif;
font-weight: 400;
font-size: 80%;
}
.table-striped tbody tr:nth-of-type(odd) {
background-color: transparent;
}
.table-hover tbody tr:hover {
background-color: transparent;
}
.table-navbar {
background-color: transparent;
padding: 0.5rem 0;
overflow-x: auto;
}
#addfilter {
color: var(--text-color);
}
.table-navbar .bi {
border: 1px solid var(--border-color);
border-radius: 5px;
padding: 0.25rem 0.5rem;
}
.table-navbar .nav-link.active {
background-color: var(--nav-active-bg);
color: var(--nav-active-text-color);
border-radius: 10px;
font-weight: 500;
}
.table-navbar .nav-link {
color: var(--text-color);
border-radius: 25px;
font-weight: 500;
}
.table-container {
width: 100%;

margin: 0 auto;
}
.alert-payment-success,
.alert-payment-pending,
.alert-payment-cancelled {
color: var(--text-color);
padding: 0.25rem 0.5rem;
border-radius: 25px;
}
.alert-payment-success {
background-color: var(--alert-success-bg);
}
.alert-payment-pending {
background-color: var(--alert-pending-bg);
color: #1e1f22 !important;
}
[data-theme="dark"] .alert-payment-pending {
color: white !important;
}
.name {
background-color: var(--alert-pending-bg);
color: #1e1f22 !important;
}
[data-theme="dark"] .name {
color: white !important;
}
.alert-payment-cancelled {
background-color: var(--alert-cancelled-bg);
}
.status-fulfilled {
color: var(--status-fulfilled-color);
background-color: transparent;
padding: 0.1rem 0.4rem;
border-radius: 25px;
border: 0.1px solid var(--status-fulfilled-color);
display: inline-block;
}
.status-unfulfilled {
color: var(--status-unfulfilled-color);
background-color: transparent;
padding: 0.25rem 0.5rem;
border-radius: 25px;
}
.status-cancelled {
color: var(--status-cancelled-color);
background-color: transparent;
padding: 0.25rem 0.5rem;
border-radius: 25px;
}
.form-select,
.btn-outline-secondary {
border-radius: 25px;
font-size: 0.7875rem;
background-color: transparent;
margin-right: -50px !important;
}
th i {
margin-right: 0.25rem;
}
th {
white-space: nowrap;
font-weight: 400;
border-top: 0.2px var(--border-color);
border-bottom: 0.2px var(--border-color);
color: var(--table-th-color);
}
th {
color: var(--table-th-color) !important;
}
td {
color: var(--table-td-color) !important;
}
.table th {
border-bottom: 2px solid var(--border-color);
border-top: 0.2px solid var(--border-color);
}
.input-group-text {
border-radius: 25px 0 0 25px;
font-size: 0.7875rem;
background-color: transparent;
border: 0.1px solid var(--border-color);
color: var(--text-color);
border-right: 0.1px solid var(--border-color);
}
.form-select {
border-radius: 25px 25px 25px 25px;
border-left: none;
border-left: 0.1px solid var(--border-color);
width: 90%;
}
strong {
color: var(--strong-text);
}
.form-select {
background-color: transparent;
border: 0.1px solid var(--border-color);
color: #9ea1a1;
-webkit-appearance: none;
-moz-appearance: none;
appearance: none;
background-repeat: no-repeat;
background-position: right 0.75rem center;
background-size: 0.65rem;
padding-right: 1.5rem;
}
.custom-select-container {
position: relative;
}
.custom-select-container .icon {
position: absolute;
color : var(--text-color);
left: 10px;
top: 50%;
transform: translateY(-50%);
pointer-events: none;
}
.form-select {
padding-left: 30px;
}
body[data-theme="light"] .form-select {
background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 6'%3E%3Cpolyline fill='none' stroke='%23a09898' stroke-width='1.2' points='0,0 5,6 10,0'/%3E%3C/svg%3E");
}
body[data-theme="dark"] .form-select {
background-image: url("data:image/svg+xml, %3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 6'%3E%3Cpolyline fill='none' stroke='%23fff' stroke-width='1.5' points='0,0 5,6 10,0'/%3E%3C/svg%3E");
}
#iconnav {
margin-left: -1px;
margin-right: 5px;
padding: 0;
color: var(--text-color);
}
#addfilter {
margin-left: -100px;
}
.custom-select-container {
margin-right: -30px;
}
.selectcontainer {
margin-right: 10px;
}
#addfilter {
margin-left: -45px;
}
table {
background-color: transparent;
}
.table th,
.table td {
background-color: transparent;
}
.table-striped tbody tr:nth-of-type(odd),
.table-hover tbody tr:hover {
background-color: transparent !important;
}
table {
width: 100%;
border-collapse: collapse;
margin-bottom: 1rem;
}
th,
td {
padding: 0.75rem;
text-align: left;
vertical-align: middle;
border-top: 0.1px solid var(--border-color);
border-bottom: 0.1px solid var(--border-color);
box-shadow: 0 0 0.1px var(--border-color);
position: relative;
}
th {
font-weight: 600;
}
@media(max-width: 767.98px) {
.table-responsive {
display: block;
width: 100%;
overflow-x: auto;
-webkit-overflow-scrolling: touch;
}
.table-responsive thead {
display: none;
}
.table-responsive tbody,
.table-responsive tr,
.table-responsive td {
display: block;
width: 100%;
}
.table-responsive tr {
margin-bottom: 1rem;
border: 0.2px solid var(--border-color);
}
.table-responsive td {
text-align: right;
padding-left: 50%;
position: relative;
}
.table-responsive td::before {
content: attr(data-label);
position: absolute;
left: 0;
width: 50%;
padding-left: 1rem;
font-weight: 600;
text-align: left;
}
}
.navbar {
flex-wrap: nowrap;
}
.navbar-nav {
flex-direction: row;
}
.navbar-nav .nav-link {
padding-left: 0.5rem;
padding-right: 0.5rem;
}
@media(max-width: 767.98px) {
.navbar-nav {
flex-wrap: wrap;
justify-content: center;
}
.navbar-nav .nav-item {
width: 100%;
text-align: center;
}
}
.form-check-input {
position: relative;
appearance: none;
width: 15px;
height: 15px;
background-color: var(--bg-checkbox);
border: 0.1px solid var(--bg-checkbox-border);
border-radius: 5px;
cursor: pointer;
}
.form-check-input:checked {
background-color: transparent;
}
.form-check-input:checked::after {
content: "";
position: absolute;
left: 2px;
top: 2px;
width: 9px;
height: 9px;
background-color: #f2eff3;
}
.form-check-input:focus {
outline: none;
}
.table-container {
background-color: var(--background-table-erea);
padding: 20px;
}
.table-container .navbar, .table-container .container {
background-color: transparent;
}
.table-container .custom-select-container .form-select {
background-color: var(--background-table-erea);
}
.table-container .table {
background-color: var(--background-table-erea);
}
.table-container .table thead {
background-color: var(--background-table-erea);
}