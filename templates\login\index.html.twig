{% extends 'base.html.twig' %}

{% block title %}Authentification{% endblock %}

{% block body %}
<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('styles/loginstyle.css') }}">

<div class="auth-container">
    <div class="row w-100">
        <div class="col-md-6 auth-form">
            {% for message in app.flashes('api_response') %}
                <div class="alert alert-info">
                    {{ message }}
                </div>
            {% endfor %}
            <form id="loginForm" action="{{ path('app_login') }}" method="post">
                <div class="auth-login">
                    <label for="emailAddress" class="font-weight-bold" style="font-size: 12px; margin-bottom: 10px;">
                        E-MAIL OU NUMERO DE TELEPHONE<span style="color: red;">*</span>
                    </label>
                    <input type="text" id="emailAddress" name="emailAddress" class="auth-input form-control" required="">
                </div>
                <div class="auth-password">
                    <label for="password" class="font-weight-bold" style="font-size: 12px; margin-bottom: 10px;">
                        MOT DE PASSE<span style="color: red;">*</span>
                    </label>
                    <input type="password" id="password" name="password" class="auth-input form-control" required="">
                    <a class="auth-link" href="#">Tu as oublié ton mot de passe ?</a>
                </div>
                <div class="auth-footer">
                    <button type="submit" class="auth-button btn btn-primary">Connexion</button>
                    <a class="auth-link" href="#">
                        <span>Besoin d'un compte?</span> S'inscrire
                    </a>
                </div>
            </form>
        </div>
        <div class="col-md-6 auth-qr">
            <img src="https://placehold.co/150" alt="QR Code" class="img-fluid mb-2">
            <div class="auth-qr-text">
                <p>Sinon, connecte-toi avec une clé d'accès.</p>
                <p>Scan this with the mobile app to log in instantly.</p>
                <a class="auth-link">Scan this with the mobile app to log in instantly.</a>
            </div>
        </div>
    </div>
</div>

{% endblock %}
