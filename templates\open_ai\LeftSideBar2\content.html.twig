<!DOCTYPE html>
<html style="    background-color: #000;">
	<head>
		<title>Sidebar Example</title>
		<link rel="stylesheet" href="style.css">
	</head>


	<style>
:root {
  
    --text-color: #333;
    --sidebar-bg-colors: #f5f5f5;
    --sidebar-text-color: gray;
    --sidebar-hover-color: #e0e0e0;
    --input-bg-color: transparent;
    --input-border-color: #444;
    --icon-color: #333;
    --footer-link-color: #333;

    --scrollbar-track-color: #ddd;
    --gray-100:#ececf1
}

[data-theme="dark"] {
  
    --text-color: #d4d4d4;
 
    --sidebar-text-color: #ccc;
    --sidebar-hover-color: #444;
    --input-bg-color: #333;
    --input-border-color: #555;
    --icon-color: #fff;
    --footer-link-color: #d4d4d4;
    --scrollbar-thumb-color: #444;
    --scrollbar-track-color: #1e1f22;
    --gray-100:#ececf1
}


.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    width: 250px;
    background-color: var(--sidebar-bg-colors);
    scrollbar-width: thin;
    
    scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    align-items:start;
    margin-top: 80px;
}


.input-container {
    display: flex;
    align-items: center;
    position: relative;
    margin-top: -20px;
}

.input-container input {
    flex: 1;
    padding: 10px ;
    border: 1px solid var(--input-border-color);
    border-radius: 4px;
    background-color: var(--input-bg-color);
    color: var(--text-color);
    font-size: 14px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    position: absolute;
    color: var(--icon-color);
}

.icon-button:first-child {
    left: 10px;
}

.icon-button:last-child {
    right: 10px;
}
input[type="text"] {
  padding-left: 35px; /* Adjust this value to control the space */
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
    color: var(--sidebar-text-color);
}

.sidebar li {
    margin-bottom: 10px;
    color: var(--sidebar-text-color);
}

.sidebar a {
    text-decoration: none;
    color: var(--sidebar-text-color);
    display: block;
    border-radius: 5px;
}

.sidebar a:hover {
 font-weight: 500;
    background-color: var(--gray-100);
    color: #000; /* Active text color */
    padding: 8px; /* Add padding for better visual space */
    border-radius: 5px; /* Rounded corners for a smoother design */
    width: 200px;
    transition: all 0.3s ease; /* Smooth transition for interactive feel */
}

.section-header {
    font-weight: bold;
    margin-bottom: 10px;
    margin-right: 50px;
    color: var(--text-color);
}

.footer-links {
    position: absolute;
    bottom: 0;
    margin-top: 20px;
}

.footer-links a {
    color: var(--footer-link-color);
    text-decoration: none;
}

.sidebar li.active a {
    font-weight: 500;
    background-color: var(--gray-100);
    color: #000; /* Active text color */
    padding: 8px; /* Add padding for better visual space */
    border-radius: 5px; /* Rounded corners for a smoother design */
    width: 200px;
    transition: all 0.3s ease; /* Smooth transition for interactive feel */
}




	</style>
	<body style="">

		<div class="sidebar">
			<div class="sidebar-header">
				<div class="input-container">
					<button class="icon-button">
						<i class="bi bi-search"></i>
					</button>
					
					<input type="text" placeholder="Search">
					<button class="icon-button">
						<i class="bi bi-qr-code-scan"></i>
					</button>
					<!-- Icône QR code -->
				</div>
			</div>
            <br>
			<h6 class="section-header">GET STARTED</h6>
			<ul>
				<li>
					<a href="#">Overview</a>
				</li>
				<li>
					<a href="#">Quickstart</a>
				</li>
				<li>
					<a href="#">Models</a>
				</li>
				<li>
					<a href="#">Changelog</a>
				</li>
				<li>
					<a href="#">Terms and policies</a>
				</li>
			</ul>
			<h6 class="section-header">CAPABILITIES</h6>
			<ul>
				<li>
					<a href="#">Text generation</a>
				</li>
				<li>
					<a href="#">Image generation</a>
				</li>
				<li>
					<a href="#">Vision</a>
				</li>
				<li>
					<a href="#">Audio generation</a>
				</li>
				<li>
					<a href="#">Text to speech</a>
				</li>
				<li>
					<a href="#">Speech to text</a>
				</li>
				<li>
					<a href="#">Embeddings</a>
				</li>
				<li>
					<a href="#">Moderation</a>
				</li>
				<li>
					<a href="#">Reasoning</a>
				</li>
			</ul>
			<h6 class="section-header">GUIDES
				<span style="color: transparent;">
					ssefe</span>
			</h6>
			<ul>
				<li>
					<a href="#">Function calling</a>
				</li>
				<li class="active">
					<a href="#">Structured Outputs</a>
				</li>
				<li>
					<a href="#">Predicted Outputs</a>
				</li>
				<li>
					<a href="#">Evaluations</a>
				</li>
				<li>
					<a href="#">Fine-tuning</a>
				</li>
				<li>
					<a href="#">Distillation</a>
				</li>
			</ul>
			<ul>
				<li>
					<a href="#">Function calling</a>
				</li>
				<li>
					<a href="#">Structured Outputs</a>
				</li>
				<li>
					<a href="#">Predicted Outputs</a>
				</li>
				<li>
					<a href="#">Evaluations</a>
				</li>
				<li>
					<a href="#">Fine-tuning</a>
				</li>
				<li>
					<a href="#">Distillation</a>
				</li>
			</ul>
		
		</div>
	</body>
</html>
