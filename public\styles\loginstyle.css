body, html {
    height: 100%;
    margin: 0;
    font-family: Arial, sans-serif;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #282b30; /* Background color for the body */
}

.auth-container {
    position: relative;
    display: flex;
    justify-content: center; /* Center the items */
    width: 100%;
    max-width: 100%; /* Max width for larger screens */
    height: auto; /* Allow height to adjust */
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
    padding: 50px;
    background: #2c2f33;
}


.auth-qr {
    display: flex;          
    flex-direction: column; 
    align-items: center;    
    justify-content: center; 
}

.auth-qr img {
    width: 150px; /* Fixed width for the QR code */
    height: 150px; /* Fixed height for the QR code */
        margin-left: 40px;

}

.auth-qr-text {
    margin-top: 20px; /* Space between image and text */
    text-align: center; /* Center the text */
            margin-left: 40px;

}

.auth-input {
    margin-bottom: 10px;
    padding: 10px;
    border: none;
    border-radius: 4px;
    background-color: #1E1F22;
    color: white;
}

.auth-input:focus {
    outline: none;
}

.auth-button {
    background-color: #3964f8;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 10px;
}

.auth-button:hover {
    background-color: #5b6eae;
}

.auth-link {
    color: #00b0f4;
    text-decoration: none;
    font-size: 13px;
}

.auth-link:hover {
    text-decoration: underline;
}

.auth-header {
    display: flex;          
    flex-direction: column; 
    align-items: center;    
    justify-content: center; 
}

.auth-password {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 22px;
}

.auth-login {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 14px;
}

.auth-footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 48px;
}

   .spinner {
        display: none; /* Hide spinner by default */
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
    }
