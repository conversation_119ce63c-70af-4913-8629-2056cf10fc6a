var DemoCommand;

(function () {
    "use strict";

    DemoCommand = {};

    DemoCommand.instance = function (selector) {
        var cmd = new CommandLine(selector);

        console.log("Command-line initialized");

        /**
         * Set command line username by command argument
         */
        cmd.addCommand("update-user username", function (command) {
            cmd.setUsername(command.getArgument('username'));
        }, "Set username by command argument");

        /**
         * Set command line username from prompt
         */
        cmd.addCommand("prompt", function () {
            this.prompt("Type your name", function (userInput) {
                if (userInput === '') {
                    cmd.error("Your name cannot be empty!");
                    this.start();
                } else {
                    cmd.setUsername(userInput);
                    cmd.startNewCommand();
                }
            });
            return false;
        }, "Set username from prompt");

        /**
         * Confirmation to remove all files
         */
        cmd.addCommand("rm", function () {
            this.confirm("Are you sure you want to delete all files?", function (yes) {
                if (yes) {
                    cmd.error("Permission denied!!");
                } else {
                    cmd.success("Great :)");
                }
                cmd.startNewCommand();
            });
            return false;
        }, "Remove all files.");

        /**
         * Login
         */
        cmd.addCommand("login", function () {
            this.prompt("Enter your username: ", function (username) {
                cmd.secret("Enter your password [Type 123]:", function (password) {
                    if (password !== '123') {
                        cmd.error('Wrong password');
                        this.start();
                    } else {
                        setTimeout(function () {
                            cmd.info('Authenticating ...');
                            setTimeout(function () {
                                cmd.info('Loading Application ...');
                                setTimeout(function () {
                                    cmd.warning("I am not saving this password! Don't worry :)");
                                    setTimeout(function () {
                                        cmd.success('System Ready to use...');
                                        cmd.setUsername(username);
                                        cmd.startNewCommand();
                                    }, 800);
                                }, 800);
                            }, 800);
                        }, 800);
                    }
                });
            });
            return false;
        }, "Login by username & password");

        /**
         * Help
         */
        cmd.addCommand("help", function () {
            var helpResults = [];
            for (var command = 0; command < this.commands.length; command++) {
                helpResults.push(this.commands[command].resolved.getSignature());
                helpResults.push(this.commands[command].description);
            }
            this.list(helpResults, 2);
        }, "Show available commands");

        /**
         * List all files
         */
        cmd.addCommand("ls", function () {
            this.list([
                'Applications',
                'User Information',
                'Library',
                'Users',
                'Volumes',
                'etc',
                'Users',
                'home',
                'var',
                'System'
            ]);
        }, "List directory files");

        /**
         * Show current date and time
         */
        cmd.addCommand("date", function () {
            this.output(new Date().toLocaleString());
            this.commandRow.hideTime();
        }, "Show current DateTime");

        /**
         * Custom TEST command
         */
        cmd.addCommand("test", function () {
            console.log("Commande TEST exécutée !");
            let outputData = ['prises > anciennete > -25 a 25'];

            if (typeof this.list === "function") {
                this.list(outputData, 1); // Affichage en colonne unique
            } else {
                outputData.forEach(item => this.output(item));
            }

        }, "Handle prises anciennete range from -25 to 25");
        cmd.addCommand("prises", function () {
            cmd.prompt(
                "Entrez les paramètres sous la forme :<br>" +
                    "📌 courtier (obligatoire) : <br>" +
                    "📍 Place : <br>" +
                    "⚙ tech : <br>" +
                    "📆 anciennete (ex: 12.24) : <br>" +
                    "⏳ arretCu : <br>" +
                    "📝 details :",
                function (input) {
                    let paramNames = ["courtier", "place", "tech", "anciennete", "arretCu", "details", "page"];
                    let params = {};
        
                    // Extraction des valeurs des paramètres
                    paramNames.forEach(name => {
                        let match = input.match(new RegExp(`${name}\\s*:\\s*([^,]*)`, "i"));
                        if (match && match[1].trim() !== "") {
                            params[name] = match[1].trim();
                        }
                    });
        
                    if (!params.courtier) {
                        cmd.error("❌ Le paramètre 'courtier' est obligatoire !");
                        return;
                    }
        
                    // Vérification du paramètre 'anciennete'
                    if (params.anciennete) {
                        let ancienneteValues = params.anciennete.split('.').map(value => value.trim());
        
                        if (ancienneteValues.length !== 2 || ancienneteValues[0] === "" || ancienneteValues[1] === "") {
                            cmd.error("❌ Le paramètre 'anciennete' doit contenir exactement deux valeurs séparées par un point !");
                            return;
                        } else {
                            params.anciennete = `${ancienneteValues[0]},${ancienneteValues[1]}`;
                        }
                    }
        
                    // Construction de l'URL
                    let queryParams = Object.entries(params)
                        .filter(([key]) => key !== "courtier")
                        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
                        .join("&");
        
                    let apiUrl = `https://api.nomadcloud.fr/api/interventions-places-cmd-line/${params.courtier}`;
                    if (queryParams) apiUrl += `?${queryParams}`;
        
                    cmd.output(`🔎 Requête envoyée à : ${apiUrl}`);
        
                    if (typeof jwtToken === "undefined") {
                        cmd.error("❌ Erreur : jeton JWT manquant !");
                        return;
                    }
        
                    fetch(apiUrl, {
                        method: "GET",
                        headers: {
                            "Authorization": `Bearer ${jwtToken}`,
                            "Content-Type": "application/json"
                        }
                    })
                        .then(response => {
                            if (!response.ok) throw new Error(`❌ Erreur HTTP! Statut : ${response.status}`);
                            return response.json();
                        })
                        .then(data => {
                            if (!Array.isArray(data) || data.length === 0) {
                                cmd.error("⚠️ Aucun résultat trouvé !");
                                return;
                            }
        
                            cmd.success("✅ Résultats reçus :");
        
                            if (params.place) {
                                let placeValue = params.place.trim();
                            
                                // Détection du type (c: ou v:)
                                let type = placeValue.match(/^(c:|v:)/i);
                                placeValue = placeValue.replace(/^(c:|v:)/i, "").trim();
                            
                                if (type && type[0].toLowerCase() === "c:") {
                                    // Recherche dans clusters
                                    let cluster = data.find(cluster => cluster.libelle_cluster.toUpperCase() === placeValue.toUpperCase());
                            
                                    if (cluster && Array.isArray(cluster.data)) {
                                        const numColumns = 4;
                                        const numRowsPerColumn = Math.ceil(cluster.data.length / numColumns);
                                        function truncateText(text, maxLength) {
                                            return text.length > maxLength ? text.substring(0, maxLength) + ".." : text;
                                        }
                                        let tableHTML = `
                                        <style>
                                            .grid-container {
                                            display: grid;
                                            grid-template-columns: repeat(${numColumns}, 1fr);
                                            gap: 10px;
                                            max-width: 94%;
                                            }
                                          .largInse{
                                            width:14%;
                                            }
                                            .largures{
                                            width:50%;
                                            }
                                            td {
                                            border-bottom: none;
                                           padding: 3px;
                                            }
                                            th {
                                            border-bottom: none;
                                            }
                                                @media screen and (min-width: 1360px) and (max-width: 1440px) and (min-height: 1024px) {
                                    .grid-container {
                                            display: grid;
                                            grid-template-columns: repeat(3, 1fr);
                                            gap: 6px;
                                            max-width: 86%;
                                        }
                                            .largures{
                                            width:60%;
                                            }
                                            .largInse{
                                            width:16%;
                                            }

                                        </style>
                                        <div class="grid-container">`;
                            
                                        for (let col = 0; col < numColumns; col++) {
                                            tableHTML += `<div class="grid-item"><table border="0">
                                                <tr>
                                                    <th class="largInse" >INSEE</th>
                                                    <th >Ville</th>
                                                    <th>Prises</th>
                                                </tr>`;
                            
                                            for (let row = col * numRowsPerColumn; row < (col + 1) * numRowsPerColumn && row < cluster.data.length; row++) {
                                                tableHTML += `
                                                <tr>
                                                    <td>${cluster.data[row].cod_insee}</td>
                                                    <td class="largures">${truncateText(cluster.data[row].ville, 23)}</td>
                                                    <td>${cluster.data[row].total_prises}</td>
                                                </tr>`;
                                            }
                            
                                            tableHTML += `</table></div>`;
                                        }
                                       
                                        
                                        tableHTML += `</div>`;
                                        cmd.output(tableHTML);
                            
                                    } else {
                                        cmd.error(`⚠️ Aucun cluster trouvé pour 'place' : ${params.place}`);
                                    }
                                } else if (type && type[0].toLowerCase() === "v:") {
                                    // Recherche dans villes
                                    let ville = data.find(v => v.ville.toUpperCase() === placeValue.toUpperCase());
                            
                                    if (ville && Array.isArray(ville.data)) {
                                        const numColumns = 4;
                                        const numRowsPerColumn = Math.ceil(ville.data.length / numColumns);
                            
                                        let tableHTML = `
                                    <style>
                                    .grid-container {
                                    display: grid;
                                    grid-template-columns: repeat(${numColumns}, 1fr);
                                        max-width: 94%;

                                    gap: 10px;
                                    }
                                    
                                   
                                    
                                    td {
                                    border-bottom: none;
                                    padding: 3px;
                                    }
                                    th {
                                    border-bottom: none;
                                    }
                                          @media screen and (min-width: 1360px) and (max-width: 1440px) and (min-height: 1024px) {
                               .grid-container {
                                    display: grid;
                                    grid-template-columns: repeat(3, 1fr);
                                    gap: 6px;
                                    max-width: 86%;
                                }
                                </style>
                                        <div class="grid-container">`;
                            
                                        for (let col = 0; col < numColumns; col++) {
                                            tableHTML += `<div class="grid-item"><table border="0">
                                                <tr>
                                                    <th >Nom Voie</th>
                                                    <th>Prise</th>
                                                </tr>`;
                            
                                            for (let row = col * numRowsPerColumn; row < (col + 1) * numRowsPerColumn && row < ville.data.length; row++) {
                                                tableHTML += `
                                                <tr>
                                                    <td>${ville.data[row].nom_voie}</td>
                                                    <td>${ville.data[row].total_prises}</td>
                                                </tr>`;
                                            }
                            
                                            tableHTML += `</table></div>`;
                                        }
                            
                                        tableHTML += `</div>`;
                                        cmd.output(tableHTML);
                            
                                    } else {
                                        cmd.error(`⚠️ Aucune donnée trouvée pour la ville : ${params.place}`);
                                    }
                                } else {
                                    cmd.error("⚠️ Format de 'place' invalide. Utilisez 'c:NomCluster' ou 'v:NomVille'.");
                                }
                            } else {
                                // Affichage général (sans filtre)
                                const numColumns = 4;
                                const numRowsPerColumn = Math.ceil(data.length / numColumns);
                                function truncateText(text, maxLength) {
                                    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
                                }
                                let tableHTML = `
                                  <style>
                                    .grid-container {
                                    display: grid;
                                    grid-template-columns: repeat(${numColumns}, 1fr);
                                    gap: 10px;
                                    max-width: 94%;
                                    }
                                    .largures{
                                    width:51%;
                                    }
                                    .largclu{
                                    width: 14%;}
                                    td {
                                    border-bottom: none;
                                    padding: 3px;
                                    }
                                    th {
                                    border-bottom: none;
                                    }
                                     @media screen and (min-width: 1360px) and (max-width: 1440px) and (min-height: 1024px) {
                               .grid-container {
                                    display: grid;
                                    grid-template-columns: repeat(3, 1fr);
                                    gap: 6px;
                                    max-width: 86%;
                                }
                                    .largures{
                                    width:60%;
                                    }
                                    .largclu{
                                    width: 16%;}

                                    }
                                </style>
                                <div class="grid-container">`;
                            
                                for (let col = 0; col < numColumns; col++) {
                                    tableHTML += `<div class="grid-item"><table border="0">
                                        <tr>
                                            <th class="largclu">Cluster</th>
                                            <th class="largures" >Nom</th>
                                            <th>Prises</th>
                                        </tr>`;
                            
                                    for (let row = col * numRowsPerColumn; row < (col + 1) * numRowsPerColumn && row < data.length; row++) {
                                        tableHTML += `
                                        <tr>
                                            <td>${data[row].code_cluster}</td>
                                            <td>$${truncateText(data[row].libelle_cluster,22)}</td>
                                            <td>${data[row].total_prises}</td>
                                        </tr>`;
                                    }
                            
                                    tableHTML += `</table></div>`;
                                }
                            
                                tableHTML += `</div>`;
                                cmd.output(tableHTML);
                            }
                            
                        })
                        .catch(error => {
                            cmd.error("❌ Erreur lors de la récupération des données !");
                            console.error(error);
                        });
                });
        
            return false;
        }, "Récupère les prises fibre en demandant tous les paramètres en une seule saisie.");
        
        cmd.addCommand("ventes", function () {
            cmd.prompt("Entrez les paramètres sous la forme <br>" +   
             
                "📍 place : <br>" +  
                "🛠️ category : <br>" +  
                "📌 option (B,V,R) : <br>" +  
                "👤 idUser : <br>" +  
                "📝 details : <br>" +  
                "📅 dateDebut: <br>" +  
                "📅 dateFin:"  
                , function (input) {
           
                let paramNames = ["code", "place", "category", "option", "idUser", "details", "dateDebut", "dateFin", "page"];
                let params = {};
        
                // Extraction des valeurs des paramètres avec une regex améliorée    "🔢 Code : <br>" +  
                paramNames.forEach(name => {
                    let regex = new RegExp(`${name}\\s*:\\s*([^,]+)`, "i");
                    let match = input.match(regex);
                    if (match && match[1].trim() !== "") {
                        params[name] = match[1].trim();
                    }
                });
        
                console.log("✅ Params après extraction :", params);
        
                // Construction de la requête avec paramètres
                let queryParams = new URLSearchParams(params).toString();
                console.log("✅ QueryParams généré :", queryParams);
        
                let apiUrl = "https://api.nomadcloud.fr/api/productions-cmd-line";
                if (queryParams) apiUrl += `?${queryParams}`;
                
                console.log("🚀 URL finale :", apiUrl);
        
                cmd.output(`🔎 Requête envoyée à : ${apiUrl}`);
        
                if (typeof jwtToken === "undefined") {
                    cmd.error("❌ Erreur : jeton JWT manquant !");
                    return;
                }
        
                fetch(apiUrl, {
                    method: "GET",
                    headers: {
                        "Authorization": `Bearer ${jwtToken}`,
                        "Content-Type": "application/json"
                    }
                })
                .then(response => {
                    if (!response.ok) throw new Error(`❌ Erreur HTTP! Statut : ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    console.log("✅ Réponse API :", data);
        
                    if (!Array.isArray(data) || data.length === 0) {
                        cmd.error("⚠️ Aucun résultat trouvé !");
                        return;
                    }
        
                    cmd.success("✅ Résultats reçus :");
        
                    if (params.place) {
                        let placeValue = params.place.trim();
                        let type = placeValue.match(/^(c:|v:)/i);
                        placeValue = placeValue.replace(/^(c:|v:)/i, "").trim();
                    
                        if (type && type[0].toLowerCase() === "c:") {
                                                  // Recherche dans clusters
                                                  let cluster = data.find(cluster => cluster.libelle_cluster.toUpperCase() === placeValue.toUpperCase());
                                                  function truncateText(text, maxLength) {
                                                    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
                                                }
                                                  if (cluster && Array.isArray(cluster.data)) {
                                                      const numColumns = 3;
                                                      const numRowsPerColumn = Math.ceil(cluster.data.length / numColumns);
                                                      function truncateText(text, maxLength) {
                                                          return text.length > maxLength ? text.substring(0, maxLength) + ".." : text;
                                                      }
                                                      let tableHTML = `
                                                      <style>
                                                          .grid-container {
                                                          display: grid;
                                                          grid-template-columns: repeat(${numColumns}, 1fr);
                                                          gap: 10px;
                                                          max-width: 85%;
                                                          }
                                                       .largtd{
                                                          width:10%;
                                                          }
                                                          td {
                                                          border-bottom: none;
                                                         padding: 3px;
                                                          }
                                                          th {
                                                          border-bottom: none;
                                                          }
                                                              @media screen and (min-width: 1360px) and (max-width: 1440px) and (min-height: 1024px) {
                                                  .grid-container {
                                                          display: grid;
                                                          grid-template-columns: repeat(3, 1fr);
                                                          gap: 6px;
                                                          max-width: 86%;
                                                      }
                                                          .largures{
                                                          width:52%;
                                                          }
                                                          .largtd{
                                                          width:14%;
                                                          }
              
                                                      </style>
                                                      <div class="grid-container">`;
                                          
                                                      for (let col = 0; col < numColumns; col++) {
                                                          tableHTML += `<div class="grid-item"><table border="0">
                                                              <tr>
                                                                      <th  class="largtd" >Insee</th>
                                                                    <th >Ville</th>
                                                                    
                                                                  
                                                                    <th>Ventes</th>
                                                              </tr>`;
                                          
                                                          for (let row = col * numRowsPerColumn; row < (col + 1) * numRowsPerColumn && row < cluster.data.length; row++) {
                                                              tableHTML += `
                                                              <tr>
                                                            <td>${cluster.data[row].cod_insee}</td>
                                                            <td>${cluster.data[row].ville}</td>
                                                            
                                                           
                                                            <td>${cluster.data[row].total_ventes}</td>
                                                              </tr>`;
                                                          }
                                          
                                                          tableHTML += `</table></div>`;
                                                      }
                                                     
                                                      
                                                      tableHTML += `</div>`;
                                                      cmd.output(tableHTML);
                     
                            } else {
                                cmd.error(`⚠️ Aucun cluster trouvé pour 'place' : ${params.place}`);
                            }
                        } else if (type && type[0].toLowerCase() === "v:") {
                            
                                 // Recherche dans villes
                                 let ville = data.find(v => v.ville.toUpperCase() === placeValue.toUpperCase());
                            
                                 if (ville && Array.isArray(ville.data)) {
                                     const numColumns = 3;
                                     const numRowsPerColumn = Math.ceil(ville.data.length / numColumns);
                         
                                     let tableHTML = `
                                     <style>
                                                          .grid-container {
                                                          display: grid;
                                                          grid-template-columns: repeat(${numColumns}, 1fr);
                                                          gap: 10px;
                                                          max-width: 94%;
                                                          }
                                                       
                                                          td {
                                                          border-bottom: none;
                                                         padding: 3px;
                                                          }
                                                          th {
                                                          border-bottom: none;
                                                          }
                                                              @media screen and (min-width: 1360px) and (max-width: 1440px) and (min-height: 1024px) {
                                                  .grid-container {
                                                          display: grid;
                                                          grid-template-columns: repeat(3, 1fr);
                                                          gap: 6px;
                                                          max-width: 86%;
                                                      }
                                                          .largures{
                                                          width:52%;
                                                          }
              
                                                      </style>
                                     <div class="grid-container">`;
                         
                                     for (let col = 0; col < numColumns; col++) {
                                         tableHTML += `<div class="grid-item"><table border="0">
                                             <tr>
                                            <th >Nom Voie</th>
                                          
                                           
                                            <th>Ventes</th>
                                             </tr>`;
                         
                                         for (let row = col * numRowsPerColumn; row < (col + 1) * numRowsPerColumn && row < ville.data.length; row++) {
                                             tableHTML += `
                                             <tr>
                                                          <td>${ville.data[row].nom_voie}</td> 
                                                        
                                                        
                                                        <td>${ville.data[row].total_ventes}</td>
                                             </tr>`;
                                         }
                         
                                         tableHTML += `</table></div>`;
                                     }
                         
                                     tableHTML += `</div>`;
                                     cmd.output(tableHTML);
                                
                            } else {
                                cmd.error(`⚠️ Aucune donnée trouvée pour la ville : ${params.place}`);
                            }
                        } else {
                            cmd.error("⚠️ Format de 'place' invalide. Utilisez 'c:NomCluster' ou 'v:NomVille'.");
                        }
            
                    
                
                    }else {

                        // Affichage général (sans filtre)
                        const numColumns = 4;
                        const numRowsPerColumn = Math.ceil(data.length / numColumns);
                    
                        let tableHTML = `
                          <style>
                            .grid-container {
                            display: grid;
                            grid-template-columns: repeat(${numColumns}, 1fr);
                            gap: 10px;
                            max-width: 94%;
                            }
                     
                            td {
                            border-bottom: none;
                            padding: 3px;
                            }
                            th {
                            border-bottom: none;
                            }
                             @media screen and (min-width: 1360px) and (max-width: 1440px) and (min-height: 1024px) {
                       .grid-container {
                            display: grid;
                            grid-template-columns: repeat(3, 1fr);
                            gap: 6px;
                            max-width: 86%;
                        }
                            .largures{
                            width:51%;
                            }

                            }
                        </style>
                        <div class="grid-container">`;
                    
                        for (let col = 0; col < numColumns; col++) {
                            tableHTML += `<div class="grid-item"><table border="0">
                                <tr>
                            
                                <th >Category</th>
                                <th>Total Ventes</th>
                                </tr>`;
                    
                            for (let row = col * numRowsPerColumn; row < (col + 1) * numRowsPerColumn && row < data.length; row++) {
                                tableHTML += `
                                <tr>
                               
                                <td>${data[row].category_name}</td>
                                <td>${data[row].total_ventes}</td>
                                </tr>`;
                            }
                    
                            tableHTML += `</table></div>`;
                        }
                    
                        tableHTML += `</div>`;
                        cmd.output(tableHTML);
                      
                    }
                    
                })
                .catch(error => {
                    console.error("❌ Erreur attrapée :", error);
                    if (error.message.includes("Failed to fetch")) {
                        cmd.error("🌐 Problème de connexion réseau !");
                    } else {
                        cmd.error(`❌ Erreur : ${error.message}`);
                    }
                });
            });
        
            return false;
        }, "Récupère les ventes en demandant tous les paramètres en une seule saisie.");
        
        /**
         * Show current user
         */
        cmd.addCommand("whoami", function () {
            this.output(this.username);
            this.commandRow.hideTime();
        }, "Show current Username");

        /**
         * Show keyboard shortcuts
         */
        cmd.addCommand("shortcuts", function () {
            this.list([
                'Ctrl + C : Cancel Command',
                'Ctrl + R : Clear Screen',
                'Arrow Up : Previous command',
                'Arrow Down : Next Command',
                'Tab : Autocomplete command'
            ], 1);
        }, "Show keyboard shortcuts");

        console.log("Commands registered:", cmd.commands.map(c => c.resolved.getSignature()));

        return cmd;
    };

})();


// }else if (params.code) {
//     let codeValue = params.code.trim();
//     let type = codeValue.match(/^(-)/i);

//     let cluster = data.find(c => c.libelle_cluster === "NANTES"); // Remplacez "NANTES" par la bonne valeur si nécessaire
    
//     if (type) {
//         if (cluster && Array.isArray(cluster.data)) {  // Vérification avant d'utiliser .forEach()
//             let tableHTML = `
//             <table style="width:60%; border-collapse: collapse;" border="0">
//                 <tr>
//                     <th style="width:15%">Code INSEE</th>
//                     <th style="width:25%">Ville</th>
//                     <th style="width:15%">Category</th>
//                     <th style="width:15%">Courtier</th>
//                     <th>Total Ventes</th>
//                 </tr>`;

//             cluster.data.forEach(row => {
//                 tableHTML += `
//                 <tr>
//                     <td>${row.cod_insee}</td>
//                     <td>${row.ville}</td>
//                     <td>${row.category_name}</td>
//                     <td>${row.cpv}</td>
//                     <td>${row.total_ventes}</td>
//                 </tr>`;
//             });

//             tableHTML += `</table>`;
//             cmd.output(tableHTML);
//         } else {
//             cmd.error(`⚠️ Aucun cluster trouvé ou les données sont mal formatées pour 'code' : ${params.code}`);
//         }
//     } else {
//         let ville = data.find(c => c.libelle_cluster === "NANTES"); // Même logique pour ville
        
//         if (ville && Array.isArray(ville.data)) {
//             let tableHTML = `
//             <table style="width:60%; border-collapse: collapse;" border="0">
//                 <tr>
//                     <th style="width:25%">Nom Voie</th>
//                     <th style="width:15%">Category</th>
//                     <th style="width:15%">Courtier</th>
//                     <th>Total Ventes</th>
//                 </tr>`;

//             ville.data.forEach(row => {
//                 tableHTML += `
//                 <tr>
//                     <td>${row.nom_voie || "N/A"}</td> 
//                     <td>${row.category_name}</td>
//                     <td>${row.cpv}</td>
//                     <td>${row.total_ventes}</td>
//                 </tr>`;
//             });

//             tableHTML += `</table>`;
//             cmd.output(tableHTML);
//         } else {
//             cmd.error(`⚠️ Aucune donnée trouvée pour la ville : ${params.code}`);
//         }
//     }
// }