{% extends 'base.html.twig' %}

{% block body %}
<!DOCTYPE html>
<html style="	scrollbar-width: thin;
				scrollbar-color: #2B2D31 transparent;" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application</title>
    <style>
        .container-fluid,
        .main-content,
        .chat-area,
        .table-area {
            background-color: transparent;
        }

        .test {
            /* For modern browsers */
            scrollbar-width: thin; /* Firefox */
            scrollbar-color: #2B2D31 transparent; /* Firefox */
        }

        .test::-webkit-scrollbar {
            width: 8px; /* Width of the scrollbar */
        }

        .test::-webkit-scrollbar-thumb {
            background-color: #2B2D31; /* Color of the scrollbar thumb */
            border-radius: 4px; /* Rounded corners */
        }

        .test::-webkit-scrollbar-thumb:hover {
            background-color: #1F2125; /* Darker shade on hover */
        }

        .test::-webkit-scrollbar-track {
            background-color: transparent; /* Background of the scrollbar track */
        }
    </style>
</head>
<body class="test" data-theme="light">
    <div class="container-fluid d-flex" style="margin: 0; padding: 0; min-height: 100vh;">
        <div class="sidebar">
            {% include 'media/LeftSideBar/content.html.twig' %}
        </div>
        <div id="leftSidebar" style="max-width: 240px;" class="left-block col-auto col-md-3 col-xl-2 px-sm-2 px-0">
            <div style="position: fixed;" class="content">
                {% include 'media/LeftSideBar2/content.html.twig' %}

                <div style="bottom: 0px; position: absolute; position: fixed;" class="bottombar footer">
                    {% include 'media/bottombar/content.html.twig' %}
                </div>
            </div>
        </div>
        <div class="main-content col">
            <div class="right-block col-24">
                <div class="chat-areablock" id="right-sidebar">
                    <div class="chat-areablock" id="right-sidebar">
                        <div class="table-area" id="dynamic-content">
                            {% include 'media/MainBloc/content.html.twig' %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const checkbox = document.getElementById('chk');

        function applyTheme(theme) {
            if (theme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            } else {
                document.body.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            }
        }

        checkbox.addEventListener('change', () => {
            applyTheme(checkbox.checked ? 'dark' : 'light');
        });

        const currentTheme = localStorage.getItem('theme') || 'light';
        checkbox.checked = currentTheme === 'dark';

        applyTheme(currentTheme);

        document.getElementById('load-cluster-dashboard').addEventListener('click', function (e) {
            e.preventDefault();

            fetch('{{ path('clusters_dashboard') }}').then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();
            }).then(html => {
                document.getElementById('dynamic-content').innerHTML = html;
            }).catch(error => {
                console.error('There was a problem with the fetch operation:', error);
            });
        });
    </script>
</body>
</html>
{% endblock %}
