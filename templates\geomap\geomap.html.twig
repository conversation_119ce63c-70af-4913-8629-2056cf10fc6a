<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
		<link href="https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap" rel="stylesheet">
		<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap-tagsinput/dist/bootstrap-tagsinput.css" rel="stylesheet">
       
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.css">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"/>
		<link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
		 <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
		<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet">
		<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
		<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
        <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">
        <link href="https://api.mapbox.com/mapbox-gl-js/v3.1.0/mapbox-gl.css" rel="stylesheet">
        {# <link href='http://fonts.googleapis.com/css?family=Roboto+Condensed' rel='stylesheet' type='text/css'> #}
		<title>GeoMap</title>
		<link rel="stylesheet" href="{{asset('geomap/AllColorsThemes.css')}}">
		<link rel="stylesheet" href="{{asset('geomap/geomap.css')}}">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

		<link rel="stylesheet" href="{{asset('styles/prisesLivraisonmap/priseslivraisonmap.css')}}">
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.Default.css" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">


        <link href="{{ asset('geomap/prises/css/jquery.dateline.css') }}" rel="stylesheet">
	<style>
.custom-popup {
	color:var(--tree-view-color);
    font-size: 14px;
    line-height: 1.5;
}
.leaflet-popup-content-wrapper, .leaflet-popup-tip{
	 	 	background:var(--nested-bg)
}
.popup-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
}

.popup-details div {
    display: flex;
    align-items: center;
    
}
.popup-details b{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.icon {
    width: 16px;
    height: 16px;
}

.TreeRueClicked{
    overflow-y: auto;
    max-height: 400px;
}

/* Modal Style */
.popup-overlay {
    position: fixed;
    top: -13px;
    left: -43px;
    z-index: 1000;
}

.popup-content {
    border-radius: 10px;
    width: 400px;

}

.modal-header-event {
    display: flex;
    justify-content: space-between;
    align-items: center;

}

.bloc1 {
    display: flex;
    align-items: center;
}

.icon-check {
    display: none;
    margin-left: 5px;
}

.close-btn {
    font-size: 25px;
    cursor: pointer;

}

/* Switch and Icon Styling */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.form-check-input {
    margin-right: 10px;
}

.form-check-label {
    display: flex;
    align-items: center;
}

.form-check-label .icon {
    width: 25px;
    height: 20px;
}

/* Optional: Hover effect on switches */
.form-check-input:checked + .form-check-label {
    color: #5a5a5a;
}

.form-check-input:focus {
    box-shadow: none;
}

/* Footer Modal Styling */
.footer-modal {
    display: flex;
    justify-content: flex-end;
    padding-top: 15px;
}

/* Icon and Switch alignment */
.form-check .form-check-label {
    display: flex;
    align-items: center;
}

.form-check-input {
    width: 40px;
    height: 20px;
    background-color: #ddd;
    border-radius: 50px;
	margin-left:0;
    position: relative;
    transition: background-color 0.3s ease;
}
.form-switch .form-check-input{
		margin-left:0 !important;
}
.form-check-input:checked {
    background-color: #0078d4; /* Change to your desired active color */
}
.space-content {
    display: flex;
    align-items: center; /* Aligne verticalement l'image, le texte et l'input */
    gap: 15px; /* Espace entre l'image, le texte et l'input */
    width: 215px;
    justify-content: space-between;

}
.event-fileUploadForm{
    display: none;    
    position: absolute;
    left: 362px;
    background-color: var(--headerTreeSection-bg);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    padding: 10px;
    z-index: 1000;
    border-radius: 5px;
}
.hover-link:hover,.dropdown-menu a:hover{
    background-color: rgb(4,57,94);
    transition: 0.3s ease;
    color: var(--activebutton-color-text) !important;
}
.ChiffreDashgrph{
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    width:100%;
}
.scrollable-content-iris {
    overflow-y: auto;
    overflow-x: hidden;
    height: 90%;
}

/* Scrollbar personnalisé uniquement pour le Y */
.scrollable-content-iris::-webkit-scrollbar {
    width: 9px; /* Largeur de la scrollbar verticale */
    background-color: var(--Tree-Froms-scrolbar-bg);
}

/* Style du thumb (curseur de défilement) */
.scrollable-content-iris::-webkit-scrollbar-thumb {
    background-color: var(--Tree-Froms-scrolbar-bg-font);
    border-radius: 4px;
    border: 2px solid var(--Tree-Froms-scrolbar-bg);
}

/* Fond de la scrollbar */
.scrollable-content-iris::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px var(--Tree-Froms-scrolbar-bg) !important;
    border-radius: 10px;
    background-color: var(--Tree-Froms-scrolbar-bg);
}

/* Ajout d'un effet au survol */
.scrollable-content-iris::-webkit-scrollbar-thumb:hover {
    background-color: var(--Tree-Froms-scrolbar-bg-font-hover);
}

/* Augmenter légèrement la taille au survol pour une meilleure interaction */
.scrollable-content-iris:hover::-webkit-scrollbar {
    width: 12px;
}
    .controls {
            position: absolute;
            top: 100px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .compass {
     width: 30px;
        height:30px;
            background: black;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .compass i {
            color: white;
            font-size: 24px;
        }

        .control-button {
    width: 30px;
    height:30px;
            background: black;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .control-button i {
            font-size: 20px;
        }
	</style>
	</head>
	<body>
		<div class="container-fluid d-flex" style="margin:0 !important; padding:0 !important; height:100vh;">
			<div class="main-content">
				<div class="left-block">
			<div class="navbar topbar topbar-left-block" style="">
			<span style="margin-top: -9px;" id="topbarText">  <i class="bi bi-plugin" style="color: var(--coloredTextPrimary); margin-left: 10px; margin-right: 4px; margin-left: 6px;"></i> Prises</span>
            <i class="bi bi-bootstrap-reboot distribution-scripts" style="cursor: pointer;margin-top: -14px;margin-left:60px"></i>
            <i class="bi bi-chevron-down dropdown-icon icon-dropdown" style="cursor: pointer;margin-top: -14px;"></i>
            <div class="dropdown-content dropdown-menu  content-dropdown " style="display: none;">
                <a href="#" class="hover-link" data-path="{{ path('production') }}">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Ventes</span>
                        <i class="bi bi-tag-fill" style=" margin-right: 8px;"></i>
                    </div>
                </a>
                <a href="#" class="hover-link" data-path="{{ path('geomap') }}">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Prises</span>
                        <i class="bi bi-plugin" style=" margin-right: 8px;"></i>
                    </div>
                </a>
                <a href="#" class="hover-link" data-path="{{ path('incident') }}">
                    <div style="display: flex; justify-content: space-between;">
                        <span >Incident</span>
                        <i class="bi bi-plugin" style=" margin-right: 8px;"></i>
                    </div>
                </a>
            </div>


    
		</div>



					<div class="content" style="">
							<!-- Section Formulaires -->
							<div class="Tree-Formulaires">
                                <div class="svg-topbar">
                                    <div class="search-container" >                                        
                                        <svg style="height: 14px;width: 14px;stroke: var(--icon-topbar);z-index: 1;position: absolute;left: 4px;" version=1.1 viewBox="0 0 14.8 14.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path fill="none" d=M5,5.3C5,2.7,7,0.7,9.6,0.7c2.5,0,4.6,2.1,4.6,4.6s-2.1,4.6-4.6,4.6C7,9.9,5,7.8,5,5.3z /><line x1=6.3 x2=0.5 y1=8.5 y2=14.3 /></g></svg>
                                        <input type="text"id="TreeSearch" placeholder="Rechercher">
                                        <svg id="micIcon"style="height: 14px;width: 14px;fill: var(--icon-topbar);position: absolute;right: 4px;" version=1.1 viewBox="0 0 12.2 16.6"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M12.2,7.9c0,3.1-2.3,5.6-5.3,6v2.7H5.3v-2.7c-3-0.4-5.3-3-5.3-6h1.7c0,2.4,2,4.4,4.4,4.4s4.4-1.9,4.4-4.4H12.2zM3.5,7.9V2.6C3.5,1.2,4.7,0,6.1,0c1.4,0,2.6,1.2,2.6,2.6v5.3c0,1.4-1.2,2.6-2.6,2.6C4.7,10.5,3.5,9.3,3.5,7.9z"/></svg>
                                    </div>
                                </div>
								<div class="headerTreeSection" style="margin-top:2px;" id="headerTreeSection">
								<div style="display: flex; align-items: center;">
									<i class="bi bi-plugin" style="color: var(--coloredTextPrimary); margin-left: 10px; margin-right: 4px; margin-left: 6px;"></i>
									<p class="sidebar-title">Prises<span class="itemChoosed" style="margin-left: 15px;"></span></p>
								</div>
                                
								<i class="bi bi-plus-circle icon-color"></i>
								</div>
                                <div class=" ClusterFilter dropdown-content dropdown-menu" id="initialDropdown" style="top: 11%;">
                                    <span class="close-btnInitialDropdown">&times;</span>
                                    <a id="triggerAddGroupe">
                                        <span>Parc</span>
                                    </a>
                                    <a id="triggerAddGroupe" class="Migrables">
                                        <span>Migrables</span>
                                    </a>
                                    <a id="triggerAddGroupe">
                                        <span>Arret cuivre</span>
                                    </a>
                                    <a id="triggerAddGroupe">
                                        <span>Anciennete</span>
                                    </a>
                                    <a id="triggerAddGroupe">
                                        <span>Vendu</span>
                                    </a>
                                     {# <a id="triggerAddGroupe">
                                        <span>distrubue</span>
                                    </a>
                                    <a id="triggerAddGroupe">
                                        <span>No distrubue</span>
                                    </a> #}
                                </div>

								<div class="sectionDivider skeleton" style="margin-top: 2px; margin-bottom: 0px;"></div>
								<div class="Tree-Froms scrollbar-custom skeleton">
									<div class="tree-view skeleton">
										<ul id="tree-root">

										</ul>
									</div>
								</div>
							</div>
							<div class=" resizer resizer2"><spans>. . .</spans></div>
							<!-- Section Roles -->
							<div class="Tree-Roles">
                            
								<div class="headerTreeSection" id="headerTreeSectionRoles" style="margin-top: 2px; margin-bottom: 2px;">
									<div style="display: flex; align-items: center;">
										<i class="fa-solid fa-location-dot" style="color:#a074c4; font-size: 17px; margin-right: 4px; margin-left: 5px;"></i>
										<p class="sidebar-title">Rues</p>
									</div>
									<i class="bi bi-plus-circle icon-color"></i>
								</div>
                                    <div class="svg-topbar">
                                        <div class="search-container" >
                                            <svg style="height: 14px;width: 14px;stroke: var(--icon-topbar);z-index: 1;position: absolute;left: 4px;" version=1.1 viewBox="0 0 14.8 14.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path fill="none" d=M5,5.3C5,2.7,7,0.7,9.6,0.7c2.5,0,4.6,2.1,4.6,4.6s-2.1,4.6-4.6,4.6C7,9.9,5,7.8,5,5.3z /><line x1=6.3 x2=0.5 y1=8.5 y2=14.3 /></g></svg>
                                            <input type="text"id="TreeSearchRues" placeholder="Rechercher">
                                            <svg style="height: 14px;width: 14px;fill: var(--icon-topbar);position: absolute;right: 4px;" version=1.1 viewBox="0 0 12.2 16.6"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M12.2,7.9c0,3.1-2.3,5.6-5.3,6v2.7H5.3v-2.7c-3-0.4-5.3-3-5.3-6h1.7c0,2.4,2,4.4,4.4,4.4s4.4-1.9,4.4-4.4H12.2zM3.5,7.9V2.6C3.5,1.2,4.7,0,6.1,0c1.4,0,2.6,1.2,2.6,2.6v5.3c0,1.4-1.2,2.6-2.6,2.6C4.7,10.5,3.5,9.3,3.5,7.9z"/></svg>
                                        </div>
                                    </div>
								<div class="sectionDivider skeleton" style="margin-top: 2px; margin-bottom: 0px;"></div>
								<div class="Tree-Roles-content scrollbar-custom skeleton">
									<div class="tree-view skeleton">
										<ul id="tree-root-roles">
									
										</ul>
									</div>
								</div>
							</div>

					</div>

					<div class="bottombar">
						<div class="user-section" onclick="showUserModal.call(this, 'Mansour', 80, -200, 'owner')">
							<div class="avatar-container">
								<img src="{{asset('discord/avatardiscord.png')}}" alt="User Icon" class="user-icon">
								<div class="status-indicator"></div>
							</div>
							<div class="user-info">
								<span class="username">{{ username }}</span>
								<span class="user-tag">en ligne</span>
								<span class="user-tag2">300570</span>
							</div>
						</div>
						<div class="controls-section">
							<div class="svg-section">
								<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewbox="0 0 24 24" width="24" height="24" preserveaspectratio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
									<defs>
										<clipPath id="__lottie_element_5">
											<rect width="24" height="24" x="0" y="0"></rect>
										</clipPath>
										<clipPath id="__lottie_element_7">
											<path d="M0,0 L600,0 L600,600 L0,600z"></path>
										</clipPath>
										<clipPath id="__lottie_element_11">
											<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
										</clipPath>
										<clipPath id="__lottie_element_18">
											<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
										</clipPath>
										<clipPath id="__lottie_element_28">
											<path d="M0,0 L1000,0 L1000,1000 L0,1000z"></path>
										</clipPath>
										<mask id="__lottie_element_29">
											<rect fill="var(--icon-topbar)" width="600" height="600" transform="matrix(1,0,0,1,200,200)"></rect>
											<path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M681.219970703125,212.79600524902344 C681.219970703125,212.79600524902344 215.69700622558594,681.75 215.69700622558594,681.75 C215.69700622558594,681.75 151.75999450683594,749.2319946289062 201.13699340820312,798.8619995117188 C255,853 319.67999267578125,785.0560302734375 319.67999267578125,785.0560302734375 C319.67999267578125,785.0560302734375 785.2030029296875,316.10198974609375 785.2030029296875,316.10198974609375 C785.2030029296875,316.10198974609375 868,234 816.2150268554688,184.4459991455078 C764.781005859375,135.22799682617188 681.219970703125,212.79600524902344 681.219970703125,212.79600524902344" fill-opacity="1"></path>
											<path fill="var(--icon-topbar)" clip-rule="nonzero" d=" M698,405 C698,405 642,405 642,405 C642,405 642,479 642,479 C642,479 698,479 698,479 C698,479 698,405 698,405" fill-opacity="1"></path>
										</mask>
									</defs>
									<g clip-path="url(#__lottie_element_5)">
										<g clip-path="url(#__lottie_element_7)" transform="matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)" opacity="1" style="display: block;">
											<g clip-path="url(#__lottie_element_28)" transform="matrix(1,0,0,1,-200,-200)" opacity="1" style="display: block;">
												<g mask="url(#__lottie_element_29)">
													<g style="display: none;" transform="matrix(-25,0,0,25,800,173)" opacity="1">
														<g opacity="1" transform="matrix(1,0,0,1,12,8.5)">
															<path fill="#F23F42" fill-opacity="1" d=" M-4,-1.3799999952316284 C-4,-3.5889999866485596 -2.2090001106262207,-5.380000114440918 0,-5.380000114440918 C2.2090001106262207,-5.380000114440918 4,-3.5889999866485596 4,-1.3799999952316284 C4,-1.3799999952316284 4,2.509999990463257 4,2.509999990463257 C4,4.718999862670898 2.2090001106262207,6.5 0,6.5 C-2.2090001106262207,6.5 -4,4.718999862670898 -4,2.509999990463257 C-4,2.509999990463257 -4,-1.3799999952316284 -4,-1.3799999952316284z"></path>
														</g>
														<g opacity="1" transform="matrix(1,0,0,1,12,14)">
															<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-7,-2.990000009536743 C-7,0.8759999871253967 -3.865999937057495,4.010000228881836 0,4.010000228881836 C3.865999937057495,4.010000228881836 7,0.8759999871253967 7,-2.990000009536743"></path>
														</g>
														<g opacity="1" transform="matrix(1,0,0,1,12,20)">
															<path fill="#F23F42" fill-opacity="1" d=" M-1,-2 C-1,-2.2760000228881836 -0.7760000228881836,-2.5 -0.5,-2.5 C-0.5,-2.5 0.5,-2.5 0.5,-2.5 C0.7760000228881836,-2.5 1,-2.2760000228881836 1,-2 C1,-2 1,2 1,2 C1,2.2760000228881836 0.7760000228881836,2.5 0.5,2.5 C0.5,2.5 -0.5,2.5 -0.5,2.5 C-0.7760000228881836,2.5 -1,2.2760000228881836 -1,2 C-1,2 -1,-2 -1,-2z"></path>
														</g>
														<g opacity="1" transform="matrix(1,0,0,1,12,22)">
															<path fill="#F23F42" fill-opacity="1" d=" M3,-1 C3.552000045776367,-1 4,-0.5519999861717224 4,0 C4,0.5519999861717224 3.552000045776367,1 3,1 C3,1 -3,1 -3,1 C-3.552000045776367,1 -4,0.5519999861717224 -4,0 C-4,-0.5519999861717224 -3.552000045776367,-1 -3,-1 C-3,-1 3,-1 3,-1z"></path>
														</g>
													</g>
													<g transform="matrix(-25,0,0,25,800,173)" opacity="1" style="display: block;">
														<g opacity="1" transform="matrix(1,0,0,1,12,8.5)">
															<path fill="#F23F42" fill-opacity="1" d=" M-4,-1.4600000381469727 C-4,-3.6689999103546143 -2.2090001106262207,-5.460000038146973 0,-5.460000038146973 C2.2090001106262207,-5.460000038146973 4,-3.6689999103546143 4,-1.4600000381469727 C4,-1.4600000381469727 4,2.5 4,2.5 C4,4.709000110626221 2.2090001106262207,6.5 0,6.5 C-2.2090001106262207,6.5 -4,4.709000110626221 -4,2.5 C-4,2.5 -4,-1.4600000381469727 -4,-1.4600000381469727z"></path>
														</g>
														<g opacity="1" transform="matrix(1,0,0,1,12,14)">
															<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-7,-3 C-7,0.8659999966621399 -3.865999937057495,4 0,4 C3.865999937057495,4 7,0.8659999966621399 7,-3"></path>
														</g>
														<g opacity="1" transform="matrix(1,0,0,1,12,20)">
															<path fill="#F23F42" fill-opacity="1" d=" M-1,-2 C-1,-2.2760000228881836 -0.7760000228881836,-2.5 -0.5,-2.5 C-0.5,-2.5 0.5,-2.5 0.5,-2.5 C0.7760000228881836,-2.5 1,-2.2760000228881836 1,-2 C1,-2 1,2 1,2 C1,2.2760000228881836 0.7760000228881836,2.5 0.5,2.5 C0.5,2.5 -0.5,2.5 -0.5,2.5 C-0.7760000228881836,2.5 -1,2.2760000228881836 -1,2 C-1,2 -1,-2 -1,-2z"></path>
														</g>
														<g opacity="1" transform="matrix(1,0,0,1,12,22)">
															<path fill="#F23F42" fill-opacity="1" d=" M3,-1 C3.552000045776367,-1 4,-0.5519999861717224 4,0 C4,0.5519999861717224 3.552000045776367,1 3,1 C3,1 -3,1 -3,1 C-3.552000045776367,1 -4,0.5519999861717224 -4,0 C-4,-0.5519999861717224 -3.552000045776367,-1 -3,-1 C-3,-1 3,-1 3,-1z"></path>
														</g>
													</g>
												</g>
											</g>
											<g clip-path="url(#__lottie_element_18)" transform="matrix(1,0,0,1,-200,-200)" opacity="1" style="display: none;">
												<g transform="matrix(25,0,0,25,200,200)" opacity="1" style="display: none;">
													<g opacity="1" transform="matrix(1,0,0,1,12,12)">
														<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-10,10 C-10,10 10,-10 10,-10"></path>
													</g>
												</g>
												<g style="display: none;">
													<g>
														<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4"></path>
													</g>
												</g>
											</g>
											<g clip-path="url(#__lottie_element_11)" style="display: block;" transform="matrix(1,0,0,1,-200,-200)" opacity="1">
												<g style="display: block;" transform="matrix(25,0,0,25,200,200)" opacity="1">
													<g opacity="1" transform="matrix(1,0,0,1,12,12)">
														<path stroke-linecap="round" stroke-linejoin="miter" fill-opacity="0" stroke-miterlimit="4" stroke="#F23F42" stroke-opacity="1" stroke-width="2px" d=" M-10,10 C-10,10 10,-10 10,-10"></path>
													</g>
												</g>
											</g>
										</g>
									</g>
								</svg>
							</div>
							<div class="svg-section">
								 <div class="theme-switcher">
                                    <div class="current-theme" tabindex="0">
                                        <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark Theme" class="theme-icon">
                                    </div>
                                    <ul class="theme-options">
                                        <li data-theme="Darkgreen">
                                            <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark light Theme" class="theme-icon">
                                            <span>Dark Green</span>
                                        </li>
                                        <li data-theme="darklight">
                                            <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark light Theme" class="theme-icon">
                                            <span>Dark Light</span>
                                        </li>
                                        <li data-theme="lightsand">
                                            <img src="{{asset('discord/light-mode.svg')}}" alt="Light sand Theme" class="theme-icon">
                                            <span>Light Sand</span>
                                        </li>
                                        <li data-theme="darkpurple">
                                            <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark purple Theme" class="theme-icon">
                                            <span>Dark Purple</span>
                                        </li>
                                        <li data-theme="darkblue">
                                            <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark Blue Theme" class="theme-icon">
                                            <span>Dark Blue</span>
                                        </li>
                                        <li data-theme="light">
                                            <img src="{{asset('discord/light-mode.svg')}}" alt="Light Theme" class="theme-icon">
                                            <span>Light</span>
                                        </li>
                                        <li data-theme="dark" class="active">
                                            <img src="{{asset('discord/dark-mode.svg')}}" alt="Dark Theme" class="theme-icon">
                                            <span>Dark</span>
                                        </li>
                                        <li data-theme="auto">
                                            <img src="{{asset('discord/auto-mode.svg')}}" alt="Auto Theme" class="theme-icon">
                                            <span>Auto</span>
                                        </li>
                                    </ul>

                                </div>
							</div>
							<div class="svg-section settings" id="settingsButton">
								<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewbox="0 0 24 24" width="24" height="24" preserveaspectratio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
									<defs>
										<clipPath id="__lottie_element_97">
											<rect width="24" height="24" x="0" y="0"></rect>
										</clipPath>
										<clipPath id="__lottie_element_99">
											<path d="M0,0 L600,0 L600,600 L0,600z"></path>
										</clipPath>
									</defs>
									<g clip-path="url(#__lottie_element_97)">
										<g clip-path="url(#__lottie_element_99)" transform="matrix(0.03999999910593033,0,0,0.03999999910593033,0,0)" opacity="1" style="display: block;">
											<g transform="matrix(25,0,0,25,300,300)" opacity="1" style="display: block;">
												<g opacity="1" transform="matrix(1,0,0,1,0,0)">
													<path fill="oklab(0.786807 -0.0025776 -0.0110238)" fill-opacity="1" d=" M-1.4420000314712524,-10.906000137329102 C-1.8949999809265137,-10.847000122070312 -2.1470000743865967,-10.375 -2.078000068664551,-9.92300033569336 C-1.899999976158142,-8.756999969482422 -2.265000104904175,-7.7210001945495605 -3.061000108718872,-7.390999794006348 C-3.8570001125335693,-7.060999870300293 -4.8480000495910645,-7.534999847412109 -5.546000003814697,-8.484999656677246 C-5.816999912261963,-8.852999687194824 -6.329999923706055,-9.008999824523926 -6.691999912261963,-8.730999946594238 C-7.458000183105469,-8.142999649047852 -8.142999649047852,-7.458000183105469 -8.730999946594238,-6.691999912261963 C-9.008999824523926,-6.329999923706055 -8.852999687194824,-5.816999912261963 -8.484999656677246,-5.546000003814697 C-7.534999847412109,-4.8480000495910645 -7.060999870300293,-3.8570001125335693 -7.390999794006348,-3.061000108718872 C-7.7210001945495605,-2.265000104904175 -8.756999969482422,-1.899999976158142 -9.92300033569336,-2.078000068664551 C-10.375,-2.1470000743865967 -10.847000122070312,-1.8949999809265137 -10.906000137329102,-1.4420000314712524 C-10.968000411987305,-0.9700000286102295 -11,-0.48899999260902405 -11,0 C-11,0.48899999260902405 -10.968000411987305,0.9700000286102295 -10.906000137329102,1.4420000314712524 C-10.847000122070312,1.8949999809265137 -10.375,2.1470000743865967 -9.92300033569336,2.078000068664551 C-8.756999969482422,1.899999976158142 -7.7210001945495605,2.265000104904175 -7.390999794006348,3.061000108718872 C-7.060999870300293,3.8570001125335693 -7.534999847412109,4.8470001220703125 -8.484999656677246,5.546000003814697 C-8.852999687194824,5.816999912261963 -9.008999824523926,6.328999996185303 -8.730999946594238,6.691999912261963 C-8.142999649047852,7.458000183105469 -7.458000183105469,8.142999649047852 -6.691999912261963,8.730999946594238 C-6.329999923706055,9.008999824523926 -5.816999912261963,8.852999687194824 -5.546000003814697,8.484999656677246 C-4.8480000495910645,7.534999847412109 -3.8570001125335693,7.060999870300293 -3.061000108718872,7.390999794006348 C-2.265000104904175,7.7210001945495605 -1.899999976158142,8.756999969482422 -2.078000068664551,9.92300033569336 C-2.1470000743865967,10.375 -1.8949999809265137,10.847000122070312 -1.4420000314712524,10.906000137329102 C-0.9700000286102295,10.968000411987305 -0.48899999260902405,11 0,11 C0.48899999260902405,11 0.9700000286102295,10.968000411987305 1.4420000314712524,10.906000137329102 C1.8949999809265137,10.847000122070312 2.1470000743865967,10.375 2.078000068664551,9.92300033569336 C1.899999976158142,8.756999969482422 2.2660000324249268,7.7210001945495605 3.062000036239624,7.390999794006348 C3.8580000400543213,7.060999870300293 4.8480000495910645,7.534999847412109 5.546000003814697,8.484999656677246 C5.816999912261963,8.852999687194824 6.328999996185303,9.008999824523926 6.691999912261963,8.730999946594238 C7.458000183105469,8.142999649047852 8.142999649047852,7.458000183105469 8.730999946594238,6.691999912261963 C9.008999824523926,6.328999996185303 8.852999687194824,5.816999912261963 8.484999656677246,5.546000003814697 C7.534999847412109,4.8480000495910645 7.060999870300293,3.8570001125335693 7.390999794006348,3.061000108718872 C7.7210001945495605,2.265000104904175 8.756999969482422,1.899999976158142 9.92300033569336,2.078000068664551 C10.375,2.1470000743865967 10.847000122070312,1.8949999809265137 10.906000137329102,1.4420000314712524 C10.968000411987305,0.9700000286102295 11,0.48899999260902405 11,0 C11,-0.48899999260902405 10.968000411987305,-0.9700000286102295 10.906000137329102,-1.4420000314712524 C10.847000122070312,-1.8949999809265137 10.375,-2.1470000743865967 9.92300033569336,-2.078000068664551 C8.756999969482422,-1.899999976158142 7.7210001945495605,-2.265000104904175 7.390999794006348,-3.061000108718872 C7.060999870300293,-3.8570001125335693 7.534999847412109,-4.8480000495910645 8.484999656677246,-5.546000003814697 C8.852999687194824,-5.816999912261963 9.008999824523926,-6.329999923706055 8.730999946594238,-6.691999912261963 C8.142999649047852,-7.458000183105469 7.458000183105469,-8.142999649047852 6.691999912261963,-8.730999946594238 C6.328999996185303,-9.008999824523926 5.817999839782715,-8.852999687194824 5.546999931335449,-8.484999656677246 C4.848999977111816,-7.534999847412109 3.8580000400543213,-7.060999870300293 3.062000036239624,-7.390999794006348 C2.2660000324249268,-7.7210001945495605 1.9010000228881836,-8.756999969482422 2.0789999961853027,-9.92300033569336 C2.1480000019073486,-10.375 1.8949999809265137,-10.847000122070312 1.4420000314712524,-10.906000137329102 C0.9700000286102295,-10.968000411987305 0.48899999260902405,-11 0,-11 C-0.48899999260902405,-11 -0.9700000286102295,-10.968000411987305 -1.4420000314712524,-10.906000137329102z M4,0 C4,2.2090001106262207 2.2090001106262207,4 0,4 C-2.2090001106262207,4 -4,2.2090001106262207 -4,0 C-4,-2.2090001106262207 -2.2090001106262207,-4 0,-4 C2.2090001106262207,-4 4,-2.2090001106262207 4,0z"></path>
												</g>
											</g>
										</g>
									</g>
								</svg>
							</div>
						</div>
					</div>
					<div id="settings-modal" class="settings-modal" style="display:none">
						<div class="settings-modal-content">
							<span class="close-settings">&times;</span>
							<h2>Settings</h2>
							<p>Adjust your settings here.</p>
						</div>
					</div>
				</div>
                
                {# <div class="destributionPanel" style="left:-320px;" id="destributionPanel">
                    <div class="destributionPanelToggle" id="destributionPanelToggle"><i class="bi bi-caret-up-fill IconRightPanelDetails"></i></div>
                    <div class="TopPanelDistrubution">
                        <strong class="UserNameForPanleDetails">Destribuer</strong>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="option1">
                            <label class="form-check-label" for="inlineRadio1">Oui </label>
                        </div>
                            <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="option2">
                        <label class="form-check-label" for="inlineRadio2">Non</label>
                        </div>
                    </div>
                    <div class="UserDetailsForPanleDetails scrollbar-custom" style="overflow: auto;height: 89%;"></div>
                </div> #}
				<div class="right-block" style="background: var(--sidebar-left-right-color);">
					<div class="topbar topbar-right-blcok" style="background-color: var(--sidebar-left-right-color);">
						<div class="left-side-topbar">
                            <div class="TypeOptionSelector" id="OptionSelector" dataType="T" onclick="updateTypeOptionTout(this)">
                                <i class="bi bi-check-circle"></i>
                                <span> Tout</span>
                            </div>
                            <div class="TypeOptionSelector" id="OptionSelector"  dataType="N" onclick="updateTypeOptionTout(this);breadcrumbsd()">
                                <i class="bi bi-check-circle"></i>
                                <span>Distribuée</span>
                            </div>
                            <div class="TypeOptionSelector" id="OptionSelector"  dataType="D" onclick="updateTypeOptionTout(this);breadcrumbsd()">
                                <i class="bi bi-check-circle"></i>
                                <span>Non distribuée</span>
                            </div>
                        </div>

						<div class="right-side-topbar">
                            <div class="svg-topbar">
                                <div class="search-container" >                                        
                                    <svg style="height: 14px;width: 14px;stroke: var(--icon-topbar);z-index: 1;position: absolute;left: 4px;" version=1.1 viewBox="0 0 14.8 14.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path fill="none" d=M5,5.3C5,2.7,7,0.7,9.6,0.7c2.5,0,4.6,2.1,4.6,4.6s-2.1,4.6-4.6,4.6C7,9.9,5,7.8,5,5.3z /><line x1=6.3 x2=0.5 y1=8.5 y2=14.3 /></g></svg>
                                    <input type="text" class="searchContainerTopbarInput"id="TreeSearchs" placeholder="Rechercher">
                                    <svg style="height: 14px;width: 14px;fill: var(--icon-topbar);position: absolute;right: 4px;" version=1.1 viewBox="0 0 12.2 16.6"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M12.2,7.9c0,3.1-2.3,5.6-5.3,6v2.7H5.3v-2.7c-3-0.4-5.3-3-5.3-6h1.7c0,2.4,2,4.4,4.4,4.4s4.4-1.9,4.4-4.4H12.2zM3.5,7.9V2.6C3.5,1.2,4.7,0,6.1,0c1.4,0,2.6,1.2,2.6,2.6v5.3c0,1.4-1.2,2.6-2.6,2.6C4.7,10.5,3.5,9.3,3.5,7.9z"/></svg>
                                </div>
                            </div>
						</div>
					</div>
					<div class="simple-resolution" style="height: 100vh;display: flex;flex-direction: column;">
						<div style="height: 96vh; width: 100%; position: relative; overflow: hidden;">
                        <div class="map-container" style="height: 100%; width: 100%; position: relative;z-index: 0;" id="map"></div>
                      <div class="controls">
                            <div class="compass" id="rotateMap">
                                <i class="fas fa-compass"></i>
                            </div>
                        <button class="control-button" id="toggleView">
                            2D
                            </button>

                        </div>


                     </div>
                        <div class="info-breadcrumbs">
                            <div class="BreadcrumpOptionSelected" dataType="T" >
                                <i class="bi bi-house-door-fill"></i>
                                <span> PRISE</span>
                            </div>
                            <div class="BreadcrumpOptionSelected TypeOptionSelectorOption" dataType="N" style="display:none">
                                <i class="bi bi-check-circle"></i>
                                <span>Distribuée</span>
                            </div>
                            <div class="BreadcrumpOptionSelected TypeOptionSelectortest" dataType="D" style="display:none" >
                                <i class="bi bi-check-circle"></i>
                                <span>Migrable</span>
                            </div>
                        </div>
  {# <canvas id="canvas"></canvas> #}
					   <div id="info-card" class="info-card" style="display:none">
								<div style=" color: #55C5D0;" class="info-item">
								<span id="nom" style="align-items: center;display: flex;"></span> 
								<span id="prenom"></span>
							</div>
                            <div class="info-item">
								<span style="align-items: center;display: flex; color:#88969F"><img class="icon" src="{{asset('image/picto-rue.svg')}}"> Iris</span> 
								  <span style="align-items: center;display: flex; color:#88969F;gap:2px"><span  class="isCheckedsomme">2</span><img src="/discord/treeview/Fleche.svg" id="toggleInfoCardIris" style="width:13px;"></span>
							</div>
							<div class="info-item">
								<span style="align-items: center;display: flex; color:#88969F"><img class="icon" src="{{asset('image/picto-rue.svg')}}"> Rues</span> 
								  <span style="align-items: center;display: flex; color:#88969F;gap:2px"><span  class="isCheckedsomme"id="isCheckedsomme">45202</span><img src="/discord/treeview/Fleche.svg" id="toggleInfoRues" style="width:13px;"></span>
							</div>
							<div class="info-item">
								<span style="    align-items: center;display: flex;color:#88969F"><img class="icon" src="{{asset('image/picto-ville.svg')}}"> Villes</span> 
									 <span style="align-items: center;display: flex; color:#88969F;gap:2px"><span  class="isCheckedsomme" id="sommedistanceville">263</span><img src="/discord/treeview/Fleche.svg" id="toggleInfoVille" style="width:13px;"></span>
							</div>
                            <div class="info-item">
								<span style="    align-items: center;display: flex;color:#88969F"><img class="icon" src="{{asset('image/picto-ville.svg')}}"> Cluster</span> 
							 <span style="align-items: center;display: flex; color:#88969F;gap:2px">	<span  class="isCheckedsomme" id="sommedistancecluster">601</span><img src="/discord/treeview/Fleche.svg" id="toggleInfoCluster" style="width:13px;"></span>
							</div>
							<div class="info-item">
								<span style="    align-items: center;display: flex;color:#88969F"> <img class="icon" src="{{asset('image/picto-kilometres.svg')}}"> Klms </span> 
							 <span style="align-items: center;display: flex; color:#88969F;gap:2px"><span  class="isCheckedsomme" id="totalDistanceSum">12 </span><img src="/discord/treeview/Fleche.svg" id="toggleInfoCardIris" style="width:13px;"></span>

							</div>
							<div class="info-item">
								<span style="align-items: center;display: flex;color:#88969F" ><img class="icon" src="{{asset('image/picto-prises.svg')}}"> Prises</span> 
									 <span style="align-items: center;display: flex; color:#88969F;gap:2px"><span  class="isCheckedsomme" id="totalGeneral">125589 </span><img src="/discord/treeview/Fleche.svg" id="toggleInfoCardIris" style="width:13px;"></span>
							</div>
                             <div id="savedVoies">
                                <img src="/discord/treeview/Fleche.svg" style="width:15px; cursor: pointer;" id="toggleArrow" onclick="toggleList()">
                                <div class="isCheckedsomme" id="voieList" style="display: none;">
                                    <!-- The list of saved nomVoie values will be added here -->
                                </div>
                            </div>
                          
							{# <div class="info-item">
								<span class="Distribuer" > Distribuer</span> 
							</div> #}
						</div>
                        <div id="info-card-iris" class="info-card-iris" style="display:none">
                            <div class="info-item">
                                <span style="align-items: center; display: flex; color: #88969F">
                                    <img class="icon" src="{{asset('image/picto-rue.svg')}}"> Iris
                                </span> 
                                <span class="isCheckedsomme" id="irisTotalprise">0</span>
                              
                            </div>
                          <div class="info-item ">
                                <div id="savedIris">
                                    <img src="/discord/treeview/Fleche.svg" 
                                        style="width:15px; cursor: pointer;" 
                                        id="toggleArrowiris" 
                                        onclick="toggleListiris()">
                                    <div class="isCheckedsomme scrollable-content-iris"  id="irisList" style="display: none; height:152px">
                                        <!-- Streets will be populated here -->
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div id="info-card-Rues" class="info-card-Rues" style="display:none">
                            <div class="info-item">
                                <span style="align-items: center; display: flex; color: #88969F">
                                    <img class="icon" src="{{asset('image/picto-rue.svg')}}"> Rues
                                </span> 
                                <span class="isCheckedsomme" id="RuesTotalprise">0</span>
                              
                            </div>
                         <div class="">
                                <div id="savedRues">
                                    
                                    <div class="isCheckedsomme scrollable-content-iris"  id="RuesList" style="display: none; height:152px">
                                        <!-- Streets will be populated here -->
                                    </div>
                                </div>
                            </div>

                        </div>
                    <div id="info-card-Villes" class="info-card-Villes" style="display:none">
                            <div class="info-item">
                                <span style="align-items: center; display: flex; color: #88969F">
                                    <img class="icon" src="{{asset('image/picto-ville.svg')}}"> Villes
                                </span> 
                                <span class="isCheckedsomme" id="VillesTotalprise">0</span>
                              
                            </div>
                         <div class="">
                                <div id="savedVille">
                                    
                                    <div class="isCheckedsomme scrollable-content-iris"  id="VilleList" style="display: none; height:152px">
                                        <!-- Streets will be populated here -->
                                    </div>
                                </div>
                            </div>
                    

                        </div>
                        <div id="info-card-Cluster" class="info-card-Cluster" style="display:none">
                            <div class="info-item">
                                <span style="align-items: center; display: flex; color: #88969F">
                                    <img class="icon" src="{{asset('image/picto-ville.svg')}}"> Cluster
                                </span> 
                                <span class="isCheckedsomme" id="ClusterTotalprise">0</span>                            
                            </div>
                            <div class="">
                                <div id="savedCluster">
                                    
                                    <div class="isCheckedsomme scrollable-content-iris"  id="ClusterList" style="display: none; height:152px">
                                        <!-- Streets will be populated here -->
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div id="popup" style="display: none;">
                            <div class="popup-overlay">
                                <div class="popup-content">
                                    <header class="popup-header"></header>
                                    <div class="modal-content-event">
                                        <div class="modal-header-event">
                                            <div class="bloc1">
                                                <span class="icon-check" style="display: none; margin-left: 5px;">
                                                    <i class="bi bi-file-earmark-check" style="color: green; font-size: 20px;"></i>
                                                </span>
                                            </div>
                                            <span class="close-btn">&times;</span>
                                        </div>

                                        <form id="fileUploadForm" class="event-fileUploadForm" enctype="multipart/form-data">
                                            <!-- Switches with Icons -->
                                            <div class="form-check form-switch space-content">
                                                <label class="form-check-label" for="nb_fyr_adsl">
                                                    <img src="{{ asset('image/icon/adsl-violet.svg') }}" alt="Router Icon" class="icon" />
                                                    <div>ADSL</div>
                                                </label>
                                                <input class="form-check-input" type="checkbox" role="switch" id="nb_fyr_adsl" checked>
                                            </div>

                                            <div class="form-check form-switch space-content">
                                                <label class="form-check-label" for="nb_fyr_fttb">
                                                    <img src="{{ asset('image/icon/adsl-violet.svg') }}" alt="FTTB Icon" class="icon" />
                                                    <div>FTTB</div>
                                                </label>
                                                <input class="form-check-input" type="checkbox" role="switch" id="nb_fyr_fttb" checked>
                                            </div>

                                            <div class="form-check form-switch space-content">
                                                <label class="form-check-label" for="nb_fyr_thd">
                                                    <img src="{{ asset('image/icon/Fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
                                                    <div>FTTH</div>
                                                </label>
                                                <input class="form-check-input" type="checkbox" role="switch" id="nb_fyr_thd" checked>
                                            </div>

                                            <div class="form-check form-switch space-content">
                                                <label class="form-check-label" for="nb_fyr_mob_mono">
                                                    <img src="{{ asset('image/icon/Mobile-violet.svg') }}" alt="Mobile Icon" class="icon" />
                                                    <div>MOBILE</div>
                                                </label>
                                                <input class="form-check-input" type="checkbox" role="switch" id="nb_fyr_mob_mono" checked>
                                            </div>

                                            <div class="form-check form-switch space-content">
                                                <label class="form-check-label" for="nb_fyr_mob_multi_thd">
                                                    <img src="{{ asset('image/icon/mobile-fibre-violet.svg') }}" alt="Plug Icon" class="icon" />
                                                    <div>FTTH + MOBILE</div>
                                                </label>
                                                <input class="form-check-input" type="checkbox" role="switch" id="nb_fyr_mob_multi_thd" checked>
                                            </div>

                                            <div class="form-check form-switch space-content">
                                                <label class="form-check-label" for="nb_fyr_mob_multi_adsl">
                                                    <img src="{{ asset('image/icon/mobile-adsl-violet.svg') }}" alt="Plug Icon" class="icon" />
                                                    <div>ADSL + MOBILE</div>
                                                </label>
                                                <input class="form-check-input" type="checkbox" role="switch" id="nb_fyr_mob_multi_adsl" checked>
                                            </div>
                                        </form>
                                        <div class="footer-modal" style="position: relative; bottom: -30px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="RueHiarchy" class="info-card-Hiarchy" style="display:none;left: 310px; top: 8%;width:300px; min-height: 200px;">
                            <div class="RueHiarchyData" id="RueHiarchyData"></div>
                            <div class="RueHiarchyData" id="RueHiarchyDataSucess"style="display:none">
                                <div class="headerTreeSection" style="margin-top: 0px;border-radius: 10px 10px 0px 0px;">
                                    <div style="display: flex; align-items: center;">
                                        <span style="align-items: center;display: flex;color:#88969F"> 
                                            <img class="icon" src="{{asset('image/picto-kilometres.svg')}}"> Km 
                                        </span> 
                                        <span style="align-items: center;display: flex; color:#88969F;gap:2px;padding-left: 22px;">
                                            <span class="isCheckedsomme" id="totalDistanceSum">0</span>
                                        </span>
                                        <p class="sidebar-title"></p>
                                    </div>
                                    <span style="align-items: center;display: flex;color:#88969F">
                                        <img class="icon" src="{{asset('image/picto-prises.svg')}}"> Prises
                                    </span> 
                                    <span style="align-items: center;display: flex; color:#88969F;gap:2px">
                                        <span class="isCheckedsomme" id="totalGeneral">0</span>
                                    </span>
                                </div>
                                <div class="search-container">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon_effbe2 visible_effbe2" aria-hidden="true" role="img" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path fill="var(--icon-topbar)" fill-rule="evenodd" d="M15.62 17.03a9 9 0 1 1 1.41-1.41l4.68 4.67a1 1 0 0 1-1.42 1.42l-4.67-4.68ZM17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" clip-rule="evenodd"></path>
                                    </svg>
                                    <input type="text" id="TreeSearchinterfacePrises" placeholder="Rechercher" style=" outline: none; width: 100%; border-radius: 2px;">
                                    <i class="bi bi-x"></i>
                                </div>
                                <span class="SuccessUpdateDistribution blink_me">l'envoi à été effectué avec succes</span>
                            </div>
                        </div>
                        <div class="col-12 bottomPaneltimeline"  >
							{# <div class="bottom-panel-toggle" style="margin-right: 80%;" id="toggle-timeline">
								<i class="bi bi-caret-up-fill IcontoggleChart"></i>
							</div> #}
                        <div class="timeline"  style="height:320px;opacity:0.8;">
                            <div id="dl" style="height:230px;"></div>
                        </div>
						
                        </div>
                        <div class="productionPannel" id="displayproductionPanel" style="bottom: -100%;" >
                            {% include 'geomap/production/panelBasProduction.html.twig' %}
                            <div id="closePanel" class="ClosingBigPanel">
                                <div class="bottom-panel-toggle">
                                    <i class="bi bi-caret-up-fill " style="transform: rotate(180deg);"></i>
                                </div>
                            </div>
                            <div class="SearchContainerINPanel">
                                <div class="container mt-4"style="display: flex;justify-content: center;">
                                    <div class="input-group" >
                                        <span class="input-group-text textPanel"style="display:none">
                                            <i class="fas fa-search"></i> <!-- Icône FontAwesome -->
                                        </span>
                                        <input type="text" class="form-control forminputPanel" style="display:none" placeholder="Rechercher...">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="productionPannel" id="displayproductionPanelConsole" style="bottom: -100%;" >
                            {% include 'geomap/production/panelBasProduction.html.twig' %}
                            <div id="closePanelConsole" class="ClosingBigPanel">
                                <div class="bottom-panel-toggle">
                                    <i class="bi bi-caret-up-fill " style="transform: rotate(180deg);"></i>
                                </div>
                            </div>
                            <div id="hiddendiv" style="width:100%;">
                                <div id="command-line" class="command-line scrollbar-custom"></div>
                            </div>
                            <img id="terminalicon"style="padding-left:100px" class="icon" src="{{ asset('image/terminal-world.png') }}" alt="Terminal Icon">
                        </div>
                    <div class="col-12 bottomPanel"  id="displayChartPanel" style="bottom: -330px;">
                          <div style="display: flex;">
                                <div class="NameClickedPlace" id="openPanel" style="display:none" >
                                    <span style="font-size: 14px;padding: 6px;color:var(--coloredTextPrimary);"></span>
                                </div>
                                 <div class="NameClickedPlaceConsole" id="openPanelConsole" >
                                    <span style="font-size: 14px;padding: 6px;color:var(--coloredTextPrimary);"></span>
                                </div>
                                <div class="bottom-panel-toggle" style="margin-right: 80%;"onclick="toggleChartPanelDown()" id="toggle-Chart">
                                    <i class="bi bi-caret-up-fill IcontoggleChart"></i>
                                </div>
                          </div>
							<div class="ChartPannel" id="" style="">
                                {% include 'geomap/PanelBasPrises.html.twig' %}
							</div>
                        </div>
					

					<div id="right-sidebar-concepteur" class="right-sidebar-concepteur col-auto col-md-3 col-xl-2 px-0 d-flex flex-column align-items-end">
						<div class="rightpanl">
                            <div class="RightPanelToggle" id="HandleRightPanel"><i class="bi bi-caret-up-fill IconRightPanelToggle"></i></div>
                            <div class="panel">
                                {# <div class="icon" id=""><i class="fas fa-calendar-alt"></i></div> <!-- Calendrier -->
                                <div class="icon"><i class="fas fa-pen"></i></div>          <!-- Crayon -->
                                <div class="icon"><i class="fas fa-chart-bar"></i></div>    <!-- Graphique -->
                                <div class="icon"><i class="fas fa-table"></i></div>        <!-- Table -->
                                <div class="icon"><i class="fas fa-user"></i></div>         <!-- Utilisateur -->
                                <div class="icon"><i class="fas fa-folder"></i></div>       <!-- Dossier -->
                                <div class="icon"><i class="fas fa-lock"></i></div>         <!-- Cadenas -->
                                <div class="icon"><i class="fas fa-key"></i></div>          <!-- Clé -->
                                <div class="icon"><i class="fas fa-cog"></i></div> #}
                                {# <div class="icon" style="top: 360px;"><i class="bi bi-layout-split"></i></div> #}
                            </div>
                         <div class="interface">
                           		<!-- Section Formulaires -->
							<div class="Tree-Formulairees">
                                <div class="svg-topbar">
                                    <div class="search-container" >                                        
                                        <svg style="height: 14px;width: 14px;stroke: var(--icon-topbar);z-index: 1;position: absolute;left: 4px;" version=1.1 viewBox="0 0 14.8 14.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path fill="none" d=M5,5.3C5,2.7,7,0.7,9.6,0.7c2.5,0,4.6,2.1,4.6,4.6s-2.1,4.6-4.6,4.6C7,9.9,5,7.8,5,5.3z /><line x1=6.3 x2=0.5 y1=8.5 y2=14.3 /></g></svg>
                                        <input type="text"id="TreeSearchinterface" placeholder="Rechercher">
                                        <svg style="height: 14px;width: 14px;fill: var(--icon-topbar);position: absolute;right: 4px;" version=1.1 viewBox="0 0 12.2 16.6"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M12.2,7.9c0,3.1-2.3,5.6-5.3,6v2.7H5.3v-2.7c-3-0.4-5.3-3-5.3-6h1.7c0,2.4,2,4.4,4.4,4.4s4.4-1.9,4.4-4.4H12.2zM3.5,7.9V2.6C3.5,1.2,4.7,0,6.1,0c1.4,0,2.6,1.2,2.6,2.6v5.3c0,1.4-1.2,2.6-2.6,2.6C4.7,10.5,3.5,9.3,3.5,7.9z"/></svg>
                                    </div>
                                </div>
								<div class="headerTreeSection" style="margin-top:2px;" id="headerTreeSection">
								<div style="display: flex; align-items: center;">
                               
									<i class="bi bi-people-fill" style="color: var(--coloredTextPrimary); margin-left: 10px; margin-right: 4px; margin-left: 6px;"></i>
									<p class="sidebar-title">Effectifs</p>
								</div>
								<i class="bi bi-plus-circle icon-color"></i>
								</div>
								<div class="sectionDivider skeleton" style="margin-top: 2px; margin-bottom: 0px;"></div>
								<div class="Tree-Froms scrollbar-custom skeleton">
								     <div class="cards active-content" id="MiseEnpageCard" >
                                <div class="Tree-Roles-content scrollbar-custom">
                                    <div class="tree-view">
                                        <ul id="tree-root-roles-test"></ul>
                                    </div>
                                </div>
                            </div>
								</div>
							</div>
							<div class=" resizer resizer2"><spans>. . .</spans></div>
							<!-- Section Roles -->
							<div class="Tree-Rolees">                         
								<div class="headerTreeSection" id="headerTreeSectionAffectations" style="margin-top: 2px; margin-bottom: 2px;">
									<div style="display: flex; align-items: center;">
										<i class="fa-solid fa-location-dot" style="color:#a074c4; font-size: 17px; margin-right: 4px; margin-left: 5px;"></i>
										<p class="sidebar-title">Affectation</p>
									</div>
									<i class="bi bi-plus-circle icon-color"></i>
								</div>
                                     
                                <div class="svg-topbar" >
                                    <div class="search-container" >
                                        <svg style="height: 14px;width: 14px;stroke: var(--icon-topbar);z-index: 1;position: absolute;left: 4px;" version=1.1 viewBox="0 0 14.8 14.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path fill="none" d=M5,5.3C5,2.7,7,0.7,9.6,0.7c2.5,0,4.6,2.1,4.6,4.6s-2.1,4.6-4.6,4.6C7,9.9,5,7.8,5,5.3z /><line x1=6.3 x2=0.5 y1=8.5 y2=14.3 /></g></svg>
                                            <input type="text"id="TreeSearchinterface2" placeholder="Rechercher">
                                        <svg style="height: 14px;width: 14px;fill: var(--icon-topbar);position: absolute;right: 4px;" version=1.1 viewBox="0 0 12.2 16.6"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M12.2,7.9c0,3.1-2.3,5.6-5.3,6v2.7H5.3v-2.7c-3-0.4-5.3-3-5.3-6h1.7c0,2.4,2,4.4,4.4,4.4s4.4-1.9,4.4-4.4H12.2zM3.5,7.9V2.6C3.5,1.2,4.7,0,6.1,0c1.4,0,2.6,1.2,2.6,2.6v5.3c0,1.4-1.2,2.6-2.6,2.6C4.7,10.5,3.5,9.3,3.5,7.9z"/></svg>
                                    </div>
                                </div>
                
								<div class="sectionDivider skeleton" style="margin-top: 2px; margin-bottom: 0px;"></div>
								<div class="Tree-Roles-content scrollbar-custom skeleton">
								 <div class="cardsSturct" id="structureCard"> 
                                    <div  class="Tree-Roles-content scrollbar-custom">
                                        <div class="tree-view-api">
                                            <ul id="tree-root-roles-test-api"></ul>
                                        </div>
                                </div>
                            </div>
								</div>
							</div>
                        </div>

                        </div>
                    </div>
                    

<div class="InfoCardSatisfaction info-card-micro" id="microCard" style="display: none;">
    <div class="statisfactionCard scrollbar-custom">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <span id="output">Parlez maintenant...</span>
            
        </div>
        <br/>
        
    </div>
</div>

<ul id="dropdownTypes" class="ULInputMessage" style="display:none;">
    <li class="ListInputMessage" id="uploadFileBtn"><span>Text</span></li>
	<li class="ListInputMessage"><span>Long Texte</span></li>
	<li class="ListInputMessage"><span>Email</span></li>
	<li class="ListInputMessage"><span>Date</span></li>
	<li class="ListInputMessage"><span>Date RDV</span></li>
	<li class="ListInputMessage"><span>Addresse</span></li>
</ul>


<ul id="dropdownRoles" class="ULInputMessage" style="display: none;bottom: auto;">
</ul>



<div id="createInputIcons" headlist="{{asset('discord/headlist.svg')}}" crayon="{{asset('discord/crayon.svg')}}" threepoint="{{asset('discord/threepointInfor.svg')}}" delete="{{asset('discord/supprimer.svg')}}" listInput="{{asset('discord/listInput.svg')}}"  ajouter="{{asset('discord/ajouter.svg')}}"></div>
<div id="TreeViewIcon" folder="{{asset('discord/treeview/dossier.svg')}}" fleche="{{asset('discord/treeview/Fleche.svg')}}" flecheFields="{{asset('discord/treeview/Fleche-contour.svg')}}" databaseIcon="{{asset('discord/treeview/databaseIcon.svg')}}"></div>



<div class="productionPannel" id="displayPanelSearching" style="bottom: -100%;" >
    <div class="DetailsCAlenderContainer">
        <div class="CmdInterventionTable scrollbar-custom ">
        </div>
    </div>
    <div id="closeBigPanel" class="ClosingBigPanel" onclick="closingBigPanel()">
        <div class="bottom-panel-toggle">
            <i class="bi bi-caret-up-fill" style="transform: rotate(180deg);"></i>
        </div>
    </div>
    {# <div class="SearchContainerINSearchPanel" style="display:none">
        <div class="container mt-4"style="display: flex;justify-content: center;">
            <div class="input-group" >
                <span class="input-group-text textPanel"style="height:100%;">
                    <i class="fas fa-search"></i> <!-- Icône FontAwesome -->
                </span>
                <input type="text" class="form-control forminputSearchPanel"  placeholder="Rechercher...">
            </div>
        </div>
    </div> #}
</div>


<script>

    var HierarchyData = {{ HierarchyData|json_encode|raw }};
    var effectifs = {{ effectifs|json_encode|raw }};
    var Migrable = {{ Migrable|json_encode|raw }};

//console.log(effectifs);
</script>



<script>
    var jwtToken = "{{ jwt }}";
  
	var cpv = "{{ cpv }}";
    var userIdLogIn = "{{ userId }}";
    var pointOfSaleId= "{{pointOfSaleId}}"

</script>
<script src="https://cdn.jsdelivr.net/npm/jquery-circle-progress@1.2.2/dist/circle-progress.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/@turf/turf@6.5.0/turf.min.js"></script>

<script src="https://www.gstatic.com/charts/loader.js"></script>
<script src="{{ asset('geomap/geomap.js') }}" defer></script>
<script src="{{ asset('geomap/StreetHiarchyHandler.js') }}" defer></script>

<script src="{{ asset('geomap/roconizeVoicePrises.js') }}" defer></script>
<script src="{{ asset('geomap/geomapGraphePrises.js') }}" defer></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

<!-- Popper.js, then Bootstrap JS -->
<script src="https://code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js" defer></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<!-- Include jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Include jQuery UI -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.min.js"></script>

<script src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet.markercluster/dist/leaflet.markercluster.js"></script>
<script src="https://api.mapbox.com/mapbox-gl-js/v3.1.0/mapbox-gl.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/underscore.js/1.9.1/underscore-min.js"></script>
<script src="https://code.jquery.com/jquery-1.12.4.min.js" integrity="sha384-nvAa0+6Qg9clwYCGGPpDQLVpLNn0fRaROjHqs13t4Ggj3Ez50XnGQqc/r8MhnRDZ" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script src="{{ asset('geomap/prises/js/jquery.dateline.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script  src="{{ asset('geomap/PanelGraphe.js') }}" ></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ asset('cmd/bundle.min.js') }}"></script><script src="{{ asset('cmd/app.min.js') }}"></script><script src="{{ asset('cmd/script.js') }}"></script>
<script>
async function fetchDataArretCuivre() {
    if (!cpv) {
        console.error("CPV is undefined");
        return [];
    }

    const url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu/${cpv}?page=1`;
    try{
        const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            var data= await response.json();
    }catch (error) {
            console.error(`Fetch error for ${url}:`, error);
            return { success: false, error: error.message };
    }
    if (!data || data.success === false) return [];

    let events = [];
    Object.keys(data).forEach(year => {
        Object.keys(data[year]).forEach(month => {
            if (data[year][month]?.clusters) {
                let formattedMonth = month.toString().padStart(2, '0');
                let eventDate = `${year}-${formattedMonth}-01 08:00:00`;

                data[year][month].clusters.forEach(cluster => {
                    events.push({
                        id: cluster.code_cluster,
                        start: eventDate,
                        text: cluster.libelle_cluster,
                        class: "col-orange",
                        attributes: { "data-id": cluster.code_cluster }
                    });
                });
            }
        });
    });

    return events;
}
async function fetchData(url) {
    try {
        if (!jwtToken) {
            throw new Error("JWT Token is missing");
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error(`Fetch error for ${url}:`, error);
        return { success: false, error: error.message };
    }
}
// Récupération des données "Prospections"
async function fetchDataProspections(clusterCode) {
    if (!pointOfSaleId) {
        console.error("pointOfSaleId is undefined");
        return [];
    }
   // var clusterCode=localStorage.getItem("clusterCode");
    var url = `https://api.nomadcloud.fr/api/prospections-cycles-list/${pointOfSaleId}?page=1`;
    if(clusterCode){
         url =+ `&cluster_code=${clusterCode}`;
    }
    const data = await fetchData(url);

    if (!data || data.success === false || !Array.isArray(data)) {
        console.error('Invalid or missing data for prospections:', data);
        return [];
    }
    return extractEventsFromClusters(data, "col-red");
}

const eventStartDates = {}; // Stocke les dates des événements

function extractEventsFromClusters(clusters = [], color) {
    let events = [];

    clusters.forEach(cluster => {
        if (cluster.distributions && typeof cluster.distributions === 'object') {
            Object.keys(cluster.distributions).forEach(dateStr => {
                let eventDate = new Date(dateStr);
                if (!isNaN(eventDate.getTime())) {
                    eventDate.setDate(eventDate.getDate() + (cluster.nbre_jours_jachere || 0));
                    let formattedDate = eventDate.toISOString().split("T")[0] + " 08:00:00";

                    eventStartDates[cluster.code_cluster] = formattedDate;

                    events.push({
                        id: cluster.code_cluster,
                        start: formattedDate,
                        text: cluster.libelle_cluster,
                        class: color,
                        attributes: { "data-id": cluster.code_cluster }
                    });
                }
            });
        }
    });

    return events;
}
function initTimeline(events) {
    if (!events || events.length === 0) {
        console.warn("No events to display on the timeline.");
        return;
    }

    let currentDate = new Date().toISOString().split('T')[0];

    $('#dl').dateline({
        start: "2024-01-01",
        end: "2028-12-31",
        cursor: currentDate,
        bands: [
            { size: "50%", scale: Dateline.MONTH, interval: 600 },
            { size: "25%", layout: "overview", scale: Dateline.YEAR, interval: 600 }
        ],
        events: events.map(event => ({
            ...event,
            attributes: { 
                "data-id": event.id,
                "data-start": event.start 
            }
        }))
    });

    // Gestion des clics sur les événements
$(document).on('click', '.d-event.col-red', async function() {
    const clusterId = $(this).data('id') || $(this).attr('data-id');
    const dataEventDate = eventStartDates[clusterId] || null;

    let formattedDate;
    if (dataEventDate) {
        let dateObj = new Date(dataEventDate);
        dateObj.setDate(dateObj.getDate() - 60);
        formattedDate = dateObj.toISOString().split('T')[0].split('-').reverse().join('-');
    } else {
        formattedDate = 'Date non trouvée';
    }

    if (!clusterId) {
     console.warn("Aucun cluster ID trouvé !");
    } 
    // Exécuter la fonction findClusterCoordinatestime
    fetchMissingcities(clusterId); 
    // await window.findClusterCoordinatestime(clusterId, '');
    // Affichage du popup avec SweetAlert2
    /*Swal.fire({
        title: 'Détails de l\'événement',
        html: `<strong>Cluster ID:</strong> ${clusterId} <br> 
               <strong>Date ajustée (-60 jours):</strong> ${formattedDate}`,
        icon: 'info',
        confirmButtonText: 'Fermer'
    });*/

    console.log("Popup affiché pour l'événement :", clusterId);
});

}
async function initAllData() {
    try {
        const [arretCuivreEvents, prospectionsEvents] = await Promise.all([
            fetchDataArretCuivre(),
            fetchDataProspections()
        ]);

        const allEvents = [...arretCuivreEvents, ...prospectionsEvents];
        initTimeline(allEvents);
    } catch (error) {
        console.error("Erreur lors de l'initialisation des données :", error);
    }
}
initAllData();

</script>


<script>
setInterval(() => {
    const totalprises = parseFloat(localStorage.getItem("selectedTotalprises") || "125589");
    const selectedTotalSum = parseFloat(localStorage.getItem("totalGeneral") || "0");

    document.getElementById("totalGeneral").innerText = selectedTotalSum;


    const sommedistancecluster = localStorage.getItem("nomberprisesclutser") || "0";


    document.getElementById("sommedistancecluster").innerText = sommedistancecluster;

    const sommedistanceville = localStorage.getItem("nomberprisesVille") || "0";


    document.getElementById("sommedistanceville").innerText = sommedistanceville;

    const totalDistanceSum = parseFloat(localStorage.getItem("totalDistanceSum") || "0");

    document.getElementById("totalDistanceSum").innerText = totalDistanceSum;
    const isCheckedsomme = localStorage.getItem("isCheckedsomme") || "0";

    document.getElementById("isCheckedsomme").innerText = isCheckedsomme;

    const nom = localStorage.getItem("selectednom");

    document.getElementById("nom").innerText = nom;
    const prenom = localStorage.getItem("selectedprenom");

    document.getElementById("prenom").innerText = prenom;

    const savedVoies = JSON.parse(localStorage.getItem("savedVoies")) || [];

    // Get the container where the data will be displayed
    const savedVoiesContainer = document.getElementById("voieList");

    // Clear any existing content in the container
    savedVoiesContainer.innerHTML = "";

    // Loop through each saved nomVoie and create an element to display it
    savedVoies.forEach((nomVoie) => {
        const voieElement = document.createElement('div');
        voieElement.style.display = 'flex'; // Optional styling for alignment

        // Add the nomVoie value
        voieElement.innerHTML = `
            <span>${nomVoie.toLowerCase()}</span>
        `;

        // Append the newly created element to the container
        savedVoiesContainer.appendChild(voieElement);
    });

    const VilleList = document.getElementById('VilleList');
    const totalElement = document.getElementById('VillesTotalprise'); // Élément pour afficher la somme totale
    let streets = localStorage.getItem("savedVilleslibelle");

    if (streets) {
        try {
            streets = JSON.parse(streets); // Convertir en tableau
        } catch (error) {
            console.error("Erreur lors du parsing de 'savedClusterslibelle' :", error);
            return;
        }
    } else {
        streets = []; // Assurer que streets est un tableau vide s'il n'y a rien en localStorage
    }

    if (VilleList) {
        VilleList.innerHTML = streets.map(street => 
            `<span style="display: flex; justify-content: space-between;">
                <span style="align-items: center; display: flex;">${street.libelle}</span> 
                <span class="isCheckedsomme">${street.total}</span>
            </span>`
        ).join('');
        VilleList.style.display = 'block';
    } else {
        console.error("⚠️ Élément #VilleList introuvable !");
    }


    // Calcul de la somme totale
    if (totalElement) {
        const totalSum = streets.reduce((sum, street) => sum + (street.total || 0), 0);
        totalElement.textContent = totalSum;
    } else {
        console.error("⚠️ Élément #ClusterTotalprise introuvable !");
    }
    
    const ClusterList = document.getElementById('ClusterList');
    const totalElementCluster = document.getElementById('ClusterTotalprise'); // Élément pour afficher la somme totale
    let streetsCuster = localStorage.getItem("savedClusterslibelle");

    if (streetsCuster) {
        try {
            streetsCuster = JSON.parse(streetsCuster); // Convertir en tableau
        } catch (error) {
            console.error("Erreur lors du parsing de 'savedClusterslibelle' :", error);
            return;
        }
    } else {
        streetsCuster = []; // Assurer que streetsCuster est un tableau vide s'il n'y a rien en localStorage
    }

    if (ClusterList) {
        ClusterList.innerHTML = streetsCuster.map(street => 
            `<span style="display: flex; justify-content: space-between;">
                <span style="align-items: center; display: flex;">${street.libelle}</span> 
                <span class="isCheckedsomme">${street.total}</span>
            </span>`
        ).join('');
        ClusterList.style.display = 'block';
    } else {
        console.error("⚠️ Élément #ClusterList introuvable !");
    }


    // Calcul de la somme totale
    if (totalElementCluster) {
        const totalSum = streetsCuster.reduce((sum, street) => sum + (street.total || 0), 0);
        totalElementCluster.textContent = totalSum;
    } else {
        console.error("⚠️ Élément #ClusterTotalprise introuvable !");
    }
    

    const RuesList = document.getElementById('RuesList');
    const totalElementRues= document.getElementById('RuesTotalprise'); // Élément pour afficher la somme totale
    let streetsRues = localStorage.getItem("savedRueslibelle");

    if (streetsRues) {
        try {
            streetsRues = JSON.parse(streetsRues); // Convertir en tableau
        } catch (error) {
            console.error("Erreur lors du parsing de 'savedRueslibelle' :", error);
            return;
        }
    } else {
        streetsRues = []; // Assurer que streetsRues est un tableau vide s'il n'y a rien en localStorage
    }

    if (RuesList) {
        RuesList.innerHTML = streetsRues.map(street => 
            `<span style="display: flex; justify-content: space-between;">
                <span style="align-items: center; display: flex;">${street.libelle}</span> 
                <span class="isCheckedsomme">${street.total}</span>
            </span>`
        ).join('');
        RuesList.style.display = 'block';
    } else {
        console.error("⚠️ Élément #RuesList introuvable !");
    }


    // Calcul de la somme totale
    if (totalElementRues) {
        const totalSum = streetsRues.reduce((sum, street) => sum + (street.total || 0), 0);
        totalElementRues.textContent = totalSum;
    } else {
        console.error("⚠️ Élément #ClusterTotalprise introuvable !");
    }
}, 500);

function toggleList() {
    const voieList = document.getElementById("voieList");
    const arrow = document.getElementById("toggleArrow");

    // Toggle the visibility of the list
    if (voieList.style.display === "none") {
        voieList.style.display = "block";  // Show the list
        arrow.style.transform = "rotate(90deg)"; // Rotate the arrow to indicate it's opened
    } else {
        voieList.style.display = "none";  // Hide the list
        arrow.style.transform = "rotate(0deg)"; // Reset the arrow rotation
    }
}

</script>




<script>
  document.getElementById('headerTreeSection').addEventListener('click', function () {
    const initialDropdown = document.getElementById('initialDropdown');
    const fileUploadForm = document.getElementById('fileUploadForm');
    const Migrables = document.querySelector('.Migrables');
    initialDropdown.style.display = 'block';
  });

  document.querySelector('.close-btnInitialDropdown').addEventListener('click', function () {
    document.getElementById('initialDropdown').style.display = 'none';
  });

  document.addEventListener('click', function (event) {
    const headerTreeSection = document.getElementById('headerTreeSection');
  });
</script>


<script>
{# let map;
document.addEventListener("DOMContentLoaded", function () {
    
let rotationInterval = null;


let theme = localStorage.getItem("theme") || "light"; 
mapboxgl.accessToken = 'pk.eyJ1IjoicmdvdW50aXRpIiwiYSI6ImNtMnA1bHJ5NDBuczcycnNieGsyamVjOTMifQ.FjXmzR2E_Di8YWn8nfTPog';

let currentBearing = 0; // Initial rotation angle
let is3D = true; // Start with 3D view enabled

// Function to initialize the map
function initializeMap(theme) {
    return map = new mapboxgl.Map({
        container: 'map',
        style: theme === "darkblue" 
            ? 'mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw' 
            : 'mapbox://styles/mapbox/standard',
            //: 'mapbox://styles/mapbox/light-v10',
        center: [2.2945, 48.8584],
        zoom: 16, 
        pitch: 60,  // Set initial pitch for 3D view
        bearing: -20 // Optional: Adjust the initial bearing if needed
    });
     map.addControl(new mapboxgl.NavigationControl());
}

// Function to switch to the 3D view
function setTiltedView() {
    map.easeTo({
        pitch: 60,  // Tilt the view to 60°
        bearing: -20,
        duration: 1000
    });
}

// Function to switch to the flat 2D view
function setFlatView() {
    map.easeTo({
        pitch: 0,  // No tilt for the 2D view
        bearing: 0,
        duration: 1000
    });
}

// Function to rotate the map by 45°
function rotateMap() {
    currentBearing += 45; // Add 45° on each click
    if (currentBearing >= 360) {
        currentBearing = 0; // Reset angle to 0 after 360°
    }
    map.easeTo({
        bearing: currentBearing,
        duration: 1000
    });
}

// Initialize the map
var map = initializeMap(theme);
map.addControl(new mapboxgl.NavigationControl());

map.on('load', () => {
    map.addLayer({
        id: '3d-buildings',
        source: 'composite',
        'source-layer': 'building',
        type: 'fill-extrusion',
        paint: {
            'fill-extrusion-color': '#89b9ed',
            'fill-extrusion-height': ['get', 'height'],
            'fill-extrusion-base': ['get', 'min_height'],
            'fill-extrusion-opacity': 0.6
        }
    });
});

// Toggle button event
document.getElementById("toggleView").onclick = function() {
    if (is3D) {
        setFlatView(); // Switch to 2D view
        this.textContent = '3D'; // Update button text
    } else {
        setTiltedView(); // Switch to 3D view
        this.textContent = '2D'; // Update button text
    }
    is3D = !is3D; // Toggle the state
};

document.getElementById("rotateMap").onclick = rotateMap;

// Configuration du clustering
const CLUSTER_SOURCE_ID = 'clusters-source';
let markers = [];

function initializeClusterSource() {
    if (!map.getSource(CLUSTER_SOURCE_ID)) {
        map.addSource(CLUSTER_SOURCE_ID, {
            type: 'geojson',
            data: { type: 'FeatureCollection', features: [] },
            cluster: true,
            clusterMaxZoom: 14,
            clusterRadius: 50
        });
    }

    // Couche des clusters
    if (!map.getLayer('clusters-layer')) {
        map.addLayer({
            id: 'clusters-layer',
            type: 'circle',
            source: CLUSTER_SOURCE_ID,
            filter: ['has', 'point_count'],
            paint: {
                'circle-color': [
                    'step',
                    ['get', 'point_count'],
                    '#51bbd6',
                    10,
                    '#51bbd6',
                    100,
                    '#51bbd6'
                ],
                'circle-radius': [
                    'step',
                    ['get', 'point_count'],
                    20,
                    10,
                    30,
                    100,
                    40
                ]
            }
        });
    }

    // Texte des clusters
    if (!map.getLayer('cluster-count')) {
        map.addLayer({
            id: 'cluster-count',
            type: 'symbol',
            source: CLUSTER_SOURCE_ID,
            filter: ['has', 'point_count'],
            layout: {
                'text-field': '{point_count_abbreviated}',
                'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
                'text-size': 12
            }
        });
    }

    // Gestion des marqueurs individuels
    updateMarkers();
}

function updateMarkers() {
    markers.forEach(marker => marker.remove());
    markers = [];

    const features = map.querySourceFeatures(CLUSTER_SOURCE_ID, {
        filter: ['!', ['has', 'point_count']]
    });

    features.forEach(feature => {
        const el = document.createElement('div');
        el.className = 'custom-marker';
        el.style.width = '30px';
        el.style.height = '40px';
        el.style.borderRadius = '50%';
        el.style.display = 'flex';
        el.style.alignItems = 'center';
        el.style.justifyContent = 'center';

        const img = document.createElement('img');
        img.src = 'image/icon_7days.png';  // Set the image source
        img.style.width = '100%';  // Adjust size if necessary
        img.style.height = '100%'; // Adjust size if necessary
       
        el.appendChild(img);

        const marker = new mapboxgl.Marker(el)
            .setLngLat(feature.geometry.coordinates)
            .addTo(map);

        markers.push(marker);
    });
}


// Récupérer les données GeoJSON et mettre à jour la carte
async function fetchAndDisplayEarthquakes(codecluster, codeinsee) {
    let url = `https://api.nomadcloud.fr/fileAttached/geo_map/prises_parc/${cpv}/coordinates/${codecluster}`;
    
    if (codeinsee) {
        url += `/${codeinsee}.geojson`;
    }else{
        url += `/coordinates.geojson`;
    }
    
 

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Erreur HTTP ! Statut : ${response.status}`);
        }
        const data = await response.json();

        if (data && data.features) {
            initializeClusterSource();
            updateClusterData(data);
          
        }
    } catch (error) {
        console.error("❌ Erreur lors du chargement des données GeoJSON :", error);
    }
}


// Mettre à jour la source des clusters avec les nouvelles données
function updateClusterData(data) {
    const source = map.getSource(CLUSTER_SOURCE_ID);
    if (source) {
        source.setData({
            type: 'FeatureCollection',
            features: data.features.map(feature => ({
                ...feature,
                geometry: {
                    ...feature.geometry,
                    coordinates: feature.geometry.coordinates.slice(0, 2).reverse() // Inverser les coordonnées (longitude, latitude)
                }
            }))
        });

        map.once('idle', () => {
            updateMarkers();
            map.triggerRepaint();
        });
    } else {
        console.error("⚠️ Source de clusters non trouvée !");
    }
}
function updateClusterDatacuivre(data) {
    const source = map.getSource(CLUSTER_SOURCE_ID);
    if (source) {
        // Conversion des données brutes en FeatureCollection GeoJSON
        const geojsonData = {
            type: 'FeatureCollection',
            features: data.map(item => ({
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [
                        parseFloat(item.longitude),
                        parseFloat(item.latitude)
                    ]
                },
                properties: {
                    nom_voie: item.nom_voie,
                    numr_voie: item.numr_voie,
                    total_prises: item.total_prises
                    // Ajouter d'autres propriétés si nécessaire
                }
            }))
        };

        source.setData(geojsonData);

        map.once('idle', () => {
            updateMarkers();
            map.triggerRepaint();
        });
    } else {
        console.error("⚠️ Source de clusters non trouvée !");
    }
}

// Mettre à jour les marqueurs quand la carte bouge ou zoome
map.on('move', updateMarkers);
map.on('zoom', updateMarkers);


// Charger les données et afficher la carte


window.findClusterCoordinates = async function(codecluster, codeinsee) {
 
      
    // fetchAndDisplayEarthquakes(codecluster, codeinsee);
  
};

async function fetchdataCoordinates(codecluster, codeinsee ) {  // default to empty string if null
    try {
        // If codeinsee is null or empty, it will be left out from the URL
        let url = `https://api.nomadcloud.fr/api/interventions-places-coordinates-v2/${cpv}/${codecluster}?page=1`;

        // Only append codeInsee parameter if codeinsee is not null or empty
        if (codeinsee) {
            url += `&codeInsee=${codeinsee}`;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`, // Assurez-vous que jwtToken est défini
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Response fetchdataCoordinates:",data)
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
}


map.on('data', (e) => {
    if (e.sourceId === CLUSTER_SOURCE_ID && e.isSourceLoaded) {
  
        map.triggerRepaint();
    }
});

// Événements de mouvement de la carte
map.on('move', updateMarkers);
map.on('zoom', updateMarkers);

// Fonction principale


    // Vérification régulière du changement de thème
    setInterval(() => {
        let newTheme = localStorage.getItem("theme");
        
        if (newTheme !== theme) {
            theme = newTheme; // Mise à jour du thème actuel
            
            if (theme === "light") {
                location.reload(); // Recharger pour appliquer le style light correctement
            } else {
                map.setStyle('mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw'); // Mise à jour du style
            }
        }
    }, 500); // Vérifie toutes les secondes





 // Variables globales pour la rotation


let rotationBound = false;
let irisPolygons = []; // Stocke les IDs des polygones IRIS sauvegardés

// Fonction pour charger les données IRIS et afficher le polygone
async function loadIrisData(codeIris) {
  if (!codeIris) return;

  const url = `https://public.opendatasoft.com/api/explore/v2.1/catalog/datasets/georef-france-iris/records?where=iris_code='${codeIris}'`;

  try {
    const response = await fetch(url);
    const data = await response.json();
    if (!data.results || data.results.length === 0) {
      console.error('Aucune donnée trouvée pour ce code IRIS');
      return;
    }
    const geoShape = data.results[0].geo_shape;
    addIrisPolygonToMap(geoShape);
  } catch (error) {
    console.error('Erreur lors de la récupération des données', error);
  }
}

// Ajoute le polygone IRIS sur la carte
function addIrisPolygonToMap(geoShape) {
  if (!geoShape || !geoShape.geometry || geoShape.geometry.type !== 'Polygon') {
    console.error('La géométrie IRIS est mal formée');
    return;
  }

  const isChecked = localStorage.getItem("isChecked") === "true";
  const polygonIndex = irisPolygons.length;
  const polygonId = `iris-shape-${polygonIndex}`;

  // Ajout de la source GeoJSON
  map.addSource(polygonId, {
    type: 'geojson',
    data: {
      type: 'Feature',
      geometry: geoShape.geometry
    }
  });

  // Ajout de la couche de remplissage
  map.addLayer({
    id: `iris-fill-${polygonIndex}`,
    type: 'fill',
    source: polygonId,
    paint: {
      'fill-color': '#ff9800',
      'fill-opacity': 0.3
    }
  });

  // En fonction de la valeur de isChecked, on conserve le polygone ou on le retire après 5 secondes
  if (isChecked) {
    irisPolygons.push(polygonId);
  } else {
    setTimeout(() => {
      if (map.getSource(polygonId)) {
        map.removeLayer(`iris-fill-${polygonIndex}`);
        map.removeSource(polygonId);
      }
    }, 5000);
  }

  // Centrage de la carte sur le polygone
  const coordinates = geoShape.geometry.coordinates[0];
  const bounds = coordinates.reduce((b, coord) => b.extend(coord), new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));
  map.fitBounds(bounds, { padding: 20, pitch: 60, zoom: 16, bearing: -20 });

  // Démarrer la rotation après le zoom
  map.once('moveend', startRotationAfterZoom);

  // Ajout de la couche 3D des bâtiments si elle n'existe pas déjà
  if (!map.getLayer('3d-buildings')) {
    map.addLayer({
      id: '3d-buildings',
      source: 'composite',
      'source-layer': 'building',
      type: 'fill-extrusion',
      paint: {
        'fill-extrusion-color': '#888',
        'fill-extrusion-height': ['get', 'height'],
        'fill-extrusion-base': ['get', 'min_height'],
        'fill-extrusion-opacity': 0.7
      }
    });
    if (map.getSource('iris-shape')) {
            map.addLayer({
                id: 'iris-border',
                type: 'line',
                source: 'iris-shape', // Même source que le fill
                layout: {},
                paint: {
                    'line-color': '#ff9800', // Rouge
                    'line-width': 2, // Épaisseur
                    'line-opacity': 1, // Opacité
                }
            });
    }

  }

  // Ajout des écouteurs d'évènements pour la rotation (une seule fois)
  if (!rotationBound) {
    rotationBound = true;
    map.on('zoomend', startRotationAfterZoom);
    map.on('click', stopRotation);
  }
}

// Supprime tous les polygones IRIS ajoutés
function removeAllIrisPolygons() {
  irisPolygons.forEach((polygonId, index) => {
    if (map.getLayer(`iris-fill-${index}`)) {
      map.removeLayer(`iris-fill-${index}`);
    }
    if (map.getSource(polygonId)) {
      map.removeSource(polygonId);
    }
  });
  irisPolygons = [];
}

// Démarre la rotation de la carte
function startRotation() {
  if (!rotationInterval) {
    rotationInterval = setInterval(() => {
      currentBearing = (currentBearing + 1) % 360;
      map.easeTo({ bearing: currentBearing, duration: 100 });
    }, 80);
  }
}

// Arrête la rotation de la carte
function stopRotation() {
  if (rotationInterval) {
    clearInterval(rotationInterval);
    rotationInterval = null;
  }
}

// Démarre la rotation après la fin du zoom
function startRotationAfterZoom() {
  if (!rotationInterval) {
   // setTimeout(startRotation, 80);
  }
}

// Fonctions pour activer la vue 3D (inclinaison) et revenir à la vue 2D
function setTiltedView() {
  map.easeTo({ pitch: 60, duration: 1000 });
}

function setFlatView() {
  map.easeTo({ pitch: 0, duration: 1000 });
}

// Vérification périodique de la valeur de codeIris dans localStorage (même onglet)
let previousCodeIris = localStorage.getItem("codeIris");
setInterval(() => {
  const currentCodeIris = localStorage.getItem("codeIris");
  if (currentCodeIris && currentCodeIris !== previousCodeIris) {
    previousCodeIris = currentCodeIris;
    loadIrisData(currentCodeIris);
  }
}, 1000);

// Écoute des changements de codeIris dans d'autres onglets
window.addEventListener("storage", (event) => {
  if (event.key === "codeIris") {
    loadIrisData(event.newValue);
  }
});

// Charger les données IRIS au démarrage de la page
document.addEventListener("DOMContentLoaded", () => {
  const initialCodeIris = localStorage.getItem("codeIris");
  if (initialCodeIris) {
    loadIrisData(initialCodeIris);
  }
});

// --- Fonctions liées à la recherche de rue ---

// Géocodage d'une adresse via Nominatim
async function geocode(address) {
  const geocodeUrl = `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(address)}&format=json&addressdetails=1&limit=1`;
  const response = await fetch(geocodeUrl);
  if (!response.ok) {
    throw new Error("Erreur de géocodage (Nominatim inaccessible).");
  }
  return response.json();
}

// Formule de Haversine pour calculer la distance entre deux points
function haversine(coord1, coord2) {
  const R = 6371;
  const toRad = x => x * Math.PI / 180;
  const dLat = toRad(coord2[1] - coord1[1]);
  const dLon = toRad(coord2[0] - coord1[0]);
  const lat1 = toRad(coord1[1]);
  const lat2 = toRad(coord2[1]);
  const a = Math.sin(dLat / 2) ** 2 + Math.sin(dLon / 2) ** 2 * Math.cos(lat1) * Math.cos(lat2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Calcul du temps de marche (en minutes) à partir d'une distance en km
function calculateWalkingTime(distance) {
  const walkingSpeed = 5; // km/h
  return (distance / walkingSpeed) * 60;
}

/**
 * Supprime les features correspondant à une rue donnée en filtrant par la clé.
 */
function removeStreet() {
  const nomvoie = localStorage.getItem('selectedVoie');
  const selectedComplement = localStorage.getItem('selectedComplement');
  if (!nomvoie || !selectedComplement) {
    console.warn('Aucune adresse trouvée dans localStorage.');
    return;
  }
  const addressKey = `${nomvoie}, ${selectedComplement}`;

  const streetSource = map.getSource('streetLayer');
  if (streetSource) {
    // Récupérer les données actuelles de la source via getSource()._data n'est pas idéal,
    // il serait préférable de stocker la FeatureCollection dans une variable globale.
    const currentData = streetSource._data;
    const newFeatures = currentData.features.filter(feature => feature.properties.key !== addressKey);

    // Mise à jour de la source avec les nouvelles features
    streetSource.setData({
      type: 'FeatureCollection',
      features: newFeatures
    });

    // Si aucune rue n'est présente, supprimer aussi les polygones IRIS
    if (newFeatures.length === 0) {
      removeAllIrisPolygons();
    }
  }
}

/**
 * Recherche une rue et ajoute ses features sans supprimer celles déjà affichées.
 * Si une rue pour la même adresse existe déjà, elle est d'abord supprimée.
 */
async function searchStreet() {
  const nomvoie = localStorage.getItem('selectedVoie');
  const selectedComplement = localStorage.getItem('selectedComplement');

  if (!nomvoie || !selectedComplement) {
    console.warn('Aucune adresse trouvée dans localStorage.');
    return;
  }

  const address = `${nomvoie}, ${selectedComplement}`;
  console.log('Adresse:', address);

  // Supprime d'éventuelles features existantes pour éviter les doublons
  removeStreet();

  try {
    const results = await geocode(address);
    if (!results || results.length === 0) {
      throw new Error("Adresse introuvable. Veuillez vérifier l'orthographe.");
    }
    const { lat, lon, display_name } = results[0];
    const radius = 1000;
    // Extraction simplifiée du nom de la rue depuis display_name
    const streetName = display_name ? display_name.split(',')[0] : nomvoie;

    // Requête Overpass pour récupérer la géométrie de la rue
    const overpassQuery = `
      [out:json][timeout:25];
      (
        way["name"="${streetName}"](around:${radius},${lat},${lon});
      );
      out geom;
    `;
    const overpassUrl = `https://overpass-api.de/api/interpreter?data=${encodeURIComponent(overpassQuery)}`;
    const response = await fetch(overpassUrl);
    if (!response.ok) {
      throw new Error("Erreur réseau ou serveur Overpass non accessible.");
    }
    const data = await response.json();
    if (!data.elements || data.elements.length === 0) {
      alert(`La rue "${address}" est introuvable à proximité.`);
      return;
    }

    let totalDistance = 0;
    const voieColor = localStorage.getItem('selectedVoieColor') || '#539AF8';
    const features = [];
    const addressKey = `${nomvoie}, ${selectedComplement}`;

    data.elements.forEach(way => {
      if (way.geometry) {
        const coordinates = way.geometry.map(coord => [coord.lon, coord.lat]);
        // Calcul de la distance pour ce segment
        const distance = coordinates.reduce((acc, curr, idx, arr) => {
          if (idx === 0) return 0;
          return acc + haversine(arr[idx - 1], curr);
        }, 0);
        totalDistance += distance;
        const walkingTime = calculateWalkingTime(totalDistance);

        features.push({
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: coordinates
          },
          properties: {
            name: way.tags && way.tags.name ? way.tags.name : streetName,
            color: voieColor,
            totalDistance: totalDistance.toFixed(2), // Chaîne de caractères
            walkingTime: walkingTime.toFixed(0),       // Chaîne de caractères
            key: addressKey
          }
        });
        // Enregistrement de la distance totale dans localStorage
        localStorage.setItem('totalDistance', totalDistance.toFixed(2));
      }
    });

    // Prépare la nouvelle FeatureCollection
    const newGeojson = {
      type: 'FeatureCollection',
      features: features
    };

    // Mise à jour ou création de la source "streetLayer"
    if (map.getSource('streetLayer')) {
      // Récupération des données existantes
      const currentData = map.getSource('streetLayer')._data;
      const mergedFeatures = currentData.features.concat(features);
      map.getSource('streetLayer').setData({
        type: 'FeatureCollection',
        features: mergedFeatures
      });
    } else {
      map.addSource('streetLayer', {
        type: 'geojson',
        data: newGeojson
      });
      map.addLayer({
        id: 'streetLayer',
        type: 'line',
        source: 'streetLayer',
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': ['get', 'color'],
          'line-width': 8,
          'line-opacity': 0.8
        }
      });
      if (map.getSource('iris-shape')) {
            map.addLayer({
                id: 'iris-border',
                type: 'line',
                source: 'iris-shape', // Même source que le fill
                layout: {},
                paint: {
                    'line-color': '#ff9800', // Rouge
                    'line-width': 2, // Épaisseur
                    'line-opacity': 1, // Opacité
                }
            });
    }

    }

    // Calcul et ajustement du zoom pour englober toutes les features
    let allCoords = [];
    const streetSource = map.getSource('streetLayer');
    if (streetSource && streetSource._data && streetSource._data.features) {
      streetSource._data.features.forEach(feature => {
        allCoords = allCoords.concat(feature.geometry.coordinates);
      });
    }
    if (allCoords.length > 0) {
      const bounds = allCoords.reduce((b, coord) => b.extend(coord), new mapboxgl.LngLatBounds(allCoords[0], allCoords[0]));
      map.fitBounds(bounds, { padding: 20, pitch: 60, bearing: -20 });
    }

  } catch (error) {
    console.error('Erreur :', error);
    alert(error.message || 'Une erreur est survenue.');
  }
}

map.on('click', 'streetLayer', function (e) {
  if (!e.features || !e.features.length) return;
  const feature = e.features[0];
  const properties = feature.properties;
  // Note : properties.totalDistance et walkingTime sont des chaînes de caractères déjà formatées
  localStorage.setItem('totalDistance', properties.totalDistance);
  const popupContent = `
    <div class="custom-popup">
      <div class="popup-title">
        <span class="location-icon"><i class="bi bi-geo-alt-fill"></i></span>
        <span style="color:${properties.color}">${properties.name}</span>
      </div>
      <div class="popup-details">
        <div>
          <span class="walk-icon"><i class="bi bi-person-walking"></i></span> Km: 
          <b style="color:${properties.color}; padding-left: 144px;">${properties.totalDistance}</b>
        </div>
        <div>
          <span class="clock-icon"><img class="icon" src="/image/picto-prises.svg" alt="Icon"></span> Prises 
          <b style="color:${properties.color}; padding-left: 144px;">${properties.walkingTime}</b>
        </div>
      </div>
    </div>
  `;
  new mapboxgl.Popup()
    .setLngLat(e.lngLat)
    .setHTML(popupContent)
    .addTo(map);
});

// Changement du curseur lorsque la souris survole la couche des rues
map.on('mouseenter', 'streetLayer', function () {
  map.getCanvas().style.cursor = 'pointer';
});
map.on('mouseleave', 'streetLayer', function () {
  map.getCanvas().style.cursor = '';
});

// Surveillance des changements de "selectedVoie" dans localStorage pour déclencher une recherche de rue
let lastSelectedVoie = localStorage.getItem('selectedVoie');
setInterval(() => {
  const currentSelectedVoie = localStorage.getItem('selectedVoie');
  if (currentSelectedVoie !== lastSelectedVoie) {
    lastSelectedVoie = currentSelectedVoie;
    searchStreet();
  }
}, 500);

// Surveillance d'un second flag ("decouche") pour ajouter ou supprimer une rue en fonction de la valeur de "isChecked"
let lastDecouche = localStorage.getItem("decouche");
setInterval(() => {
  const currentDecouche = localStorage.getItem("decouche");
  const isChecked = localStorage.getItem("isChecked");
  if (currentDecouche !== lastDecouche) {
    lastDecouche = currentDecouche;
    if (currentDecouche && isChecked === 'false') {
      removeStreet();
    } 
  }
}, 500);

}); #}







{# document.querySelector('.Distribuer').addEventListener('click', function () { 
     const dataString = localStorage.getItem("clusterInseeDatag"); 
        // Parse the string back to an array/object
        const data = JSON.parse(dataString);
 
        let codeClusters = new Set(); 
        let codeInsees = new Set(); 
        let nomsVoies = new Set(); 
 
        // Now data is an array that you can iterate over
        data.forEach(cluster => { 
            codeClusters.add(cluster.codeCluster); 
            cluster.villes.forEach(ville => { 
                codeInsees.add(ville.codInsee); 
                ville.voies.forEach(voie => { 
                    nomsVoies.add(voie.nomVoie); 
                }); 
            }); 
        }); 
 
        // Convert sets to comma-separated strings 
        const codeClusterStr = Array.from(codeClusters).join(","); 
        const codeInseeStr = Array.from(codeInsees).join(","); 
        const nomsVoiesStr = Array.from(nomsVoies).join(","); 
            const url = `/GeoMapp/effectifUser-data`;

            fetch(url, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ selectedUserId,codeClusterStr,codeInseeStr,nomsVoiesStr }) // Ajouter selectedUserId dans le corps de la requête
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => { throw new Error(`HTTP error! status: ${response.status}, body: ${text}`); });
                }
                return response.json();
            })
            .then(data => {
                console.log('Data updated successfully:', data);
                localStorage.removeItem('clusterInseeData');
                localStorage.removeItem('savedVoies');
                localStorage.removeItem('selectedUserId');
                localStorage.removeItem('selectednom');
                localStorage.removeItem('selectedprenom');
            })
            .catch(error => {
                console.error('Error updating data:', error);
            });
}); #}





    

</script>

{# let rotationInterval = null;


let theme = localStorage.getItem("theme") || "light"; 
mapboxgl.accessToken = 'pk.eyJ1IjoicmdvdW50aXRpIiwiYSI6ImNtMnA1bHJ5NDBuczcycnNieGsyamVjOTMifQ.FjXmzR2E_Di8YWn8nfTPog';

let currentBearing = 0; // Initial rotation angle
let is3D = true; // Start with 3D view enabled

// Function to initialize the map
function initializeMap(theme) {
    return new mapboxgl.Map({
        container: 'map',
        style: theme === "darkblue" 
            ? 'mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw' 
            : 'mapbox://styles/mapbox/light-v10',
        center: [2.3522, 48.8566],
        zoom: 5, 
        pitch: 60,  // Set initial pitch for 3D view
        bearing: -20 // Optional: Adjust the initial bearing if needed
    });
}

// Function to switch to the 3D view
function setTiltedView() {
    map.easeTo({
        pitch: 60,  // Tilt the view to 60°
        bearing: -20,
        duration: 1000
    });
}

// Function to switch to the flat 2D view
function setFlatView() {
    map.easeTo({
        pitch: 0,  // No tilt for the 2D view
        bearing: 0,
        duration: 1000
    });
}

// Function to rotate the map by 45°
function rotateMap() {
    currentBearing += 45; // Add 45° on each click
    if (currentBearing >= 360) {
        currentBearing = 0; // Reset angle to 0 after 360°
    }
    map.easeTo({
        bearing: currentBearing,
        duration: 1000
    });
}

// Initialize the map
var map = initializeMap(theme);
map.addControl(new mapboxgl.NavigationControl());

map.on('load', () => {
    map.addLayer({
        id: '3d-buildings',
        source: 'composite',
        'source-layer': 'building',
        type: 'fill-extrusion',
        paint: {
            'fill-extrusion-color': '#89b9ed',
            'fill-extrusion-height': ['get', 'height'],
            'fill-extrusion-base': ['get', 'min_height'],
            'fill-extrusion-opacity': 0.6
        }
    });
});

// Toggle button event
document.getElementById("toggleView").onclick = function() {
    if (is3D) {
        setFlatView(); // Switch to 2D view
        this.textContent = '3D'; // Update button text
    } else {
        setTiltedView(); // Switch to 3D view
        this.textContent = '2D'; // Update button text
    }
    is3D = !is3D; // Toggle the state
};

document.getElementById("rotateMap").onclick = rotateMap;

// Configuration du clustering
const CLUSTER_SOURCE_ID = 'clusters-source';
let markers = [];

function initializeClusterSource() {
    if (!map.getSource(CLUSTER_SOURCE_ID)) {
        map.addSource(CLUSTER_SOURCE_ID, {
            type: 'geojson',
            data: { type: 'FeatureCollection', features: [] },
            cluster: true,
            clusterMaxZoom: 14,
            clusterRadius: 50
        });
    }

    // Couche des clusters
    if (!map.getLayer('clusters-layer')) {
        map.addLayer({
            id: 'clusters-layer',
            type: 'circle',
            source: CLUSTER_SOURCE_ID,
            filter: ['has', 'point_count'],
            paint: {
                'circle-color': [
                    'step',
                    ['get', 'point_count'],
                    '#51bbd6',
                    10,
                    '#51bbd6',
                    100,
                    '#51bbd6'
                ],
                'circle-radius': [
                    'step',
                    ['get', 'point_count'],
                    20,
                    10,
                    30,
                    100,
                    40
                ]
            }
        });
    }

    // Texte des clusters
    if (!map.getLayer('cluster-count')) {
        map.addLayer({
            id: 'cluster-count',
            type: 'symbol',
            source: CLUSTER_SOURCE_ID,
            filter: ['has', 'point_count'],
            layout: {
                'text-field': '{point_count_abbreviated}',
                'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
                'text-size': 12
            }
        });
    }

    // Gestion des marqueurs individuels
    updateMarkers();
}

function updateMarkers() {
    // Supprimer les anciens marqueurs
    markers.forEach(marker => marker.remove());
    markers = [];

    // Récupérer les features non clusterisées
    const features = map.querySourceFeatures(CLUSTER_SOURCE_ID, {
        filter: ['!', ['has', 'point_count']]
    });

    features.forEach(feature => {
        const el = document.createElement('div');
        el.className = 'custom-marker';
        el.style.width = '30px';
        el.style.height = '40px';
        el.style.backgroundSize = 'cover';
        el.style.borderRadius = '50%';
        el.style.display = 'flex';
        el.style.alignItems = 'center';
        el.style.justifyContent = 'center';

        const icon = document.createElement('i');
        icon.className = 'bi bi-geo-alt-fill';
        icon.style.fontSize = '30px';
        icon.style.color = '#51bbd6';

        el.appendChild(icon);
            
        const marker = new mapboxgl.Marker(el)
            .setLngLat(feature.geometry.coordinates)
            .addTo(map);

        markers.push(marker);
    });
}

async function updateClusterData(data) {
    let rawFeatures = [];

    if (Array.isArray(data)) {
        rawFeatures = data.flatMap(item => item.features || []);
    } else if (data.features) {
        rawFeatures = data.features;
    } else {
        console.error("❌ Format de données inattendu :", data);
        return;
    }

    const features = rawFeatures.map(feature => {
        const coords = feature.geometry.coordinates;
        if (Array.isArray(coords) && coords.length === 2 && Math.abs(coords[0]) <= 90) {
            return {
                ...feature,
                geometry: {
                    ...feature.geometry,
                    coordinates: [coords[1], coords[0]] // Correction lat/lng
                }
            };
        }
        return feature;
    });

    const source = map.getSource(CLUSTER_SOURCE_ID);
    if (source) {
        source.setData({ type: 'FeatureCollection', features });

        map.once('idle', () => {
            updateMarkers();
            map.triggerRepaint();

            if (features.length > 0) {
                setTimeout(() => {
                    zoomToLargestCluster(features);
                }, 300); // Petit délai avant le zoom
            }
        });
    } else {
        console.error("⚠️ Source de clusters non trouvée !");
    }
}


function findLargestClusterArea(features) {
    if (features.length === 0) return null;

    // Définition d'une grille pour regrouper les clusters proches
    const gridSize = 0.05; // Ajustable pour définir la "taille" d'un groupe
    const gridMap = new Map();

    features.forEach(feature => {
        const [lng, lat] = feature.geometry.coordinates;

        // Group by a grid key, taking into account the gridSize for both lat and lng
        const key = `${Math.floor(lng / gridSize)}-${Math.floor(lat / gridSize)}`;

        if (!gridMap.has(key)) {
            gridMap.set(key, []);
        }
        gridMap.get(key).push(feature);
    });

    // Trouver la zone avec le plus grand nombre de points
    let maxCluster = [];
    gridMap.forEach(cluster => {
        if (cluster.length > maxCluster.length) {
            maxCluster = cluster;
        }
    });

    if (maxCluster.length === 0) return null;
    
    // Calculer les bounds du plus grand cluster détecté
    const bounds = new mapboxgl.LngLatBounds();
    maxCluster.forEach(feature => {
        bounds.extend(feature.geometry.coordinates);
    });

    return bounds;
}

function zoomToLargestCluster(features) {
    const largestBounds = findLargestClusterArea(features);
    if (largestBounds) {
        map.fitBounds(largestBounds, {
            padding: 50,
            pitch: 60,
            bearing: -20,
            zoom: 15, 
            duration: 1000
        });

        // Start rotation after zoom animation completes
        map.once('moveend', startRotationAfterZoom);
    }
}

function startRotation() {
    if (!rotationInterval) {
        rotationInterval = setInterval(() => {
            currentBearing = (currentBearing + 1) % 360;
            map.easeTo({ bearing: currentBearing, duration: 100 });
        }, 50);
    }
}

function stopRotation() {
    if (rotationInterval) {
        clearInterval(rotationInterval);
        rotationInterval = null;
    }
}

function startRotationAfterZoom() {
    stopRotation(); // On arrête la rotation existante avant de redémarrer
    setTimeout(startRotation, 500); // Petite pause avant le début
}

// Bind the rotation control to zoom and click events
map.on('zoomend', startRotationAfterZoom);
map.on('click', stopRotation); // Stoppe la rotation quand on clique sur la carte

// Fonction principale avec codeinsee
window.findClusterCoordinates = async function(codecluster, codeinsee) {
    try {
        const data = await fetchdataCoordinates(codecluster, codeinsee);
        console.log("fetchdataCoordinates ===", codecluster );
          console.log("fetchdataCoordinates ===", codeinsee );
            console.log("fetchdataCoordinates ===", data );
        if (data) {
            initializeClusterSource();
            await updateClusterData(data);
        }
    } catch (error) {
        console.error('Error:', error);
    }
};
async function fetchdataCoordinates(codecluster, codeinsee ) {  // default to empty string if null
    try {
        // If codeinsee is null or empty, it will be left out from the URL
        let url = `https://api.nomadcloud.fr/api/interventions-places-coordinates-v2/${cpv}/${codecluster}?page=1`;

        // Only append codeInsee parameter if codeinsee is not null or empty
        if (codeinsee) {
            url += `&codeInsee=${codeinsee}`;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`, // Assurez-vous que jwtToken est défini
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Response fetchdataCoordinates:",data)
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
}


map.on('data', (e) => {
    if (e.sourceId === CLUSTER_SOURCE_ID && e.isSourceLoaded) {
  
        map.triggerRepaint();
    }
});

// Événements de mouvement de la carte
map.on('move', updateMarkers);
map.on('zoom', updateMarkers); #}




</body>
</html>
