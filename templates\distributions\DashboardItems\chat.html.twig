<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .chat-container {
            max-width: 100%;
            display: flex;
            flex-direction: column;
            height: 240px;
            position: relative;
            font-family: "Consolas";
        }

        .chat-header {
            font-weight: bold;
            margin-bottom: 0;
        }

        .chat-body {
            font-size: 12px;
            flex: 1;
            background-color: #e7efff;
            padding: 5px;
            border-radius: 20px;
            overflow-y: auto;
            margin: 0;
        }

        .chat-body::-webkit-scrollbar {
            width: 8px;
        }

        .chat-body::-webkit-scrollbar-thumb {
            border-radius: 10px;
        }

        .chat-body::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

      

        .bonus-expand-icon {
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 0.9rem;
            color: #777;
            cursor: pointer;
        }

        .chat-footer {
            display: flex;
            position: absolute;
            padding-top: 10px;
            bottom: 0;
            width: 100%;
        }

        .form-control {
            border-radius: 30px;
            background-color: #b6c2fb;
            padding: 20px;
            width: 100%;
            transition: background-color 0.3s, color 0.3s;
        }

        .form-control::placeholder {
            color: #333;
            font-weight: bold;
            opacity: 1;
        }

        /* Dark Mode Styles */
        body.dark-mode .chat-body {
            background-color: #111;
            color: #fff;
        }

        body.dark-mode .form-control {
            background-color: #222;
            color: #fff;
            border-color: transparent;
        }

        body.dark-mode .form-control::placeholder {
            color: #ccc;
        }

        body.dark-mode .bonus-expand-icon {
            color: #bbb;
        }

        body.dark-mode .chat-section {
            background-color: #4d254d;
            border-radius: 15px;
        }

        .chat-section {
            padding-left: 0;
            padding-right: 0;
        }
    </style>
</head>
<body>

<div class="chat-section">
    <h3>AI</h3>
    <div class="chat-container">
        <div class="chat-body">
            <div class="ai-text">
                <p>Voyons cela.<br>
                Objectif de ventes mensuel : 250 ventes par territoire.<br>
                Nombre de territoires : 5 territoires.<br>
                Objectif total pour le mois : 250 × 5 = 1250<br>
                Vous avez 4 vendeurs, et chaque vendeur doit réaliser 1 vente par jour.</p>
            </div>
        </div>
        
        <div class="chat-footer">
            <input type="text" class="form-control" placeholder="Posez une question" aria-label="Input field for questions"/>
        </div>
    </div>
</div>

<script>
    const checkbox = document.getElementById('chk'); 
    if (checkbox) {
        checkbox.addEventListener('change', () => {
            applyTheme(checkbox.checked ? 'dark' : 'light');
        });
    }

    const applyTheme = theme => {
        document.body.classList.toggle('dark-mode', theme === 'dark');
        localStorage.setItem('theme', theme);
    };

    const currentTheme = localStorage.getItem('theme') || 'light';
    applyTheme(currentTheme);

    function fetchAndUpdateContent(path, elementId) {
        fetch(path)
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.text();
            })
            .then(html => {
                document.getElementById(elementId).innerHTML = html;
            })
            .catch(error => {
                console.error('There was a problem with the fetch operation:', error);
            });
    }
</script>

</body>
</html>
