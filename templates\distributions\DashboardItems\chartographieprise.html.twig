<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
		<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
		<style>

			.maps {
				height: 395px;
				border-radius: 15px;
				position: relative;
				background-size: cover;

			}

			#map {
				height: 395px;
				border-radius: 15px;
				padding: 0;
				background-size: cover;
				background-color: transparent;
			}

			.floating-titles {
				position: absolute;
				color: #333;
				margin: 0;
				z-index: 1000;
				font-family: 'Ubuntu';
				width: 100%;
				height: 70px;
				 /* Texture fumée et dégradé vers le haut */
			}

			.filtarge{
				border-radius: 13px 13px 0 0;
				background-image: linear-gradient(to top, 	 transparent ,#b7b7ef);	
				background-blend-mode: overlay;
			}
			body.dark-mode .floating-titles {
				color: #fff;
				background-image: linear-gradient(to top, transparent,#4d254d); /* Texture fumée et dégradé vers le haut */
				 /* Fusionner les deux arrière-plans */

			}


			body.dark-mode .custom-icon i {
				color: #b2e8b2; /* Light green for icons in dark mode */
			}
		</style>
	</head>
	<body>
		<div class="floating-titles filtarge"> 
		<h6  style="margin: 10px;">Cartographie des prises</h6>
		</div>
		


		<!-- Map container -->
		<div class="maps" id="map"></div>


		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>
		<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
		<script>
			document.addEventListener('DOMContentLoaded', function () {
var map = L.map('map', {
attributionControl: false,
zoomControl: false
}).setView([
48.8214, 2.3522
], 12);

var tileLayerLight = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
maxZoom: 20,
attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
});

var tileLayerDark = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
maxZoom: 20,
attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
});

tileLayerLight.addTo(map);

function toggleDarkMode() {
var isChecked = document.getElementById('chk').checked;
if (isChecked) {
map.removeLayer(tileLayerLight);
tileLayerDark.addTo(map);
document.body.classList.add('dark-mode');
} else {
map.removeLayer(tileLayerDark);
tileLayerLight.addTo(map);
document.body.classList.remove('dark-mode');
}
}

document.getElementById('chk').addEventListener('change', toggleDarkMode);

function addRandomMarkers() {
for (let i = 0; i < 10; i++) {
let lat = 48.8566 + (Math.random() - 0.5) * 0.1;
let lng = 2.3522 + (Math.random() - 0.5) * 0.1;

const greenIcon = L.divIcon({
className: 'custom-icon',
html: '<i class="bi bi-geo-alt-fill" style="color: #785EF0; font-size: 20px;"></i>',
iconSize: [
40, 40
],
iconAnchor: [30, 20]
});

L.marker([
lat, lng
], {icon: greenIcon}).addTo(map);
}
}

addRandomMarkers();
});
		</script>

	</body>
</html>
