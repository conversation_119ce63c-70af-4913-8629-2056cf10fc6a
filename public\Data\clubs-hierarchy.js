/**
 * Composant JavaScript pour la Hiérarchie des Clubs
 * Gère l'affichage hiérarchique des clubs organisés par compétition
 * Similaire à competition.js mais spécialisé pour les clubs
 */

class ClubsHierarchy {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            showPlayerCount: true,
            showMarketValue: true,
            enableSearch: true,
            animationDuration: 300,
            loadOnClick: true,
            ...options
        };
        
        this.hierarchyData = null;
        this.loadedClubs = new Map();
        this.expandedCompetitions = new Set();
        this.searchQuery = '';
        
        this.init();
    }

    /**
     * Initialisation du composant
     */
    async init() {
        try {
            console.log('🏟️ Initialisation de la hiérarchie des clubs...');
            await this.loadHierarchyConfig();
            this.setupEventListeners();
            this.render();
            console.log('✅ Hiérarchie des clubs initialisée avec succès');
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation:', error);
            this.showError('Impossible de charger la hiérarchie des clubs');
        }
    }

    /**
     * Chargement de la configuration de la hiérarchie
     */
    async loadHierarchyConfig() {
        try {
            const response = await fetch('Data/hierarchy-config.json');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            this.hierarchyData = await response.json();
            console.log('📊 Configuration de hiérarchie chargée:', this.hierarchyData);
        } catch (error) {
            console.error('❌ Erreur lors du chargement de la configuration:', error);
            throw error;
        }
    }

    /**
     * Configuration des écouteurs d'événements
     */
    setupEventListeners() {
        // Recherche globale
        if (this.options.enableSearch) {
            document.addEventListener('keyup', (e) => {
                if (e.target.matches('.clubs-search-input')) {
                    this.handleSearch(e.target.value);
                }
            });
        }

        // Gestion des clics sur les compétitions
        this.container.addEventListener('click', (e) => {
            if (e.target.matches('.competition-toggle') || e.target.closest('.competition-toggle')) {
                e.preventDefault();
                const competitionElement = e.target.closest('.competition-item');
                const competitionId = competitionElement.dataset.competitionId;
                const continentId = competitionElement.dataset.continentId;
                this.toggleCompetition(continentId, competitionId);
            }

            // Gestion des clics sur les clubs
            if (e.target.matches('.club-item') || e.target.closest('.club-item')) {
                const clubElement = e.target.closest('.club-item');
                const clubId = clubElement.dataset.clubId;
                this.handleClubClick(clubId);
            }
        });
    }

    /**
     * Rendu principal de la hiérarchie
     */
    render() {
        if (!this.hierarchyData) {
            this.showError('Aucune donnée de hiérarchie disponible');
            return;
        }

        const html = this.generateHierarchyHTML();
        this.container.innerHTML = html;
    }

    /**
     * Génération du HTML de la hiérarchie
     */
    generateHierarchyHTML() {
        let html = '<div class="clubs-hierarchy-container">';
        
        // En-tête avec recherche
        if (this.options.enableSearch) {
            html += this.generateSearchHTML();
        }

        // Hiérarchie par continents
        html += '<div class="continents-container">';
        
        Object.keys(this.hierarchyData.continents).forEach(continentId => {
            const continent = this.hierarchyData.continents[continentId];
            html += this.generateContinentHTML(continentId, continent);
        });
        
        html += '</div></div>';
        return html;
    }

    /**
     * Génération du HTML de recherche
     */
    generateSearchHTML() {
        return `
            <div class="clubs-search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" 
                           class="clubs-search-input" 
                           placeholder="Rechercher un club..."
                           value="${this.searchQuery}">
                    <button class="clear-search-btn" style="display: ${this.searchQuery ? 'block' : 'none'}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Génération du HTML d'un continent
     */
    generateContinentHTML(continentId, continent) {
        return `
            <div class="continent-section" data-continent-id="${continentId}">
                <div class="continent-header">
                    <span class="continent-icon">${continent.icon}</span>
                    <h3 class="continent-name">${continent.name}</h3>
                    <span class="competitions-count">${continent.competitions.length} compétitions</span>
                </div>
                <div class="competitions-container">
                    ${continent.competitions.map(competition => 
                        this.generateCompetitionHTML(continentId, competition)
                    ).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Génération du HTML d'une compétition
     */
    generateCompetitionHTML(continentId, competition) {
        const isExpanded = this.expandedCompetitions.has(competition.id);
        const clubsLoaded = this.loadedClubs.has(competition.id);
        
        return `
            <div class="competition-item" 
                 data-competition-id="${competition.id}" 
                 data-continent-id="${continentId}">
                <div class="competition-header competition-toggle">
                    <div class="competition-info">
                        <div class="competition-logo">${competition.logo}</div>
                        <div class="competition-details">
                            <h4 class="competition-name">${competition.name}</h4>
                            <span class="competition-country">${competition.country}</span>
                        </div>
                    </div>
                    <div class="competition-stats">
                        <span class="clubs-count" id="clubs-count-${competition.id}">
                            ${clubsLoaded ? this.loadedClubs.get(competition.id).clubs.length : '?'} clubs
                        </span>
                        <i class="fas fa-chevron-right expand-icon ${isExpanded ? 'expanded' : ''}"></i>
                    </div>
                </div>
                <div class="clubs-container ${isExpanded ? 'expanded' : ''}" 
                     id="clubs-${competition.id}">
                    ${isExpanded ? this.generateClubsHTML(competition.id) : ''}
                </div>
            </div>
        `;
    }

    /**
     * Génération du HTML des clubs
     */
    generateClubsHTML(competitionId) {
        const clubsData = this.loadedClubs.get(competitionId);
        
        if (!clubsData) {
            return '<div class="loading-clubs"><i class="fas fa-spinner fa-spin"></i> Chargement des clubs...</div>';
        }

        if (clubsData.clubs.length === 0) {
            return '<div class="no-clubs">Aucun club trouvé</div>';
        }

        let html = '<div class="clubs-grid">';
        
        clubsData.clubs.forEach(club => {
            html += this.generateClubHTML(club);
        });
        
        html += '</div>';
        
        // Ajouter les statistiques si disponibles
        if (clubsData.statistics && this.options.showMarketValue) {
            html += this.generateStatsHTML(clubsData.statistics);
        }
        
        return html;
    }

    /**
     * Génération du HTML d'un club
     */
    generateClubHTML(club) {
        const marketValue = this.formatMarketValue(club.market_value);
        const capacity = club.capacity ? club.capacity.toLocaleString() : 'N/A';
        
        return `
            <div class="club-item" data-club-id="${club.id}">
                <div class="club-header">
                    <div class="club-logo-placeholder"></div>
                    <div class="club-info">
                        <h5 class="club-name">${club.name}</h5>
                        <span class="club-short-name">${club.short_name || club.name}</span>
                    </div>
                </div>
                <div class="club-details">
                    <div class="club-detail">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Fondé en ${club.founded}</span>
                    </div>
                    <div class="club-detail">
                        <i class="fas fa-home"></i>
                        <span>${club.stadium}</span>
                    </div>
                    <div class="club-detail">
                        <i class="fas fa-users"></i>
                        <span>${capacity} places</span>
                    </div>
                    ${this.options.showPlayerCount ? `
                        <div class="club-detail">
                            <i class="fas fa-running"></i>
                            <span>${club.players_count || 0} joueurs</span>
                        </div>
                    ` : ''}
                    ${this.options.showMarketValue ? `
                        <div class="club-detail market-value">
                            <i class="fas fa-euro-sign"></i>
                            <span>${marketValue}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Génération du HTML des statistiques
     */
    generateStatsHTML(stats) {
        return `
            <div class="competition-stats-summary">
                <h6>Statistiques de la compétition</h6>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Total clubs</span>
                        <span class="stat-value">${stats.total_clubs}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Valeur totale</span>
                        <span class="stat-value">${this.formatMarketValue(stats.total_market_value)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Valeur moyenne</span>
                        <span class="stat-value">${this.formatMarketValue(stats.average_market_value)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Capacité totale</span>
                        <span class="stat-value">${stats.total_capacity?.toLocaleString() || 'N/A'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Toggle d'une compétition (développer/réduire)
     */
    async toggleCompetition(continentId, competitionId) {
        const isExpanded = this.expandedCompetitions.has(competitionId);

        if (isExpanded) {
            // Réduire la compétition
            this.expandedCompetitions.delete(competitionId);
            this.updateCompetitionDisplay(competitionId, false);
        } else {
            // Développer la compétition
            this.expandedCompetitions.add(competitionId);
            this.updateCompetitionDisplay(competitionId, true);

            // Charger les clubs si pas encore fait
            if (!this.loadedClubs.has(competitionId) && this.options.loadOnClick) {
                await this.loadClubs(continentId, competitionId);
            }
        }
    }

    /**
     * Mise à jour de l'affichage d'une compétition
     */
    updateCompetitionDisplay(competitionId, expanded) {
        const competitionElement = this.container.querySelector(`[data-competition-id="${competitionId}"]`);
        if (!competitionElement) return;

        const clubsContainer = competitionElement.querySelector('.clubs-container');
        const expandIcon = competitionElement.querySelector('.expand-icon');

        if (expanded) {
            clubsContainer.classList.add('expanded');
            expandIcon.classList.add('expanded');

            // Animation d'ouverture
            clubsContainer.style.maxHeight = clubsContainer.scrollHeight + 'px';
        } else {
            clubsContainer.classList.remove('expanded');
            expandIcon.classList.remove('expanded');

            // Animation de fermeture
            clubsContainer.style.maxHeight = '0px';
        }
    }

    /**
     * Chargement des clubs d'une compétition
     */
    async loadClubs(continentId, competitionId) {
        try {
            console.log(`🏟️ Chargement des clubs pour la compétition ${competitionId}...`);

            const competition = this.findCompetition(continentId, competitionId);
            if (!competition) {
                throw new Error(`Compétition ${competitionId} non trouvée`);
            }

            // Afficher le loader
            this.showClubsLoader(competitionId);

            const response = await fetch(`Data/clubs/${competition.clubs_file}`);
            if (!response.ok) {
                throw new Error(`Fichier ${competition.clubs_file} non trouvé (${response.status})`);
            }

            const clubsData = await response.json();
            this.loadedClubs.set(competitionId, clubsData);

            // Mettre à jour l'affichage
            this.updateClubsDisplay(competitionId);
            this.updateClubsCount(competitionId, clubsData.clubs.length);

            console.log(`✅ ${clubsData.clubs.length} clubs chargés pour ${competition.name}`);

        } catch (error) {
            console.error(`❌ Erreur lors du chargement des clubs:`, error);
            this.showClubsError(competitionId, error.message);
        }
    }

    /**
     * Affichage du loader pour les clubs
     */
    showClubsLoader(competitionId) {
        const clubsContainer = this.container.querySelector(`#clubs-${competitionId}`);
        if (clubsContainer) {
            clubsContainer.innerHTML = '<div class="loading-clubs"><i class="fas fa-spinner fa-spin"></i> Chargement des clubs...</div>';
        }
    }

    /**
     * Mise à jour de l'affichage des clubs
     */
    updateClubsDisplay(competitionId) {
        const clubsContainer = this.container.querySelector(`#clubs-${competitionId}`);
        if (clubsContainer) {
            clubsContainer.innerHTML = this.generateClubsHTML(competitionId);
        }
    }

    /**
     * Mise à jour du compteur de clubs
     */
    updateClubsCount(competitionId, count) {
        const countElement = this.container.querySelector(`#clubs-count-${competitionId}`);
        if (countElement) {
            countElement.textContent = `${count} clubs`;
        }
    }

    /**
     * Affichage d'une erreur pour les clubs
     */
    showClubsError(competitionId, message) {
        const clubsContainer = this.container.querySelector(`#clubs-${competitionId}`);
        if (clubsContainer) {
            clubsContainer.innerHTML = `
                <div class="clubs-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Erreur: ${message}</span>
                </div>
            `;
        }
    }

    /**
     * Recherche d'une compétition
     */
    findCompetition(continentId, competitionId) {
        const continent = this.hierarchyData.continents[continentId];
        return continent?.competitions.find(comp => comp.id === competitionId);
    }

    /**
     * Gestion de la recherche
     */
    handleSearch(query) {
        this.searchQuery = query.toLowerCase().trim();

        // Mettre à jour le bouton de suppression
        const clearBtn = this.container.querySelector('.clear-search-btn');
        if (clearBtn) {
            clearBtn.style.display = this.searchQuery ? 'block' : 'none';
        }

        // Filtrer l'affichage
        this.filterDisplay();
    }

    /**
     * Filtrage de l'affichage selon la recherche
     */
    filterDisplay() {
        const clubItems = this.container.querySelectorAll('.club-item');
        const competitionItems = this.container.querySelectorAll('.competition-item');

        if (!this.searchQuery) {
            // Afficher tout
            clubItems.forEach(item => item.style.display = 'block');
            competitionItems.forEach(item => item.style.display = 'block');
            return;
        }

        // Filtrer les clubs
        clubItems.forEach(item => {
            const clubName = item.querySelector('.club-name')?.textContent.toLowerCase() || '';
            const stadium = item.querySelector('.club-detail span')?.textContent.toLowerCase() || '';

            const matches = clubName.includes(this.searchQuery) || stadium.includes(this.searchQuery);
            item.style.display = matches ? 'block' : 'none';
        });

        // Afficher/masquer les compétitions selon les clubs visibles
        competitionItems.forEach(item => {
            const visibleClubs = item.querySelectorAll('.club-item[style*="block"], .club-item:not([style])');
            item.style.display = visibleClubs.length > 0 ? 'block' : 'none';
        });
    }

    /**
     * Gestion du clic sur un club
     */
    handleClubClick(clubId) {
        console.log(`🏟️ Clic sur le club ${clubId}`);

        // Émettre un événement personnalisé
        const event = new CustomEvent('clubSelected', {
            detail: { clubId, clubsHierarchy: this }
        });
        this.container.dispatchEvent(event);
    }

    /**
     * Formatage de la valeur marchande
     */
    formatMarketValue(value) {
        if (!value || value === '0') return 'N/A';

        const num = parseInt(value);
        if (num >= 1000000000) {
            return `€${(num / 1000000000).toFixed(1)}B`;
        } else if (num >= 1000000) {
            return `€${(num / 1000000).toFixed(1)}M`;
        } else if (num >= 1000) {
            return `€${(num / 1000).toFixed(1)}K`;
        }
        return `€${num.toLocaleString()}`;
    }

    /**
     * Affichage d'une erreur générale
     */
    showError(message) {
        this.container.innerHTML = `
            <div class="clubs-hierarchy-error">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>Erreur</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> Réessayer
                </button>
            </div>
        `;
    }

    /**
     * Méthodes publiques pour l'API
     */

    // Développer toutes les compétitions
    expandAll() {
        Object.keys(this.hierarchyData.continents).forEach(continentId => {
            this.hierarchyData.continents[continentId].competitions.forEach(competition => {
                if (!this.expandedCompetitions.has(competition.id)) {
                    this.toggleCompetition(continentId, competition.id);
                }
            });
        });
    }

    // Réduire toutes les compétitions
    collapseAll() {
        this.expandedCompetitions.clear();
        this.container.querySelectorAll('.clubs-container.expanded').forEach(container => {
            container.classList.remove('expanded');
            container.style.maxHeight = '0px';
        });
        this.container.querySelectorAll('.expand-icon.expanded').forEach(icon => {
            icon.classList.remove('expanded');
        });
    }

    // Recharger les données
    async reload() {
        this.loadedClubs.clear();
        this.expandedCompetitions.clear();
        await this.loadHierarchyConfig();
        this.render();
    }

    // Obtenir les clubs chargés
    getLoadedClubs() {
        return Object.fromEntries(this.loadedClubs);
    }
}

// Export pour utilisation globale
window.ClubsHierarchy = ClubsHierarchy;
