<?php

namespace App\Command;

use App\Service\CommandService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:integrateClubs',
    description: 'Add a short description for your command',
)]
class IntegrateClubsCommand extends Command
{

    private $forCommandService;

    public function __construct(CommandService $forCommandService)
    {
        $this->forCommandService = $forCommandService;

        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('arg1', InputArgument::OPTIONAL, 'Argument description')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->forCommandService->integrateClubs();

        return Command::SUCCESS;
    }
}
