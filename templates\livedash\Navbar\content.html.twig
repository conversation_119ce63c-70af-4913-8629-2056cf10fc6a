<style>

:root {
    --color-search-text: #0e567e; 
    --color-buttons-nav: #2d475a;
    --search-input-color: #e5e5e7;
}

[data-theme="dark"] {
    --color-search-text: #afb1b5; 
        --color-buttons-nav: #a4a7ab;
    --search-input-color: #1e1f21;

}

.nav-buttons-group {
    display: flex;
}

.nav-button {
    padding: 3px 15px;
    color: var( --color-buttons-nav);
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 12px; 
}

.nav-button.active {
    background-color: #007bff; 
    color: #fff;
}

.nav-button:hover {
    background-color: #555;
    color: #fff;
}

.search-container {
    position: relative;
    display: inline-flex;
    align-items: center;
background-color: var(--search-container-input);
    border-radius: 10px; 
    width: 233px;
    padding: 3px 20px; 
}

.search-input {
    background-color: transparent;
    border: none;
    color: var( --color-search-text);
    outline: none;
    font-family: 'Ubuntu', sans-serif;
    padding-right: 25px; 
    font-size: 14px;
}

.search-input::placeholder {
    color: #aaa; 
}

.search-icon {
    position: absolute;
    right: 15px; 
    color: #888; 
    pointer-events: none; 
}

.checkbox {
    opacity: 0;
    position: absolute;
}

.label {
    background-color: #111;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    position: relative;
    height: 15px;
    width: 35px;
    transform: scale(1.5);
}

.label .ball {
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: -10px;
    height: 12px;
    width: 12px;
    transform: translateX(10px);
    transition: transform 0.2s linear;
}

.checkbox:checked + .label .ball {
    transform: translateX(30px);
}

.fa-moon {
    color: #f1c40f;
}

        .collapsed {
            width: 0 !important;
            overflow: hidden;
            transition: width 0.3s;
        }

.switch-container{

}
.right-side-topbar {
    margin-right: 20.5%;
}
</style>

<div class="left-side-topbar">

</div>

<div class="right-side-topbar">
    <div class="switch-container">
        <input type="checkbox" class="checkbox" id="chk" />
        <label class="label" for="chk">
            <div class="ball"></div>
        </label>
    </div>

    <div class="svg-topbar" id="sidebarToggleBtn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-chevron-left" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
        </svg>
    </div>

    <div class="svg-topbar" id="collapse-right-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-chevron-right" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
        </svg>
    </div>

    <div  class="search-container">
        <input type="text" placeholder="Rechercher Ventes" class="search-input">
        <span class="search-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-search" viewBox="0 0 16 16">
                <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.099zm-5.442 1.36a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11z"/>
            </svg>
        </span>
    </div>
</div>

