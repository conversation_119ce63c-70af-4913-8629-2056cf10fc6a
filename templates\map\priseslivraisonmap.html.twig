
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
		<link href="https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap" rel="stylesheet">
		<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

		<link href="https://cdn.jsdelivr.net/npm/bootstrap-tagsinput/dist/bootstrap-tagsinput.css" rel="stylesheet">

		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.css">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"/>
		<link
		rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
		 <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
		<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet">
		<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
		<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">

		<link rel="stylesheet" href="{{asset('styles/AllColorsThemes.css')}}">
		<link rel="stylesheet" href="{{asset('styles/geomap.css')}}">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
		<link href="https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap" rel="stylesheet">
		<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

		<link href="https://cdn.jsdelivr.net/npm/bootstrap-tagsinput/dist/bootstrap-tagsinput.css" rel="stylesheet">

		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.css">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"/>
		<link
		rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
		 <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
		<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet">
		<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
		<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">

		{# <link rel="stylesheet" href="{{asset('css/discord.css')}}"> #}
		<link rel="stylesheet" href="{{asset('styles/prisesLivraisonmap/priseslivraisonmap.css')}}">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap">
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
        
        <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.css" />
        <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.Default.css" />

  <!-- Left Panel -->
<style>
.statDash {
    display: flex;
    flex-wrap: wrap; /* Allows elements to adjust */
    gap: 10px;
    margin: 16px;
    position: absolute;
    top: 73%; /* Adjust as needed */
    left: 0; /* Adjust if needed */
    right: 0; /* Adjust if needed */
    width: auto;
    z-index: 2; /* In front of the card */
    max-height: calc(100vh - 70%); /* Limits height if needed */
    overflow-y: scroll; /* Enables vertical scrolling */
    scrollbar-width: none; /* Thin scrollbar (Firefox) */
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track); /* Scrollbar colors */
}
</style>

  <!-- Content Area -->
  <div class="content">
   

<div class="maindashbords">
<div class="maindash">
	<div class="mafrenchsoilp" id="map" data-coords="[46.603354, 1.888334]" data-zoom="6"></div>
<div class="toggle-button" onclick="toggleRightPanel()">
  <div class="icon" id="HandleRightPanel"><i class="fas fa-calendar-alt"></i></div> <!-- Calendrier -->
  <div class="icon"><i class="fas fa-pen"></i></div>          <!-- Crayon -->
  <div class="icon" id="toggle-Chart"><i class="fas fa-chart-bar"></i></div>    <!-- Graphique -->
  <div class="icon"><i class="fas fa-table"></i></div>        <!-- Table -->
  <div class="icon"><i class="fas fa-user"></i></div>         <!-- Utilisateur -->
  <div class="icon"><i class="fas fa-folder"></i></div>       <!-- Dossier -->
  <div class="icon"><i class="fas fa-lock"></i></div>         <!-- Cadenas -->
  <div class="icon"><i class="fas fa-key"></i></div>          <!-- Clé -->
  <div class="icon"><i class="fas fa-cog"></i></div>
  <div class="icon" style="top: 360px;"><i class="bi bi-layout-split"></i></div>
</div>

<div class="right-panel" id="rightPanel">
    <div class="cards">
        <div class="headers">
            <span class="actives" id="btnMiseEnPage">Mise en page</span>
            <span id="btnStructure">Structure</span>
        </div>
    </div>
</div>

    <div class="statDash">

		<div class="statDashcard" style="height: 25vh;">
			<div class="chart-container" style="width: 270px;">
				<div style="display: flex; gap: 5px; width: 95%;">
					<span class="move-span"></span>
					<div style="display: flex; flex-direction: column; width: 59%;">
						<div style="background-color: #778dfa; height: 4px; margin-top: -10px; width: 100%; border-radius: 17px; margin-bottom: 10px;"></div>
						<span class="usersActiveCurve_chart" style="font-size: 9px;">
							Durée d'engagement moyenne par utilisateur actif
						</span>
					</div>
					<div>
						<span class="usersActiveCurve_chart" style="color: var(--chart-spans);">
                           <br>
						</span>
					</div>
					<span class="move-span"></span>
					<i class="bi bi-check-circle check-mark-icon"></i>
				</div>
				<div id="curve_chart" style="width: 100%; height: 50%; margin-top:-15px"></div>
			</div>
		</div>

	        <div class="statDashcard" style="height: 25vh;">
			    <div class="chart-container" style="width: 190px;height: 350px;">
                <i class="bi bi-check-circle check-mark-icon"></i>
                <div class="chart-header">
                    <span class="chart-header-details"style="text-underline-offset: 2px;font-size: 11px;">
                    UTILISATEUR ACTIFS AU COURS DES 30 DERNIERE MINUTES</span>
                </div>
                <span style="font-size: 18px;color: var(--chart-spans2);">257</span>
                <span style="font-size: 9px;">UTILISATEURS ACTIFS PAR MINUTES</span>
                <div class="chart-content" >
                    <div style="display:flex;width: 100%;height: 80%;margin-top:8px;">
                        <div id="audienceChartStat" class="audienceChart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>

                <div class="statistic-header">
                        <span class="static-header-nom">NOM DE L'EVENT</span>
                        <span class="static-header-number">NOMBRE DE L'EVENT</span>
                </div>
                <div class="trait-seperator" ></div>
                <div class="list-container">
                    <div class="statistic">
                        <div class="statistic-content">
                            <div class="stat-title">All Users</div>
                            <div class="stat-number">311</div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    <div class="statistic">
                        <div class="statistic-content">
                            <div class="stat-title">7-day unnotified users</div>
                            <div class="stat-number">310</div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 80%;"></div>
                        </div>
                    </div>
                </div>
            </div>
		</div>




		<div class="statDashcard" style="height: 25vh;">
			<div class="chart-container" style="width: 250px;">
				<div class="top-right-section" style="top: 15px;">
					<i class="bi bi-check-circle" style="margin-top: -3px; color: #22d722; padding: 3px;"></i>
					<i class="bi bi-caret-down-fill" style="font-size: 13px; padding: 2px;"></i>
				</div>
				<div class="chart-header" style="font-size: 15px;">
					Activité des utilisateurs par cohorte
				</div>
				<div class="chart-subheader" style="font-size: 9px; ">
					À partir des données de l'appareil uniquement
				</div>
				<table class="custom-retention-table">
					<thead>
						<tr>
							<th></th>
							<th>100,0 %</th>
							<th>16,3 %</th>
							<th>9,1 %</th>
							<th>6,4 %</th>
							<th>4,6 %</th>
							<th>4,1 %</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td class="custom-row-header">6 oct. - 12 oct.</td>
							<td>
								<div class="custom-bar custom-bar-100"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-16"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-9"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-6"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-4"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-4"></div>
							</td>
						</tr>
						<tr>
							<td class="custom-row-header">13 oct. - 19 oct.</td>
							<td>
								<div class="custom-bar custom-bar-100"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-16"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-9"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-6"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-4"></div>
							</td>
							<td></td>
						</tr>
						<tr>
							<td class="custom-row-header">20 oct. - 26 oct.</td>
							<td>
								<div class="custom-bar custom-bar-100"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-16"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-9"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-6"></div>
							</td>
							<td></td>
							<td></td>
						</tr>
						<tr>
							<td class="custom-row-header">27 oct. - 2 nov.</td>
							<td>
								<div class="custom-bar custom-bar-100"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-16"></div>
							</td>
							<td>
								<div class="custom-bar custom-bar-9"></div>
							</td>
							<td></td>
							<td></td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
        
	
	
		<div class="statDashcard" style="height: 25vh;">
			 <div class="chart-container" style="width: 260px;">
                <div class="top-right-section"style=" width: 48px; top: 15px;">
                    <i class="bi bi-check-circle" style="margin-top: -3px;color: #22d722;padding: 3px;"></i>
                    <i class="bi bi-caret-down-fill" style="font-size: 13px;padding: 2px;"></i>
                </div>
                <div class="chart-header-details" style="font-size: 14px;padding: 10px; margin-bottom: 10px;">Activité des utilisateurs dans le temps</div>
                <div style=" display: flex;">
                    <canvas id="userActivityChart" style="height: 70%;width: 69%;"></canvas>
                    <div class="legend">
                        <div class="legend-item">
                            <div style="margin-bottom: 5px;">
                                <span class="legend-dot" style="background-color: #539AF8;"></span>30 jours
                            </div>
                            <span class="legend-taux">119 K</span>
                        </div>
                        <div class="legend-item">
                            <div style="margin-bottom: 5px;">
                                <span class="legend-dot" style="background-color: #8C54FF;"></span>7 jours
                            </div>
                            <span class="legend-taux">32 K</span>
                        </div>
                        <div class="legend-item">
                            <div style="margin-bottom: 5px;">
                                <span class="legend-dot" style="background-color: #FF6188;"></span>1 jour
                            </div>
                            <span class="legend-taux">3.1 K</span>
                        </div>
                    </div>
                </div>
            </div>

		</div>

        	        <div class="statDashcard" style="height: 25vh;">
			    <div class="chart-container" style="width: 230px;height: 350px;">
                <i class="bi bi-check-circle check-mark-icon"></i>
                <div class="chart-header">
                    <span class="chart-header-details"style="text-underline-offset: 2px;font-size: 11px;">
                    UTILISATEUR ACTIFS AU COURS DES 30 DERNIERE MINUTES</span>
                </div>
                <div class="statistic-header">
                        <span class="static-header-nom">NOM DE L'EVENT</span>
                        <span class="static-header-number">NOMBRE DE L'EVENT</span>
                </div>
                <div class="trait-seperator" ></div>
                <!-- List Container -->
                <div class="list-containers">
                    <div class="statistic">
                        <div class="statistic-content">
                            <div class="stat-title">All Users</div>
                            <div class="stat-number">311</div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    <div class="statistic">
                        <div class="statistic-content">
                            <div class="stat-title">7-day unnotified users</div>
                            <div class="stat-number">310</div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 80%;"></div>
                        </div>
                    </div>
                        <div class="statistic">
                        <div class="statistic-content">
                            <div class="stat-title">7-day unnotified users</div>
                            <div class="stat-number">310</div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 60%;"></div>
                        </div>
                    </div>
                        <div class="statistic">
                        <div class="statistic-content">
                            <div class="stat-title">7-day unnotified users</div>
                            <div class="stat-number">310</div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 40%;"></div>
                        </div>
                    </div>
                        <div class="statistic">
                        <div class="statistic-content">
                            <div class="stat-title">7-day unnotified users</div>
                            <div class="stat-number">310</div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 20%;"></div>
                        </div>
                    </div>
                </div>
                <a class="chart-footer">
                    Afficher "Temps réel"
                    <i class="bi bi-arrow-right-short" style="margin-right: 3px;margin-left: 5px;color: #778dfa;font-size: 21px;"></i>
                </a>
            </div>
		</div>
		</div>
	</div>
</div>
</div> 
  </div>


<script src="{{ asset('js/prisesLivraisonmap/prisesLivraisonmap.js') }}" defer></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script> 
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script> 
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
    const polygonsData = {{ polygons|json_encode|raw }};
   // console.log(polygonsData);
</script>


     <script src="https://www.gstatic.com/charts/loader.js"></script> 
     <script type="text/javascript">
	  google.charts.load('current', { packages: ['corechart'] });
	  google.charts.setOnLoadCallback(drawChart);
	
	  function drawChart() {
	    var data = google.visualization.arrayToDataTable([
	      ['Date', 'Durée d’engagement moyenne'],
	      ['27 Oct', 3.0],
	      ['29 Oct', 3.2],
	      ['01 Nov', 4.0],
	      ['03 Nov', 5.5],
	      ['05 Nov', 6.0],
	      ['07 Nov', 5.8],
	      ['10 Nov', 5.9],
	      ['15 Nov', 5.5],
	      ['17 Nov', 5.0],
	      ['19 Nov', 3.2]
	    ]);
	
	    var options = {
	      title: '',
	      curveType: 'function',
	      legend: { position: 'none' },
	      chartArea: { width: '80%', height: '50%', right: '10%' }, // Shift chart area to the left
	      hAxis: {
	        textStyle: { color: '#909090' },
	        textPosition: 'in',
	        textAlign: 'right'  
	      },
	      vAxis: {
	        textStyle: { color: '#909090' },
	        ticks: [0, 1, 3, 5, 6],
	        direction: -1
	      },
	      backgroundColor: 'transparent',
	      colors: ['#1e88e5'],
	    };
	
	    var chart = new google.visualization.LineChart(document.getElementById('curve_chart'));
	    chart.draw(data, options);
	  }
	</script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    const ctx = document.getElementById('userActivityChart').getContext('2d');

    const data = {
        labels: ['27 oct.', '03 nov.', '10 nov.', '17 nov.'],
        datasets: [
            {
                label: '30 jours',
                data: [150000, 145000, 140000, 119000],
                borderColor: '#539AF8',
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.4
            },
            {
                label: '7 jours',
                data: [60000, 50000, 40000, 32000],
                borderColor: '#8C54FF',
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.4
            },
            {
                label: '1 jour',
                data: [5000, 4000, 3500, 3100],
                borderColor: '#FF6188',
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.4
            }
        ]
    };

    const options = {
        responsive: false, // Disable responsiveness
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
        },
        scales: {
            x: {
                grid: { display: false },
                ticks: { color: '#909090' }
            },
            y: {
                grid: { color: '#383838' },
                ticks: {
                    color: '#909090',
                    callback: function (value) {
                        return value >= 1000 ? value / 1000 + ' k' : value;
                    }
                }
            }
        }
    };

    const chart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: options
    });
    {# const chart2 = new Chart(ctx2, {
        type: 'line',
        data: data,
        options: options
    }); #}
</script>
<script>
    google.charts.load('current', { 'packages': ['corechart'] });
    google.charts.setOnLoadCallback(drawAudienceOverview);

    function drawAudienceOverview() {
        drawAudienceChart();
        setupThemeSwitcher();
    }

    async function drawAudienceChart() {
        const data = google.visualization.arrayToDataTable([
            ['Day', 'Users'],
            ['Mon', 20],
            ['Tue', 40],
            ['Wed', 60],
            ['test1', 45],
            ['test2', 30],
            ['test3', 50],
            ['Mon1', 20],
            ['Tue2', 40],
            ['Wed3', 60],
            ['test11', 45],
            ['test22', 30],
            ['test33', 50],
            ['Mon12', 20],
            ['Tue12', 40],
            ['Wed12', 60],
            ['test112', 0],
            ['test232', 0],
            ['test3123', 0],
            ['Mon321', 20],
            ['Tue123', 40],
            ['Wed321', 60],
            ['test1456', 45],
            ['test2654', 30],
            ['test3654', 0],
            ['Mon789', 20],
            ['Tue987', 40],
            ['Wed87', 0],
            ['test198', 45],
            ['test2123', 30],
            ['test3456', 50],
        ]);

        const selectedTheme = document.documentElement.getAttribute("data-theme") || "light";
        const chartColor = selectedTheme === "dark" ? "#FFA500" : "#539AF8";

        const options = {
            title: '',
            legend: { position: 'none' },
            hAxis: {
                textPosition: 'none',
                gridlines: { color: 'transparent' },
                baselineColor: 'transparent'
            },
            vAxis: {
                minValue: 0,
                textPosition: 'none',
                gridlines: { color: 'transparent', count: 4 },
                baselineColor: 'transparent'
            },
            backgroundColor: 'transparent',
            colors: [chartColor],
            bar: { groupWidth: '100%' },
            chartArea: {
                left: 0,
                top: '20px',
                width: '100%',
                height: '60%'
            }
        };

        const chart = new google.visualization.ColumnChart(document.getElementById('audienceChartStat'));
        chart.draw(data, options);
    }

    function setupThemeSwitcher() {
        const themeOptionItems = document.querySelectorAll(".theme-options li");
        themeOptionItems.forEach((item) => {
            item.addEventListener("click", () => {
                const selectedTheme = item.getAttribute("data-theme");
                document.documentElement.setAttribute("data-theme", selectedTheme);

                // Update active class
                themeOptionItems.forEach((el) => el.classList.remove("active"));
                item.classList.add("active");

                // Redraw the chart with the new theme
                drawAudienceChart();
            });
        });
    }
</script>
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet.markercluster/dist/leaflet.markercluster.js"></script>
<script>
    var map = L.map('map').setView([48.8566, 2.3522], 5); // Paris

    // Light tile layer
    var tileLayerInverted = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 20
    }).addTo(map);

    // Dark tile layer
    var tileLayerDark = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 20
    });

    // Marker clustering
    var clusterGroup = L.markerClusterGroup();
    var individualMarkers = [];

    // Retrieve and add markers from coordinates (assuming coordinates are defined)
    var coordinates = {{ coordinates|raw }};
    if (coordinates && coordinates.length > 0) {
        coordinates.forEach(function(result) {
            if (result.voies && result.voies.length > 0) {
                result.voies.forEach(function(voie) {
                    if (voie.coordinates && voie.coordinates.length > 0) {
                        voie.coordinates.forEach(function(coord) {
                            var marker = L.marker([coord.longitude, coord.latitude])
                                .bindPopup(`<b>${voie.nom_voie}</b><br>Numéro: ${coord.numr_voie || 'N/A'}`);
                            clusterGroup.addLayer(marker);
                            individualMarkers.push(marker);
                        });
                    }
                });
            }
        });
    }

    map.addLayer(clusterGroup);

    // Zoom end event handler
    map.on('zoomend', function () {
        if (map.getZoom() > 16) {
            individualMarkers.forEach(function(marker) {
                marker.addTo(map);
            });
        } else {
            individualMarkers.forEach(function(marker) {
                map.removeLayer(marker);
            });
            map.addLayer(clusterGroup);
        }
    });

    // Theme toggling function
    function toggleDarkMode(selectedTheme) {
        if (selectedTheme === "dark") {
            map.removeLayer(tileLayerInverted);
            tileLayerDark.addTo(map);
            document.body.classList.add('dark-mode');
        } else {
            map.removeLayer(tileLayerDark);
            tileLayerInverted.addTo(map);
            document.body.classList.remove('dark-mode');
        }
    }

    // Setup theme switcher event listeners
    function setupThemeSwitcher() {
        const themeOptionItems = document.querySelectorAll(".theme-options li");
        themeOptionItems.forEach((item) => {
            item.addEventListener("click", () => {
                const selectedTheme = item.getAttribute("data-theme");
                document.documentElement.setAttribute("data-theme", selectedTheme);

                // Update active class
                themeOptionItems.forEach((el) => el.classList.remove("active"));
                item.classList.add("active");

                // Toggle map theme
                toggleDarkMode(selectedTheme);

                // Redraw the chart with the new theme
                drawAudienceChart();
            });
        });
    }

    // Initialize the map with the current theme
    const currentTheme = document.documentElement.getAttribute("data-theme") || "light";
    toggleDarkMode(currentTheme);

    // Set up the theme switcher
    setupThemeSwitcher();
</script>
