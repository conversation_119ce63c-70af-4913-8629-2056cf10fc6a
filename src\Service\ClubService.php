<?php

namespace App\Service;

use Symfony\Contracts\HttpClient\HttpClientInterface;

class ClubService
{

    private $client;

    public function __construct(HttpClientInterface $client)
    {
        $this->client = $client;
    }

    public function getClubsBySearch($search, $page_number = 1){
        try {
            // Fetch user data
            $response = $this->client->request('GET',
                'https://transfermarkt-api.fly.dev/clubs/search/'.$search.'?page_number='.$page_number, [
                'headers' => [
                    'accept' => 'application/json',
                ]
            ]);
            $userData = json_decode($response->getContent(), true);

            if(is_array($userData) && array_key_exists('results', $userData)){
                    return $userData;
            }

            return  [];

        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
            return  [];
        }
    }

    public function getAllClubsBySearch($search){
        $page_number = 1;
        $data = [];

        $clubData = $this->getClubsBySearch($search, $page_number);
        if(is_array($clubData) && array_key_exists('results', $clubData) && array_key_exists('pageNumber', $clubData) && array_key_exists('lastPageNumber', $clubData)){
            $pageNumber = $clubData['pageNumber'];
            $lastPageNumber = $clubData['lastPageNumber'];
            if($pageNumber <= $lastPageNumber){
                for ($i=1; $i<=$lastPageNumber; $i++){
                    $itemClub = $this->getClubsBySearch($search, $i);
                    if(is_array($itemClub) && array_key_exists('results', $itemClub)){
                        foreach ($itemClub['results'] as $club){
                            $data[]=$club;
                        }
                    }
                }
            }
        }


        return $data;
    }

    public function getClubProfileByClubId($clubId){

    }

}