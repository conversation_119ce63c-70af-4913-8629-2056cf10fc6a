{# content.html.twig #}

<style>
:root {
  --background-color: #fff;
  --text-color: #060607;
  --lefttopbar-borderbottom-color: #d8d8d8;
}

[data-theme="dark"] {
  --background-color: #1e1e1e;
  --text-color: #b9bbbe;
  --lefttopbar-borderbottom-color: #222427;
}

.current-theme {
  color: var(--text-color);
  padding: 8px 10px;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 5%;
  left: 8px;
}

.theme-options {
  
  position: absolute;
  background: var(--lefttopbar-borderbottom-color);
  border-radius: 5px;
  display: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  bottom: 9%;
  left: 13px;
    width: 100px;
}

.theme-options.active {
  display: block;
}

.theme-options li {
  padding: 8px 10px;
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
}

.theme-options li:hover {
  background-color: var(--background-color);
}

.theme-icon {
  width: 24px;
  height: 24px;
}
</style>

<div class="sitem">
  <img src="https://wiwork.konnekt.fr/discord/LOGO-WI--blanc.png" alt="Logo" class="dark-mode-logo">
  <img src="https://wiwork.konnekt.fr/discord/LOGO-WI--noir.png" alt="Logo" class="light-mode-logo">
</div>
<div class="line"></div>
<div class="board-list">
  <div class="boards"></div>
</div>
<div class="line param-items"></div>
<div class="sitem" id="ServerAdd" data-tooltip="Ajouter un serveur"></div>
<div class="line"></div>

<div class="theme-switcher">
  <div class="current-theme" tabindex="0">
    <img src="{{ asset('discord/dark-mode.svg') }}" alt="Dark Theme" class="theme-icon">
  </div>
  <ul class="theme-options">


    <li data-theme="light">
      <img src="{{ asset('discord/light-mode.svg') }}" alt="Light Theme" class="theme-icon">
      <span>Light</span>
    </li>
    <li data-theme="dark" class="active">
      <img src="{{ asset('discord/dark-mode.svg') }}" alt="Dark Theme" class="theme-icon">
      <span>Dark</span>
    </li>

  </ul>
</div>

<script>
document.addEventListener("DOMContentLoaded", () => {
  const currentThemeElement = document.querySelector(".current-theme");
  const themeOptions = document.querySelector(".theme-options");
  const themeOptionItems = document.querySelectorAll(".theme-options li");

  // Show/hide theme options
  currentThemeElement.addEventListener("click", () => {
    themeOptions.classList.toggle("active");
  });

  // Apply selected theme
  themeOptionItems.forEach((item) => {
    item.addEventListener("click", () => {
      const selectedTheme = item.getAttribute("data-theme");
      document.documentElement.setAttribute("data-theme", selectedTheme);

      // Update active class
      themeOptionItems.forEach((el) => el.classList.remove("active"));
      item.classList.add("active");

      // Hide theme options
      themeOptions.classList.remove("active");
    });
  });

  // Hide theme options when clicking outside
  document.addEventListener("click", (event) => {
    if (!currentThemeElement.contains(event.target) && !themeOptions.contains(event.target)) {
      themeOptions.classList.remove("active");
    }
  });

  // Hide theme options with Escape key
  document.addEventListener("keydown", (event) => {
    if (event.key === "Escape") {
      themeOptions.classList.remove("active");
    }
  });
});
</script>
