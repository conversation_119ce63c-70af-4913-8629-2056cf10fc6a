<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('styles/ClusterDetailsTable.css') }}">
<style>
.pagination {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin-top: 20px;
}

.pagination li {
    margin: 0 5px;
    padding: 6px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
    font-weight: 500;
    color: var(--nav-active-text-color);
}

.pagination li:hover {
    background-color: var(--nav-active-bg);
    color: var(--nav-active-text-color);
}

.pagination .active {
    background-color: var(--nav-active-bg);
    color: var(--nav-active-text-color);
}

#clusterdetailstable {
    padding: 20px;
}
</style>
<div id="clusterdetailstable" >
<div class="full-tabledataclusters">
	<nav class="navbar table-navbar navbar-expand-lg">
		<ul class="navbar-nav me-auto mb-1 mb-lg-0">
			<li class="nav-item" id="tablenav">
				<a class="nav-link active" href="#">All</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unfulfilled</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unpaid</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Open</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Closed</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Local Delivery</a>
			</li>
		</ul>
		<div class="navbar-nav">
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-search"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-sliders"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-filter"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-arrow-down-up"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-three-dots"></i>
			</a>
		</div>
	</nav>

	<div class="container mt-2" style="margin-left: -0.5%;">
		 <div class="row mb-3 align-items-center">
        <div class="col-10 col-md-2">
            <div class="custom-select-container">
                <i class="bi bi-box icon"></i>
<select class="form-select" id="categorySelect">
    <option disabled selected>Select City</option>
</select>
            </div>
        </div>
        <div id="addfilter" class="col-12 col-md-2 d-flex align-items-center justify-content-center">
            <span>+ Add filter</span>
        </div>
    </div>
	</div>

<table class="table">
        <thead>
                <tr>
                    <th><input type="checkbox" class="form-check-input" id="customCheck"></th>
                    <th><i class="bi bi-hash"></i>Insee Code</th>
                    <th><i class="bi bi-person"></i>City</th>
                    <th><i class="bi bi-currency-dollar"></i>CP</th>
                    <th><i class="bi bi-stack"></i>Total Lines</th>
                    <th><i class="bi bi-stack"></i>Total Prises</th>
                    <th><i class="bi bi-stack"></i>Total Pavillons</th>
                    <th><i class="bi bi-box"></i>FTTH</th>
                    <th><i class="bi bi-box"></i>FTTB</th>
                    <th><i class="bi bi-box"></i>SFR Infra</th>
                    <th><i class="bi bi-box"></i>Orange Infra</th>
                    <th><i class="bi bi-box"></i>Other Infra</th>
                </tr>
            </thead>
        <tbody id="tableBody">
        </tbody>
    </table>
    <ul class="pagination" id="paginationControls"></ul>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    const rowsPerPage = 11;
    const tableData = [
        {% for detail in clusterDetails %}
            {
                inseeCode: "{{ detail.kpi.inseeCode | default('N/A') }}",
                ville: "{{ detail.kpi.vill | default('N/A') }}",
                cp: "{{ detail.kpi.cp | default('N/A') }}",
                totalLignes: "{{ detail.kpi.totalLignes | default('N/A') }}",
                totalPrises: "{{ detail.kpi.totalPrises | default('N/A') }}",
                totalPavillons: "{{ detail.kpi.totalPavillons | default('N/A') }}",
                totalPrisesFTTH: "{{ detail.kpi.totalPrisesFTTH | default('N/A') }}",
                totalPrisesFTTB: "{{ detail.kpi.totalPrisesFTTB | default('N/A') }}",
                SFR_infra: "{{ detail.kpi.SFR_infra | default('N/A') }}",
                ORANGE_infra: "{{ detail.kpi.ORANGE_infra | default('N/A') }}",
                Other_infra: "{{ detail.kpi.Other_infra | default('N/A') }}"
            },
        {% endfor %}
    ];

    // Step 1: Function to populate the city dropdown with unique, sorted values
    function populateCityDropdown() {
        const cityDropdown = document.getElementById("categorySelect");
        const cities = Array.from(new Set(tableData.map(row => row.ville))) // Get unique cities
                            .sort(); // Sort cities alphabetically

        cities.forEach(city => {
            const option = document.createElement("option");
            option.value = city;
            option.textContent = city;
            cityDropdown.appendChild(option);
        });
    }

    // Step 2: Call populateCityDropdown after tableData is defined
    populateCityDropdown();

    let currentPage = 1;

    function renderTable(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;
        const rows = tableData.slice(start, end);
        const tableBody = document.getElementById("tableBody");
        tableBody.innerHTML = "";

        rows.forEach(row => {
            const tr = document.createElement("tr");
            tr.innerHTML = `
                <td><input type="checkbox" class="form-check-input"></td>
                <td><strong>${row.inseeCode}</strong></td>
                <td><strong>${row.ville}</strong></td>
                <td>${row.cp}</td>
                <td>${row.totalLignes}</td>
                <td>${row.totalPrises}</td>
                <td>${row.totalPavillons}</td>
                <td>${row.totalPrisesFTTH}</td>
                <td>${row.totalPrisesFTTB}</td>
                <td>${row.SFR_infra}</td>
                <td>${row.ORANGE_infra}</td>
                <td>${row.Other_infra}</td>
            `;
            tableBody.appendChild(tr);
        });
    }

    function setupPagination() {
        const totalPages = Math.ceil(tableData.length / rowsPerPage);
        const paginationControls = document.getElementById("paginationControls");
        paginationControls.innerHTML = "";

        for (let i = 1; i <= totalPages; i++) {
            const li = document.createElement("li");
            li.textContent = i;
            li.classList.add("page-item");
            if (i === currentPage) li.classList.add("active");
            li.onclick = () => {
                currentPage = i;
                renderTable(currentPage);
                setupPagination();
            };
            paginationControls.appendChild(li);
        }
    }

    renderTable(currentPage);
    setupPagination();
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
