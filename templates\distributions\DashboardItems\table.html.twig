<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Styled Table</title>
		<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
		<link rel="stylesheet" href="{{ asset('styles/MainBloc.css') }}">
		<style>
			.table-responsive {
				max-height: 350px; /* Set desired max height */


				border-radius: 15px;
			}
			.table {
				width: 100%;
				border-collapse: collapse;
				background-color: #2c2f33;
				border-radius: 8px;

				box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
			}
			.table thead th {
				font-weight: 500;
				text-align: left;
				position: sticky;
				top: 0;
				z-index: 1;
			}
			.table thead th .sortable-icon {
				margin-left: 8px;
				font-size: 0.8em;
				color: #6c757d;
			}
			.table tbody tr {
				transition: background-color 0.2s;
			}
			.table tbody tr:hover {
				background-color: #495057;
			}
			.table tbody td {
				border-top: 1px solid #444;
				color: #f8f9fa;
			}
			.form-check-input {
				appearance: none;
				width: 15px;
				height: 15px;
				border-radius: 4px;
				cursor: pointer;
				margin-left: 10px;
				position: relative;
			}

			.table-container {
				border-radius: 15px;
				padding: 0 !important;
			}


			.table-responsive {
				max-height: 400px;
				border-radius: 10px;

				scrollbar-width: thin;
				scrollbar-color: #666 transparent;
				
				
			}
			.dark-mode .table-responsive {
				max-height: 400px;
				border-radius: 10px;
				scrollbar-width: thin;
				scrollbar-color: #2B2D31 transparent;
		
			}

			@media(min-width: 1367px) {

				.table-responsive {

					border-radius: 10px;

					max-height: 270px;
				}
				.dark-mode .table-responsive {
				max-height: 270px;
				border-radius: 10px;
				scrollbar-width: thin;
				scrollbar-color: #2B2D31 transparent;
		
			}

			}
			.table-responsive::-webkit-scrollbar {
				width: 12px;
				background-image: linear-gradient(135deg, #495057 25%, #6c757d 75%);
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
			}

			.table-responsive::-webkit-scrollbar-thumb {
				background-color: #495057;
				border-radius: 6px;
				border: 3px solid #2c2f33;
				background-image: linear-gradient(135deg, #495057 25%, #6c757d 75%);
				box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
			}
		</style>
	</head>
	<body>
		<div class="table-container">
			<div class="table-responsive">
				<table class="table">

					<tr>
						<th>
						<div style="padding-left: 14px;">
						#
						</div>
					
						</th>
						<th>
							<i class="bi bi-arrow-down-up sortable-icon"></i>
							TET_GRP</th>
						<th>
							<i class="bi bi-arrow-down-up sortable-icon"></i>
							Ventes Brutes</th>
						<th>
							<i class="bi bi-arrow-down-up sortable-icon"></i>
							Ventes Valides</th>
						<th>
							<i class="bi bi-arrow-down-up sortable-icon"></i>
							Tx VV/VB</th>
						<th>
							<i class="bi bi-arrow-down-up sortable-icon"></i>
							Ventes Raccordées</th>
						<th>
							<i class="bi bi-arrow-down-up sortable-icon"></i>
							Tx Raccordées</th>
					</tr>


					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1049484-CIRCET DISTRIBUTION</td>
						<td>4011</td>
						<td>3885</td>
						<td>64.5%</td>
						<td>4011</td>
						<td>64.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>

					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<tr>
						<td><input type="checkbox" class="form-check-input"></td>
						<td>1053520-LITE YEAR</td>
						<td>2458</td>
						<td>2308</td>
						<td>63.5%</td>
						<td>2458</td>
						<td>63.5%</td>
					</tr>
					<!-- Add more rows as needed -->
				</tbody>
			</table>
		</div>
	</body>
</html></div></body></html>
