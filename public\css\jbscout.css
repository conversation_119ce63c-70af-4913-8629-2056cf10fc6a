body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    margin: 0;
    padding: 20px;
    color: #333;
}

.search-container {
    background-color: #004d40;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    color: white;
}

input[type="text"] {
    padding: 10px;
    width: 300px;
    margin-right: 10px;
    border: none;
    border-radius: 5px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    background-color: #26a69a;
    color: white;
    cursor: pointer;
    margin-top: 20px;
}

button:hover {
    background-color: #00796b;
}

.results-container {
    margin-top: 20px;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
}

.results-container div {
    border-bottom: 1px solid #ccc;
    padding: 10px;
    color: #333;
}

.results-container div:last-child {
    border-bottom: none;
}
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}
.market-history-list {
    background-color: #f8f9fa;
    padding: 20px;
    margin-top: 20px;
    border-radius: 8px;
    color: #333;
}

.market-history-list ul {
    list-style-type: none;
    padding: 0;
}

.market-history-list li {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ccc;
}

.market-history-list li:last-child {
    border-bottom: none;
}

.ranking-info {
    margin-top: 20px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
}

canvas {
    width: 100%;
    /*max-width: 600px;  */
    height: auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.market-value-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.market-value-table th, .market-value-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.market-value-table th {
    background-color: #f4f4f4;
    color: #333;
}

.market-value-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.ranking-info {
    margin-top: 20px;
    font-size: 16px;
    color: #333;
    font-weight: bold;
}
td{    padding: 10px;}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;    border: 1px solid #00796b;
}

table, th, td {
    border: 1px solid #ddd;
    text-align: left;
    padding: 8px;    border: 1px solid #00796b;
}

th {
    background-color: #f4f4f4;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f1f1f1;
}
/* #loader {
    border: 16px solid #f3f3f3; 
    border-top: 16px solid #00796b;
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
    display: none;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} */
.loader {
    position: absolute;
    border: 10px solid #f3f3f3;
    border-top: 10px solid #00796b;
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
    /* position: relative; */
    margin: 50px auto;
    display: flex
;
    justify-content: center;
    align-items: center;
    z-index: 10;
    top: 40%;
    left: 40%; align-items: center;
}

.loader i {
    position: absolute;
    font-size: 60px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

  