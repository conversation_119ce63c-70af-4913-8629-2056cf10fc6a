.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.col {
    flex: 1;
    padding: 10px;
}

.map-analytics,
.clusters-info,
.top-flop-cluster,
.bonus-cluster {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    margin-right: 20px; /* Adjust margin if needed */
    display: flex; /* Added to allow flex styling */
    flex-direction: column; /* Ensures children stack vertically */
}


.chat-section {
    background-color: #e6f7ff;
    border-radius: 10px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    margin-right: 20px; /* Adjust margin if needed */
    display: flex; /* Added to allow flex styling */
    flex-direction: column; /* Ensures children stack vertically */
}




@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }

    .col {
        margin-bottom: 20px;
    }
}

.map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}


.mafrenchsoilp {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

