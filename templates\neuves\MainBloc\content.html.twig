<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Analytics Dashboard</title>

		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<style>:root
		{
			/* Couleurs par défaut (mode clair) */
			--bg-color: #fff;
			--text-color: #000;
			--header-bg: #fff;
			--header-text: #333;
			--card-bg: #fff;
			--card-text: #444;
			--accent-color: #539AF8;
			--border-color: #ddd;
			--scrollbar-thumb: #bbb;
			--scrollbar-track: #eee;
			--chiffre-color: #000;
			--header-span-color: #ddd;
			--pourcent-color: #555;
		}

		[data-theme="dark"] {
			/* Couleurs pour le mode sombre */
			--bg-color: #111;
			--text-color: #fff;
			--header-bg: #222;
			--header-text: #b0b0b0;
			--card-bg: #111;
			--card-text: #b0b0b0;
			--accent-color: #539AF8;
			--border-color: #444;
			--scrollbar-thumb: #2B2D31;
			--scrollbar-track: #555;
			--chiffre-color: #fff;
			--header-span-color: rgba(255, 255, 255, 0.6);
			--pourcent-color: #b0b0b0;
		}

		/* Application des couleurs dans vos styles */
		body {
			margin: 0;
			font-family: 'Roboto', sans-serif;
			background-color: var(--bg-color);
			color: var(--text-color);
		}

		.dashboard {
			display: flex;
			flex-direction: column;
			gap: 10px;

			overflow-y: auto; /* Enable scrolling when content overflows */
			max-height: 95vh; /* Limit the height, adjust as needed */
			scrollbar-width: thin;
			scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
			font-family: 'Roboto', sans-serif;
		}

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			color: var(--header-text);
		}

		.header h1 {
			font-size: 1.5rem;
			color: var(--text-color);
		}

		.header button {
			padding: 5px 15px;
			border: 2px solid var(--border-color);
			color: var(--accent-color);
			background-color: var(--header-bg);
			border-radius: 5px;
			cursor: pointer;
			font-size: 0.8em;
			z-index: 100;
		}

		.map-section {
			background-color: var(--card-bg);
			padding: 10px -20px !important;
		}

		#map {
			width: 100%;
			height: 530px;
		}

		.stats-section {
			display: flex;
			flex-wrap: wrap; /* This allows the cards to wrap to the next line when necessary */
			gap: 14px;
			padding-left: 32px;
			justify-content: flex-start; /* Align cards to the left */
		}

		.stats-card {
			background-color: var(--card-bg);
			padding: 20px;
			border-radius: 8px;
			color: var(--card-text);
			width: 306px; /* Ensure a fixed width */
			border: 1px solid var(--border-color);
			height: 402px;
			overflow: hidden;
			margin-bottom: 20px; /* Adds space between rows */
		}

		.stats-cards {
			position: absolute;
			top: -263px;

			z-index: 2000; /* Make sure it is above the map */
			background-color: var(--card-bg);
			padding: 15px;
			border-radius: 8px;
			width: 626px;
			height: 262px;

		}


		.stats-cards h2,
		.stats-card h2 {
			font-size: 1.2rem;
			color: var(--card-text);

		}

		.chart-container {
			margin-top: 15px;
		}

		.table-section {
			margin-top: 20px;
		}

		.table-section table {
			width: 100%;
			border-collapse: collapse;
		}

		.table-section td {
			padding: 8px;
			border-bottom: 1px solid var(--border-color);
		}

		.table-section td:first-child {
			font-weight: bold;
		}

		.rows {
			display: flex;
			justify-content: space-between; /* Ou center, space-around, etc. selon vos besoins */
			align-items: center; /* Pour centrer verticalement les éléments */
		}

		.p {
			font-size: 10px;
		}

		.source-item {
			display: flex;
			justify-content: space-between;
			padding: 3px 0;
			border-bottom: 1px solid var(--border-color);
		}

		.progress-bar-custom {
			width: 270px;
			height: 3px;
			background-color: var(--border-color);
		}

		.progress-bar-fill {
			height: 100%;
			background-color: var(--accent-color);
		}


		.u {
			width: 50%;
			margin: 20px auto;
			border: 1px solid #000;

		}

		.containers {
			position: relative; /* This makes the container the reference point for absolute positioning */
			padding-left: 32px;
		}
		.chiffre {
			flex: 1;
			color: var(--chiffre-color);
		}
		.header .span {
			color: var(--header-span-color)
		}
		.pourcent {
			color: var(--pourcent-color);
		}
		.textp {
			color: var(--chiffre-color);
			font-size: 24px;
		}
		@media screen(min-width: 1400px) {}
	</style>
	<style>
  /* Default Light Theme (Root Variables) */
  :root {
    --background-color: #ccc;
    --chart-background-color: #ccc;
    --progress-bar-background-color: #f4f4f4;
    --progress-bar-fill-color: #ddd;
    --footer-background-color: #ccc;
    --skeleton-text-color: #ccc;
  }

  /* Dark Theme Variables */
  [data-theme="dark"] {
    --background-color: #666;
    --chart-background-color: #666;
    --progress-bar-background-color: #333;
    --progress-bar-fill-color: #666;
    --footer-background-color: #666;
    --skeleton-text-color: #666;
  }

  /* Skeleton Loading Styles */
  .skeleton-text {
    background-color: var(--skeleton-text-color);
    height: 20px;
    width: 100%;
    margin-bottom: 10px;
    border-radius: 4px;
    animation: pulse 1.5s infinite ease-in-out;
  }

  .skeleton-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .skeleton-chart {
    background-color: var(--chart-background-color);
    width: 180px;
    height: 85px;
    border-radius: 6px;
    animation: pulse 1.5s infinite ease-in-out;
  }

  .skeleton-progress-bar {
    background-color: var(--progress-bar-background-color);
    width: 100%;
    height: 6px;
    margin: 10px 0;
    border-radius: 3px;
    animation: pulse 1.5s infinite ease-in-out;
  }

  .skeleton-progress-bar-fill {
    background-color: var(--progress-bar-fill-color);
    height: 100%;
    width: 0;
    border-radius: 3px;
  }

  footer.skeleton-text {
    background-color: var(--footer-background-color);
    height: 20px;
    width: 100px;
    border-radius: 4px;
    animation: pulse 1.5s infinite ease-in-out;
  }

  /* Pulse Animation */
  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 0.6;
    }
  }
</style>

</head>
<body>
	<div class="dashboard">

		{% include 'neuves/MainBloc/header.html.twig' %}
		<div style=" margin-bottom:0rem;">
			<section style=" color: #b0b0b0;" class="stats-section">

			<div id="stats-container" style="display: flex
;
    flex-wrap: wrap;
    gap: 14px;
    padding-left: 32px;
    justify-content: flex-start;"></div>
</section>
<script>
    window.onload = function() {
        const statsContainer = document.getElementById('stats-container');
        
        const numCards = 6; // Specify the number of times you want to repeat the stats card
        
        for (let i = 0; i < numCards; i++) {
            const statsCard = document.createElement('div');
            statsCard.classList.add('stats-card');
            
            statsCard.innerHTML = `
                <div class="skeleton">
                    <h2 class="skeleton-text"></h2>
                    <h2 class="skeleton-text"></h2>
                    <h2 class="skeleton-text" style="font-weight: bold;"></h2>
                    
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div class="skeleton-container">
                            <div class="skeleton-text"></div>
                            <div class="skeleton-text"></div>
                        </div>
                        <div class="skeleton-chart skeleton"></div>
                    </div>

                    <div class="source-list">
                        <div class="source-item skeleton-text"></div>
                        <div class="source-item skeleton-text"></div>
                        <div class="skeleton-progress-bar">
                            <div class="skeleton-progress-bar-fill"></div>
                        </div>
                        <div class="source-item skeleton-text"></div>
                        <div class="skeleton-progress-bar">
                            <div class="skeleton-progress-bar-fill"></div>
                        </div>
                        <div class="source-item skeleton-text"></div>
                        <div class="skeleton-progress-bar">
                            <div class="skeleton-progress-bar-fill"></div>
                        </div>
                    </div>
                    
                    <footer class="skeleton-text"></footer>
                </div>

                <h2>Utilisateurs actifs par</h2>
                <h2>premiere source Utilisateurs
                    <i class="bi bi-caret-down-fill"></i>
                </h2>
                <h2 style="font-weight: bold;">#1 google</h2>
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <br>
                        <div style="margin-left: 20px; margin-top: -30px;">
                            <span class="textp">103</span>
                            <br>
                            <span class="pourcent">64,38%</span>
                        </div>
                    </div>
                    <div style="width: 180px; height: 85px;" class="chart-container">
                        <canvas style="width: 180px; height: 85px;" id="users30MinutesCharts"></canvas>
                    </div>
                </div>

                <div class="source-list">
                    <div class="source-item pourcent">
                        <u>premiere source</u>
                        <u>Utilisateurs</u>
                    </div>
                    <div class="source-item">
                        <span>google</span>
                        <span>103</span>
                    </div>
                    <div class="progress-bar-custom">
                        <div class="progress-bar-fill"></div>
                    </div>
                    <div class="source-item">
                        <span>(direct)</span>
                        <span>49</span>
                    </div>
                    <div class="progress-bar-custom">
                        <div style="width: 50%;" class="progress-bar-fill"></div>
                    </div>
                    <div class="source-item">
                        <span>google-play</span>
                        <span>8</span>
                    </div>
                    <div class="progress-bar-custom">
                        <div style="width: 20%;" class="progress-bar-fill"></div>
                    </div>
                </div>

                <!-- Footer Section -->
                <footer style="margin-top: 15px; text-align:end;">
                    <div>1 - 6 sur 13
                        <i style="color: #444;" class="bi bi-chevron-left"></i>
                        <i class="bi bi-chevron-right"></i>
                    </div>
                </footer>
            `;
            
            statsContainer.appendChild(statsCard);
        }
        
        setTimeout(function () {
            document.querySelectorAll('.skeleton').forEach(skeleton => {
                skeleton.style.display = 'none';
            });
            document.querySelectorAll('.content').forEach(content => {
                content.style.display = 'block';
            });
        }, 2000); // 2 seconds
    };
</script>

				</script>
			

		</div>
	</div>


	<script>
		// Users in the Last 30 Minutes Chart
const users30MinutesCtxz = document.getElementById('users30MinutesCharts').getContext('2d');
new Chart(users30MinutesCtxz, {
type: 'bar',
data: {
labels: Array.from(
{
length: 20
},
(_, i) => ` `
),
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 20
},
() => Math.floor(Math.random() * 100)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 1
}
]
},
options: {
responsive: false,
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Set to true to display y-axis ticks
},
grid: {
display: false // Hide grid lines
}
},
x: {
ticks: {
display: true // Set to true to display x-axis ticks
},
grid: {
display: false // Hide grid lines
}
}
},
plugins: {
legend: {
display: false // Hide legend
},
// Ensure smilePlugin is properly defined if used
smilePlugin: {
display: false
}
}
}
});
const users30MinutesCtxzs = document.getElementById('users30MinutesChartes').getContext('2d');
new Chart(users30MinutesCtxzs, {
type: 'bar',
data: {
labels: Array.from(
{
length: 20
},
(_, i) => ` `
),
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 20
},
() => Math.floor(Math.random() * 100)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 1
}
]
},
options: {
responsive: false,
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Set to true to display y-axis ticks
},
grid: {
display: false // Hide grid lines
}
},
x: {
ticks: {
display: true // Set to true to display x-axis ticks
},
grid: {
display: false // Hide grid lines
}
}
},
plugins: {
legend: {
display: false // Hide legend
},
// Ensure smilePlugin is properly defined if used
smilePlugin: {
display: false
}
}
}
});
const users30MinutesCtxzes = document.getElementById('users30MinutesChartees').getContext('2d');
new Chart(users30MinutesCtxzes, {
type: 'bar',
data: {
labels: Array.from(
{
length: 5
},
(_, i) => ` `
),
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 5
},
() => Math.floor(Math.random() * 50)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 1
}
]
},
options: {
responsive: false,
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Set to true to display y-axis ticks
},
grid: {
display: false // Hide grid lines
}
},
x: {
ticks: {
display: true // Set to true to display x-axis ticks
},
grid: {
display: false // Hide grid lines
}
}
},
plugins: {
legend: {
display: false // Hide legend
},
// Ensure smilePlugin is properly defined if used
smilePlugin: {
display: false
}
}
}
});
	</script>
</body></html>
