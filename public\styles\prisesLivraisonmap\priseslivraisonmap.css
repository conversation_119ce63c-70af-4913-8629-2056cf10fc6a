:root {
    --chart-spans2: #333;
    --statDashcard-bg: #fff;
    --progress-bar: #539AF8;
    --scrollbar-thumb: #aaa;
    --scrollbar-track: #f0f0f0;

    /* Light mode bar colors */
    --custom-bar-100: #1976d2;
    --custom-bar-16: #42a5f5;
    --custom-bar-9: #90caf9;
    --custom-bar-6: #bbdefb;
    --custom-bar-4: #e3f2fd;
}

/* Dark Mode */
[data-theme="dark"] {
    --chart-spans2: #ddd;
    --statDashcard-bg: #0f181f;
    --chart-spans: #ddd;
    --progress-bar: #FFA500;
    --scrollbar-thumb: #555; /* Darker thumb in dark mode */
    --scrollbar-track: #333; /* Darker track in dark mode */
    
    /* Dark mode bar colors */
    --custom-bar-100: #FFA500;
    --custom-bar-16: #ffc26c;
    --custom-bar-9: #ffe0b5;
    --custom-bar-6: #fff0da;
    --custom-bar-4: #fff0da;
}
.mafrenchsoilp {
	height: 100vh; /* Full viewport height */
	width: 100%;
	margin: 0;
	padding: 0;
	position: absolute;
	z-index: 1; /* Arrière-plan */
}

.custom-table-container {
    margin-top: 30px;
    padding: 0;
      background-color: #222;
      width: 450px;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
    }

    .custom-table-header {
      font-size: 14px;
      margin-bottom: 10px;
    }

    .custom-table-header span {
      font-weight: bold;
    }

    .custom-retention-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
    }

    .custom-retention-table th,
    .custom-retention-table td {
      text-align: left;
      padding: 5px 0;
    }

    .custom-retention-table th {
      color: #aaa;
      border-bottom: 1px solid #444;
    }

    .custom-retention-table td {
      color: var(--chart-spans2);
    }

    .custom-row-header {
    
      white-space: nowrap;
      font-size:9px;
    }

    .custom-bar {
      display: inline-block;
      width: 93%;
      height: 14px;
      margin-right: 6px;
      border-radius: 2px;
    }

    /* Bar colors */
 .custom-bar-100 { background-color: var(--custom-bar-100); }
.custom-bar-16 { background-color: var(--custom-bar-16); }
.custom-bar-9 { background-color: var(--custom-bar-9); }
.custom-bar-6 { background-color: var(--custom-bar-6); }
.custom-bar-4 { background-color: var(--custom-bar-4); }
.top-right-section{
position: absolute;
    top: 20px;
    right: 5px;
    display: flex;
    justify-content: space-between;
    /* gap: 14px; */
    width: 48px;
    border-radius: 20px;
    border: 1px solid #666464;
    padding: 4px;
}
.footer-retention-chart{
    font-size: 12px;
    color: #aaa;
    margin-top: 10px;
    /* text-align: center; */
    position: absolute;
    bottom: 18px;
    width: 32%;
    left: 20px;
}
.td-etat{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100px;
}
    table {
      width: 100%;
        border-collapse: collapse;
    }

    th, td {
      text-align: left;
      padding: 8px;
      font-size: 12px; /* Matches the small font size in the image */
    }

    th {
      color: #aaa; /* Grey text for header */
      font-weight: normal;
      border-bottom: 1px solid #555; /* Subtle border for the header */
    }

    td {
      border-bottom: 1px solid #333; /* Subtle row separators */
    }

    .android-icon {
      color: #7cb342; /* Green color for Android icon */
      font-weight: bold;
    }

    .ios-icon {
      font-weight: bold;
    }
.check-mark-icon{color: #22d722;
    margin-top: 9px;
    padding: 3px;
    font-size: 20px;
    position: absolute;
    top: 0;
    right: 9px;
}
.usersActiveCurve_chart{color: #778dfa;width: 102%;font-size: 14px;white-space: nowrap;text-overflow: ellipsis;display: inline-block;margin-top: 5px;}
.move-span{font-size: 20px;}
.statCurve_chart{font-size: 25px;color: var(--chart-spans2);}
        .legend {
            display: flex;

            flex-direction: column;
            gap: 20px;
        }
        .legend-item {
            color: var(--chart-spans2);
            font-size: 12px;
            margin-right: 15px;
        }
        .legend-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .legend-taux{
            font-size: 15px;
    padding: 10px;
        }

    .chart-footer{
        margin-top: 15px;
        display: flex;
        color: #778dfa;
        font-size: 12px;
        /* align-items: flex-end; */
        justify-content: end;
    }

    .title-graphe{
            font-size: 14px;
    width: 76%;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    overflow: hidden;
    height: 20px;
    margin-left: 11px;
}
.chart-header-details{color:var(--chart-spans);text-decoration: underline;
            text-decoration-color: #909090;
            text-decoration-style: dashed;text-underline-offset: 4px; font-size:13px;}
.livedashboard {
    margin: 25px;
    margin-right: 20px;
    position: absolute;
    top: 315px;
    max-width: 98%;
    z-index: 1000;
}
.charts{
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.chart-container {
    padding: 15px 10px 0px 0px !important;
    background: var(--Mainchart-background);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--chart-border);
    color: var(--chart-spans);
    margin-right: 0;
    width: 220px;
    height: 100%;
    border: 1px solid #3e4e54;
}

.chart-header{
    padding: 5px;
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.audienceChart {
    flex: 3;  /* Adjust this value to control how much space the chart takes relative to the header */
    /*background: #333;*/
}


.chart-title { font-size: 16px;padding: 5px;margin:10px;color:var(--chart-spans2);}
.chart-total{ font-size: 22px;color: var(--chart-spans2); padding-top: 5px;}
.chart-percentage { font-size: 12px; padding: 4px;}


.list-containers {
    width: 100%;
    background: var(--Mainchart-background);
    color: var(--chart-spans);
    border-radius: 10px;
    margin-top: 10px; 
}


.statistic-content {
    display: flex;

}

.statistic-header{
    display: flex; /* Use Flexbox */
    justify-content: space-between; /* Align items on both ends */
    align-items: center;
}
.statistic-header span {
    display: inline-block;
    border-bottom: 1px dashed #909090;
    font-size: 8px;
    font-family: roboto, arial;
}

.stat-title {
    flex-grow: 1;
}
.containermotifechec{
    height: 100%;
}
.stat-number {
    margin-right: 10px;    
    /* font-size: 10px; */
}

.progress-container {
    background-color: var(--progress-container);
    width: 100%;
    height: 2px;
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    /* background-color: var(--progress-bar); */
    border-radius: 5px;
}


.Main-chart {
    display: flex;
    width: 626px;
    height: 262px;
    background-color: var(--Mainchart-background) ;
    flex-direction: column;
    padding: 7px;
    border-radius: 11px;
    border: 1px solid var(--chart-border);
    color: var(--chart-spans);
    margin-bottom: 10px;
}

.content-description{
    display:flex;
    font-size:12px;
    color:var(--chart-spans);
    /*font-family: roboto, arial;
    flex:2;*/
}

.description-number{
    font-size: 27px;
    margin-left: 20px;
    font: 400 36px / 44px Google Sans, Arial, Helvetica, sans-serif;
    color: var(--chart-spans2);
    }
.description-title{
    font: 500 12px / 21px ROBOTO,arial;
    letter-spacing: .5px;
    text-transform:uppercase;
}

.statDash {
        display: flex    ;
        flex-wrap: wrap;
        gap: 2px;
        margin: 5px;
        width: 87%;
        z-index: 2;
        /* max-height: calc(100vh - 70%); */
        overflow-y: scroll;
        scrollbar-width: none;
        scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
        margin-top: 0;
}

/* Style for scrollbars in Webkit browsers (Chrome, Edge, Safari) */
.statDash::-webkit-scrollbar {
    width: 8px;
}

.statDash::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb); /* Thumb color */
    border-radius: 4px;
}

.statDash::-webkit-scrollbar-track {
    background-color: var(--scrollbar-track); /* Track color */
}

.maindash {
	display: flex;
	overflow: hidden;
	scrollbar-width: thin;
	scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
	overflow-y: auto;
	max-height: 100vh;
}
.maindashbords {
	display: flex;
	overflow: hidden;
	scrollbar-width: thin;
	scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
	overflow-y: auto;
	max-height: 100vh;
}
/* .statDashcard {
	background-color: var(--statDashcard-bg);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); 
    border:1px solid #87CEEB;
} */


.right-panel {
  width: 250px;
  background-color: #1f2a38;
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow-y: auto;
  border: 1px solid #3a4a5e;
  transition: transform 0.3s ease;
  position: absolute;
  right: -250px;
  top: 10;
  height: 100%;
    z-index: 3;
}

.right-panel.open {
  transform: translateX(-250px);
}

.toggle-button {
  position: absolute;
  right: 0;
  top: 10;
  background-color: #2e3b4e;
  color: #ffffff;
  border: none;
  padding: 10px 15px;
 
  cursor: pointer;
  transition: background-color 0.3s, right 0.3s;
  z-index: 3;
  height: 100%;
}

.toggle-button.open {
  right: 250px;
}


:root {
    --activebutton-color-text: #fff ;
    --bgcolor-active-button : #bfbfbf; 
}

[data-theme="dark"] {
  
    --bgcolor-active-button : #0f181f; 
    --activebutton-color-text: #4b96c1 ;
}

    .channel-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    .channel-item a {
        text-decoration: none;
        color: gray;
    }

    .channel-item a:hover,
    .channel-item a.active {
        color: darkgray;
    }

    .sidebar-link {
        font-size: 32px;
        width: 250px;
        display: block;
        padding: 8px; 
        padding-left: 24px;
        border-radius: 10px;
        transition: background-color 0.3s;
        color: #fff;
        text-decoration: none;
    }

    .sidebar-link:hover {
        background-color: var(--bgcolor-active-button); 
    }

    .sidebar-link.active {
        background-color: var(--bgcolor-active-button); 
        color: var(--activebutton-color-text);
    }

    .sectionDivider {
        color: #222;
    }

    .sidebar-header {
        text-align: center;
        margin-bottom: 10px;
    }

    .toggle-section-sideb2 {
        margin: 0; /* Remove or reduce margin to reduce space between items */
    }

    .toggle-section-sideb2 a {
        text-decoration: none;
    }

    .sidebar-section-titleactive {
        color: var(--activebutton-color-text);
        font-size: 14.2px;
        text-transform: uppercase;
        margin: 2px 0; 
    }
    .sidebar-section-title{
          color: var(--section-title-color);
        font-size: 14.2px;
        text-transform: uppercase;
        margin: 1px 0; 
    }
    .contentent {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-right: 1px solid var(--sidebar-color);
    height: 89vh;
    flex-grow: unset;
    padding: 0px;
    font-family: Arial, sans-serif;
    color: #ddd;
    width: 315px;
    height: 100vh;
    overflow-y: auto;
    border: 2px solid var(--bottom-barder-color);
    flex-grow: 1;
    overflow-y: auto;
    padding: 0px;
    background-color: var(--sidebar-left-right-color);
}
/* .Tree-data, .Tree-Formulaires, .Tree-Roles {
    height: 32%;
    flex-grow: 1;
    overflow: hidden;
} */
.headerTreeSection {
    display: flex;
    height: 34px;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 10px;
    margin-left: 0px;
    margin-top: 7px;
    width: 100%;
    background-color: var(--headerTreeSection-bg);
}
.sectionDivider {
    height: 2px;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
    background-color: var(--lefttopbar-borderbottom-color);
}

.resizer, .resizer3 {
    display: flex;
    height: 10px;
    background: var(--nav-link-active-bg);
    cursor: row-resize;
    align-items: flex-end;
    justify-content: space-around;
    font-size: 20px;
}


.tree-view {
    background-color: var(--tree-view-bg, #fff); /* Default fallback color */
    border-radius: 5px;
    color: var(--tree-view-color, #000); /* Default fallback color */
    display: flex;
    flex-direction: column; /* Make sure it's column for proper alignment */
}

.tree-view ul {
    list-style-type: none;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    font-size: 14px;
    font-family: 'Ubuntu', sans-serif; /* Added fallback font */
}

.tree-view li {
    cursor: pointer;
    /* border-bottom: 1px solid var(--border-fieldSpan, #ccc);  */
    /* Default fallback color */
    position: relative;
    display: flex;
    flex-direction: column;
}

.tree-view .caret {
    user-select: none;
    padding: 3px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.tree-view .nested {
    display: none; /* Hide by default */
    margin-left: 0px; /* Indent nested lists for clarity */
    background-color: var(--nested-bg, #f9f9f9); /* Default fallback color */
}

.tree-view .nested.active {
    display: flex; /* Display when active */
    flex-direction: column;
}

.caret::before {
    color: black;
    display: inline-block;
    margin-right: 5px;
    transition: transform 0.3s;
}

.caret-down .flecheimg {
    transform: rotate(90deg);
}

:root {
    --color-search-text: #0e567e; 
    --color-buttons-nav: #2d475a;
    --search-input-color: #ffffff;
}

[data-theme="dark"] {
    --color-search-text: #afb1b5; 
        --color-buttons-nav: #a4a7ab;
    --search-input-color: #0f181f;

}

.nav-buttons-group {
    display: flex;
}

.nav-button {
    padding: 3px 15px;
    color: var( --color-buttons-nav);
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 12px; 
}

.nav-button.active {
    background-color: #007bff; 
    color: #fff;
}

.nav-button:hover {
    background-color: #555;
    color: #fff;
}

.search-input {
    background-color: transparent;
    border: none;
    color: var( --color-search-text);
    outline: none;
    font-family: 'Ubuntu', sans-serif;
    padding-right: 25px; 
    font-size: 14px;
}

.search-input::placeholder {
    color: #aaa; 
}

.search-icon {
    position: absolute;
    right: 15px; 
    color: #888; 
    pointer-events: none; 
}

.checkbox {
    opacity: 0;
    position: absolute;
}

.label {
    background-color: #111;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    position: relative;
    height: 15px;
    width: 35px;
    transform: scale(1.5);
}

.label .ball {
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: -10px;
    height: 12px;
    width: 12px;
    transform: translateX(10px);
    transition: transform 0.2s linear;
}

.checkbox:checked + .label .ball {
    transform: translateX(30px);
}

.fa-moon {
    color: #f1c40f;
}

        .collapsed {
            width: 0 !important;
            overflow: hidden;
            transition: width 0.3s;
        }

.switch-container{
 margin-top: 3%;
}
.right-side-topbar {
    margin-right: 3.5%;
}


.checkbox {
    opacity: 0;
    position: absolute;
}

.label {
    background-color: #111;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    position: relative;
    height: 15px;
    width: 35px;
    transform: scale(1.5);
}

.label .ball {
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    left: -10px;
    height: 12px;
    width: 12px;
    transform: translateX(10px);
    transition: transform 0.2s linear;
}

.checkbox:checked + .label .ball {
    transform: translateX(30px);
}

.fa-moon {
    color: #f1c40f;
}

        .collapsed {
            width: 0 !important;
            overflow: hidden;
            transition: width 0.3s;
        }

  .nav-buttons {
        
        padding: 5px 10px;
        background-color: transparent;
        color: #000;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        margin-left: 30px;
    }
    /* .nav-button {
        
        padding: 4px 8px;
        background-color: #ad78e1;
        color: #FFF;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        font-weight: bold;
        width: 100px;
        margin-left: 20px;
        
    } */
 .dark-mode .nav-buttons{
    color: darkgrey;
 }
  .dark-mode .nav-button{
    color: #000;
 }