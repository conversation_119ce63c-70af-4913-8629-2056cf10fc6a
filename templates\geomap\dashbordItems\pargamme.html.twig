
<style>
.pargamme-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.pargamme-chart-and-legend {
    display: flex;
    align-items: center;
    margin-top: 5%;
    gap: 20px;
}

.pargamme-chart-container {
    height: 112px;
    width: 112px;
}

.pargamme-legend {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pargamme-legend li {
    display: flex;
    align-items: center;
    font-size: 0.8em;
    color: var( --4text-colors);
    margin-bottom: 5px;
}

.pargamme-legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 30%;
}
</style>
<div class="bonustechno-container">
    <h5 class="text-header4s">Par Gamme</h5>
    <div class=" ChartsByType bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="pargammePieChart"></canvas>
        </div>
        <ul class="bonustechno-legendpargamme" style="margin-bottom:0;">
            <!-- The legend will be populated dynamically with JavaScript -->
        </ul>
    </div>
</div>
<script>
var existingChart;

function ParGamme(chartData) {
    console.log('ParGamme', chartData);

    // Generate colors for each item
    function generateColors(count) {
        const predefinedColors = ['#9a4eca', '#62eade', '#145277', '#f4a261', '#2a9d8f', '#e76f51'];
        return Array.from({ length: count }, (_, i) => predefinedColors[i % predefinedColors.length]);
    }

    function createChart() {
        const chartCanvas = document.getElementById('pargammePieChart');
        if (!chartCanvas) {
            console.warn("Chart container not found!");
            return;
        }

        var bonustechnoCtx = chartCanvas.getContext('2d');

        if (existingChart) {
            existingChart.destroy();
        }

        const backgroundColors = generateColors(chartData.length);

        // Create the chart
        existingChart = new Chart(bonustechnoCtx, {
            type: 'doughnut',
            data: {
                labels: chartData.map(item => item.productName),
                datasets: [{
                    data: chartData.map(item => item.totalVentes),
                    backgroundColor: backgroundColors,
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false }
                },
                cutout: '80%',
            }
        });

        // Create the legend after chart creation
        createLegend(chartData, backgroundColors);
    }

    // Create legend showing only the first 3 items
    function createLegend(chartData, backgroundColors) {
        const legendContainer = document.querySelector('.bonustechno-legendpargamme');
        legendContainer.innerHTML = ''; // Clear existing legend items

        // Limit the legend to the first 3 items
        const limitedData = chartData.slice(0, 3);

        limitedData.forEach(function(item, index) {
            const legendItem = document.createElement('li');

            // Create legend color block
            const legendColor = document.createElement('span');
            legendColor.classList.add('bonustechno-legend-color');
            legendColor.style.background = backgroundColors[index]; // Set color based on index

            // Create text node with 10px font size and append it
            const textNode = document.createTextNode(`${item.productName} `);
            const textSpan = document.createElement('span');
            textSpan.style.fontSize = '9px'; // Set the font size to 10px
            textSpan.appendChild(textNode);

            // Append color block and text with font size
            legendItem.appendChild(legendColor);
            legendItem.appendChild(textSpan);

            legendContainer.appendChild(legendItem);
        });
    }

    createChart();
}

</script>

