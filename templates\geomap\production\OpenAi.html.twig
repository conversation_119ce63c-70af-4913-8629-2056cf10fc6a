
<div class="dropdown">
  <button class="btn btn-secondary dropdown-toggle"style="background: none;width:100%;" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
    Select a Question
  </button>
  <div class="dropdownsQuestions dropdown-menu" aria-labelledby="dropdownMenuButton">
    <!-- Buttons will be populated here dynamically -->
  </div>
</div>
<div id="output"class=" outputQuestion scrollbar-custom" ></div>

<script>

document.addEventListener('DOMContentLoaded', function() {
    var GifAi = document.getElementById("openAiGif").getAttribute("OpneAiGifCard");
    lottie.loadAnimation({
      container: document.getElementById("lottie-animation"),
      renderer: "svg",
      loop: true,
      autoplay: true,
      path: GifAi
    });
    async function fetchQuestions(){
   var url =`https://api.nomadcloud.fr/api/open_ai_questions?page=1 `;
   try {
        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${jwtToken}`,
                "Content-Type": "application/json"
            }
        });

        if (!response.ok) {
            console.error('HTTP error', response.status);
            return [];
        }

        const data = await response.json();
        const dropdownsQuestions = document.querySelector(".dropdownsQuestions");
        dropdownsQuestions.innerHTML = ''; // Clear existing buttons if any

       data['hydra:member'].forEach(question => {
    const button = document.createElement('button');
    button.className = 'dropdown-item';
    button.textContent = question.titre;
    button.setAttribute("description", question.description); // Corrected 'buttoon' to 'button'
    button.onclick = function() { fetchStreamedResponse(question.description); };
    dropdownsQuestions.appendChild(button);
});


    } catch (error) {
        console.error('Failed to fetch or parse data:', error);
    }
    }
    fetchQuestions();
});
async function fetchStreamedResponse(question) {
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
         headers: {
            "Authorization": "", // Remplace par ta clé API
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            model: "gpt-4",
            messages: [{ role: "user", content: question }],
            stream: true
        })
    });

     const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullText = "";

    while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter(line => line.startsWith("data:"));

        for (const line of lines) {
            try {
                const json = JSON.parse(line.substring(5).trim());

                if (json.choices && json.choices[0].delta && json.choices[0].delta.content) {
                    fullText += json.choices[0].delta.content;
                    const outputElement = document.getElementById("output");
                    outputElement.innerText = fullText;

                    // Force update scroll position after a slight delay to ensure DOM updates
                    setTimeout(() => {
                        outputElement.scrollTop = outputElement.scrollHeight;
                    }, 0); // Adjust delay as needed, 0 might be enough in most cases
                }
            } catch (e) {
                console.error("Error parsing JSON", e);
            }
        }
    }
}
</script>





