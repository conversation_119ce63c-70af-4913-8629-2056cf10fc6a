<!DOCTYPE html>
<html lang="fr">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Objectif de Ventes</title>
		<style>
			.containers {
				font-family: 'CONSOLAS'!important;
				color: #d3e4e6;
				border-radius: 8px;
				margin: 0;
			}

			.top-bar {

				height: 20px;
				display: flex;
				align-items: center;
				justify-content: space-between;

			}

			.top-bar-left {
				color: #a8b4b8;
				font-weight: bold;
			}

			.top-bar-icons {
				display: flex;
				gap: 10px;
			}

			.top-bar-icons img {
				width: 20px;
				height: 20px;
				cursor: pointer;
			}

			.containeres {
				background-color: #4d254d;
				padding: 10px;
				border-radius: 8px;
				box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
				

			}

			h1 {
				text-align: center;
				color: #e3f2fd;
			}

			p {
				margin: 10px 0;
			}

			.calculation {
				font-weight: bold;
				color: #ce00c4;
			}

			.input-icon {
				position: relative;
				display: flex;
				align-items: center;
			}

			.input-icon input {
				padding-right: 35px; /* pour laisser de la place pour l'icône */
				height: 30px;
				font-size: 16px;
				flex: 1;
				border-radius: 5px;
			}

			.input-icon .icon {
				position: absolute;
				right: 10px; /* placer l'icône à droite de l'input */
				width: 20px;
				height: 20px;
			}
			
.command-line {
    font-size: 14px;
    width: 100%;
    height: 100px;
    overflow-x: hidden;
    overflow-y: scroll
}

.command-line.light {
    background-color: #fff
}

.command-line.light .command-row {
    position: relative;
    margin-bottom: 5px
}

.command-line.light .command-row.active {
    background: #f5f5f5
}

.command-line .command-row {
    position: relative;
    margin-bottom: 5px
}


.command-line .command-row .command-time,.command-line .command-row .command-user {
    color: #e7e7e7;
    display: inline-block;
    padding-right: 5px
}

.command-line .command-row .command-user {
    font-weight: 700
}

.command-line .command-row .command-entry {
    padding-right: 5px;
    color: #fff;
    display: inline;
    overflow-wrap: break-word;
    word-wrap: break-word;
    -ms-word-break: break-all;
    word-break: break-all;
    -ms-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto
}

.command-line .command-row .command-entry.command-entry-protected:empty {
    display: none
}

.command-line .command-row .command-entry.block {
    display: block
}

.command-line .command-row .command-entry:focus {
    outline: none
}

.command-line .command-row .secret {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    opacity: 0
}

.command-line .command-row.error .command-entry {
    font-weight: 700;
    color: red
}

.command-line .command-row.success .command-entry {
    font-weight: 700;
    color: #00c300
}

.command-line .command-row.info .command-entry {
    font-weight: 700;
    color: #00a9ff
}

.command-line .command-row.warning .command-entry {
    font-weight: 700;
    color: orange
}
		</style>
	</head>
	<body>
		<!-- Top Bar Section -->
		<div class="containers">
			<div class="containeres">
				<div class="top-bar">
					<div class="top-bar-left calculation">DESKTOP-ON5C84Q ></div>
					<div class="top-bar-icons">
						<div class="input-icon">
							<img src="https://img.icons8.com/ios-glyphs/30/4b484b/filter.png" alt="Filter" class="icon">
							<input type="text" style="background-color: #350232;" placeholder="">
						</div>
						<i class="bi bi-download fs-5"></i>
						{# <img src="https://img.icons8.com/ios-glyphs/30/ffffff/download.png" alt="Download"> #}
						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/add-file.png" alt="Add File">

						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/settings.png" alt="Settings">
						<i class="bi bi-chevron-up"></i>

						<img src="https://img.icons8.com/ios-glyphs/30/ffffff/microphone.png" alt="Microphone">

						<img src="https://img.icons8.com/?size=100&id=45&format=png&color=FFFFFF" alt="close">

					</div>

				</div>

			 <div>
    <div id="command-line" class="command-line"></div>
  </div>
			</div>
		</div>
	</body>
</html>
  <script src="{{ asset('cmd/bundle.min.js') }}"></script>
  <script src="{{ asset('cmd/app.min.js') }}"></script>
    <script src="{{ asset('cmd/script.js') }}"></script>