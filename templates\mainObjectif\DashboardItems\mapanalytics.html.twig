<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('js/franceGeoJSON.js') }}">
<style>
    .floating {
        animation: pulse 3s infinite;
    }
    .mafrenchsoilp {
        height: 100%;
        width: 100%;
        margin: 0; /* Ensure no margin */
        padding: 0; /* Ensure no padding */
        box-sizing: border-box; /* Include padding and border in width/height */
    }
    @keyframes pulse {
        0% { fill-opacity: 0.3; }
        50% { fill-opacity: 0.6; }
        100% { fill-opacity: 0.3; }
    }
    .custom_floating-title {
        position: absolute;
        top: 10px;
        left: 10px;
        font-size: 1.5em;
        color: #333;
        margin: 0;
        pointer-events: none;
        z-index: 1000;
    }
</style>

<div class="bonus-expand-icon">
    <i class="bonus-expand-icon bi bi-fullscreen"></i>
</div>
<h3 class="custom_floating-title">Map analytics</h3>
<div class="mapswrapper">
    <iframe width="100%" height="440" loading="lazy" allowfullscreen src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8&q=franca&zoom=5&maptype=roadmap"></iframe>
</div>

<script>
    window.addEventListener('load', () => {
        document.querySelectorAll('.mafrenchsoilp').forEach((mapElement) => {
            const coords = JSON.parse(mapElement.dataset.coords);
            const zoomLevel = parseInt(mapElement.dataset.zoom);
            const map = L.map(mapElement.id, {
                center: coords,
                zoom: zoomLevel,
                zoomControl: false, // Disable zoom control
                attributionControl: false, // Disable attribution control
                maxBounds: L.latLngBounds(L.latLng(41.0, -5.0), L.latLng(51.5, 9.0)),
                maxBoundsViscosity: 1.0,
                minZoom: zoomLevel, // Set min zoom level to the initial zoom level
                maxZoom: 18 // Optional: set a maximum zoom level
            });
            const adjustedCoords = [coords[0] + 0.5, coords[1]]; // Increase latitude by 0.5 for better visibility

            L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                maxZoom: 18,
            }).addTo(map);

            var franceGeoJSON = {
                "type": "FeatureCollection",
                "features": [
                    {
                        "type": "Feature",
                        "geometry": {
                            "type": "MultiPolygon",
                            "coordinates": [ 
                                // Example coordinates for France (you need to replace these with accurate GeoJSON data)
                                [
                                    [
                                        [ -5.0, 41.0 ], [ 9.0, 41.0 ], [ 9.0, 51.5 ], [ -5.0, 51.5 ], [ -5.0, 41.0 ]
                                    ]
                                ]
                            ]
                        },
                        "properties": {
                            "name": "France"
                        }
                    }
                ]
            };

            var geoJsonStyle = {
                color: "green",
                weight: 1,
                opacity: 1,
                fillColor: "green",
                fillOpacity: 0.3 // Light transparent green
            };

            var franceLayer = L.geoJSON(franceGeoJSON, {
                style: geoJsonStyle
            }).addTo(map);

            // Marker configuration
            const markers = [
                { coords: [46.8, 1.5], color: 'yellow', label: 'A' },
                { coords: [46.6, 1.7], color: 'blue', label: 'B' },
                { coords: [46.4, 1.3], color: 'purple', label: 'C' },
                { coords: [46.5, 1.6], color: 'green', label: 'D' }
            ];

            markers.forEach(marker => {
                const iconUrl = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cpath d='M20 0 C28 0, 35 7, 35 15 C35 20, 28 27, 20 35 C12 27, 5 20, 5 15 C5 7, 12 0, 20 0 Z' fill='${marker.color}'/%3E%3Ctext x='20' y='18' font-size='16' text-anchor='middle' fill='white'>${marker.label}</text%3E%3C/svg%3E`;

                const icon = L.icon({
                    iconUrl: iconUrl,
                    iconSize: [40, 40],
                    iconAnchor: [20, 40]
                });

                L.marker(marker.coords, { icon }).addTo(map);
            });

            // Add main marker with popup
            const mainIcon = L.icon({
                iconUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 512 512'%3E%3Cpath fill='%23E74C3C' d='M256 14C146 14 57 102 57 211c0 172 199 295 199 295s199-120 199-295c0-109-89-197-199-197zm0 281a94 94 0 110-187 94 94 0 010 187z'/%3E%3Cpath fill='%23C0392B' d='M256 14v94a94 94 0 010 187v211s199-120 199-295c0-109-89-197-199-197z'/%3E%3C/svg%3E",
                iconSize: [40, 40],
                iconAnchor: [20, 40]
            });

            // Coordinates for the main marker (adjust as needed)
            const mainMarkerCoords = [46.5, 1.5]; // Adjust to your desired location
            L.marker(mainMarkerCoords, { icon: mainIcon })
                .addTo(map)
                .bindPopup('This is the main marker!'); // Popup content
        });
    });
</script>
