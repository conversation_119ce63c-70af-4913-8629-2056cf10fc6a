<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Occupation des Moniteurs</title>
  <style>
    h2 {
      margin-bottom: 10px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .header select {
      padding: 5px;
      font-size: 14px;
    }

    .table-container {
      display: grid;
      grid-template-columns: 116px repeat(31, 40px); /* 1 colonne pour heures, jusqu'à 31 colonnes pour jours */
      gap: 5px;
      align-items: center;
      grid-template-rows: repeat(10, 40px); /* 10 lignes pour les heures */
    }

    .cell {
      text-align: center;
      padding: 8px ;
      font-size: 12px;
      font-weight: bold;
      border-radius: 4px;
      background-color: #d4edfc;
      color: white;
    }

    .header-cell {
      font-weight: bold;
      text-align: center;
      color: black;
      border-radius: 4px;
      padding: 5px;
    }

    .hour-cell {
      font-weight: bold;
      text-align: center;
      color: black;
      border-radius: 4px;
    }

    /* Couleurs en fonction de l'occupation */
    .cell[data-value="0"] { background-color: #f0f8ff; color: #f0f8ff; }
    .cell[data-value="10"] { background-color: #d4edfc; }
    .cell[data-value="20"] { background-color: #a9ddfb; }
    .cell[data-value="30"] { background-color: #7fcdf9; }
    .cell[data-value="40"] { background-color: #56bdf8; }
    .cell[data-value="50"] { background-color: #33acf7; }
    .cell[data-value="60"] { background-color: #33acf7; }
    .cell[data-value="70"] { background-color: #33acf7; }
    .cell[data-value="80"] { background-color: #33acf7; }
    .cell[data-value="90"] { background-color: #33acf7; }
    .cell[data-value="100"] { background-color: #33acf7; }
  </style>
</head>
<body>
  <div class="header">
    <h2>Occupation des moniteurs</h2>
    <select id="month-select">
      <option value="0">Janvier</option>
      <option value="1" selected>Février</option>
      <option value="2">Mars</option>
      <option value="3">Avril</option>
      <option value="4">Mai</option>
      <option value="5">Juin</option>
      <option value="6">Juillet</option>
      <option value="7">Août</option>
      <option value="8">Septembre</option>
      <option value="9">Octobre</option>
      <option value="10">Novembre</option>
      <option value="11">Décembre</option>
    </select>
  </div>
  <h6 style="display: flex; justify-content: center;">occupation des moniteurs par heures</h6>
  <div class="table-container" id="heatmap">
    <!-- Heatmap cells will be dynamically generated here -->
  </div>

  <script>
    // Pass kpiData from Symfony controller to JavaScript
    const kpiData = {{ kpiData|json_encode|raw }};

    // Function to get the number of days in a given month
    function getDaysInMonth(month, year) {
        return new Date(year, month + 1, 0).getDate();
    }

    // Function to generate the heatmap
    function generateHeatmap(daysInMonth) {
        // Créer un tableau pour accumuler les quantités totales pour chaque jour, par utilisateur
        const dailyQuantitiesByUser = kpiData.map(() => Array(daysInMonth).fill(0)); // Initialisation avec 0 pour chaque jour

        // Parcourir les données des utilisateurs
        kpiData.forEach((user, userIndex) => {
            const salesDetails = user.details;

            // Utiliser totalVentes directement pour chaque utilisateur
            const totalSales = user.ventes.totalVentes;

            // Parcourir les ventes pour chaque utilisateur
            for (const date in salesDetails) {
                if (salesDetails.hasOwnProperty(date)) {
                    // Extraire le jour du mois à partir de la date (format : "DD/MM/YYYY")
                    const [day, month, year] = date.split('/').map(num => parseInt(num));

                    // Ajouter la quantité totale de produits vendus au jour correspondant
                    if (day >= 1 && day <= daysInMonth) {
                        dailyQuantitiesByUser[userIndex][day - 1] += totalSales; // Ajouter le total des ventes pour le jour correspondant
                    }
                }
            }
        });

        // Sélectionner l'élément du conteneur de la heatmap
        const heatmapContainer = document.getElementById('heatmap');

        // Clear existing content
        heatmapContainer.innerHTML = '';

        // Set grid template columns based on the number of days
        heatmapContainer.style.gridTemplateColumns = `116px repeat(${daysInMonth}, 40px)`;

        // Créer l'en-tête pour les jours
        const emptyHeader = document.createElement('div');
        emptyHeader.classList.add('header-cell');
        heatmapContainer.appendChild(emptyHeader);

        // Créer l'en-tête pour les jours du mois
        for (let day = 1; day <= daysInMonth; day++) {
            const dayCell = document.createElement('div');
            dayCell.classList.add('header-cell');
            dayCell.textContent = day; // Afficher le numéro du jour
            heatmapContainer.appendChild(dayCell);
        }

        // Créer les lignes pour les utilisateurs (prénom ici)
        kpiData.forEach((user, userIndex) => {
            // Créer la cellule pour l'utilisateur (prénom ici)
            const hourCell = document.createElement('div');
            hourCell.classList.add('hour-cell');
            hourCell.textContent = user.prenom; // Afficher le prénom de l'utilisateur
            heatmapContainer.appendChild(hourCell);

            // Créer une cellule pour chaque jour et afficher le total des quantités vendues pour cet utilisateur
            dailyQuantitiesByUser[userIndex].forEach((totalQuantity) => {
                const cell = document.createElement('div');
                cell.classList.add('cell');
                cell.setAttribute('data-value', totalQuantity); // Ajouter l'attribut de valeur
                cell.textContent = `${totalQuantity}`; // Afficher le total des ventes pour le jour
                heatmapContainer.appendChild(cell);
            });
        });
    }

    // Initialize with the default month (February in this case)
    const currentYear = new Date().getFullYear();
    const monthSelect = document.getElementById('month-select');
    generateHeatmap(getDaysInMonth(parseInt(monthSelect.value), currentYear));

    // Update the heatmap when a new month is selected
    monthSelect.addEventListener('change', () => {
        const selectedMonth = parseInt(monthSelect.value);
        generateHeatmap(getDaysInMonth(selectedMonth, currentYear));
    });
  </script>
</body>
</html>
