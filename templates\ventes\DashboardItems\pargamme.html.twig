<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

<style>
* {
    font-family: 'Ubuntu', sans-serif;
}

:root {
    --pargamme-color-background-light: #C0E7F7;
    --pargamme-color-background-dark: #e9f5fd;
    --4text-colors: #1d4d6b;
}

[data-theme="dark"] {
    --pargamme-color-background-light: #1e1e1e;
    --pargamme-color-background-dark: #2a2a2a;
        --4text-colors: #919093; 
}

.pargamme-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}



.pargamme-chart-and-legend {
    display: flex;
    align-items: center;
    margin-top: 5%;
    gap: 20px;
}

.pargamme-chart-container {
    height: 112px;
    width: 112px;
}

.pargamme-legend {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pargamme-legend li {
    display: flex;
    align-items: center;
    font-size: 0.8em;
    color: var( --4text-colors);
    margin-bottom: 5px;
}

.pargamme-legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 30%;
}
</style>
<div class="bonustechno-container">
    <h5 class="text-header4s">Par Gamme</h5>
    <div class="bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="pargammePieChart"></canvas>
        </div>
        <ul class="bonustechno-legend">
            {% for item in productionsGroupData.Produit %}
                {% set gradient = "linear-gradient(to right, #027fc0, #4bb3e5)" %}
            
            {% endfor %}
        </ul>
    </div>
</div>


<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    var chartData = {{ productionsGroupData.Produit | json_encode() | raw }}; // Pass data from PHP to JS

    var bonustechnoCtx = document.getElementById('pargammePieChart').getContext('2d');

    // Create gradients for the segments
    var bonustechnoGradientA = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientA.addColorStop(0, '#49b9ee');
    bonustechnoGradientA.addColorStop(1, '#a8e2f5');

    var bonustechnoGradientB = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientB.addColorStop(0, '#027fc0');
    bonustechnoGradientB.addColorStop(1, '#4bb3e5');

    var bonustechnoGradientC = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientC.addColorStop(0, '#145277');
    bonustechnoGradientC.addColorStop(1, '#3a7d9c');

    // Process chartData for the chart's labels and data
    var labels = [];
    var data = [];
    var backgroundColors = []; // Array to hold background colors for each segment

    chartData.forEach(function(item, index) {
        labels.push(item.productName); // Use productName as label
        data.push(item.totalVentes); // Use totalVentes as data

        // Assign different background colors dynamically
        if (index % 3 === 0) {
            backgroundColors.push(bonustechnoGradientA);
        } else if (index % 3 === 1) {
            backgroundColors.push(bonustechnoGradientB);
        } else {
            backgroundColors.push(bonustechnoGradientC);
        }
    });

    // Create pie chart with the processed data
    var pargammePieChart = new Chart(bonustechnoCtx, {
        type: 'pie',
        data: {
            labels: labels, // Product names as labels
            datasets: [{
                data: data, // Total sales data for each product
                backgroundColor: backgroundColors, // Dynamic background colors
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false // Hide the default legend (you've created a custom one)
                }
            }
        }
    });
</script>

