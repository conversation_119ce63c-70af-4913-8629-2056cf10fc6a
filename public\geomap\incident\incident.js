document.getElementById('headerTreeSection').addEventListener('click', function (event) {
    event.stopPropagation();
    const initialDropdown = document.getElementById('initialDropdown');
    const headerTreeSection = document.getElementById('headerTreeSection');
    const rect = headerTreeSection.getBoundingClientRect();
    initialDropdown.style.position = 'absolute';
    initialDropdown.style.top = `${rect.bottom + window.scrollY }px`;
    initialDropdown.style.left = `${rect.left + window.scrollX -10}px`;
    initialDropdown.style.display = 'block';
});



var currentDate = new Date();
// Format the current date as 'dd-mm-yyyy'
var day = String(currentDate.getDate()).padStart(2, '0');
var month = String(currentDate.getMonth() + 1).padStart(2, '0');

var year = currentDate.getFullYear();
let Type='V';
const formattedDate = `${day}-${month}-${year}`;
function formatDateRange(month, year) {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);
    const formatStartDate = `${String(startDate.getDate()).padStart(2, '0')}-${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;
    const formatEndDate = `${String(endDate.getDate()).padStart(2, '0')}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${endDate.getFullYear()}`;
    return `debut=${formatStartDate}&fin=${formatEndDate}`;
}
document.addEventListener('DOMContentLoaded', function() {
    setCurrentMonthName();

    var dropdownButton = document.getElementById('MonthSelector');
    var dropdownMenu = document.querySelector('.MonthSelectorDropdown');
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
    dropdownButton.addEventListener('click', function() {
        var isExpanded = this.getAttribute('aria-expanded') === 'true';
        this.setAttribute('aria-expanded', !isExpanded);
        dropdownMenu.style.display = isExpanded ? 'none' : 'flex';
        if (isExpanded) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });
});
function setCurrentMonthName() {
    const date = new Date();
    const monthIndex = date.getMonth();
    const monthNames = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
        "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"];

    const currentMonthName = monthNames[monthIndex];
    document.querySelector('.CurrentlyMonthApplyed').textContent = currentMonthName;
}
function updateMonth(selectedMonth,monthName) {
    document.querySelector('.CurrentlyMonthApplyed').textContent = monthName;
    month = selectedMonth;

    document.querySelector('.MonthSelectorDropdown').style.display = 'none';
    recallFunctionUpdatedParam();
    const dropdownIcon = document.querySelector(".toggleMonthSelector");
    dropdownIcon.classList.remove("bi-chevron-down");
    dropdownIcon.classList.add("bi-x-lg");
}
function updateTypeOption(selectedElement) {
    var selectors = document.querySelectorAll('.TypeOptionSelector');

    selectors.forEach(function(selector) {
        selector.classList.remove('clickedType');
    });

    selectedElement.classList.add('clickedType');
    Type = selectedElement.getAttribute('dataType');
    localStorage.setItem('clusterForOnedays', '');
    localStorage.setItem('codeinseeForOnedays', '');

    recallFunctionUpdatedParam();
}


let isProductionsUsersNested;
let firstLoading;


// document.addEventListener('DOMContentLoaded', function() {

// });






document.addEventListener('DOMContentLoaded', function () {
    const resizers = document.querySelectorAll('.resizer');

    resizers.forEach((resizer) => {
        let startY, startHeightPrev, startHeightNext;

        resizer.addEventListener('mousedown', function (e) {
            const prevDiv = resizer.previousElementSibling;
            const nextDiv = resizer.nextElementSibling;

            // Store the initial mouse position and heights
            startY = e.pageY;
            startHeightPrev = prevDiv.offsetHeight;
            startHeightNext = nextDiv.offsetHeight;

            // Add mousemove and mouseup event listeners
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);

            function mouseMoveHandler(e) {
                const dy = e.pageY - startY;

                // Calculate new heights
                const newHeightPrev = Math.max(50, startHeightPrev + dy);
                const newHeightNext = Math.max(50, startHeightNext - dy);

                // Apply new heights to the sections
                prevDiv.style.height = `${newHeightPrev}px`;
                nextDiv.style.height = `${newHeightNext}px`;
            }

            function mouseUpHandler() {
                // Remove the event listeners when mouse is released
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            }
        });
    });
    document.getElementById('HandleRightPanel').addEventListener('click', function() {
        var sidebar = document.querySelector('.right-sidebar-concepteur');
        var sidebarStyle = window.getComputedStyle(sidebar);
        var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
        if (sidebarStyle.right === '0px') {
            sidebar.style.right = '-319px';
            IconRightPanelToggle.style.transform = 'rotate(-90deg)';
        } else {
            sidebar.style.right = '0px';
            IconRightPanelToggle.style.transform = 'rotate(90deg)';
        }
    });
});
document.addEventListener('DOMContentLoaded', () => {
    const currentThemeDisplay = document.querySelector('.current-theme');
    const themeOptions = document.querySelector('.theme-options');
    const currentThemeIcon = currentThemeDisplay.querySelector('img');
    const currentThemeText = currentThemeDisplay.childNodes[1];

    let currentTheme = localStorage.getItem('theme') || 'dark'||'darkblue'||'lightsand'||'darklight'||"darkpurple";
    document.body.setAttribute('data-theme', currentTheme);

    const currentThemeElement = document.querySelector(`li[data-theme="${currentTheme}"]`);
    if (currentThemeElement) {
      updateThemeDisplay(currentThemeElement);
    }

    currentThemeDisplay.addEventListener('click', () => {
      themeOptions.style.display = themeOptions.style.display === 'block' ? 'none' : 'block';
    });

    themeOptions.addEventListener('click', event => {
      const themeChoice = event.target.closest('li');
      if (themeChoice) {
        const selectedTheme = themeChoice.getAttribute('data-theme');
        const imgSrc = themeChoice.querySelector('img').src;
        const themeName = themeChoice.textContent.trim();

        currentThemeIcon.src = imgSrc;
        currentThemeText.nodeValue = " " + themeName + " ";

        document.body.setAttribute('data-theme', selectedTheme);
        localStorage.setItem('theme', selectedTheme);

        themeOptions.style.display = 'none';

        document.querySelectorAll('.theme-options li').forEach(li => li.classList.remove('active'));
        themeChoice.classList.add('active');
      }
    });
});
function updateThemeDisplay(themeElement) {
    const iconSrc = themeElement.querySelector('img').src;
    const iconName = themeElement.textContent.trim();
    document.querySelector('.current-theme img').src = iconSrc;
    document.querySelector('.current-theme').childNodes[1].nodeValue = " " + iconName + " ";
}
const toggleButtonTable = document.getElementById('toggle-Chart');
const TablePanel = document.getElementById('displayChartPanel');
let isTableVisible = false;
TablePanel.style.bottom = '-330px';

toggleButtonTable.addEventListener('click', function () {
    TablePanel.style.transition='bottom 0.3s ease-in-out';
    TablePanel.style.height = '350px';
    var IcontoggleChart = document.querySelector('.IcontoggleChart');
    if (isTableVisible) {
        TablePanel.style.bottom = '-330px';
        setTimeout(() => IcontoggleChart.style.transform = 'rotate(0deg)', 300);
    } else {
        setTimeout(() => TablePanel.style.bottom = '0', 10);
        IcontoggleChart.style.transform = 'rotate(180deg)';
    }

    isTableVisible = !isTableVisible;
    document.querySelectorAll('.ChartsByType').forEach(element => {
        if (TablePanel.style.height === '350px' || TablePanel.style.height === '') { 
            element.style.height = '100%';
        } else {
            element.style.height = '157px';
        }
    });
    //drawAudienceChart();
    //changeCanvaschartHeight(TablePanel);
});


const openPanelButton = document.getElementById('openPanel');
const closePanelButton = document.getElementById('closePanel');
const productionPanel = document.getElementById('displayproductionPanel');

openPanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '0';
});

closePanelButton.addEventListener('click', function () {
    productionPanel.style.bottom = '-100%';
    closePanelButton.style.top='39px';
});
function replaceParams(query) {
    return query.replace("debut=", "dateDebut=").replace("fin=", "dateFin=");
}
async function fetchDATAIncident() {
    let date=replaceParams(formatDateRange(month, year));
    console.log('date',date);
    const url=`https://api.nomadcloud.fr/api/incidents-tickets/${cpv}?${date}&page=1 `;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error in API fetchingProductionKPI:", error.message);
    }

}

async function fetchHiearchicalData(){
    var url =`https://api.nomadcloud.fr/api/produconsolidationctions-/${userIdLogIn}/${Type}`;
    try {
        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        HierarchyData=data;
    } catch (error) {
        console.error('Error:', error);
        return null;
    }
}
async function recallFunctionUpdatedParam(){
    localStorage.setItem('clusterCode', '');
    try {
        let HierarchyData = await fetchDATAIncident();
        await fetchingProductionKPI();
        removeAllVilles();
        const treeRoot = document.getElementById('tree-root');
        let data = HierarchyData;
        var itemChoosed=document.querySelector('.itemChoosed');
        var categoryFilter = itemChoosed.getAttribute('categoryFilter');
        if (categoryFilter==='Tous'||categoryFilter===''){categoryFilter=null;}
        const htmlContent = generateHierarchyHtml(data,categoryFilter);
        treeRoot.innerHTML = htmlContent;
      //  updateMapWithAllData(data);
        setupCaretListeners('tree-root');
        setupSearchFunctionality();
        setupDropdownListeners();
        productionsByDay();
        //fetchdataMonthweekdayhour();
        //fetchProductionsIntervention();
        //fetchProductionsAnnulation();
        applyBorderColors();
        // fetchProductionKPI();
        // fetchData();
        // productionsTopFlop();
        // fetchAndDrawChartsCohorte();
        //fetchUserHiarchy();

    } catch (error) {
        console.error('Error in fetchingProductionKPI:', error);
    }
    // drawChartproductionsMotif();
    // fetchAnalystic();
    // infoCardSatisfaction();
}

let allHiearchyData;
document.addEventListener("DOMContentLoaded", async function () {
    let HierarchyData = await fetchDATAIncident();
    productionsByDay();
    await fetchingProductionKPI();
    localStorage.setItem('clusterCode', '');
    const treeRoot = document.getElementById('tree-root');
    console.log("HierarchyData",HierarchyData);
    data = HierarchyData;
    //console.log("HierarchyData",data);
    const htmlContent = generateHierarchyHtml(data);
    treeRoot.innerHTML = htmlContent;
    //updateMapWithAllData(data);
    setupCaretListeners('tree-root');
    setupSearchFunctionality();
    setupDropdownListeners();
    //fetchProductionsAnnulation();
    applyBorderColors();
    //fetchUserHiarchy();
});


function setupDropdownListeners() {
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    var ClusterFilter = document.querySelector('.ClusterFilter');
    const itemChoosed = document.querySelector('.itemChoosed');
    dropdownItems.forEach(item => {
        item.addEventListener('click', function () {
            var categoryFilter = this.classList[1];
            if (categoryFilter==='TousCluster'){categoryFilter=null;}
            data = HierarchyData[0][Number(year)][Number(month)];
            const htmlContent = generateHierarchyHtml(data, categoryFilter);
            itemChoosed.textContent = this.textContent;
            itemChoosed.setAttribute('categoryFilter', categoryFilter);
            ClusterFilter.style.display = 'none';
            const treeRoot = document.getElementById('tree-root');
            treeRoot.innerHTML = htmlContent;
            setupCaretListeners('tree-root');
            setupSearchFunctionality();
            setupDropdownListeners();
            applyBorderColors();
        });
    });
}


function generateHierarchyHtml(data, categoryFilter = null,FilterByEtatCluster = false) {
    allHiearchyData=data;
    let htmlContent = '';
    const fleche = document.getElementById('TreeViewIcon').getAttribute('fleche');
    const flecheFields = document.getElementById('TreeViewIcon').getAttribute('flecheFields');
    
    data.forEach( cluster => {
        let clusterTotal =  cluster.villes.reduce((sum, ville) => sum + (ville.total_incidents || 0), 0);
        console.log("clusterTotal",clusterTotal);
        let placesContent = '';

        cluster.villes.forEach(place => {
            if (!categoryFilter || (place.categories[categoryFilter] && place.categories[categoryFilter] > 0)) {
                placesContent += `
                    <li class="field-list-item">
                        <div class="caret fieldLiSpan" style="display: flex; align-items: center; width: 100%;">
                            <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                                <div class="fieldLiSpan" style="flex-grow: 1; display: flex; align-items: center;"
                                    data-cluster-code="${cluster.codeCluster}" data-insee-code="${place.code_insee}"
                                    onclick="saveClusterInsee('${cluster.codeCluster}', '${place.code_insee}','${place.ville}'); culsterinseeForOnedays('${cluster.clusterCode}','${place.code_insee}');"
                                >
                                    <div style="display: flex; align-items: center;" >
                                        <svg style="height: 12px; width: 12px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><path d=M12.7,1.1c0-0.6-0.5-1.1-1.1-1.1H1.1C0.5,0,0,0.5,0,1.1v4.7h12.7V1.1z /><path d=M12.7,11.6V6.9H0v4.7c0,0.6,0.5,1.1,1.1,1.1h10.4C12.2,12.7,12.7,12.2,12.7,11.6z /></g></svg>
                                    </div>
                                    ${place.ville}
                                </div>
                                <div class="total-place" style="margin-right: 15px; color: #326E78;">
                                    ${place.total_incidents || 0}
                                </div>
                            </div>
                        </div>
                    </li>
                `;
            }
        });

        if (placesContent !== '') {
            htmlContent += `
                <li class="formListItem">
                    <div class="caret formSpan ClusterSpan" data-CLusterCode="${cluster.codeCluster}"style="display: flex; justify-content: space-between; align-items: center; width: 100%;" 
                    onclick="handleClusterClick('${cluster.codeCluster}','${cluster.libelleCluster}');culsterinseeForOnedays('${cluster.codeCluster}',''); simulateClickByClusterCode('${cluster.codeCluster}'); "> 
                        <div style="z-index:1;display: flex;justify-content: space-between;align-items: center;width: 100%;"  >
                            <div style="display: flex; align-items: center;">
                                <div class="flecheimg caretImg" style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var( --tree-view-color); opacity: 0.5;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><g><polygon points="1,1.7 1,11.1 11.7,6.4 	"/></g></svg>
                                </div>
                                <div style="display: flex; align-items: center;" >
                                    <svg style="height: 12.7px; width: 12.7px; fill: var(--coloredTextPrimary); margin-right: 5px; font-size: 20px; border-radius: 3em;" version=1.1 viewBox="0 0 12.7 12.7"x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink y=0px><path d="M11.6,12.7H1.1c-0.6,0-1.1-0.5-1.1-1.1V1.1C0,0.5,0.5,0,1.1,0h10.4c0.6,0,1.1,0.5,1.1,1.1v10.4 C12.7,12.2,12.2,12.7,11.6,12.7z"/></svg>
                                </div>
                                ${cluster.libelleCluster}
                            </div>
                            <div class="total-cluster" style="display: flex; align-items: center; margin-right: 5px; font-size: 16px;"
                                data-CLusterID="${cluster.cluster_id}" data-CLusterCode="${cluster.codeCluster}" data-CLusterName="${cluster.libelleCluster}">
                                ${clusterTotal}
                            </div>
                        </div>
                    </div>
                    <ul class="nested">${placesContent}</ul>
                </li>
            `;
        }
    });
    return htmlContent;
}
function culsterinseeForOnedays(cluster, codeinsee) {
    // Set the values in localStorage
    localStorage.setItem('clusterForOnedays', cluster);
    localStorage.setItem('codeinseeForOnedays', codeinsee);
  }
  
function saveClusterInsee(clusterCode, codInsee,clusterName) {
    console.log('Saving codInsee',codInsee);
    simulateClickByVilleCode(codInsee);
    //fetchUserHiarchy(clusterCode, codInsee); 
    productionsByDay(clusterCode, codInsee);
    let savedData = JSON.parse(localStorage.getItem("clusterInseeData")) || [];
    savedData.push({ clusterCode, codInsee });
    localStorage.setItem("clusterInseeData", JSON.stringify(savedData));
    localStorage.setItem("codInsee",codInsee);
    SetClusterName(clusterName);

}

function SetClusterName(Name) {
    document.querySelector('.NameClickedPlace span').textContent = Name;
}
async function handleClusterClick(clusterCode,name){
    // List of all asynchronous functions to execute
    console.log('savingCodeCluster',clusterCode);
    localStorage.setItem('clusterCode', clusterCode);
    SetClusterName(name);
    const tasks = [
       // productionsByDay(clusterCode),
       // fetchAnalystic(clusterCode),
        //fetchMissingcities(clusterCode),
        //createUserHiarchy(clusterCode),
        //sendRightdataforChartDAys(clusterCode,false),
        //ProductionKPIByPointOfSaleId(clusterCode),
        // fetchdataCluster(clusterCode),
        // findClusterData(clusterCode),
        // fetchInterventionsMigrable(clusterCode)
    ];

    try {
        // Wait for all tasks to complete
        const results = await Promise.all(tasks);
    } catch (error) {
        console.error('Error during operations:', error);
    }
}



function toggleClusterVisibility(event, clusterCode) {
    event.preventDefault();
    const clusterList = document.getElementById(`cluster-${clusterCode}`);
    const isVisible = clusterList.style.display === 'block';
    clusterList.style.display = isVisible ? 'none' : 'block';

    // Optional: Change icon or styles if needed
    const icon = event.currentTarget.querySelector('i.bi-chevron-down');
    if (icon) {
        icon.className = isVisible ? 'bi bi-chevron-down' : 'bi bi-chevron-up';
    }
}


    function setupSearchFunctionality() {
        const searchInput = document.getElementById('TreeSearch');
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase().trim();
            filterFormListItems(searchText);
        });
    }
    
    function filterFormListItems(searchText) {
        const formListItems = document.querySelectorAll('.formListItem');
    
        if (searchText.length < 3) {
            // Show all items if the search text length is less than 3
            formListItems.forEach(item => {
                item.style.display = '';
                item.querySelectorAll('.field-list-item').forEach(subItem => subItem.style.display = '');
            });
        } else {
            formListItems.forEach(item => {
                let hasMatch = false;
                const placeItems = item.querySelectorAll('.field-list-item');
    
                // Check each place within the cluster for a match
                placeItems.forEach(subItem => {
                    const placeName = subItem.querySelector('.fieldLiSpan').textContent.toLowerCase();
                    if (placeName.includes(searchText)) {
                        subItem.style.display = '';
                        hasMatch = true; // Mark as match if any place matches
                    } else {
                        subItem.style.display = 'none';
                    }
                });
    
                // Check the cluster name itself
                const clusterName = item.querySelector('.formSpan').textContent.toLowerCase();
                if (clusterName.includes(searchText) || hasMatch) {
                    item.style.display = ''; // Show the cluster if it or any place matches
                } else {
                    item.style.display = 'none'; // Hide the cluster if no places match
                }
            });
        }
    }

let totalGeneral = 0;
let totalDistanceSum = 0;
let somme = 0;
let decouche =false ;
function handleClick(totalprises, checkbox) {
    // Vérifie si la checkbox est cochée
    const isChecked = checkbox.checked;
    const totalDistance = parseFloat(localStorage.getItem("totalDistance"));

    // Ajoute ou soustrait la valeur de la checkbox au total général
    if (isChecked) {
        totalGeneral += parseInt(totalprises); // Ajoute si la checkbox est cochée
        totalDistanceSum += totalDistance;
        somme++;
        decouche = true;
    } else {
        totalGeneral -= parseInt(totalprises); // Soustrait si la checkbox est décochée
        totalDistanceSum -= totalDistance;
        somme--;
        decouche = false
    }

    // Sauvegarde les valeurs dans le localStorage
    localStorage.setItem('isCheckedsomme', somme);
    localStorage.setItem('totalGeneral', totalGeneral);
    localStorage.setItem('totalDistanceSum', totalDistanceSum.toFixed(2));
    
    // Sauvegarde isChecked dans localStorage
    localStorage.setItem('isChecked', isChecked ); // Si isChecked est vrai, enregistre "true", sinon "false"
    localStorage.setItem('decouche', decouche );
}


function saveToLocalStorages(nomVoie, complement, backgroundColor, totalprises, spanElement) { 
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;

    // Sauvegarde des informations dans localStorage
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises);
    localStorage.setItem('isChecked', isChecked);
    // Récupération des voies sauvegardées
    let savedVoies = JSON.parse(localStorage.getItem('savedVoies')) || [];

    // Vérifier si la voie est déjà enregistrée pour éviter les doublons
    if (!savedVoies.includes(nomVoie)) {
        savedVoies.push(nomVoie);
        localStorage.setItem('savedVoies', JSON.stringify(savedVoies));
    }
}


/**** */


function updateTotales() {
      // Mise à jour des totaux des lieux
      document.querySelectorAll('.total-place').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;

        element.textContent = sum || totalPrises || 0;
    });
    // Mise à jour des totaux des rues
    document.querySelectorAll('.total-street').forEach(element => {
        const adsl = parseInt(element.dataset.nbFyrAdsl) || 0;
        const thd = parseInt(element.dataset.nbFyrThd) || 0;
        const mobMono = parseInt(element.dataset.nbFyrMobMono) || 0;
        const mobMultiThd = parseInt(element.dataset.nbFyrMobMultiThd) || 0;
        const mobMultiAdsl = parseInt(element.dataset.nbFyrMobMultiAdsl) || 0;
        const totalPrises = parseInt(element.dataset.totalPrises) || 0;

        let sum = 0;
        if (document.getElementById('nb_fyr_adsl').checked) sum += adsl;
        if (document.getElementById('nb_fyr_thd').checked) sum += thd;
        if (document.getElementById('nb_fyr_mob_mono').checked) sum += mobMono;
        if (document.getElementById('nb_fyr_mob_multi_thd').checked) sum += mobMultiThd;
        if (document.getElementById('nb_fyr_mob_multi_adsl').checked) sum += mobMultiAdsl;

        element.textContent = sum || totalPrises || 0;
    });
}

document.querySelectorAll('#fileUploadForm input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', updateTotales);
});

const colorMap = new Map();

function getColorForCodeIris(code_iris) {
    if (!colorMap.has(code_iris)) {
        // Alterne entre rouge et bleu de manière cohérente
        const color = colorMap.size % 2 === 0 ? '#094044' : '#094044';
        colorMap.set(code_iris, color);
    }
    return colorMap.get(code_iris);
}

function generateHierarchyHtmlscuivre(data) {
    let htmlContent = '';
    const years = Object.keys(data).sort((a, b) => b - a); // Tri des années en ordre décroissant

    years.forEach(year => {
        const yearData = data[year][1]; // Accès aux données de l'année
        const voies = yearData.voies; // Liste des 'voies' pour cette année

        voies.forEach(voie => {
            // Obtenir une couleur unique pour chaque code_iris
            const backgroundColor = getColorForCodeIris(voie.code_iris);

            // Construire le HTML pour chaque 'voie'
            htmlContent += `
                <li class="formListItem" style="background-color:  var(--nested-bg, #f9f9f9); border-right: 2px ${backgroundColor}; ">
                    <span class="caret formSpan caret-down" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span class="formSpanvoie" data-nom-voie="${voie.nom_voie}" style="display: flex; align-items: center;"
                            onclick="selectVoieAndSearchStreet('${voie.nom_voie || 'N/A'},${voie.complement}, ${backgroundColor}, ${voie.total_prises || 0},this')">
                             <div class="" style="margin-left: 15px;">
                            <div style="margin-left: 15px;">
                            <input class="checkbox-voie" data-nomvoie= "${street.nom_voie }"onclick="handleClick( '${total}',this)"  id="checkbox8" type="checkbox">
                            </div>
                            </div>
                            <div style="height: 12px; width: 12px; margin-right: 5px; font-size: 20px; border-radius: 2px;"></div>
                            ${voie.nom_voie || 'N/A'}
                        </span>
                        <div style="display: flex; color: ${backgroundColor}; align-items: center; margin-right: 15px; font-size: 14px;">
                            ${voie.total_prises || 0} <!-- Afficher totalPrises ou 0 si non défini -->
                        </div>
                    </span>
                </li>
            `;
        });
    });

    return htmlContent; // Retourne le HTML généré
}


function selectVoieAndSearchStreet(nomVoie,complement,backgroundColor,totalprises,spanElement) {
    const checkbox = spanElement.querySelector('input[type="checkbox"]');
    if (!checkbox) return;

    const isChecked = checkbox.checked;
    localStorage.setItem('selectedVoie', nomVoie);
    localStorage.setItem('selectedVoieColor', backgroundColor);
    localStorage.setItem('selectedComplement', complement);
    localStorage.setItem('selectedTotalprises', totalprises); 
    localStorage.setItem('isChecked', isChecked);
}
function toggleStyle(element, isChecked) {
    const span = element.querySelector('span');

    // Toggle background color and border
    if (span.style.backgroundColor === 'rgb(45, 140, 235)') {
        span.style.backgroundColor = 'var(--nav-link-active-bg)';
        span.style.border = '4px solid var(--bg-table-section)';

    } else {
        span.style.backgroundColor = '#2D8ceb';
        span.style.border = '4px solid var(--bg-table-section)';
  
    }
}


    
function setupCaretListeners(rootElementId) {
    const rootElement = document.getElementById(rootElementId);
    const togglers = rootElement.querySelectorAll(".caret, .formSpan");
    
    togglers.forEach(caret => {
        caret.addEventListener("click", function(event) {
            event.stopPropagation();
    
            let nestedUl = this.closest('li').querySelector('.nested');
            if (nestedUl) {
                nestedUl.classList.toggle("active");
            }
    
            document.querySelectorAll('.active-field').forEach(el => {
                el.classList.remove('active-field');
            });
            this.classList.add('active-field');
    
            this.classList.toggle("caret-down");
        });
    });
}

function setupSearchFunctionalityRues() {
    const searchInput = document.getElementById('TreeSearchRues');
    searchInput.addEventListener('input', function() {
        const searchText = this.value.toLowerCase().trim();
        filterFormListItemsRues(searchText);
    });
}
    
function filterFormListItemsRues(searchText) {
    const formListItems = document.querySelectorAll('.formListItem');

    if (searchText.length < 3) {
        // Afficher tous les éléments si la recherche contient moins de 3 caractères
        formListItems.forEach(item => {
            item.style.display = '';
        });
    } else {
        formListItems.forEach(item => {
            const streetNameElement = item.querySelector('.formSpanvoie');
            if (streetNameElement) {
                const streetName = streetNameElement.textContent.toLowerCase();
                if (streetName.includes(searchText)) {
                    item.style.display = ''; // Afficher l'élément si le nom correspond
                } else {
                    item.style.display = 'none'; // Cacher sinon
                }
            }
        });
    }
}

const treeViewIcon = document.getElementById('TreeViewIcon');
const fleche = '/discord/treeview/Fleche.svg';
const flecheFields = treeViewIcon ? treeViewIcon.getAttribute('flecheFields') : '';





let map;

document.addEventListener("DOMContentLoaded", function () {
    let theme = localStorage.getItem("theme") || "darkpurple";
    mapboxgl.accessToken = 'pk.eyJ1IjoicmdvdW50aXRpIiwiYSI6ImNtMnA1bHJ5NDBuczcycnNieGsyamVjOTMifQ.FjXmzR2E_Di8YWn8nfTPog';

    function initializeMap(theme) {
        let MapTheme;
        if (theme === "darkpurple") {
            MapTheme = 'mapbox://styles/rgountiti/cm70iodpa01iu01sa8ihf5wch';
        } else if (theme === "darkblue") {
            MapTheme = 'mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw';
        } else {
            MapTheme = 'mapbox://styles/mapbox/light-v10';
        }

        const mapContainer = document.getElementById('map');
        mapContainer.innerHTML = '';

        map = new mapboxgl.Map({
            container: 'map',
            style: MapTheme,
            center: [2.3522, 48.8566],
            zoom: 6
        });
        map.addControl(new mapboxgl.NavigationControl());
    }


    initializeMap(theme);

    setInterval(() => {
        let newTheme = localStorage.getItem("theme");
        if (newTheme !== theme) {
            theme = newTheme;
            if (theme === "darkpurple") {
                location.reload();
            } else if (theme === "light") {
                location.reload();
            }
            else if (theme === "darkblue") {
                location.reload();
            }
            else {
                map.setStyle('mapbox://styles/rgountiti/cm6s320oh014y01pb83m5gsaw'); // Update the map style
            }
        }
    }, 500);
});

function infoCardByPolygon(ville,villesName, coordinates) {

    findClusterDataForUser(localStorage.getItem('clusterCode'),ville);
    //getTotalPriseVenteByVille(localStorage.getItem('clusterCode'),ville);

    // const card = document.getElementById("info-card-ByPolygon");
    // card.style.display = "block";
    // var villesTest=document.querySelector('.villesTest');
    // villesTest.textContent=villesName;

    // let isDragging = false;
    // let offsetX = 0, offsetY = 0;

    // card.style.position = "absolute";

    // card.addEventListener("mousedown", (e) => {
    //     if (e.button !== 0) return;
    //     isDragging = true;
    //     offsetX = e.clientX - card.offsetLeft;
    //     offsetY = e.clientY - card.offsetTop;
    //     card.style.transition = "none";
    // });

    // document.addEventListener("mousemove", (e) => {
    //     if (!isDragging) return;
    //     card.style.left = `${e.clientX - offsetX}px`;
    //     card.style.top = `${e.clientY - offsetY}px`;
    // });

    // document.addEventListener("mouseup", () => {
    //     isDragging = false;
    //     card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
    // });
    //displayInterventionsMigrable(ville);
}
async function infoCardByPolygonCluster(cluster,villesName, coordinates) {
    //getTotalPriseVenteByVille(localStorage.getItem('clusterCode'),ville);
    const card = document.getElementById("info-card-ByPolygon");
    card.style.display = "block";
    var villesTest=document.querySelector('.villesTest');
    villesTest.textContent=villesName;

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return;
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
    });
    //displayInterventionsMigrable(ville);
}

async function infoCardSatisfaction() {
    const card = document.getElementById("info-card-statisfaction");
    const satisfactionCard = document.querySelector(".statisfactionCard");
    const data = await displaySatisfactionDetails();

    satisfactionCard.innerHTML = '';
    adjustContainerHeight(satisfactionCard);  // Set initial container properties

    for (const item of data) {
        const orderElement = document.createElement('div');
        orderElement.textContent = `Order Number: ${item.orderNumber}`;
        orderElement.style.padding = '10px';
        satisfactionCard.appendChild(orderElement);

        const verbatimElement = document.createElement('div');
        verbatimElement.style.padding = '10px';
        satisfactionCard.appendChild(verbatimElement);

        await typeText(verbatimElement, item.verbatim);
    }
    setupDraggableCard(card);
}
function adjustContainerHeight(container) {
    const maxHeight = 200;
    container.style.maxHeight = `${maxHeight}px`;
    container.style.overflowY = 'auto';
}



function typeText(element, text) {
    return new Promise((resolve) => {
        if (!text) {
            element.textContent = "aucune note";
            checkAndScrollIntoView(element);
            resolve();
            return;
        }

        let index = 0;
        function typeChar() {
            if (index < text.length) {
                element.textContent += text.charAt(index);
                index++;
                if (index === text.length || index % 20 === 0) {
                    checkAndScrollIntoView(element);
                }
                setTimeout(typeChar, 40);
            } else {
                resolve();
            }
        }

        typeChar();
    });
}

function checkAndScrollIntoView(element) {
    setTimeout(() => {
        const container = element.parentElement;  // Reference to the parent container
        container.scrollTop = container.scrollHeight;  // Scroll the container to the bottom
    }, 0);
}






function setupDraggableCard(card) {
    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";
    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return; // Only react to left-clicks
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none"; // Remove transitions during drag
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out"; // Reapply transitions
    });
}

async function displaySatisfactionDetails() {
    const date = `${year}-${String(month).padStart(2, '0')}`;
    const url=`https://api.nomadcloud.fr/api/satisfaction-clients-details/${pointOfSaleId}?yearMonth=${date}&note=3&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json(); // Make sure to await the parsing of the JSON


        return data; // You might return this if needed elsewhere
    } catch (error) {
        console.error("Error in API fetchingProductionKPI:", error.message);
    }
}
function getClusterByCode(code) {
    const cluster = allHiearchyData.find(cluster => cluster.clusterCode === code);
    updateMapWithVilles(cluster);updateMapWithVillesWithoutProduction();
}



function addVilleLayer(ville) {
    if (map.getLayer(ville.ville)) {
        console.warn('Layer already exists:', ville.ville);
        return;
    }
    map.addLayer({
        'id': ville.ville,
        'type': 'fill',
        'source': {
            'type': 'geojson',
            'data': {
                'type': 'Feature',
                'geometry': ville.polygon
            }
        },
        'layout': {},
        'paint': {
            'fill-color': '#088',
            'fill-opacity': 0.5
        }
    });
}

let dataByCPVProduction;

async function fetchingProductionKPI() {
    const url = `https://api.nomadcloud.fr/api/objectifs-clusters-productions?mois=${month}&annee=${year}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json(); // Make sure to await the parsing of the JSON
        dataByCPVProduction = data.find(item => item.cpv === cpv);

        // You can continue processing here or call another function
        // getClustersWithoutSales(); // Uncomment if needed

        return data; // You might return this if needed elsewhere
    } catch (error) {
        console.error("Error in API fetchingProductionKPI:", error.message);
    }
}

function getClustersWithoutSales() {
    if (dataByCPVProduction && HierarchyData) {
        console.log(' getClustersWithoutSales Data is available');
    } else {
        console.log(' getClustersWithoutSales Data is not available');
        return;
    }

    const ClustersWithoutSales = dataByCPVProduction.clusters.filter(cluster => {
        return cluster.objectifCluster.pourcentageVente === '0%';
    });

    const ClustersWithout = ClustersWithoutSales.map(cluster => {
        const match = HierarchyData.find(hierarchy => hierarchy.clusterCode === cluster.codeCluster);
        return match;
    }).filter(cluster => cluster !== undefined);

}


function applyBorderColors() {
    const clusters = document.querySelectorAll('.ClusterSpan');

    clusters.forEach(cluster => {
        const clusterCode = cluster.getAttribute('data-CLusterCode');
        const dataCluster = dataByCPVProduction.clusters.find(c => c.codeCluster === clusterCode);
        if (dataCluster && dataCluster.objectifCluster && dataCluster.objectifCluster.pourcentageVente) {
            const pourcentageVente = parseFloat(dataCluster.objectifCluster.pourcentageVente.replace('%', ''));
            let borderColor = '';

            if (pourcentageVente >= 100) {
                borderColor = 'green';
            } else if (pourcentageVente >= 80) {
                borderColor = 'yellow';
            } else if (pourcentageVente >= 51) {
                borderColor = 'orange';
            } else if (pourcentageVente <= 49) {
                borderColor = 'red';
            }

            if (borderColor) {
                cluster.style.borderRight = `3px solid ${borderColor}`; // Apply the border color

            }
        }
    });
}
document.addEventListener("DOMContentLoaded", () => {
    const dropdownIcon = document.querySelector(".icon-dropdown");
    const dropdownContent = document.querySelector(".content-dropdown");

    // Show/Hide the menu and change the icon on click
    dropdownIcon.addEventListener("click", () => {
        const isDisplayed = dropdownContent.style.display === "block";
        dropdownContent.style.display = isDisplayed ? "none" : "block";

        // Toggle icon between "chevron-down" and "x-lg"
        if (isDisplayed) {
            dropdownIcon.classList.remove("bi-x-lg");
            dropdownIcon.classList.add("bi-chevron-down");
        } else {
            dropdownIcon.classList.remove("bi-chevron-down");
            dropdownIcon.classList.add("bi-x-lg");
        }
    });

    // Handle custom navigation on link click
    const dropdownLinks = document.querySelectorAll(".content-dropdown a");
    dropdownLinks.forEach(link => {
        link.addEventListener("click", (event) => {
            event.preventDefault();  // Prevent default anchor behavior
            const path = link.getAttribute("data-path");
            window.location.href = path;  // Manually navigate to the path
        });
    });
});
let Interventionsmigrable;

async function fetchInterventionsMigrable(clusterCode) {
    const url = `https://api.nomadcloud.fr/api/interventions-places-hierarchy-migrable/${cpv}?codeCluster=${clusterCode}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`, // Ensure jwtToken is defined and valid
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        Interventionsmigrable = await response.json();
        displayTP(Interventionsmigrable.taux_penetration);
        return Interventionsmigrable;
    } catch (error) {
        console.error('Error fetching interventions migrable:', error);
        return null;
    }
}
function displayTP(data){
    console.log('displayTP',data);
    document.querySelector('.TauxPenetration').textContent =data ;
}

async function displayInterventionsMigrable(Code) {
    let dataToDisplay;
    let data = Interventionsmigrable;
    const codeNumeric = parseInt(Code, 10);

    dataToDisplay = data.villes.find(v => v.cod_insee === codeNumeric);
    updateDisplayValues(dataToDisplay);
    displayTP(dataToDisplay.taux_penetration);
}

function updateDisplayValues(data) {

    const keys = ['nb_fyr_fttb', 'nb_fyr_adsl', 'nb_fyr_mob_mono', 'nb_fyr_mob_multi_thd', 'nb_fyr_mob_multi_adsl', 'nb_fyr_thd'];
    keys.forEach(key => {
        const element = document.querySelector(`.${key}`);
        if (element) {
            element.textContent = data && data.hasOwnProperty(key) ? data[key] : '-';
        }
    });
}

/******handling map clicking */
let MissingCities;
async function fetchMissingcities(clusterCode) {
    const url = `https://api.nomadcloud.fr/api/productions-missing-cities/${pointOfSaleId}/${clusterCode}/${year}/${month}?optionSelect=${Type}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        MissingCities = await response.json();

        getClusterByCode(clusterCode);
    } catch (error) {
        console.error('Error fetching interventions migrable:', error);
        return null;
    }
}

function updateMapWithVilles(data) {

    if (!data.villes || !Array.isArray(data.villes)) {
        console.error("Invalid data: missing 'villes'");
        return;
    }

    const sourceId = 'villes-production';
    const layerIdFill = 'villes-production-fill';
    const layerIdOutline = 'villes-production-outline';

    const geojsonData = {
        type: "FeatureCollection",
        features: data.villes.map(ville => ({
            type: "Feature",
            id: ville.code_insee,
            properties: {
                ville: ville.code_insee,
                villeName: ville.ville
            },
            geometry: {
                type: "Polygon",
                coordinates: [ville.polygon.coordinates[0]]
            }
        }))
    };

    if (map.getSource(sourceId)) {
        map.getSource(sourceId).setData(geojsonData);
    } else {
        setupNewSourceAndLayers(sourceId, geojsonData, '#088'); // Assuming setupNewSourceAndLayers abstracts layer setup
    }
    updateBoundsAndEvents(sourceId, 'villes-production-fill');
}

function updateMapWithVillesWithoutProduction() {

    if (!MissingCities || !MissingCities.villes || !Array.isArray(MissingCities.villes)) {
        console.error("Invalid data: MissingCities is undefined or missing 'villes'");
        return;
    }

    const sourceId = 'villes-missing';
    const layerIdFill = 'villes-missing-fill';

    const geojsonData = createGeoJSONData(MissingCities);

    if (map.getSource(sourceId)) {
        map.getSource(sourceId).setData(geojsonData);
    } else {
        setupNewSourceAndLayers(sourceId, geojsonData, '#ffb5b5'); // Color for missing production
    }
    updateBoundsAndEvents(sourceId, 'villes-missing-fill');
}

function updateBoundsAndEvents(sourceId, layerIdFill) {
    const bounds = new mapboxgl.LngLatBounds();
    const source = map.getSource(sourceId);

    if (source && source._data && source._data.features) {
        source._data.features.forEach(feature => {
            feature.geometry.coordinates.forEach(polygon => {
                polygon.forEach(coord => {
                    if (coord && coord.length >= 2) {
                        bounds.extend(coord);
                    } else {
                        console.error('Invalid coordinate:', coord);
                    }
                });
            });
        });

        try {
            map.fitBounds(bounds, { padding: 20, maxZoom: 9 });
        } catch (error) {
            console.error('Error fitting bounds:', error);
        }

        map.on('click', layerIdFill, function(e) {
            if (e.features.length > 0) {
                const feature = e.features[0];
            }
        });
    } else {
        console.error('Source data is not loaded or is invalid:', sourceId);
    }
}


function createGeoJSONData(data) {
    if (!data || !Array.isArray(data.villes)) {
        console.error('Data is undefined or not correctly structured:', data);
        return null; // Return null or an empty geoJSON structure if data is not available
    }

    return {
        type: "FeatureCollection",
        features: data.villes.map(ville => {
            if (!ville.polygon || !Array.isArray(ville.polygon.coordinates) || ville.polygon.coordinates.length === 0) {
                console.error('Malformed or missing coordinates for ville:', ville.ville);
                return null;
            }
            return {
                type: "Feature",
                id: ville.code_insee,
                properties: {
                    ville: ville.code_insee,
                    villeName: ville.ville
                },
                geometry: {
                    type: "Polygon",
                    coordinates: ville.polygon.coordinates
                }
            };
        }).filter(feature => feature !== null)
    };
}

function setupNewSourceAndLayers(sourceId, geojsonData, fillColor) {
    if (!map.getSource(sourceId)) {
        map.addSource(sourceId, {
            type: "geojson",
            data: geojsonData
        });
    }

    if (!map.getLayer(`${sourceId}-fill`)) {
        map.addLayer({
            id: `${sourceId}-fill`,
            type: "fill",
            source: sourceId,
            layout: {},
            paint: {
                'fill-color': fillColor,
                'fill-opacity': 0.5
            }
        });
    }

    if (!map.getLayer(`${sourceId}-outline`)) {
        map.addLayer({
            id: `${sourceId}-outline`,
            type: "line",
            source: sourceId,
            layout: {},
            paint: {
                'line-color': [
                    'case',
                    ['boolean', ['feature-state', 'selected'], false],
                    '#55C5D0', // Color when selected
                    'transparent' // Default (non-selected) color
                ],
                'line-width': [
                    'case',
                    ['boolean', ['feature-state', 'selected'], false],
                    3, // Width when selected
                    0  // Default (non-selected) width
                ]
            }
        });
    }

    setupClickHandling(sourceId, `${sourceId}-fill`);
}

function setupClickHandling(sourceId, layerIdFill) {
    map.on('click', layerIdFill, function(e) {
        if (e.features.length > 0) {
            const feature = e.features[0];

            resetAllSelections();

            map.setFeatureState(
                { source: sourceId, id: feature.id },
                { selected: true }
            );

            const coordinates = feature.geometry.coordinates[0];
            const villeCode = feature.properties.ville;
            const villeName = feature.properties.villeName;
            console.log('Call infoCardByPolygon 1841');
            infoCardByPolygon(villeCode, villeName, coordinates);
        }
    });
}

function resetAllSelections() {
    // Reset states for all sources that might have selected features
    ['villes-production', 'villes-missing'].forEach(source => {
        const sourceData = map.getSource(source);
        if (sourceData) {
            sourceData._data.features.forEach(feature => {
                map.setFeatureState(
                    { source, id: feature.id },
                    { selected: false }
                );
            });
        }
    });
}
function simulateClickByVilleCode(villeCode) {

    const sourceId = 'villes-production';
    const layerIdFill = `${sourceId}-fill`;

    const source = map.getSource(sourceId);
    if (!source) {
        console.error('Source not found:', sourceId);
        return;
    }

    const feature = source._data.features.find(f => f.properties.ville === villeCode);
    if (!feature) {
        console.error('Ville not found with code:', villeCode);
        return;
    }

    resetAllSelections();

    map.setFeatureState(
        { source: sourceId, id: feature.id },
        { selected: true }
    );

    const coordinates = feature.geometry.coordinates[0];
    const villeName = feature.properties.villeName;
    console.log('Call infoCardByPolygon 1887');

    infoCardByPolygon(villeCode, villeName, coordinates);
}
function simulateClickByClusterCode(ClusterCode) {
    removeAllVilles();
    findClusterDataForUser(ClusterCode);
}
/******end handling map clicking */


function afficherGraphique(graphId, label, xLabel, data, color) {
    const canvas = document.getElementById(graphId);
    const ctx = canvas.getContext('2d');

    if (canvas.chartInstance) {
        canvas.chartInstance.destroy();
    }

    // 🏷️ Définition des labels pour les mois et les jours
    const labelsMois = ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'];
    const labelsJours = ['S','D','L', 'M', 'M', 'J', 'V',  ]; // Corrigé : Commence par "L"

    // 📊 Déterminer le type de graphique et adapter les labels
    let labels = Object.keys(data);
    
    if (graphId.includes('mois')) {
        labels = labels.map(num => labelsMois[parseInt(num) - 1] || num);
    } else if (graphId.includes('semaine')) {
        labels = labels.map(num => labelsJours[parseInt(num) % 7] || num);
    }
    const newChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,       // Labels formatés
            datasets: [{
                label: label,
                data: Object.values(data),
                borderColor: color,
                borderWidth: 2,
                fill: true,
                tension: 0.2,
                pointRadius: 0,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    grid: { display: false },
                    title: { display: true, text: xLabel, color: '#909090' }
                },
                y: {
                    title: { display: true, text: label, color: '#909090' },
                    grid: { color: 'rgba(255,255,255,0.1)' },
                    beginAtZero: true
                }
            },
            plugins: {
                legend: { display: false },
                tooltip: { enabled: true, intersect: false }
            }
        }
    });

    canvas.chartInstance = newChart;
}

/*************************************** renderEffectifs */
function createTreeElementEffectifs (user, level = 0) {
    const nodeElement = document.createElement('div');
    nodeElement.style.paddingLeft = `${level * 20}px`;
    nodeElement.style.cursor = 'pointer';
    nodeElement.textContent = `${user.prenom} ${user.nom}`;
    nodeElement.setAttribute('data-id', user.id);


    nodeElement.onclick = function(event) {
        event.stopPropagation();
        const childContainer = nodeElement.nextSibling;
        if (childContainer.style.display === 'none') {
            childContainer.style.display = 'block';
        } else {
            childContainer.style.display = 'none';
        }
    };

    const childrenContainer = document.createElement('div');
    childrenContainer.style.display = 'none';
    user.children.forEach(child => {
        childrenContainer.appendChild(createTreeElementEffectifs(child, level + 1));
    });

    const elementContainer = document.createElement('div');
    elementContainer.appendChild(nodeElement);
    elementContainer.appendChild(childrenContainer);

    return elementContainer;
}

async function findClusterDataForUser(codecluster, codeInsee) {

    // fetchUserHiarchy(codecluster, codeInsee);  // Assuming fetchUserHierarchy doesn't need to be awaited or its result used here.
    // var sidebar = document.querySelector('.right-sidebar-concepteur');
    // var sidebarStyle = window.getComputedStyle(sidebar);
    // var IconRightPanelToggle = document.querySelector('.IconRightPanelToggle');

    // if (sidebarStyle.right !== '0px') {
    //     sidebar.style.right = '0px';
    //     IconRightPanelToggle.style.transform = 'rotate(90deg)';
    // }

    // //var data = await fetchtotalClusterUser(codecluster, codeInsee);
    // if (!data) {  // This will be true if data is undefined, null, or empty array
    //     return;  // Exit function if no data
    // }

    // try {
    //     var totalPrisesDistribuer = sumTotalPrises(data);
    //     var totalPrisesByVille = await getTotalPriseVenteByVille(codecluster, codeInsee);

    //     var distribuerPercentage = (totalPrisesDistribuer / totalPrisesByVille.total_prises) * 100;
    //     var remainingPercentage = 100 - distribuerPercentage;

    //     var chartData = [
    //         {
    //             categorie: 'Attribuer',
    //             totalVentes: distribuerPercentage,
    //             label: `Attribuer (${totalPrisesDistribuer})`
    //         },
    //         {
    //             categorie: 'Prises',
    //             totalVentes: remainingPercentage,
    //             label: `Prises (${totalPrisesByVille.total_prises})`
    //         }
    //     ];

    //     ChartPriseTotalDistrubuer(chartData);
    //     const container = document.getElementById('MiseEnpageCard');
    //     container.innerHTML = ''; // Clear previous content

    //     if (!Array.isArray(data) || data.length === 0) {
    //         console.warn("No data found.");
    //         container.textContent = 'Aucune donnée trouvée.';
    //         return;
    //     }

    //     container.innerHTML = generateHierarchyHtmlForUsers(data, false);
    // } catch (error) {
    //     console.error('Error fetching or rendering data:', error);
    // }
}

async function findClusterDataForUserCluster(codecluster, codeInsee) {
    //fetchUserHiarchy(codecluster);
    var sidebar = document.querySelector('.right-sidebar-concepteur');
    var sidebarStyle = window.getComputedStyle(sidebar);
    var IconRightPanelToggle=document.querySelector('.IconRightPanelToggle');
    if (sidebarStyle.right !== '0px'){
        sidebar.style.right = '0px';
        IconRightPanelToggle.style.transform = 'rotate(90deg)';
    }
    try {
        var data = await fetchtotalClusterUser(codecluster, codeInsee);
        var totalPrisesDistrubuer = sumTotalPrises(data);
        var totalPrisesByVille = await getTotalPriseVenteByVille(codecluster, codeInsee);
        var distribuerPercentage = (totalPrisesDistrubuer / totalPrisesByVille[0].total_prises) * 100;
        var remainingPercentage = 100 - distribuerPercentage;

        var chartData = [
            {
                categorie: 'Attribuer',
                totalVentes: distribuerPercentage, // This will be the percentage of the total
                label: `Attribuer (${totalPrisesDistrubuer})`
            },
            {
                categorie: 'Prises ',
                totalVentes: remainingPercentage, // Remaining percentage
                label: `Prises (${totalPrisesByVille[0].total_prises})`
            }
        ];

        ChartPriseTotalDistrubuer(chartData);
        const container = document.getElementById('MiseEnpageCard');
        container.innerHTML = ''; // Clear previous content

        if (!Array.isArray(data) || data.length === 0) {
            console.warn("No data found.");
            container.textContent = 'Aucune donnée trouvée.';
            return;
        }

        container.innerHTML = generateHierarchyHtmlForUsers(data,false);
    } catch (error) {
        console.error('Error fetching or rendering data:', error);
    }
}
function sumTotalPrises(data) {
    let total = 0;

    data.forEach(entry => {
        if (Array.isArray(entry.voies)) {
            total += entry.voies.reduce((sum, voie) => sum + parseInt(voie.total_prises, 10), 0);
        }

        if (Array.isArray(entry.children) && entry.children.length > 0) {
            total += sumTotalPrises(entry.children);
        }
    });

    return total;
}

async function fetchtotalClusterUser(codecluster, codeInsee) {
    //document.getElementById('MiseEnpageCard').innerHTML = '';
    try {
        let url = `https://api.nomadcloud.fr/api/interventions-places-total-by-user/${cpv}/${codecluster}?page=1`;
        if (codeInsee) {
            url += `&codeInsee=${codeInsee}`;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 404) {
                // Handle 404 specifically
                const responseData = await response.json();  // Parse the JSON to check the message
                if (responseData.error && responseData.error === "Aucun résultat trouvé pour les critères donnés") {
                    // If the specific error message is found, exit quietly
                    document.getElementById('MiseEnpageCard').innerHTML = '';  // Optionally update the UI silently
                    return;  // Exit without logging
                }
                // If another error message or not the specific one, you could log or handle differently here
            }
            return;  // Exit for all other non-OK responses if you decide not to handle them specifically
        }

        const data = await response.json();
        return data;  // Continue with normal processing if response is OK
    } catch (error) {
        document.getElementById('MiseEnpageCard').innerHTML = '';  // Handle network or parsing errors quietly
        return;  // Exit function quietly on catch
    }
}





async function getTotalPriseVenteByVille(codeCluster,codeInsee){
    const baseURL= `https://api.nomadcloud.fr/api/interventions-places-hierarchy/${cpv}?codeCluster=${codeCluster}&page=1`;
    let url = new URL(baseURL);
    if (codeInsee) {
        url.searchParams.set('codeInsee', codeInsee);
    }
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();

        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        return;
    }
}

function FindClustersByEtat() {
    const icon = document.getElementById('FilterByEtatCluster');
    const modal = document.getElementById('modalByEtatCluster');

    icon.addEventListener('click', function () {
        modal.style.display = modal.style.display === 'flex' ? 'none' : 'flex';

        if (modal.style.display === 'flex') {
            const iconRect = icon.getBoundingClientRect();
            modal.style.top = `${iconRect.top + window.scrollY}px`;
            modal.style.left = `${iconRect.right + 20}px`;
        }
    });

    window.addEventListener('click', function (event) {
        if (!icon.contains(event.target) && !modal.contains(event.target)) {
            modal.style.display = 'none';
        }
    });

    modal.addEventListener('click', function (event) {
        if (event.target.classList.contains('FilterClusterByEtat')) {
            let categoryData = event.target.getAttribute('data');
            let dataClusters = HierarchyData[0][Number(year)][Number(month)]; // Define here
            let filteredData;

            if (categoryData === "CategoryAll") {
                filteredData = dataClusters;
            } else {
                const ClustersFiltered = filterClustersByCategory(dataClusters, dataByCPVProduction.clusters, categoryData);
                filteredData = getDataForFilteredClusters(dataClusters, ClustersFiltered);
            }

            const htmlContent = generateHierarchyHtml(filteredData);
            document.getElementById('tree-root').innerHTML = htmlContent;

            setupCaretListeners('tree-root');
            setupSearchFunctionality();
            setupDropdownListeners();
            applyBorderColors();
        }
    });
}

async function fetchingProductionKPI() {
    const url = `https://api.nomadcloud.fr/api/objectifs-clusters-productions?mois=${month}&annee=${year}&page=1`;
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json(); // Make sure to await the parsing of the JSON
        dataByCPVProduction = data.find(item => item.cpv === cpv);

        // You can continue processing here or call another function
        // getClustersWithoutSales(); // Uncomment if needed

        return data; // You might return this if needed elsewhere
    } catch (error) {
        console.error("Error in API fetchingProductionKPI:", error.message);
    }
}

function filterClustersByCategory(dataClusters, clusters, categoryData) {
    console.log("Filtering clusters by category:", categoryData);

    if (categoryData === "CategoryHorsZone") {
        const existingClusterCodes = clusters.map(cluster => cluster.codeCluster);
        
        return dataClusters.filter(cluster => !existingClusterCodes.includes(cluster.clusterCode))
                           .map(cluster => cluster.clusterCode);
    }

    const { min, max } = getThresholdBounds(categoryData);
    if (min === 0 && max === 0) {
        return [];
    }

    return clusters.filter(cluster => {
        const percentage = parseFloat(cluster.objectifCluster.pourcentageVente.replace('%', ''));
        return percentage >= min && percentage < max;
    }).map(cluster => cluster.codeCluster);
}

function getThresholdBounds(categoryData) {
    const thresholds = {
        "Category100": { min: 100, max: Infinity },
        "Category80": { min: 80, max: 100 },
        "Category51": { min: 51, max: 80 },
        "Category49": { min: 0, max: 49 }
    };

    return thresholds[categoryData] || { min: 0, max: 0 };
}

function getDataForFilteredClusters(allClusters, filteredClusterCodes) {
    return allClusters.filter(cluster => filteredClusterCodes.includes(cluster.clusterCode));
}

document.addEventListener("DOMContentLoaded", () => {
    localStorage.removeItem('clusterForOnedays');
    localStorage.removeItem('codeinseeForOnedays');
})

function infoCardOpneAi() {
    const card = document.getElementById("info-card-OpenAi");
    card.style.display = "block";

    let isDragging = false;
    let offsetX = 0, offsetY = 0;

    card.style.position = "absolute";

    card.addEventListener("mousedown", (e) => {
        if (e.button !== 0) return;
        isDragging = true;
        offsetX = e.clientX - card.offsetLeft;
        offsetY = e.clientY - card.offsetTop;
        card.style.transition = "none";
    });

    document.addEventListener("mousemove", (e) => {
        if (!isDragging) return;
        card.style.left = `${e.clientX - offsetX}px`;
        card.style.top = `${e.clientY - offsetY}px`;
    });

    document.addEventListener("mouseup", () => {
        isDragging = false;
        card.style.transition = "left 0.1s ease-in-out, top 0.1s ease-in-out";
    });
}


const closePanelButtones = document.getElementById('closePanel');
const productionPaneles = document.getElementById('displayproductionPanel');


closePanelButtones.addEventListener('click', function () {
    productionPaneles.style.bottom = '-100%';
});








// function updateMapWithAllData(allData) {
//     if (!Array.isArray(allData) || allData.length === 0) {
//         console.error("Invalid data: allData should be an array with elements");
//         return;
//     }

//     allData.forEach(cluster => {
//         if (!cluster.villes || !Array.isArray(cluster.villes)) {
//             console.error(`Invalid data: missing 'villes' for cluster ${cluster.libelleCluster}`);
//             return;
//         }

//         cluster.villes.forEach(ville => {
//             if (!ville.polygon || !Array.isArray(ville.polygon.coordinates) || ville.polygon.coordinates.length === 0 || !ville.polygon.coordinates[0]) {
//                 // Skip this ville if coordinates are not properly formatted
//                 return;
//             }

//             const sourceId = `villes-${cluster.cluster_id}-${ville.code_insee}`;
//             const layerIdFill = `${sourceId}-fill`;
//             const layerIdOutline = `${sourceId}-outline`;

//             const geojsonData = {
//                 type: "FeatureCollection",
//                 features: [{
//                     type: "Feature",
//                     id: ville.code_insee,
//                     properties: {
//                         ville: ville.code_insee,
//                         villeName: ville.ville
//                     },
//                     geometry: {
//                         type: "Polygon",
//                         coordinates: [ville.polygon.coordinates[0]]  // Ensure correct structure
//                     }
//                 }]
//             };

//             if (!map.getSource(sourceId)) {
//                 map.addSource(sourceId, {
//                     type: "geojson",
//                     data: geojsonData
//                 });
//             } else {
//                 map.getSource(sourceId).setData(geojsonData);
//             }

//             if (!map.getLayer(layerIdFill)) {
//                 map.addLayer({
//                     id: layerIdFill,
//                     type: "fill",
//                     source: sourceId,
//                     layout: {},
//                     paint: {
//                         'fill-color': '#54C5d0',  // Adjust color as needed
//                         'fill-opacity': 0.5
//                     }
//                 });

//                 // Bind click event to the layer to handle clicking on villes
//                 map.on('click', layerIdFill, function(e) {
//                     if (e.features.length > 0) {
//                         const villeCode = e.features[0].properties.ville;
//                         TestVilleCode(villeCode, cluster.cluster_id); // Pass cluster ID as well
//                     }
//                 });
//             }

//             if (!map.getLayer(layerIdOutline)) {
//                 map.addLayer({
//                     id: layerIdOutline,
//                     type: "line",
//                     source: sourceId,
//                     layout: {},
//                     paint: {
//                         'line-color': [
//                             'case',
//                             ['boolean', ['feature-state', 'selected'], false],
//                             '#55C5D0', // Color when selected
//                             'transparent' // Default (non-selected) color
//                         ],
//                         'line-width': [
//                             'case',
//                             ['boolean', ['feature-state', 'selected'], false],
//                             3, // Width when selected
//                             0  // Default (non-selected) width
//                         ]
//                     }
//                 });
//             }
//         });
//     });
// }
function TestVilleCode(villeCode, clusterId) { // Accept clusterId as a parameter
    const sourceId = `villes-${clusterId}-${villeCode}`; // Use clusterId here
    const layerIdFill = `${sourceId}-fill`;

    const source = map.getSource(sourceId);
    if (!source) {
        console.error('Source not found:', sourceId);
        return;
    }

    const feature = source._data.features.find(f => f.properties.ville === villeCode);
    if (!feature) {
        console.error('Ville not found with code:', villeCode);
        return;
    }

    resetAllSelectionsVilles();

    map.setFeatureState(
        { source: sourceId, id: feature.id },
        { selected: true }
    );

    const coordinates = feature.geometry.coordinates[0];
    const villeName = feature.properties.villeName;
    console.log('Call infoCardByPolygon 2626');

    infoCardByPolygon(villeCode, villeName, coordinates);
}

function resetAllSelectionsVilles() {
    const sources = map.getStyle().sources;
    Object.keys(sources).forEach(sourceId => {
        if (sourceId.startsWith('villes-')) {
            const source = map.getSource(sourceId);
            if (source && source._data && source._data.features) {
                source._data.features.forEach(feature => {
                    map.setFeatureState(
                        { source: sourceId, id: feature.id },
                        { selected: false }
                    );
                });
            }
        }
    });
}





function removeAllVilles() {
    const mapSources = map.getStyle().sources;

    for (let sourceId in mapSources) {
        if (sourceId.startsWith('villes-')) {
            const layerIdFill = `${sourceId}-fill`;
            const layerIdOutline = `${sourceId}-outline`;

            if (map.getLayer(layerIdFill)) {
                map.removeLayer(layerIdFill);
            }
            if (map.getLayer(layerIdOutline)) {
                map.removeLayer(layerIdOutline);
            }

            map.removeSource(sourceId);
        }
    }
}




/***********Calender Handle */

function generateCalendar(month, year,currentMonthData,PrevMonthData) {
    const monthNames = ["January", "February", "March", "April", "May", "June",
                        "July", "August", "September", "October", "November", "December"];
    const calendarContainer = document.querySelector('.calendar');
    const calendarContainerActual = document.querySelector('.calendarActual');
    const  monthNameContainerPrev = document.querySelector('.MonthName.Prev');
    const monthNameContainerActual = document.querySelector('.MonthName.actual');
    calendarContainer.innerHTML = '';
    calendarContainerActual.innerHTML = '';
    const firstDayOfMonth = new Date(year, month - 1, 1);
    const startingDayOfWeek = firstDayOfMonth.getDay();
    monthNameContainerActual.innerHTML = monthNames[month - 1];
    monthNameContainerPrev.innerHTML = monthNames[month - 2];
    const offset = (startingDayOfWeek + 6) % 7;

    for (let i = 0; i < offset; i++) {
        const emptyDiv = document.createElement('div');
        emptyDiv.classList.add('day');
        calendarContainer.appendChild(emptyDiv);
        calendarContainerActual.appendChild(emptyDiv);
    }

    const daysInMonthPrev = new Date(year, month-1, 0).getDate();

    const firstDay = new Date(year, month - 1, 1);
    let offsetActual = (firstDay.getDay() + 6) % 7;

    let daysHtml = '';
    for (let i = 0; i < offsetActual; i++) {
        daysHtml += `<div class="day"></div>`;
    }


    for (let day = 1; day <= daysInMonthPrev; day++) {
        let dayValue = PrevMonthData[day] || 0;
        let displayValue = dayValue === 0 ? '' : dayValue;
        daysHtml += `<div class="day OpenContatPanel" >
            <span>${day}</span>
            <div class="DaysDetails">${displayValue}</div>
            </div>`;
    }


    calendarContainer.innerHTML += daysHtml;


    let daysPrevHtml = '';
    const firstDayPrev = new Date(year, month - 1, 1);
    let offsetPrev = (firstDayPrev.getDay() + 6) % 7;

    for (let i = 0; i < offsetPrev; i++) {
        daysPrevHtml += `<div class="day"></div>`;
    }

    const daysInMonth = new Date(year, month, 0).getDate();
    for (let day = 1; day <= daysInMonth; day++) {
        let dayValue = currentMonthData[day] || 0;
        let displayValue = dayValue === 0 ? '' : dayValue;
        daysPrevHtml += `<div class="day OpenContactPanel" >
            <span>${day}</span>
            <div class="DaysDetails">${displayValue}</div>
            </div>`;
    }

    calendarContainerActual.innerHTML = daysPrevHtml;

    document.querySelectorAll('.OpenContatPanel').forEach(element => {
    element.addEventListener('click', function() {
        var sidebar = document.querySelector('.PanelContrats');
        var sidebarStyle = window.getComputedStyle(sidebar);
        if (sidebarStyle.right === '0px') {
            sidebar.style.right = '-360px';
        } else {
            sidebar.style.right = '0px';
        }
    });
});

}
function generateCalendarIncident(month, year, currentMonthData) {
    const daysForActualMonth = new Date(year, month, 0).getDate();
    let daysProduction = '';

    const firstDay = new Date(year, month - 1, 1);
    let offset = (firstDay.getDay() + 6) % 7;

    for (let i = 0; i < offset; i++) {
        daysProduction += `<div class="DaysProductionTreeView"></div>`;
    }

    for (let day = 1; day <= daysForActualMonth; day++) {
        const date = new Date(year, month - 1, day);
        let dayValue = currentMonthData[day] || 0;
        let displayValue = dayValue === 0 ? '' : dayValue;

        daysProduction += `<div class="DaysProductionTreeView OpenContactPanel" onclick="DisplayincidentForOnedays('${day}','${month}', '${year}')">
            <span>${day}</span>
            <div class="DaysDetails" style="text-align: center; height: 22px;">${displayValue}</div>
        </div>`;
    }

    const calendarContainer = document.querySelector('.ActualMonthProductionsDays');
    calendarContainer.innerHTML = daysProduction;
}

async function productionsByDay(clusterCode= null, codeInsee = null) {
    var baseUrl = `https://api.nomadcloud.fr/api/incidents-tickets-by-day/${cpv}?mois=${month}&annee=${year}&page=1`;
    var url = new URL(baseUrl);
    if (clusterCode !== null) {
    url.searchParams.append('codeCluster', clusterCode);
    }
    if (codeInsee !== null) {
    url.searchParams.append('codeInsee', codeInsee);
    }

    try {
    const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${jwtToken}`,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    var currentMonthData = data.incidents_par_jour;
    var PrevMonthData = data["incidents_par_jour_mois-1"];
    const dayIndex = String(day-1);
    generateCalendarIncident(parseInt(month), parseInt(year),currentMonthData);
    //generateCalendar(parseInt(month), parseInt(year),currentMonthData,PrevMonthData);

    } catch ( error ) {
    console.error('Error:', error);
    }
}

async function DayDetails(clusterCode= null, codeInsee = null) {
    var baseUrl = `https://api.nomadcloud.fr/api/productions-for-one-day/${pointOfSaleId}?jour=${day}&page=1`;
    var url = new URL(baseUrl);
    if (clusterCode !== null) {
    url.searchParams.append('codeCluster', clusterCode);
    }
    if (codeInsee !== null) {
    url.searchParams.append('codeInsee', codeInsee);
    }

    try {
    const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${jwtToken}`,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    } catch ( error ) {
    console.error('Error:', error);
    }
}


// Main function to display incidents for a specific date
let DataDisplayincidentForOnedays;
let currentView;
async function DisplayincidentForOnedays(day, month, year) {
    
    const date = `${String(day).padStart(2, '0')}-${String(month).padStart(2, '0')}-${year}`;
    const codeClusters = localStorage.getItem('clusterForOnedays');
    const codeInsees = localStorage.getItem('codeinseeForOnedays');
    DataDisplayincidentForOnedays={
        day:date,
        codeClusters:codeClusters,
        codeInsees:codeInsees,
        month:month, year:year,day:day
    }
    // Hide search container and adjust panel position
    const searchContainer = document.querySelector('.SearchContainerINPanel');
    if (searchContainer) {
        searchContainer.style.display = 'none';
    }

    const productionPanel = document.getElementById('displayproductionPanel');
    if (productionPanel) {
        productionPanel.style.bottom = '0';
    }
    const sidebar = document.querySelector('.DetailsCAlenderdata');
    if (!sidebar) {
        console.error("Sidebar element not found.");
        return;
    }
    sidebar.innerHTML = '';

    // Create view toggle button
    const switchingBtn = document.createElement('button');
    switchingBtn.id = 'viewToggleButton';
    switchingBtn.className = 'btn btn-primary mb-3';
    switchingBtn.innerHTML = '<i class="bi bi-table IconTableIncident"></i>';

    currentView = 'tableau';

    switchingBtn.onclick = async () => {
        if (currentView === 'tableau') {
            currentView = 'cards';
            switchingBtn.innerHTML = '<i class="bi bi-card-list" style="margin-top: 2px;"></i>';
            const cardsContent = await fetchProductionsForOnedaysCardsPanel(day, month, year);
            const contentContainer = document.getElementById('viewContentContainer');
            if (contentContainer) {
                contentContainer.innerHTML = cardsContent;
            }
        } else {
            currentView = 'tableau';
            switchingBtn.innerHTML = '<i class="bi bi-table IconTableIncident"></i>';
            const tableContent = await fetchProductionsForOnedaysTableauPanel(date, codeClusters, codeInsees);
            const contentContainer = document.getElementById('viewContentContainer');
            if (contentContainer) {
                contentContainer.innerHTML = tableContent;
            }
        }
    };
    sidebar.appendChild(switchingBtn);
    const viewContentContainer = document.createElement('div');
    viewContentContainer.id = 'viewContentContainer';
    viewContentContainer.style.filter = 'blur(0px);';
    sidebar.appendChild(viewContentContainer);
    const initialTableContent = await fetchProductionsForOnedaysTableauPanel(date, codeClusters, codeInsees);
    viewContentContainer.innerHTML = initialTableContent;
}
async function fetchIncidentData(date, codeClusters, codeInsees) {
    try {
        let url = `https://api.nomadcloud.fr/api/incidents-tickets/${cpv}?dateDebut=${date}&dateFin=${date}&page=1`;
         const response = await fetch(url, {
           method: 'GET',
           headers: {
             'Authorization': `Bearer ${jwtToken}`,
             'Content-Type': 'application/json',
           },
         });
     
         if (!response.ok) {
           console.error('HTTP error!', response.status, response.statusText);
           throw new Error(`HTTP error! Status: ${response.status}`);
         }
     
         const data = await response.json();
         return data;
       } catch (error) {
         console.error('Fetch error:', error);
         throw error;
       }
}

async function fetchProductionsForOnedaysTableauPanel(date, codeClusters, codeInsees) {
    document.querySelector('.DetailsCAlenderContainer').style.marginTop = '15px';
    try {
        let tableHTML = `
            <table id="productionTable" class="productionTable">
                <thead>
                    <tr>
                        <th style="width: 10%;">Cluster</th>
                        <th style="width: 10%;">Code Insee</th>
                        <th style="width: 10%;">Ville</th>
                        <th style="width: 10%;">Date Création</th>
                        <th style="width: 10%;">Référence Commande</th>
                        <th style="width: 10%;">Explication</th>
                        <th style="width: 10%;">Statut</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody id="productionTableBody">
        `;

        const data = await fetchIncidentData(date, codeClusters, codeInsees);

        if (data && data.length > 0) {
            data.forEach(cluster => {
                if (cluster.villes && Array.isArray(cluster.villes)) {
                    cluster.villes.forEach(ville => {
                        if (ville.incidents && Array.isArray(ville.incidents)) {
                            ville.incidents.forEach(incident => {
                                const createdDate = new Date(incident.createdAt);
                                tableHTML += `
                                   <tr class="incident-row" data-incident-id="${incident.id}"
                                         onclick="showDetailsPopup(${incident.id})">
                                        <td>${cluster.libelleCluster }</td>
                                        <td>${ville.code_insee }</td>
                                        <td>${ville.ville }</td>
                                        <td>${createdDate.toLocaleDateString()} 
                                            ${createdDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</td>
                                        <td>${incident.refCmdRm }</td>
                                        <td>${incident.explication }</td>
                                        <td>${incident.statut }</td>
                                        <td></td>
                                    </tr>
                                `;
                            });
                        }
                    });
                }
            });
        } else {
            tableHTML += '<tr><td colspan="9">No data found for the selected date.</td></tr>';
        }

        tableHTML += `
                </tbody>
            </table>
        `;

        return tableHTML;
    } catch (error) {
        console.error('Error generating table view:', error);
        return '<p>Failed to load data. Please try again later.</p>';
    }
}




async function fetchProductionsForOnedaysCardsPanel(day, month, year) {
    document.querySelector('.DetailsCAlenderContainer').style.marginTop = '0px';
    try {
        const date = `${String(day).padStart(2, '0')}-${String(month).padStart(2, '0')}-${year}`;
        const codeClusters = localStorage.getItem('clusterForOnedays');
        const codeInsees = localStorage.getItem('codeinseeForOnedays');
        const incidentStates = await fetchAllEtatIncidents();
        const data = await fetchIncidentData(date, codeClusters, codeInsees);

        let cardsHTML = '<div class="container-fluid">';
        cardsHTML += '<div class="CardsContainer d-flex flex-row flex-nowrap">';

        let stateContainers = incidentStates.map(state => {
            let stateIncidentCount = 0;
            if (data && data.length > 0) {
                data.forEach(cluster => {
                    cluster.villes.forEach(ville => {
                        stateIncidentCount += ville.incidents.filter(incident => incident.idEtat === state.id).length;
                    });
                });
            }
            return {
                stateId: state.id,
                incidentCount: stateIncidentCount,
                cards: []
            };
        });
        console.log(incidentStates);
        if (data && data.length > 0) {
            data.forEach(cluster => {
                cluster.villes.forEach(ville => {
                    ville.incidents.forEach(incident => {
                        let container = stateContainers.find(c => c.stateId === incident.idEtat);
                        const stateColor = incidentStates.find(s => s.id === incident.idEtat)?.color || '#000';

                        if (container) {
                            container.cards.push(`
                                <div class="card mb-2" data-incident-id="${incident.id}" onclick="UpdateIncidentTicket('${incident.id}')" style="border-left: 3px solid ${stateColor};">
                                    <div class="card-header p-2">
                                        <h6 class="card-title mb-0">${cluster.libelleCluster} - ${ville.ville}</h6>
                                    </div>
                                    <div class="card-body p-2">
                                        <p class="card-text mb-1">Ref CMD: ${incident.refCmdRm}</p>
                                        <p class="card-text mb-1">${incident.explication || 'No explanation provided'}</p>
                                        <p class="card-text"><small>Created: ${new Date(incident.createdAt).toLocaleString()}</small></p>
                                    </div>
                                </div>
                            `);
                        }
                    });
                });
            });
        }

        stateContainers.forEach(container => {
            cardsHTML += `
                <div class="state-column">
                    <div class="StateCard CardWithAction" >
                        <span style="font-size: 14px;"><i class="bi bi-icon iconCardIncident" data-stat-id="${container.stateId}"></i>${incidentStates.find(s => s.id === container.stateId)?.titre || 'Unknown State'}</span>
                        <span class="totalventes" style="font-size: 20px; color: var(--coloredTextPrimary);">${container.incidentCount}</span>
                    </div>
                    <div class="d-flex flex-column gap-2">
                        ${container.cards.join('')}
                    </div>
                </div>
            `;
        });

        cardsHTML += '</div>';
        cardsHTML += '</div>';

        return cardsHTML;
    } catch (error) {
        console.error('Error generating cards view:', error);
        return '<p>Failed to load data. Please try again later.</p>';
    }
}



function showDetailsPopup(item) {
    const popup = document.getElementById("detailsPopup");
    const popupContent = document.getElementById("popupContent");

    // Remplir le popup avec les données de l'élément sélectionné
    popupContent.innerHTML = `
          <span class="formListItem">
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Numéro de Commande:</span> ${item.refCmdRm ?? ''}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Date de Commande:</span> 
        ${new Date(item.createdAt).toLocaleDateString()} ${new Date(item.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>explication:</span> ${item.explication ?? ''}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>statut:</span> ${item.statut ?? ''}
    </span>
    </span>

    `;

    popup.style.display = "block"; // Afficher le popup
}

function closePopup() {
    document.getElementById("detailsPopup").style.display = "none";
}

async function incidentForOnedays(date, codeCluster, codeInsee,FilterType=null) {

  try {
   let url = `https://api.nomadcloud.fr/api/incidents-tickets/${cpv}?dateDebut=${date}&dateFin=${date}&page=1`;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('HTTP error!', response.status, response.statusText);
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}

async function fetchAllEtatIncidents() {

    try {
     let url = `https://api.nomadcloud.fr/api/etat-incidents?page=1 `;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json',
        },
      });
  
      if (!response.ok) {
        console.error('HTTP error!', response.status, response.statusText);
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
  
      const data = await response.json();
      return data['hydra:member'];
    } catch (error) {
      console.error('Fetch error:', error);
      throw error;
    }
  }

/*********End Calender handle */


function openTopPanel(){
    const PanelTop = document.getElementById('PanelTop');
    var IcontoggleChart = document.querySelector('.IcontoggleTopPanel');
    var ClosingTopPanel = document.querySelector('.ClosingTopPanel');
    if (PanelTop.style.top === '0px') {
        PanelTop.style.top = '-128px';
        setTimeout(() => IcontoggleChart.style.transform = 'rotate(180deg)', 300);
        ClosingTopPanel.style.marginTop = '50px';
    } else {
        ClosingTopPanel.style.marginTop = '0px';
        setTimeout(() => PanelTop.style.top = '0px', 10);
        IcontoggleChart.style.transform = 'rotate(0deg)';
    }
}
/**********Incident Ticekt */
async function getIncidentTicketDetails(id) {
    try {
        const response = await fetch(`https://api.nomadcloud.fr/api/incidents-ticket/${id}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            console.error('HTTP error!', response.status, response.statusText);
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}
async function UpdateIncidentTicket(id) {
    const incidentStates = await fetchAllEtatIncidents();
    document.getElementById('detailsPopup').style.display = 'none';
    var incidentTicket = await getIncidentTicketDetails(id);
    const productionPanel = document.getElementById('PanelIncidentTicket');
    productionPanel.style.right = '0';
    document.getElementById('viewContentContainer').style.filter = 'blur(5px)';

    try {
        productionPanel.innerHTML = '';
        let tabsHeader = `
        <div class="" id="IncidentTicketClose"><i class="bi bi-x-square"></i></div>
        <div class="tab" style="background:transparent;">
            <button class="tablinks btnTabsIncidentTicket" onclick="openTabsIncidentTicket(event, 'Information')">Information</button>
            <button class="tablinks btnTabsIncidentTicket" onclick="openTabsIncidentTicket(event, 'Tab2')">Tab 2</button>
            <button class="tablinks btnTabsIncidentTicket" onclick="openTabsIncidentTicket(event, 'Tab3')">Tab 3</button>
            <button class="tablinks btnTabsIncidentTicket" onclick="openTabsIncidentTicket(event, 'Tab4')">Tab 4</button>
        </div>`;
        //console.log(incidentTicket.etat.id);
        const etatDropdown = `
            <div class="search-container SerachIncident">
                <select id="etatIncidentSelect" class="InputSearshTreeView" style="height: 32px;">
                    ${incidentStates.map(state => `
                        <option
                            value="${state.id}"
                            ${incidentTicket.etat.id === state.id ? 'selected' : ''}
                        >
                            ${state.titre}
                        </option>
                    `).join('')}
                </select>
                <span class="FormLabelIncident">État Incident</span>
            </div>
        `;
        let tabsContent = `
        <!-- Tab content -->
        <div id="Information" class="tabcontent">
            <form id="incidentForm" class="incidentForm">
                <div class="formListItemIncident">
                    
                    <div class="search-container SerachIncident">
                        <input class="InputSearshTreeView" id="nomUpdate" type="text" placeholder="Nom" style="height: 32px;" value="${incidentTicket.nomComM || ''}">
                        <span class="FormLabelIncident">Nom</span>
                    </div>
                    <div class="search-container SerachIncident">
                        <input class="InputSearshTreeView" id="prenomUpdate" type="text" placeholder="Prenom" style="height: 32px;" value="${incidentTicket.prenomComRm || ''}">
                        <span class="FormLabelIncident">Prenom</span>
                    </div>
                    <div class="search-container SerachIncident">
                        <input class="InputSearshTreeView" id="emailUpdate" type="text" placeholder="Email" style="height: 32px;" value="${incidentTicket.emailComRm || ''}">
                        <span class="FormLabelIncident">Email</span>
                    </div>
                    <div class="search-container SerachIncident">
                        <input class="InputSearshTreeView" id="telUpdate" type="text" placeholder="Numéro de Telephone" style="height: 32px;" value="${incidentTicket.telComRm || ''}">
                        <span class="FormLabelIncident">Numéro de Telephone</span>
                    </div>
                    <div class="search-container SerachIncident">
                        <input class="InputSearshTreeView" id="nomCltRmUpdate" type="text" placeholder="Nom Client RM" style="height: 32px;" value="${incidentTicket.nomCltRm || ''}">
                        <span class="FormLabelIncident">Nom Client RM</span>
                    </div>
                    <div class="search-container SerachIncident">
                        <input class="InputSearshTreeView" id="refCmdRmUpdate" type="text" placeholder="Référence commande" style="height: 32px;" value="${incidentTicket.refCmdRm || ''}">
                        <span class="FormLabelIncident">Référence Commande</span>
                    </div>
                    ${etatDropdown}
                    <button class="submitIncident submit-btn" type="button" id="submitIncidentTicketUpdate">Modifier</button>
                </div>
            </form>
        </div>

        <div id="Tab2" class="tabcontent">
            <h3>Tab 2</h3>
            <p>Tab 2 content goes here.</p>
        </div>

        <div id="Tab3" class="tabcontent">
            <h3>Tab 3</h3>
            <p>Tab 3 content goes here.</p>
        </div>

        <div id="Tab4" class="tabcontent">
            <h3>Tab 4</h3>
            <p>Tab 4 content goes here.</p>
        </div>`;

        productionPanel.innerHTML = tabsHeader + tabsContent;
        document.getElementById('submitIncidentTicketUpdate').addEventListener('click', function() {
            sendIncidentRequestUpdate(id);
        });

        document.getElementById('IncidentTicketClose').addEventListener('click', function() {
            const productionPanel = document.getElementById('PanelIncidentTicket');
            productionPanel.style.right = '-100%';
            document.getElementById('viewContentContainer').style.filter = 'blur(0px)';
        });
        document.querySelector('.tablinks').click();
    } catch (error) {
        console.error('Error updating incident ticket:', error);
    }
}

function openTabsIncidentTicket(evt, tabName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    document.getElementById(tabName).style.display = "block";
    evt.currentTarget.className += " active";
}
async function sendIncidentRequestUpdate(id) {
    const data = {
        nomComM: document.getElementById('nomUpdate').value,
        prenomComRm: document.getElementById('prenomUpdate').value,
        emailComRm: document.getElementById('emailUpdate').value,
        telComRm: document.getElementById('telUpdate').value,
        nomCltRm: document.getElementById('nomUpdate').value,
        refCmdRm: document.getElementById('refCmdRmUpdate').value,
        etat: "/api/etat-incidents/"+ document.getElementById('etatIncidentSelect').value
    };



    try {
        const response = await fetch(`https://api.nomadcloud.fr/api/incidents-tickets/${id}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            throw new Error('Failed to submit incident');
        }

        const result = await response.json();
        console.log('Incident submitted successfully:', result);
        document.getElementById('PanelIncidentTicket').style.right = '-100%';
        document.querySelector('.PanelIncidentTicket').innerHTML = '';
        document.getElementById('viewContentContainer').style.filter = 'blur(0px);';document.getElementById('viewContentContainer').style.filter = '';
        let successModal = new bootstrap.Modal(document.getElementById('statusSuccessModal'));
        successModal.show();
        document.getElementById('viewToggleButton').innerHTML='<i class="bi bi-card-list"></i>';
        console.log(DataDisplayincidentForOnedays);
        const cardsContent = await fetchProductionsForOnedaysCardsPanel(DataDisplayincidentForOnedays.day, DataDisplayincidentForOnedays.month, DataDisplayincidentForOnedays.year);
        const contentContainer = document.getElementById('viewContentContainer');
        if (contentContainer) {
            contentContainer.innerHTML = cardsContent;
        }
    } catch (error) {
        console.error('Error submitting incident:', error);
        let errorModal = new bootstrap.Modal(document.getElementById('statusErrorsModal'));
        errorModal.show();
    }
}
async function NewIncidentTicket(){
    const productionPanel = document.getElementById('displayproductionPanel');
       productionPanel.style.bottom = '0';
       document.querySelector('.DetailsCAlenderContainer').style.marginTop = '15px';
       try {
           const sidebar = document.querySelector('.DetailsCAlenderdata');
           if (!sidebar) {
               console.error("Sidebar element not found.");
               return;
           }
           sidebar.innerHTML = '';
           let CreateForm=`
            <form id="incidentForm" class="incidentForm">
              <div class="formListItemIncident">
                <div class="search-container SerachIncident" >
                    <input class="InputSearshTreeView"id="nomComrr"type="text" placeholder="Nom"style="    height: 32px;">
                </div>
                <div class="search-container SerachIncident" >
                    <input class="InputSearshTreeView"id="prenomComrr"type="text" placeholder="Prenom" style="    height: 32px;">
                </div>
                <div class="search-container SerachIncident" >
                    <input class="InputSearshTreeView"id="emailComrr"type="text" placeholder="Email"style="    height: 32px;">
                </div>
                <div class="search-container SerachIncident" >
                    <input class="InputSearshTreeView" id="telComrr"type="text" placeholder="Numéro de Telephone"style="    height: 32px;">
                </div>
                <div class="search-container SerachIncident" >
                    <input class="InputSearshTreeView"id="nomCltRm" type="text" placeholder="nomCltRm"style="    height: 32px;">
                </div>
                <div class="search-container SerachIncident" >
                    <input class="InputSearshTreeView"id="refCmdRm" type="text" placeholder="referance Cmd"style="    height: 32px;">
                </div>
                <button class="submitIncident submit-btn" type="button" id="submitIncident">Creer</button>
              </div>
            </form>
           `;
           sidebar.innerHTML = CreateForm;
           document.getElementById('submitIncident').addEventListener('click', sendIncidentRequest);
        } catch (error) {
            console.error('Error create incident:', error);
        }
}
async function sendIncidentRequest() {
    const data = {
        cpv: cpv,
        nomComM: document.getElementById('nomComrr').value,
        prenomComRm: document.getElementById('prenomComrr').value,
        emailComRm: document.getElementById('emailComrr').value,
        telComRm: document.getElementById('telComrr').value,
        nomCltRm: document.getElementById('nomCltRm').value,
        refCmdRm: document.getElementById('refCmdRm').value,
        etat: "/api/etat-incidents/3"
    };
    
    const codeCluster = localStorage.getItem('clusterCode');
    const codeInsee = localStorage.getItem('codInsee');
    if (codeCluster !== null) {
        data.codeCluster = codeCluster;
    }
    if (codeInsee !== null) {
        data.codeInsee = codeInsee;
    }
    console.log(localStorage.getItem('clusterCode'));
    console.log(localStorage.getItem('codInsee'));
    console.log(data);

    try {
        const response = await fetch('https://api.nomadcloud.fr/api/incidents-tickets', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            throw new Error('Failed to submit incident');
        }

        const result = await response.json();
        console.log('Incident submitted successfully:', result);

        // Hide the production panel
        document.getElementById('displayproductionPanel').style.bottom = '-100%';
        document.querySelector('.DetailsCAlenderdata').innerHTML = '';
        sendEmailWithAttachment(JSON.stringify(data));
        // Show success modal
        let successModal = new bootstrap.Modal(document.getElementById('statusSuccessModal'));
        successModal.show();

    } catch (error) {
        console.error('Error submitting incident:', error);

        // Show error modal
        let errorModal = new bootstrap.Modal(document.getElementById('statusErrorsModal'));
        errorModal.show();
    }
}

async function sendEmailWithAttachment(content) {
    console.log('Sending email with content:', content);
    const emailData = {
        from: "<EMAIL>",
        to: "<EMAIL>",
        subject: "New Incident Created",
        content: content
    };

    try {
        const response = await fetch('https://api.nomadcloud.fr/api/send-email', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(emailData)
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Email sent successfully:', result);
            alert('Email sent successfully!');
        } else {
            throw new Error(`Failed to send email: ${response.status} ${response.statusText}`);
        }
    } catch (error) {
        console.error('Error sending email:', error);
        alert('Failed to send email. Please check the details and try again.');
    }
}

/**********End Incident Ticekt */


function OpenBigPAnel(){
    const productionPanel = document.getElementById('displayproductionPanel');
        productionPanel.style.bottom = '0';
        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = '';
    }
/****************incident verbatim */
async function FetchIncidentVerbatim(date, codeCluster, codeInsee){

    try {
       let url = `https://api.nomadcloud.fr/api/incidents-verbatim/${cpv}?page=1`;
       const response = await fetch(url, {
         method: 'GET',
         headers: {
           'Authorization': `Bearer ${jwtToken}`,
           'Content-Type': 'application/json',
         },
       });
   
       if (!response.ok) {
         console.error('HTTP error!', response.status, response.statusText);
         throw new Error(`HTTP error! Status: ${response.status}`);
       }
   
       const data = await response.json();
   
       return data;
     } catch (error) {
       console.error('Fetch error:', error);
       throw error;
     }
}

async function DisplayIncidentVerbatim() {
    const productionPanel = document.getElementById('displayproductionPanel');
    productionPanel.style.bottom = '0';
    try {
        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = '';
        let table = document.getElementById("productionTable");
        if (!table) {
            sidebar.innerHTML = `
                <table id="productionTable" class="productionTable">
                    <thead>
                        <tr>
                            <th style="width: 7%;">Titre</th>
                            <th style="width:20%;">Proposition</th>
                            <th style="width: 25%;">Tips</th>
                            <th style="width:25%;">PropositionBo</th>
                            <th style=""></th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody"></tbody>
                </table>
            `;
            table = document.getElementById("productionTable");
        }
        const tbody = document.getElementById("productionTableBody");
        tbody.innerHTML = '';
        const data = await FetchIncidentVerbatim();
        updateTableIncidentVerbatim(data);
    } catch (error) {
        console.error('Error fetching production data:', error);
        document.querySelector('.DetailsCAlenderdata').innerHTML = '<p>Failed to load data.</p>';
    }
}

function updateTableIncidentVerbatim(data) {
    const tbody = document.getElementById("productionTableBody");
    tbody.innerHTML = '';

    if (data && data.length > 0) {
        data.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${checkValue(item.title)}</td>
                <td>${checkValue(item.proposition)}</td>
                <td>${checkValue(item.tips)}</td>
                <td>${checkValue(item.propositionBo)}</td>
                <td data-incident-id="${item.id}" data-title="${item.title}" 
                    data-proposition="${item.proposition}" data-tips="${item.tips}" 
                    data-proposition-bo="${item.propositionBo}">
                    <i class="bi bi-pencil-square UpdateIncidentIcon"></i>
                </td>
            `;
            tbody.appendChild(row);
        });

        document.querySelectorAll(".UpdateIncidentIcon").forEach(icon => {
            icon.addEventListener("click", function () {
                const parentTd = this.parentElement;
                const incidentData = {
                    id: parentTd.getAttribute("data-incident-id"),
                    title: parentTd.getAttribute("data-title"),
                    proposition: parentTd.getAttribute("data-proposition"),
                    tips: parentTd.getAttribute("data-tips"),
                    propositionBo: parentTd.getAttribute("data-proposition-bo"),
                };

                UpdateIncidentVervatim(incidentData);
            });
        });
    } else {
        tbody.innerHTML = '<tr><td colspan="6">No data found for the selected date.</td></tr>';
    }
}

function UpdateIncidentVervatim(incidentData) {
    const sidebar = document.querySelector('.DetailsCAlenderdata');
    try{sidebar.innerHTML = `
        <form id="incidentForm" class="incidentForm">
            <div class="formListItemIncident" style="gap: 12px;">
                <span class="FormLabelIncident">Titre</span>
                <div class="search-container" style="height: auto;min-height: 35px;">
                    <input class="InputSearshTreeView" id="Titre" type="text" placeholder="Titre" style="margin-left:10px; height: 32px;" value="${incidentData.title}">
                </div>
                <span class="FormLabelIncident">Proposition</span>
                <div class="search-container " style="    height: auto;min-height: 35px;">
                    <input class="InputSearshTreeView" id="Proposition" type="text" placeholder="Proposition" style="margin-left:10px; height: 32px;" value="${incidentData.proposition}">
                </div>
                <span class="FormLabelIncident">Tips</span>
                <div class="search-container" style="height: auto; min-height: 50px;">
                    <textarea class="InputSearshTreeView" id="Tips" placeholder="Titre" 
                        style=" resize: none; overflow-wrap: break-word; word-break: break-word;"
                        oninput="adjustHeight(this)">${incidentData.tips}</textarea>
                </div>
                <span class="FormLabelIncident">PropositionBo</span>
                <div class="search-container" style="height: auto; min-height: 50px;">
                    <textarea class="InputSearshTreeView" id="PropositionBo" placeholder="Titre" 
                        style="resize: none; overflow-wrap: break-word; word-break: break-word;"
                        oninput="adjustHeight(this)">${incidentData.propositionBo}</textarea>
                </div>
                <button class="submitIncident submit-btn" type="button" id="UpdateIncidentVerbatim">Update</button>
            </div>
        </form>
    `;

    document.getElementById('UpdateIncidentVerbatim').addEventListener('click', function () {
        sendIncidentVerbatimUpdate(incidentData.id);
    });
    } catch (error) {
        console.error('Error create incident:', error);
    }
}
function adjustHeight(element) {
    element.style.height = "auto";
    element.style.height = (element.scrollHeight) + "px";
}

document.addEventListener("DOMContentLoaded", () => {
    document.querySelectorAll(".InputSearshTreeView").forEach(el => adjustHeight(el));
});
function checkValue(value) {
    return value === "(non défini)" ? "" : value;
}

async function sendIncidentVerbatimUpdate(incidentId){
    var incidentData = {
        title: document.getElementById("Titre").value,
        proposition: document.getElementById("Proposition").value,
        tips: document.getElementById("Tips").value,
        propositionBo: document.getElementById("PropositionBo").value,
    };

    try {
        const response = await fetch(`https://api.nomadcloud.fr/api/incidents-verbatim/${incidentId}`, {
            method: "PATCH",
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                "Content-Type": "application/merge-patch+json"
            },
            body: JSON.stringify(incidentData)
        });

        if (!response.ok) {
            throw new Error(`Error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log("Incident updated successfully", result);
        document.getElementById('displayproductionPanel').style.bottom = '-100%';
        document.querySelector('.DetailsCAlenderdata').innerHTML = '';
        let successModal = new bootstrap.Modal(document.getElementById('statusSuccessModal'));
        successModal.show();
    } catch (error) {
        console.error('Error submitting incident:', error);
        let errorModal = new bootstrap.Modal(document.getElementById('statusErrorsModal'));
        errorModal.show();
    }

}

/****************end incident verbatim */

document.querySelector('.close-btnInitialDropdown').addEventListener('click', function (event) {
    event.stopPropagation();
    document.getElementById('initialDropdown').style.display = 'none';
});