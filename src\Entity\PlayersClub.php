<?php

namespace App\Entity;

use App\Repository\PlayersClubRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PlayersClubRepository::class)]
class PlayersClub
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $clubId = null;

    #[ORM\Column(nullable: true)]
    private ?array $players = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClubId(): ?string
    {
        return $this->clubId;
    }

    public function setClubId(?string $clubId): static
    {
        $this->clubId = $clubId;

        return $this;
    }

    public function getPlayers(): ?array
    {
        return $this->players;
    }

    public function setPlayers(?array $players): static
    {
        $this->players = $players;

        return $this;
    }
}
