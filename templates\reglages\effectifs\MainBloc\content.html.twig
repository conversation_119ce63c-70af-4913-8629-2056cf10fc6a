<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('styles/MainBloc.css') }}">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jquery-treegrid@0.3.0/css/jquery.treegrid.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-table@1.23.5/dist/bootstrap-table.min.css">


<style>
	.table-container .table {
		margin-left: -9px;
	}

	.bar.table th {
		border-bottom: 0 solid var(--border-color);
		border-top: -5.8px solid var(--border-color);
	}

	.table th,
	.table td {
		background-color: transparent;
	}

	.table >:not(:first-child) {
		border-top: none;
	}

	.bars,
	.bars .table td,
	.bars .table tr,
	.bars .table th {
		border: none;
	}

	.table td,
	.table th {
		border: none;
	}

	.table,
	.table td {
		border: none;
	}
	@media screen and(min-width: 1400px) {}

	@media screen and(max-width: 1366px) and(min-width: 400px) {
		.table-container {
			width: 100%;
			border-collapse: collapse; /* Avoid space between cells */
		}

		.table-container th,
		.table-container td {
			padding: 8px 10px;
			text-align: left;
			vertical-align: middle;
		}

		.table-container th:nth-child(1),
		.table-container td:nth-child(1) {
			width: 10%;
			text-align: center;
		}

		.table-container th:nth-child(2),
		.table-container td:nth-child(2) {
			width: 15%;
		}

		.table-container th:nth-child(3),
		.table-container td:nth-child(3) {
			width: 15%;
		}

		.table-container th:nth-child(4),
		.table-container td:nth-child(4) {
			width: 40%;
		}

		.table-container th:nth-child(5),
		.table-container td:nth-child(5) {
			width: 20%;
		}

		/* Alignment for child and subchild tables */
		.table.treegrid,
		.table.treegrid .bars,
		.table.treegrid .bar {
			width: 100%;
			table-layout: fixed; /* Maintain column widths */
		}

		.table.treegrid td,
		.table.treegrid th {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.arrow {
			margin-right: 5px;
			display: inline-block;
			vertical-align: middle;
		}
	}

	.borderparent {
		border-right: 2px solid #234abb;
	}
	.bgparent {
		background: linear-gradient(to right,transparent, #234abb);
	}
	.table, .table td {
    border: none;
    margin-bottom: auto;
}
</style>
<style>
    /* Add a specific background when the row is expanded */
    .name-cell.expanded {
        background: linear-gradient(to right, rgba(35, 74, 187, 0.3) 50px, transparent 30%);
    }

    .children-row .name-cell.expanded {
        background: linear-gradient(to right, rgba(123, 31, 162, 0.3) 50px, transparent 30%);
    }

    .subchildren-row .name-cell.expanded {
        background: linear-gradient(to right, rgba(138, 14, 79, 0.3) 50px, transparent 30%);
    }

/* Styles pour le panneau latéral */
.info-panel {
    position: fixed;
    top: 20%;
    right: -350px; /* Commence en dehors de l'écran */
    width: 70%;
	height: 100%;
    background-color: #1E1F22;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
    padding: 20px;
    overflow-y: auto;
    transition: right 0.3s ease; /* Animation pour un effet glissant */
    z-index: 1000;



	overflow-y: auto; /* Enable scrolling when content overflows */
	max-height: 100%; /* Limit the height, adjust as needed */
}
.info-panel.open {
    right: 0; /* S'affiche */
}

.info-panel.hidden {
    display: none; /* Optionnel pour masquer initialement */
}

.info-panel button {
    padding: 10px 15px;
    background-color: #2B2D31;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    justify-content: flex-end; /* Alignement à droite */

}

.info-panel h3 span {
    color: #fff; /* Change la couleur du texte en blanc */
}
/* Conteneur du header */
.panel-header {
    display: flex;
    align-items: center; /* Aligner verticalement */
    justify-content: space-between; /* Espacement entre le bouton et le titre */
    margin-bottom: 15px; /* Ajouter un espace sous le header */
}

/* Bouton de fermeture */
.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #7b1fa2;
    cursor: pointer;
    display: flex;
    align-items: center; /* Centrer l'icône dans le bouton */
}

.close-btn:hover {
    color: #5a0e8c;
}

/* Titre */
.panel-header h3 {
    margin: 0;
    font-size: 1.5rem;
    
}
#userForm table {
    width: 50%;

}

#userForm td {
    padding: 8px;
   border-top:none;
border-bottom:none;
}

#userForm label {
    font-weight: bold;
}

#userForm input {
   width: 100%;
    padding: 5px;
 
	background-color: #444;
	color: #fff;
	border-radius: 8px;
	border-color: #444;
}

</style>
<div class="table-container">
	<nav class="navbar table-navbar navbar-expand-lg">
		<ul class="navbar-nav me-auto mb-1 mb-lg-0">
			<li class="nav-item" id="tablenav">
				<a class="nav-link active" href="#">All</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unfulfilled</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unpaid</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Open</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Closed</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Local Delivery</a>
			</li>
		</ul>
		<div class="navbar-nav">
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-search"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-sliders"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-filter"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-arrow-down-up"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-three-dots"></i>
			</a>
		</div>
	</nav>

	<div class="container mt-2">
		<div class="row mb-3 align-items-center">
			<div class="col-10 col-md-2">
				<div class="custom-select-container">
					<i class="bi bi-person-circle icon"></i>
					<select class="form-select">
						<option>Customer</option>
					</select>
				</div>
			</div>
			<div class="col-10 col-md-2">
				<div class="custom-select-container">
					<i class="bi bi-plus-circle-dotted icon"></i>
					<select class="form-select">
						<option>Payment</option>
					</select>
				</div>
			</div>
			<div class="col-10 col-md-2">
				<div class="custom-select-container">
					<i class="bi bi-credit-card icon"></i>
					<select class="form-select">
						<option>Payment Method</option>
					</select>
				</div>
			</div>
			<div id="addfilter" class="col-12 col-md-2 d-flex align-items-center justify-content-center">
				<span>+ Add filter</span>
			</div>
		</div>
	</div>

	<table class="table treegrid">
		<thead>
			<tr>
				<th style="width:2%; ">
					<label class="custom-checkbox">
						<input type="checkbox" class="form-check-input" id="customCheck">
					</label>
				</th>
				<th style="width:15%;" scope="col">Nom</th>
				<th style="width:15%;" scope="col">Prénom</th>
				<th style="width:14.8%;" scope="col">Email</th>
				<th scope="col">Rôle</th>
			</tr>
		</thead>
		<tbody>
			{% for effectif in effectifs %}
				<tr class="parent" data-id="{{ effectif.id }}" style="cursor: pointer;" aria-expanded="false">
					<td class="checkbox-cell">
						<input type="checkbox" class="form-check-input custom-check">
					</td>
					<td class="name-cell">
						<span class="arrow">
							<i class="bi bi-chevron-right"></i>
						</span>
						{{ effectif.nom ?? 'N/A' }}
					</td>
					<td>{{ effectif.prenom ?? 'N/A' }}</td>
					<td>{{ effectif.email ?? 'N/A' }}</td>
					<td>{{ effectif.roleApp.name ?? 'N/A' }}</td>
				</tr>

    {% if effectif.children is not empty %}
				<!-- Child rows for level 1 -->
				<tr class="children-row" id="children-{{ effectif.id }}" style="display:none; padding: 0;">
					<td colspan="5">
						<table class="table treegrid bars">
							<tbody>
								{% for child in effectif.children %}
									<tr class="child" data-id="{{ child.id }}">
										<td style=" width: 2%; padding-right: 15px; border-right: 2px solid #7b1fa2;">
											<input type="checkbox" class="form-check-input custom-check">
										</td>
										<td style="width:15%;background: linear-gradient(to right, rgba(123, 31, 162, 0.3) 50px, transparent 30%);">
											<span class="arrow">
												<i class="bi bi-chevron-right"></i>
											</span>
											{{ child.nom ?? 'N/A' }}
										</td>
										<td style="width:15.3%;">{{ child.prenom ?? 'N/A' }}</td>
										<td style="width:15.5%;">{{ child.email ?? 'N/A' }}</td>
										<td>{{ child.roleApp.name ?? 'N/A' }}</td>
									</tr>

									<!-- Sub-child rows for level 2 -->
									    {% if child.children is not empty %}
									<tr class="subchildren-row" id="subchildren-{{ child.id }}" style="display:none;">
										<td colspan="5">
											<table class="table treegrid bar">
												<tbody>
													{% for subchild in child.children %}
														<tr class="subchild" data-id="{{ subchild.id }}">
															<td style="width:3.2%; border-right: 2px solid #880e4f;">
																<input type="checkbox" class="form-check-input custom-check">
															</td>
															<td style="width:14.2%; ">
																{{ subchild.nom ?? 'N/A' }}</td>
															<td style="width:15.4%;">{{ subchild.prenom ?? 'N/A' }}</td>
															<td style="width:15.7%;">{{ subchild.email ?? 'N/A' }}</td>
															<td>{{ subchild.roleApp.name ?? 'N/A' }}</td>
														</tr>
													{% endfor %}
												</tbody>
											</table>
										</td>
									</tr>
									       {% endif %}
								{% endfor %}
							</tbody>
						</table>
					</td>
				</tr>
				  {% endif %}
			{% endfor %}
		</tbody>
	</table>
<!-- Panneau latéral -->
<div id="infoPanel" class="info-panel hidden" style="color: #fff;" >
    <div class="panel-header">
        <h3>Détails de l'utilisateur</h3>
        <button onclick="closePanel()" class="close-btn">
            <i class="bi bi-x-circle"></i>
        </button>
    </div>
    <form id="userForm" onsubmit="saveChanges(event)" >
        <table border="none">
            <tr>
                <td style="width: 30%;"><label for="panel-nom">Nom:</label></td>
                <td><input type="text" id="panel-nom" name="nom" /></td>
            </tr>
            <tr>
                <td><label for="panel-prenom">Prénom:</label></td>
                <td><input type="text" id="panel-prenom" name="prenom" /></td>
            </tr>
            <tr>
                <td><label for="panel-email">Email:</label></td>
                <td><input type="email" id="panel-email" name="email" /></td>
            </tr>
            <tr>
                <td><label for="panel-role">Rôle:</label></td>
                <td><input type="text" id="panel-role" name="role" /></td>
            </tr>
            <tr>
                <td><label for="panel-parent-nom">Parent Nom:</label></td>
                <td><input type="text" id="panel-parent-nom" name="parent_nom" /></td>
            </tr>
            <tr>
                <td><label for="panel-parent-email">Parent Email:</label></td>
                <td><input type="email" id="panel-parent-email" name="parent_email" /></td>
            </tr>


            <tr>
                <td><label for="point-of-sale-name">Point of Sale Name:</label></td>
                <td><input type="text" id="point-of-sale-name" name="point-of-sale-name" /></td>
            </tr>

            <tr>
                <td><label for="point-of-sale-code">Point of Sale Code:</label></td>
                <td><input type="text" id="point-of-sale-code" name="point-of-sale-code"/></td>
            </tr>

            <tr>
                <td><label for="panel-origin-id">Origin ID:</label></td>
                <td><input type="text" id="panel-origin-id" name="origin_id" /></td>
            </tr>
            <tr>
                <td><label for="panel-parent-origin-id">Parent Origin ID:</label></td>
                <td><input type="text" id="panel-parent-origin-id" name="parent_origin_id" /></td>
            </tr>
            <tr>
                <td><label for="panel-productions">Productions:</label></td>
                <td><input type="text" id="panel-productions" name="productions" /></td>
            </tr>
        </table>
        <button type="submit">Save Changes</button>
    </form>
</div>


<script>
document.addEventListener("DOMContentLoaded", function () {
    const rows = document.querySelectorAll("table.treegrid tbody tr");

    rows.forEach(row => {
        row.addEventListener("click", function () {
            // Déselectionner les autres lignes
            rows.forEach(r => r.classList.remove("selected"));

            // Sélectionner la ligne cliquée
            this.classList.add("selected");

            const userId = this.getAttribute("data-id"); // Récupérer l'ID de l'utilisateur

            if (userId) {
                // Afficher un indicateur de chargement
                const panel = document.getElementById("infoPanel");
                panel.classList.add("loading");

                fetch(`/reglages/effectifs/${userId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Remplir le formulaire avec les données de l'utilisateur
                    document.getElementById("panel-nom").value = data.nom ?? "";
                    document.getElementById("panel-prenom").value = data.prenom ?? "";
                    document.getElementById("panel-email").value = data.email ?? "";
                    document.getElementById("panel-role").value = data.roleApp?.name ?? "";
                    document.getElementById("panel-parent-nom").value = data.parent?.nom ?? "";
                    document.getElementById("panel-parent-email").value = data.parent?.email ?? "";

                    // Vérification de pointOfSale
                    const pointOfSale = data?.pointOfSale ?? "";
                 
                        document.getElementById("point-of-sale-name").value = pointOfSale.name ?? "N/A";
                        document.getElementById("point-of-sale-code").value = pointOfSale.code ?? "N/A";
                 

                    document.getElementById("panel-origin-id").value = data.originId ?? "";
                    document.getElementById("panel-parent-origin-id").value = data.parentOriginId ?? "";
                    document.getElementById("panel-productions").value = data.productions ?? "";

                    // Afficher le panneau et retirer l'indicateur de chargement
                    panel.classList.remove("loading");
                    panel.classList.remove("hidden");
                    panel.classList.add("open");
                })
                .catch(error => {
                    console.error('Error fetching user data:', error);
                    
                });
            }
        });
    });
});

function closePanel() {
    const panel = document.getElementById("infoPanel");
    panel.classList.remove("open");
    panel.classList.add("hidden");

    // Optionnel : Réinitialiser les champs
    const fields = panel.querySelectorAll("input, span");
    fields.forEach(field => {
        if (field.tagName === "INPUT") field.value = "";
        else field.textContent = "";
    });
}

function saveChanges(event) {
    event.preventDefault();

    // Vérifier s'il y a une ligne sélectionnée
    const selectedRow = document.querySelector("table.treegrid tbody tr.selected");


    const userId = selectedRow.getAttribute("data-id"); // Récupérer l'ID de l'utilisateur sélectionné

    const userData = {
        nom: document.getElementById("panel-nom").value,
        prenom: document.getElementById("panel-prenom").value,
        email: document.getElementById("panel-email").value,
        role: document.getElementById("panel-role").value,
        parent_nom: document.getElementById("panel-parent-nom").value,
        parent_email: document.getElementById("panel-parent-email").value,
        point_of_sale_name: document.getElementById("point-of-sale-name").value,
        point_of_sale_code: document.getElementById("point-of-sale-code").value,
        origin_id: document.getElementById("panel-origin-id").value,
        parent_origin_id: document.getElementById("panel-parent-origin-id").value,
        productions: document.getElementById("panel-productions").value
    };

    // Appeler l'API pour mettre à jour les données de l'utilisateur
    fetch(`/reglages/effectifs/${userId}`, {
        method: 'PATCH', // Utiliser PATCH pour la mise à jour partielle
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error("Failed to update user data.");
        }
    })
    .then(updatedData => {
        closePanel();
        console.log('User data updated:', updatedData);
      
    })
    .catch(error => {
        console.error('Error saving user data:', error);
   
    });
}


</script>

</div>

<script>
    document.querySelectorAll('.parent').forEach(item => {
        item.addEventListener('click', function () {
            const nameCell = this.querySelector('.name-cell');
            
            // Toggle the gradient background on the .name-cell
            if (nameCell.style.background) {
                nameCell.style.background = ''; // Remove the background if already set
            } else {
                nameCell.style.background = 'linear-gradient(to right, rgba(35, 74, 187, 0.3) 50px, transparent 30%)'; // Add the gradient
            }

            // Optionally, toggle the border-right if you still want this effect as well
            this.querySelector('td').style.borderRight = this.querySelector('td').style.borderRight ? '' : '2px solid #234abb';
        });
    });
</script>
<script>
    // Add functionality to expand/collapse child rows for each level
    document.querySelectorAll('.parent').forEach(row => {
        row.addEventListener('click', function () {
            const targetId = this.dataset.id;
            const targetRow = document.getElementById('children-' + targetId);
            const arrow = this.querySelector('.arrow i');
            targetRow.style.display = targetRow.style.display === 'none' ? '' : 'none';
            arrow.classList.toggle('bi-chevron-right');
            arrow.classList.toggle('bi-chevron-down');
            this.setAttribute('aria-expanded', targetRow.style.display !== 'none');

            // Toggle background only when row is expanded
            const nameCell = this.querySelector('.name-cell');
            if (targetRow.style.display !== 'none') {
                nameCell.classList.add('expanded'); // Add class when expanded
            } else {
                nameCell.classList.remove('expanded'); // Remove class when collapsed
            }
        });
    });

    // Add functionality to expand/collapse subchildren rows
    document.querySelectorAll('.child').forEach(row => {
        row.addEventListener('click', function () {
            const targetId = this.dataset.id;
            const targetRow = document.getElementById('subchildren-' + targetId);
            const arrow = this.querySelector('.arrow i');
            targetRow.style.display = targetRow.style.display === 'none' ? '' : 'none';
            arrow.classList.toggle('bi-chevron-right');
            arrow.classList.toggle('bi-chevron-down');

            // Toggle background only when row is expanded
            const nameCell = this.querySelector('.name-cell');
            if (targetRow.style.display !== 'none') {
                nameCell.classList.add('expanded'); // Add class when expanded
            } else {
                nameCell.classList.remove('expanded'); // Remove class when collapsed
            }
        });
    });
</script>


</div>
