<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">

<style>
	.channel-list {
		list-style-type: none;
		padding: 0;
		margin: 0;
	}

	.channel-item a {
		text-decoration: none;
		color: gray;
	}

	.channel-item a:hover,
	.channel-item a.active {
		color: darkgray;
	}

	.sidebar-link {
		font-size: 32px;
		width: 210px;
		display: block;
		padding: 8px 8px 8px 24px;
		border-radius: 10px;
		transition: background-color 0.3s;
		color: #fff;
		text-decoration: none;
	}

	.sidebar-link:hover {
		background-color: #3d3f46;
	}

	.sidebar-link.active {
		background-color: #3d3f46;
		color: #4b96c1;
	}

	.sectionDividers {
		color: #222;
		margin-left: 120px;

	}

.bi-search::before{
	margin-left: 75px;
}

	.sidebar-header {
		text-align: center;

	}

	

	

	.sidebar-section-titleactive {
		color: #fff;
		font-size: 14.2px;
		text-transform: uppercase;
		margin: 2px 0;
	}
	.sidebar-section-title {
		color: gray;
		font-size: 14.2px;
		text-transform: uppercase;
		margin: 0;


	}
	.toggle-section-sideb2 {
    background-color: #2B2D31;

    border-radius: 5px;
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 50%;
    color: #fff; /* Couleur de l'icône */
    font-size: 16px;
}

.search-bar {
  
    padding: 4px 8px 4px 20px; /* Espace pour l'icône à gauche */

    border-radius: 5px;
    background-color: #111;
    color: #fff; /* Couleur du texte */
}

.search-bar::placeholder {
    color: #aaa; /* Couleur du placeholder */
	border-color: transparent;
}

</style>


<div class="sidebar-header" id="eventTrigger">
	<i class="bi bi-calendar-event" style="color: transparent"></i>
</div>

<div class="sectionDividers">
<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        
        <input type="text" placeholder="Rechercher" class="search-bar">
		<i class="bi bi-search search-icon"></i>
    </div>
</div>


	<div class="sidebar-section">
		<div class="toggle-section-sideb2">
			<a href="#" class="sidebar-link ">
				<p class="sidebar-section-title">parameter utilisateur</p>
			</a>
		</div>
	</div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="{{ path('app_profile_compte') }}" 
           class="sidebar-link {% if app.request.pathinfo == path('app_profile_compte') %}active{% endif %}">
            <p class="{% if app.request.pathinfo == path('app_profile_compte') %}sidebar-section-titleactive{% else %}sidebar-section-title{% endif %}">
                Mon compte
            </p>
        </a>
    </div>
</div>

<div class="sidebar-section">
    <div class="toggle-section-sideb2">
        <a href="{{ path('app_profile') }}" 
           class="sidebar-link {% if app.request.pathinfo == path('app_profile') %}active{% endif %}">
            <p class="{% if app.request.pathinfo == path('app_profile') %}sidebar-section-titleactive{% else %}sidebar-section-title{% endif %}">
                Profils
            </p>
        </a>
    </div>
</div>

	<div class="sidebar-section">
		<div class="toggle-section-sideb2">
			<a href="#" class="sidebar-link">
				<p class="sidebar-section-title">Confidentialite</p>
			</a>
		</div>
	</div>
	<div class="sidebar-section">
		<div class="toggle-section-sideb2">
			<a href="#" class="sidebar-link">
				<p class="sidebar-section-title">integration</p>
			</a>
		</div>
	</div>
</div>
<script>
	const modal = document.getElementById("eventModal");
const trigger = document.getElementById("eventTrigger");
const closeBtn = document.querySelector(".close-btn");

trigger.addEventListener("click", function () {
modal.style.display = "block";
});

closeBtn.addEventListener("click", function () {
modal.style.display = "none";
});

window.addEventListener("click", function (event) {
if (event.target == modal) {
modal.style.display = "none";
}
});
</script>
