<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ClubDataService
{
    private $projectDir;
    
    public function __construct(ParameterBagInterface $params)
    {
        $this->projectDir = $params->get('kernel.project_dir');
    }
    
    /**
     * Récupère les clubs d'une compétition depuis les fichiers JSON locaux
     */
    public function getClubsByCompetition(string $competitionId): ?array
    {
        // Mapping complet de toutes les compétitions vers leurs fichiers JSON
        $clubFiles = [
            // UEFA - Compétitions Internationales
            'UEFA Champions League' => 'UEFA-champions-league-clubs.json',
            'UEFA Europa League' => 'UEFA-europa-league-clubs.json',
            'UEFA Europa Conference League' => 'UEFA-conference-league-clubs.json',

            // UEFA - Championnats Nationaux
            'GB1' => 'premier-league-clubs.json',
            'ES1' => 'laliga-clubs.json',
            'IT1' => 'serie-A.json',
            'L1' => 'bundesliga.json',
            'FR1' => 'Ligue1-clubs.json',
            'PO1' => 'Primeira-Liga.json',
            'TR1' => 'super-lig-clubs.json',
            'RU1' => 'russian-premier-league-clubs.json',
            'FR2' => 'ligue2-clubs.json',
            'LUX1' => 'bgl-ligue-clubs.json',

            // CAF - Afrique
            'TUN1' => 'tunisia-ligue1-clubs.json',
            'ALG1' => 'algeria-ligue1-clubs.json',

            // CONMEBOL - Amérique du Sud
            'BR1' => 'brasileirao-clubs.json',
            'AR1' => 'liga-argentina-clubs.json',

            // AFC - Asie
            'JP1' => 'j1-league-clubs.json',
        ];
        
        if (!isset($clubFiles[$competitionId])) {
            return null;
        }
        
        $filePath = $this->projectDir . '/public/Data/clubs/' . $clubFiles[$competitionId];
        
        if (!file_exists($filePath)) {
            return null;
        }
        
        try {
            $fileContent = file_get_contents($filePath);
            $data = json_decode($fileContent, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }
            
            return $data;
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Vérifie si une compétition a des données de clubs disponibles
     */
    public function hasClubsData(string $competitionId): bool
    {
        return $this->getClubsByCompetition($competitionId) !== null;
    }
    
    /**
     * Retourne la liste des compétitions supportées
     */
    public function getSupportedCompetitions(): array
    {
        return [
            // UEFA - Compétitions Internationales
            'UEFA Champions League' => 'UEFA Champions League',
            'UEFA Europa League' => 'UEFA Europa League',
            'UEFA Europa Conference League' => 'UEFA Europa Conference League',

            // UEFA - Championnats Nationaux
            'GB1' => 'Premier League',
            'ES1' => 'LaLiga',
            'IT1' => 'Serie A',
            'L1' => 'Bundesliga',
            'FR1' => 'Ligue 1',
            'PO1' => 'Liga Portugal',
            'TR1' => 'Süper Lig',
            'RU1' => 'Russian Premier League',
            'FR2' => 'Ligue 2',
            'LUX1' => 'BGL Ligue',

            // CAF - Afrique
            'TUN1' => 'Ligue Professionnelle 1 (Tunisia)',
            'ALG1' => 'Ligue Professionnelle 1 (Algeria)',

            // CONMEBOL - Amérique du Sud
            'BR1' => 'Brasileirão',
            'AR1' => 'Liga Argentina',

            // AFC - Asie
            'JP1' => 'J1 League',
        ];
    }

    /**
     * Retourne toutes les compétitions disponibles avec leurs fichiers
     */
    public function getAllCompetitionsWithFiles(): array
    {
        $clubFiles = [
            'UEFA Champions League' => 'UEFA-champions-league-clubs.json',
            'UEFA Europa League' => 'UEFA-europa-league-clubs.json',
            'UEFA Europa Conference League' => 'UEFA-conference-league-clubs.json',
            'GB1' => 'premier-league-clubs.json',
            'ES1' => 'laliga-clubs.json',
            'IT1' => 'serie-A.json',
            'L1' => 'bundesliga.json',
            'FR1' => 'Ligue1-clubs.json',
            'PO1' => 'Primeira-Liga.json',
            'TR1' => 'super-lig-clubs.json',
            'RU1' => 'russian-premier-league-clubs.json',
            'FR2' => 'ligue2-clubs.json',
            'LUX1' => 'bgl-ligue-clubs.json',
            'TUN1' => 'tunisia-ligue1-clubs.json',
            'ALG1' => 'algeria-ligue1-clubs.json',
            'BR1' => 'brasileirao-clubs.json',
            'AR1' => 'liga-argentina-clubs.json',
            'JP1' => 'j1-league-clubs.json',
        ];

        $result = [];
        foreach ($clubFiles as $competitionId => $fileName) {
            $filePath = $this->projectDir . '/public/Data/clubs/' . $fileName;
            $result[$competitionId] = [
                'name' => $this->getSupportedCompetitions()[$competitionId] ?? $competitionId,
                'file' => $fileName,
                'available' => file_exists($filePath)
            ];
        }

        return $result;
    }
}
