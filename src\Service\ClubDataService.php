<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ClubDataService
{
    private $projectDir;
    
    public function __construct(ParameterBagInterface $params)
    {
        $this->projectDir = $params->get('kernel.project_dir');
    }
    
    /**
     * Récupère les clubs d'une compétition depuis les fichiers JSON locaux
     */
    public function getClubsByCompetition(string $competitionId): ?array
    {
        // Pour l'instant, on ne gère que la Premier League
        $clubFiles = [
            'GB1' => 'premier-league-clubs.json',
            // On peut ajouter d'autres compétitions plus tard
            // 'ES1' => 'laliga-clubs.json',
            // 'IT1' => 'serie-A.json',
        ];
        
        if (!isset($clubFiles[$competitionId])) {
            return null;
        }
        
        $filePath = $this->projectDir . '/public/Data/clubs/' . $clubFiles[$competitionId];
        
        if (!file_exists($filePath)) {
            return null;
        }
        
        try {
            $fileContent = file_get_contents($filePath);
            $data = json_decode($fileContent, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }
            
            return $data;
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Vérifie si une compétition a des données de clubs disponibles
     */
    public function hasClubsData(string $competitionId): bool
    {
        return $this->getClubsByCompetition($competitionId) !== null;
    }
    
    /**
     * Retourne la liste des compétitions supportées
     */
    public function getSupportedCompetitions(): array
    {
        return [
            'GB1' => 'Premier League'
        ];
    }
}
