// async function fetchdataproductionsbyday(codeClustercuivre, codeinseecuivre) { 
//     try {
//         if (!jwtToken) {
//             console.error("JWT Token is missing!");
//             return;
//         }

//         if (!pointOfSaleId) {
//             console.error("pointOfSaleId is missing!");
//             return;
//         }

//         let url = `https://api.nomadcloud.fr/api/productions-by-day/${pointOfSaleId}`;
//         let params = new URLSearchParams();

//         if (codeClustercuivre) {
//             params.append("codeCluster", codeClustercuivre);
//         }
//         if (codeinseecuivre) {
//             params.append("codeInsee", codeinseecuivre);
//         }

        

//         url += "?" + params.toString()+`&mois=${month}&annee=${year}&optionSelect=${Type}&page=1`;

//         console.log("Final URL:", url); // Debugging

//         const response = await fetch(url, {
//             method: 'GET',
//             headers: {
//                 'Authorization': `Bearer ${jwtToken}`,
//                 'Content-Type': 'application/json'
//             }
//         });

//         if (!response.ok) {
//             throw new Error(`HTTP error! Status: ${response.status}`);
//         }

//         const data = await response.json();
//         console.log('Response fetchdataproductionsbyday:', data);
//         updateUI(data.ventes_par_jour);

//     } catch (error) {
//         console.error('Fetch error:', error);
//         document.getElementById("consommation-container").innerHTML = `<p>Erreur lors du chargement des données. ${error.message}</p>`;
//     }
// }

// // Test avec valeurs nulles au lieu de chaînes vides
// fetchdataproductionsbyday(null, null);
// async function productionsbyday(codeClustercuivre, codeinseecuivre) {
//     await fetchdataproductionsbyday(codeClustercuivre || null, codeinseecuivre || null);  
// }
// function updateUI(ventesParJour) {
//     let container = document.getElementById("consommation-container");
//     if (!ventesParJour) {
//         container.innerHTML = "<p>Aucune donnée disponible.</p>";
//         return;
//     }

//     let content = "<h2>Ventes par jour</h2><ul>";
//     Object.entries(ventesParJour).forEach(([jour, valeur]) => {
//         content += `<li>Jour ${jour} : ${valeur} ventes</li>`;
//     });
//     content += "</ul>";

//     container.innerHTML = content;
// }
