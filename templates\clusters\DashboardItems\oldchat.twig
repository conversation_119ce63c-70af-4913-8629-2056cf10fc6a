<style>
.chat-section {
  border-radius: 10px;
  flex-direction: column;
  display: flex;
  flex-grow: 1; 
}

.selected-user {
  font-weight: 600;
  border-bottom: 1px solid #eee;
  text-align: center; 
}

.chat-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 10px;
  background: #f5f5f5; 
}

.chat-list {
  list-style-type: none;
  padding: 0;
}

.chat-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

.in {
  justify-content: flex-start; 
}

.out {
  justify-content: flex-end; 
}

.chat-img {
  width: 32px; 
  height: 32px;
  margin: 5px; 
  flex-shrink: 0;
}

.chat-img img {
  border-radius: 50%;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-message {
  border-radius: 8px; 
  padding: 8px 12px;
  max-width: 75%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); 
}

.in .chat-message {
  background-color: #fff; 
  margin-left: 5px; 
}

.out .chat-message {
  background-color: #B1C3FF; 
  color: white;
  margin-right: 5px; 
}

.chat-message h5 {
  margin: 0 0 5px 0;
  font-weight: 600;
  font-size: 0.8rem; 
}

.chat-message p {
  line-height: 1.3;
  margin: 0;
  font-size: 0.9rem; 
  font-family: Consolas, monospace; 
}

.chat-form {
  background-color: #fff;
  padding: 10px;
  border-top: 1px solid #eee;
  display: flex; 
  align-items: center;
}

.chat-form textarea {
  border: 1px solid #ccc;
  border-radius: 5px;
  resize: none;
  padding: 10px;
  width: 100%;
  height: 40px; 
  margin-right: 10px;
  flex-grow: 1; 
}

.chat-form button {
  background-color: #b1c3ff;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px; 
  cursor: pointer;
}
</style>
    <i class="bonus-expand-icon bi bi-fullscreen"></i> 
  <H3>AI Chat</h3>
  <div class="chat-container">
    <ul class="chat-list">
      <li class="in">
        <div class="chat-img">
          <img alt="Avatar" src="https://miro.medium.com/v2/resize:fit:1200/1*C_LFPy6TagD1SEN5SwmVRQ.jpeg">
        </div>
        <div class="chat-message">
          <h5>AI</h5>
          <p>Bonjour , commment je peut vous aidez?</p>
        </div>
      </li>
      <li class="out">
        <div class="chat-message">
          <h5>{user.name}</h5>
          <p>Bonjour, j'ai besoin ..</p>
        </div>
        <div class="chat-img">
          <img alt="Avatar" src="https://bootdey.com/img/Content/avatar/avatar6.png">
        </div>
      </li>
    </ul>
  </div>
  <div class="chat-form">
    <textarea placeholder="Type your message here..."></textarea>
    <button type="button"><i class="bi bi-send-fill"></i></button>
  </div>