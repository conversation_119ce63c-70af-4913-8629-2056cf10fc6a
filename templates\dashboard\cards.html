<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Styled Navbar</title>
<style>
    /* Container for the navigation buttons */
    .navbar {
        display: flex;
        gap: 10px;
        padding: 10px;
        background-color: #f0f1f4; /* Light background */
        justify-content: flex-start; /* Align buttons to the left */
        align-items: center;
    }
    
    /* Individual button styling */
    .nav-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 20px; /* Round edges */
        font-size: 1rem;
        font-weight: bold;
        color: #6b6b6b; /* Default text color */
        background-color: transparent;
        border: none;
        transition: all 0.3s;
        cursor: pointer;
    }

    /* Icon styling */
    .nav-button .icon {
        font-size: 1.2rem;
    }
    
    /* Active button styling */
    .nav-button.active {
        background-color: #f20000; /* Red background when active */
        color: #ffffff; /* White text when active */
    }
</style>
</head>
<body>

<div class="navbar">
    <!-- Button examples -->
    <button class="nav-button active">
        <span class="icon">📶</span> <!-- Placeholder icon -->
        <span>12 526</span>
    </button>
    <button class="nav-button">
        <span class="icon">🌱</span> <!-- Placeholder icon -->
        <span>9 562</span>
    </button>
    <button class="nav-button">
        <span class="icon">📱</span> <!-- Placeholder icon -->
        <span>5 662</span>
    </button>
    <button class="nav-button">
        <span class="icon">📟</span> <!-- Placeholder icon -->
        <span>3 222</span>
    </button>
    <button class="nav-button active">
        <span class="icon">💻</span> <!-- Placeholder icon -->
        <span>9 056</span>
    </button>
</div>

</body>
</html>
