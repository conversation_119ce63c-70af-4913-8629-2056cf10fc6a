<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Nouvelle Hiérarchie avec JSON</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 2rem 0;
        }
        
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .continent-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 5px solid var(--continent-color, #007bff);
        }
        
        .continent-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .continent-header:hover {
            color: var(--continent-color, #007bff);
        }
        
        .continent-icon {
            font-size: 1.5rem;
            margin-right: 12px;
        }
        
        .continent-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin: 0;
        }
        
        .competitions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 1rem;
        }
        
        .competition-card {
            background: white;
            border-radius: 10px;
            padding: 1.25rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .competition-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
            border-color: #007bff;
        }
        
        .competition-card.expanded {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .competition-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }
        
        .competition-title {
            display: flex;
            align-items: center;
        }
        
        .competition-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 8px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2d3436;
            font-weight: bold;
            font-size: 12px;
        }
        
        .competition-name {
            font-weight: bold;
            color: #2d3436;
            margin: 0;
            font-size: 1.1rem;
        }
        
        .competition-country {
            font-size: 0.9rem;
            color: #636e72;
        }
        
        .expand-icon {
            color: #007bff;
            transition: transform 0.3s ease;
        }
        
        .expand-icon.rotated {
            transform: rotate(90deg);
        }
        
        .clubs-section {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .clubs-section.active {
            display: block;
        }
        
        .clubs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 0.75rem;
        }
        
        .club-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 0.75rem;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .club-item:hover {
            background: #fff8e1;
            transform: translateX(5px);
        }
        
        .club-logo {
            width: 24px;
            height: 24px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .club-info {
            flex-grow: 1;
        }
        
        .club-name {
            font-weight: 600;
            color: #495057;
            margin: 0;
            font-size: 0.95rem;
        }
        
        .club-details {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }
        
        .loading-clubs {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 1rem;
        }
        
        .stats-bar {
            display: flex;
            justify-content: space-between;
            margin-top: 0.75rem;
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            margin-right: 4px;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="main-card">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-globe text-primary"></i>
                        Nouvelle Hiérarchie - Compétitions par Continent
                    </h1>
                    <p class="text-center text-muted mb-4">
                        Test de votre nouvelle hiérarchie avec chargement des clubs depuis les fichiers JSON
                    </p>
                    
                    <div class="text-center mb-4">
                        <button class="btn btn-primary btn-sm me-2" onclick="loadHierarchy()">
                            <i class="fas fa-refresh"></i> Recharger
                        </button>
                        <button class="btn btn-success btn-sm me-2" onclick="expandAll()">
                            <i class="fas fa-expand"></i> Tout Développer
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="collapseAll()">
                            <i class="fas fa-compress"></i> Tout Réduire
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div id="hierarchy-container">
                    <div class="main-card">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                            <p class="mt-2">Chargement de la hiérarchie...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let hierarchyConfig = null;
        let loadedClubs = new Map();
        
        // Initialisation
        document.addEventListener("DOMContentLoaded", function () {
            console.log("🚀 Initialisation de la nouvelle hiérarchie");
            loadHierarchy();
        });
        
        // Charger la configuration de la hiérarchie
        async function loadHierarchy() {
            try {
                console.log("📊 Chargement de la configuration...");
                
                const response = await fetch('Data/hierarchy-config.json');
                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
                
                hierarchyConfig = await response.json();
                console.log("✅ Configuration chargée:", hierarchyConfig);
                
                generateHierarchyHTML();
                
            } catch (error) {
                console.error("❌ Erreur lors du chargement:", error);
                showError("Impossible de charger la configuration de la hiérarchie: " + error.message);
            }
        }
        
        // Générer le HTML de la hiérarchie
        function generateHierarchyHTML() {
            const container = document.getElementById('hierarchy-container');
            let html = '';
            
            Object.keys(hierarchyConfig.continents).forEach(continentId => {
                const continent = hierarchyConfig.continents[continentId];
                
                html += `
                    <div class="main-card">
                        <div class="continent-section" style="--continent-color: ${continent.color}">
                            <div class="continent-header" onclick="toggleContinent('${continentId}')">
                                <span class="continent-icon">${continent.icon}</span>
                                <h2 class="continent-name">${continent.name}</h2>
                                <i class="fas fa-chevron-right expand-icon ms-auto" id="continent-icon-${continentId}"></i>
                            </div>
                            <div class="competitions-grid" id="continent-${continentId}" style="display: block;">
                `;
                
                continent.competitions.forEach(competition => {
                    html += `
                        <div class="competition-card" onclick="toggleCompetition('${continentId}', '${competition.id}')">
                            <div class="competition-header">
                                <div class="competition-title">
                                    <div class="competition-logo">${competition.logo}</div>
                                    <div>
                                        <h4 class="competition-name">${competition.name}</h4>
                                        <div class="competition-country">${competition.country}</div>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right expand-icon" id="comp-icon-${competition.id}"></i>
                            </div>
                            <div class="stats-bar">
                                <div class="stat-item">
                                    <i class="fas fa-trophy stat-icon"></i>
                                    ${competition.name}
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-flag stat-icon"></i>
                                    ${competition.country}
                                </div>
                            </div>
                            <div class="clubs-section" id="clubs-${competition.id}">
                                <div class="loading-clubs">
                                    <i class="fas fa-spinner fa-spin"></i> Chargement des clubs...
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += `
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Toggle continent
        function toggleContinent(continentId) {
            const section = document.getElementById(`continent-${continentId}`);
            const icon = document.getElementById(`continent-icon-${continentId}`);
            
            if (section.style.display === 'none') {
                section.style.display = 'block';
                icon.classList.add('rotated');
            } else {
                section.style.display = 'none';
                icon.classList.remove('rotated');
            }
        }
        
        // Toggle competition et charger les clubs
        async function toggleCompetition(continentId, competitionId) {
            const clubsSection = document.getElementById(`clubs-${competitionId}`);
            const icon = document.getElementById(`comp-icon-${competitionId}`);
            const card = event.currentTarget;
            
            if (clubsSection.classList.contains('active')) {
                clubsSection.classList.remove('active');
                icon.classList.remove('rotated');
                card.classList.remove('expanded');
            } else {
                clubsSection.classList.add('active');
                icon.classList.add('rotated');
                card.classList.add('expanded');
                
                // Charger les clubs si pas encore fait
                if (!loadedClubs.has(competitionId)) {
                    await loadClubs(continentId, competitionId);
                }
            }
        }
        
        // Charger les clubs depuis le fichier JSON
        async function loadClubs(continentId, competitionId) {
            const clubsSection = document.getElementById(`clubs-${competitionId}`);
            const competition = findCompetition(continentId, competitionId);
            
            if (!competition) {
                showClubsError(clubsSection, "Compétition non trouvée");
                return;
            }
            
            try {
                console.log(`🏟️ Chargement des clubs pour ${competition.name}...`);
                
                const response = await fetch(`Data/clubs/${competition.clubs_file}`);
                if (!response.ok) {
                    throw new Error(`Fichier non trouvé: ${competition.clubs_file}`);
                }
                
                const clubsData = await response.json();
                console.log(`✅ ${clubsData.clubs.length} clubs chargés pour ${competition.name}`);
                
                // Générer le HTML des clubs
                let clubsHtml = '<div class="clubs-grid">';
                
                clubsData.clubs.forEach(club => {
                    clubsHtml += `
                        <div class="club-item">
                            <div class="club-logo"></div>
                            <div class="club-info">
                                <div class="club-name">${club.name}</div>
                                <div class="club-details">
                                    ${club.stadium} • ${club.capacity?.toLocaleString()} places
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                clubsHtml += '</div>';
                
                // Ajouter les statistiques
                if (clubsData.statistics) {
                    clubsHtml += `
                        <div class="mt-3 p-2 bg-light rounded">
                            <small class="text-muted">
                                <strong>Statistiques:</strong> 
                                ${clubsData.statistics.total_clubs} clubs • 
                                Valeur totale: ${formatCurrency(clubsData.statistics.total_market_value)} • 
                                Capacité totale: ${clubsData.statistics.total_capacity?.toLocaleString()} places
                            </small>
                        </div>
                    `;
                }
                
                clubsSection.innerHTML = clubsHtml;
                loadedClubs.set(competitionId, clubsData);
                
            } catch (error) {
                console.error(`❌ Erreur lors du chargement des clubs:`, error);
                showClubsError(clubsSection, error.message);
            }
        }
        
        // Trouver une compétition
        function findCompetition(continentId, competitionId) {
            const continent = hierarchyConfig.continents[continentId];
            return continent?.competitions.find(comp => comp.id === competitionId);
        }
        
        // Afficher une erreur pour les clubs
        function showClubsError(clubsSection, message) {
            clubsSection.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    Erreur: ${message}
                </div>
            `;
        }
        
        // Afficher une erreur générale
        function showError(message) {
            const container = document.getElementById('hierarchy-container');
            container.innerHTML = `
                <div class="main-card">
                    <div class="error-message">
                        <h5><i class="fas fa-exclamation-triangle"></i> Erreur</h5>
                        <p>${message}</p>
                        <button class="btn btn-primary btn-sm" onclick="loadHierarchy()">
                            <i class="fas fa-refresh"></i> Réessayer
                        </button>
                    </div>
                </div>
            `;
        }
        
        // Formater la devise
        function formatCurrency(value) {
            if (!value) return 'N/A';
            const num = parseInt(value);
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B €';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M €';
            }
            return num.toLocaleString() + ' €';
        }
        
        // Fonctions utilitaires
        function expandAll() {
            document.querySelectorAll('.competition-card').forEach(card => {
                const onclick = card.getAttribute('onclick');
                if (onclick) {
                    const matches = onclick.match(/'([^']*)', '([^']*)'/);
                    if (matches) {
                        const clubsSection = document.getElementById(`clubs-${matches[2]}`);
                        if (!clubsSection.classList.contains('active')) {
                            card.click();
                        }
                    }
                }
            });
        }
        
        function collapseAll() {
            document.querySelectorAll('.clubs-section.active').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelectorAll('.expand-icon.rotated').forEach(icon => {
                icon.classList.remove('rotated');
            });
            document.querySelectorAll('.competition-card.expanded').forEach(card => {
                card.classList.remove('expanded');
            });
        }
    </script>
</body>
</html>
