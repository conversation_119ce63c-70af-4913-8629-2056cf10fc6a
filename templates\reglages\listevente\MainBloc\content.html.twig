<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;300;400;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('styles/MainBloc.css') }}">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jquery-treegrid@0.3.0/css/jquery.treegrid.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-table@1.23.5/dist/bootstrap-table.min.css">


<style>
   :root {
    --skeleton-color: #e0e0e0;  /* Default skeleton color */
    --skeleton-animation-color: #c0c0c0;  /* Skeleton animation color */
}

/* Styles for dark mode */
[data-theme="dark"] {
    --skeleton-color: #444444;  /* Darker skeleton color for dark mode */
    --skeleton-animation-color: #666666;  /* Darker animation color for dark mode */
}

/* Styling for the table rows */
.table-striped>tbody>tr:nth-of-type(odd)>* {
    --bs-table-bg-type: unset;  /* Resets background */
    --bs-table-accent-bg: unset;  /* Resets accent background */
    color: unset;  /* Resets color */
}

/* Skeleton loader style */
.skeleton {
    background-color: var(--skeleton-color);  /* Default skeleton color */
    animation: pulse 1.5s infinite ease-in-out;
    border-radius: 4px;
    height: 1.2rem;  /* Set a default height */
}

/* Pulse animation for skeleton loading */
@keyframes pulse {
    0% {
        background-color: var(--skeleton-color);
    }
    50% {
        background-color: var(--skeleton-animation-color);
    }
    100% {
        background-color: var(--skeleton-color);
    }
}

/* Adjust skeleton width based on the content (e.g., tables) */
.skeleton.short {
    width: 80px; /* Short skeleton (for smaller content) */
}

.skeleton.medium {
    width: 150px; /* Medium skeleton (for medium content) */
}

.skeleton.long {
    width: 200px; /* Long skeleton (for larger content) */
}

/* Example of hiding content during loading */
.skeleton + span {
    display: none;  /* Hide content until skeleton is replaced */
}

/* Adjustments for dark mode skeleton */
[data-theme="dark"] .skeleton {
    background-color: var(--skeleton-color);
    animation: pulse 1.5s infinite ease-in-out;
}


</style>
<div class="table-container">
	<nav class="navbar table-navbar navbar-expand-lg">
		<ul class="navbar-nav me-auto mb-1 mb-lg-0">
			<li class="nav-item" id="tablenav">
				<a class="nav-link active" href="#">All</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unfulfilled</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Unpaid</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Open</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Closed</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#">Local Delivery</a>
			</li>
		</ul>
		<div class="navbar-nav">
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-search"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-sliders"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-filter"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-arrow-down-up"></i>
			</a>
			<a class="nav-link" id="iconnav" href="#">
				<i class="bi bi-three-dots"></i>
			</a>
		</div>
	</nav>

<div class="container mt-2">
    <div class="row mb-3 align-items-center">
        <div class="col-10 col-md-2">
            <div class="custom-select-container">
                <i class="bi bi-person-circle icon"></i>
                <select class="form-select" id="categoryFilter">
                    <option value="">All Categories</option>
                    {% for production in productions %}
                        {% if production.product.category %}
                            <option value="{{ production.product.category.id }}" 
                                {% if production.product.category.id == app.request.query.get('categoryId') %} selected {% endif %}>
                                {{ production.product.category.name }}
                            </option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
        </div>
        
        <div class="col-10 col-md-2">
            <div class="custom-select-container">
                <i class="bi bi-plus-circle-dotted icon"></i>
                <select class="form-select" id="productFilter">
                    <option value="">All Products</option>
                    {% for production in productions %}
                        <option value="{{ production.product.id }}" 
                            {% if production.product.id == app.request.query.get('productId') %} selected {% endif %}>
                            {{ production.product.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
        </div>
        
        <div class="col-10 col-md-2">
            <div class="custom-select-container">
                <i class="bi bi-plus-circle-dotted icon"></i>
                <select class="form-select" id="productEtat">
                    <option value="">All etats</option>
                    {% for production in productionsStates %}
                        <option value="{{ production.id }}" 
                            {% if production.id == app.request.query.get('etatId') %} selected {% endif %}>
                            {{ production.nom }}
                        </option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>
</div>

	
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const categoryFilter = document.getElementById("categoryFilter");
        const productFilter = document.getElementById("productFilter");
		const productionEtat=document.getElementById("productEtat");

        function fetchFilteredData() {
            const categoryId = categoryFilter.value;
            const productId = productFilter.value;
			const etatId= productionEtat.value;
  
            window.location.href = `liste?categoryId=${categoryId}&productId=${productId}&etatId=${etatId}`;
        }

        categoryFilter.addEventListener("change", fetchFilteredData);
        productFilter.addEventListener("change", fetchFilteredData);
		productionEtat.addEventListener("change",fetchFilteredData);
    });
</script>



<table class="table table-striped">
    <thead>
        <tr>
            <th><label class="custom-checkbox">
                <input type="checkbox" class="form-check-input" id="customCheck">
            </label></th>
            <th><i class="fas fa-id-badge"></i> Commande ID</th>
            <th><i class="fas fa-barcode"></i> Numéro de commande</th>
            <th><i class="fas fa-calendar-alt"></i> Date de commande</th>
            <th><i class="fas fa-check-circle"></i> Date de vente validée</th>
            <th><i class="fas fa-store"></i> Point de vente</th>
            <th><i class="fas fa-cogs"></i> Produit</th>
            <th><i class="fas fa-exclamation-circle"></i> État</th>
        </tr>
    </thead>
    <tbody>
        {% if productions is empty %}
            <tr>
                <td colspan="8" class="text-center">Aucune production trouvée pour cette période.</td>
            </tr>
        {% else %}
            {% for production in productions %}
                <tr>
                    <td class="checkbox-cell">
                        <input type="checkbox" class="form-check-input custom-check">
                    </td>
                    <td>
                        <div class="skeleton" style="width: 100px; height: 1.2rem;"></div>
                        <span style="display:none;">{{ production.id }}</span>
                    </td>
                    <td>
                        <div class="skeleton" style="width: 150px; height: 1.2rem;"></div>
                        <span style="display:none;">{{ production.numCommande }}</span>
                    </td>
                    <td>
                        <div class="skeleton" style="width: 120px; height: 1.2rem;"></div>
                        <span style="display:none;">{{ production.dateCmdA | date('d/m/Y') }}</span>
                    </td>
                    <td>
                        <div class="skeleton" style="width: 120px; height: 1.2rem;"></div>
                        <span style="display:none;">{{ production.dateVenteValidB | date('d/m/Y') }}</span>
                    </td>
                    <td>
                        <div class="skeleton" style="width: 150px; height: 1.2rem;"></div>
                        <span style="display:none;">
                            {% if production.pointOfSale is not empty %}
                                {{ production.pointOfSale.code }}
                            {% else %}
                                Non spécifié
                            {% endif %}
                        </span>
                    </td>
                    <td>
                        <div class="skeleton" style="width: 150px; height: 1.2rem;"></div>
                        <span style="display:none;">
                            {% if production.product is not empty %}
                                {{ production.product.name }}
                            {% else %}
                                Non spécifié
                            {% endif %}
                        </span>
                    </td>
                    <td>
                        <div class="skeleton" style="width: 150px; height: 1.2rem;"></div>
                        <span style="display:none;">
                            {% if production.etat is not empty %}
                                {{ production.etat.nom }}
                            {% else %}
                                Non spécifié
                            {% endif %}
                        </span>
                    </td>
                </tr>
            {% endfor %}
        {% endif %}
    </tbody>
</table>





</div>

</div>
<script>
    // Simulate a delay of 2 seconds before loading the actual data
    setTimeout(function() {
        // Remove skeleton classes after 2 seconds
        const skeletons = document.querySelectorAll('.skeleton');
        skeletons.forEach(skeleton => {
            skeleton.style.display = 'none';
        });

        // Show the actual content (this part will be automatically rendered by the backend)
        const actualContent = document.querySelectorAll('.skeleton + span');
        actualContent.forEach(content => {
            content.style.display = 'inline';
        });
    }, 2000);
</script>
