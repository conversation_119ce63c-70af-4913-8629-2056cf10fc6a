<style>
.calendar,.calendarActual {
  display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    padding: 4px;
    width: 100%;
}

.day {
        background-color: transparent;
    color: #437192;
    display: flex
;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    border: 1px solid #437192;
    width: 50px;
    height: 54px;
    border-radius: 4px;
    flex-direction: column; 
}
.day-name {
    width: 70px;
    font-size: 14px;
    padding: 4px;
    color: #a074c4;
    text-align: center;
}
.AllDaysName{
   display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    gap: 0px;
}
.DaysDetails{
       border-top: 1px solid #437192;
    height: 30px;
    width: 100%;
    font-size: 14px;
    color: var(--coloredTextPrimary);
    padding: 4px;
}
.DetailsCAlenderContainer{
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    gap: 40px;
    padding: 10px;
    font-size: 14px;
    margin-top: 50px;
    width: 100%;
}
.MonthName,.MontNameActual{
    margin-bottom: 20px;
    color: #928a19;
    text-align: center;
}
.PrevMonth,.ActualMonth{
  width: 430px;
}
.calenderContainer{
      display: flex;
    padding: 20px;
    width: 90%;
}
.productionPannel{
    bottom: 0px;
    position: fixed;
    width: 84%;
    height: 95.5%;
    background: var(--channel-item-bg);
    transition: bottom 0.3s ease-in-out;
    width: 86%;
    height: 100%;
    z-index: 10;
}
.PanelContrats{
        width: 314px;
    background: var(--background-color);
    color: var(--text-color);
    border: 2px solid var(--border-color);
        height: 100%;
    position: absolute;
    top: 40px;
    transition: right 0.5s ease-in-out;
    z-index: 2;
    
}

.DaysProductionTreeView{
    background-color: var(--sidebar-left-right-color);
    color: var(--tree-view-color,#000);
    display: flex;
    align-items: center;
    font-size: 14px;
    border: 1px solid var(--sidebar-left-right-color);
    width: 42px;
    height: 40px;
    border-radius: 4px;
    flex-direction: column;
    cursor:pointer;

}
.DaysProductionTreeView:hover{
    background-color: var(--active-field-bg);
    border: 1px solid var(--coloredTextPrimary);
}

.DaysProductionTreeView:hover span{
    color: var(--activebutton-color-text);
}
.DetailsCAlenderdata {
  width: 100%;
  height: 100%;
    max-height: 90vh;
     overflow-y: auto;
    overflow-x: hidden;
  
   
  font-family: Arial, sans-serif; /* Set a clean font */
}

.DetailsCAlenderdata .contract-item {

  padding: 14px; /* Space inside each contract item */
  margin-bottom: 10px; /* Space between contract items */

  border-radius: 4px; /* Rounded corners for each item */
}

.DetailsCAlenderdata .contract-item p {
  margin: 4px 0; /* Margin for each paragraph */
  font-size: 14px; /* Font size */
}

.DetailsCAlenderdata .contract-item p  {
 color:#88969F;
}
#productionTable {
  width: 100%;
  border-collapse: collapse;
  color: var(--channel-item-color);
  border-radius: 4px;
  overflow: hidden;
  font-family: Consolas, monospace;
}

#productionTable thead tr {
  background: var(--sidebar-left-right-color);
}

#productionTable th, 
#productionTable td {
  padding: 4px;
  text-align: left;
  border-bottom: 1px solid var(--border-fieldSpan);
}

/* Largeur spécifique pour chaque colonne */
#productionTable th:nth-child(1) { width: 2%; }
#productionTable th:nth-child(2) { width: 9%; }
#productionTable th:nth-child(3) { width: 9%; }
#productionTable th:nth-child(4) { width: auto; } /* Ajustement automatique pour le statut */

#productionTable tbody tr td {
  padding: 4px;
}

@media screen and (max-width: 1367px) and (min-width: 800px) {
  /* Largeur spécifique pour chaque colonne */
#productionTable th:nth-child(1) { width: 2%; }
#productionTable th:nth-child(2) { width: 9%; }
#productionTable th:nth-child(3) { width: 15%; }
#productionTable th:nth-child(4) { width: auto; } /* Ajustement automatique pour le statut */
}





.popup {
    display: none;
    position: fixed;
    top: 10%;
    left: 50%;
    border-radius: 12px;
    /* background: var(--headerTreeSection-bg); */
    background : var(--PopUpBackground);
   /* color: #55C5D0;*/
  color: var(--coloredTextPrimary);
    justify-content: center;
    align-items: center;
    transition: left 0.1sease-out, top 0.1sease-out;
    font-family: Consolas, monospace;
}

.popup-content {
    /*border: 1px solid var(--border-fieldSpan);*/
    padding: 10px;
    border-radius: 8px;
    max-width: 400px;
    width: 100%;
}

.close-btn {
    float: right;
    font-size: 20px;
    cursor: pointer;
}
/* Adding a hover effect on table rows */
.productionTable tbody tr:hover {
    background-color: var(--active-field-bg) ; /* Light gray background */
    cursor: pointer; /* Change cursor to indicate it's clickable */
     color: var(--active-field-color);
}

</style>
<div class="DetailsCAlenderContainer">
    <div class="DetailsCAlenderdata scrollbar-custom ">
    </div>
    <div id="detailsPopup" class="popup">
    <div class="popup-content">
        <span class="close-btn" onclick="closePopup()">&times;</span>
        <div id="popupContent"></div>
    </div>
</div>

</div>
<div class="PanelContrats" style="right:-360px">
</div>
 <script>

function generateCalendar(month, year,currentMonthData,PrevMonthData) {
    const monthNames = ["January", "February", "March", "April", "May", "June",
                        "July", "August", "September", "October", "November", "December"];
    const calendarContainer = document.querySelector('.calendar');
    const calendarContainerActual = document.querySelector('.calendarActual');
    const  monthNameContainerPrev = document.querySelector('.MonthName.Prev');
    const monthNameContainerActual = document.querySelector('.MonthName.actual');
    calendarContainer.innerHTML = '';
    calendarContainerActual.innerHTML = '';
    const firstDayOfMonth = new Date(year, month - 1, 1);
    const startingDayOfWeek = firstDayOfMonth.getDay();
    monthNameContainerActual.innerHTML = monthNames[month - 1];
    monthNameContainerPrev.innerHTML = monthNames[month - 2];
    const offset = (startingDayOfWeek + 6) % 7;

    for (let i = 0; i < offset; i++) {
        const emptyDiv = document.createElement('div');
        emptyDiv.classList.add('day');
        calendarContainer.appendChild(emptyDiv);
        calendarContainerActual.appendChild(emptyDiv);
    }

    const daysInMonthPrev = new Date(year, month-1, 0).getDate();

    const firstDay = new Date(year, month - 1, 1);
    let offsetActual = (firstDay.getDay() + 6) % 7;

    let daysHtml = '';
    for (let i = 0; i < offsetActual; i++) {
        daysHtml += `<div class="day"></div>`;
    }


    for (let day = 1; day <= daysInMonthPrev; day++) {
        let dayValue = PrevMonthData[day] || 0;
        let displayValue = dayValue === 0 ? '' : dayValue;
        daysHtml += `<div class="day OpenContatPanel" >
            <span>${day}</span>
            <div class="DaysDetails">${displayValue}</div>
            </div>`;
    }


    calendarContainer.innerHTML += daysHtml;


    let daysPrevHtml = '';
    const firstDayPrev = new Date(year, month - 1, 1);
    let offsetPrev = (firstDayPrev.getDay() + 6) % 7;

    for (let i = 0; i < offsetPrev; i++) {
        daysPrevHtml += `<div class="day"></div>`;
    }

    const daysInMonth = new Date(year, month, 0).getDate();
    for (let day = 1; day <= daysInMonth; day++) {
        let dayValue = currentMonthData[day] || 0;
        let displayValue = dayValue === 0 ? '' : dayValue;
        daysPrevHtml += `<div class="day OpenContactPanel" >
            <span>${day}</span>
            <div class="DaysDetails">${displayValue}</div>
            </div>`;
    }

    calendarContainerActual.innerHTML = daysPrevHtml;

    document.querySelectorAll('.OpenContatPanel').forEach(element => {
    element.addEventListener('click', function() {
        var sidebar = document.querySelector('.PanelContrats');
        var sidebarStyle = window.getComputedStyle(sidebar);
        if (sidebarStyle.right === '0px') {
            sidebar.style.right = '-360px';
        } else {
            sidebar.style.right = '0px';
        }
    });
});

}
function generateCalendarProduction(month, year, currentMonthData) {
    const daysForActualMonth = new Date(year, month, 0).getDate(); // Get the number of days in the month
    let daysProduction = '';

    const firstDay = new Date(year, month - 1, 1); // Get the first day of the month
    let offset = (firstDay.getDay() + 6) % 7; // Calculate the day of the week offset for the first day

    // Add empty divs for the offset to align the start of the month correctly
    for (let i = 0; i < offset; i++) {
        daysProduction += `<div class="DaysProductionTreeView"></div>`;
    }

    // Loop through all the days of the month
    for (let day = 1; day <= daysForActualMonth; day++) {
        const date = new Date(year, month - 1, day); // Create a date object for each day
        let dayValue = currentMonthData[day] || 0; // Get the production data for each day, default to 0 if not available
        let displayValue = dayValue === 0 ? '' : dayValue; // Determine the display value, empty if zero

        // Create a div for each day with onclick event to fetch productions
        daysProduction += `<div class="DaysProductionTreeView OpenContactPanel" onclick="fetchProductionsForOnedays('${day}','${month}', '${year}')">
            <span>${day}</span>
            <div class="DaysDetails" style="text-align: center; height: 22px;">${displayValue}</div>
        </div>`;
    }

    // Set the inner HTML of the calendar container with the days' production
    const calendarContainer = document.querySelector('.ActualMonthProductionsDays');
    calendarContainer.innerHTML = daysProduction;
}

async function productionsByDay(clusterCode= null, codeInsee = null) {
    var baseUrl = `https://api.nomadcloud.fr/api/productions-by-day/${pointOfSaleId}?mois=${month}&annee=${year}&optionSelect=${Type}&page=1`;
    var url = new URL(baseUrl);
    if (clusterCode !== null) {
    url.searchParams.append('codeCluster', clusterCode);
    }
    if (codeInsee !== null) {
    url.searchParams.append('codeInsee', codeInsee);
    }

    try {
    const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${jwtToken}`,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    var currentMonthData = data.ventes_par_jour;
    var PrevMonthData = data["ventes_par_jour_mois-1"];
    const dayIndex = String(day-1);
    generateCalendarProduction(parseInt(month), parseInt(year),currentMonthData);
    //generateCalendar(parseInt(month), parseInt(year),currentMonthData,PrevMonthData);

    } catch ( error ) {
    console.error('Error:', error);
    }
}

async function DayDetails(clusterCode= null, codeInsee = null) {
    var baseUrl = `https://api.nomadcloud.fr/api/productions-for-one-day/${pointOfSaleId}?jour=${day}&page=1`;
    var url = new URL(baseUrl);
    if (clusterCode !== null) {
    url.searchParams.append('codeCluster', clusterCode);
    }
    if (codeInsee !== null) {
    url.searchParams.append('codeInsee', codeInsee);
    }

    try {
    const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${jwtToken}`,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    } catch ( error ) {
    console.error('Error:', error);
    }
}


async function fetchProductionsForOnedays(day, month, year) {
    var date = `${String(day).padStart(2, '0')}-${String(month).padStart(2, '0')}-${year}`;
    var SearchContainerINPanel =document.querySelector('.SearchContainerINPanel');
    SearchContainerINPanel.style.display = 'none';
    const productionPanel = document.getElementById('displayproductionPanel');
    productionPanel.style.bottom = '0';
 

    try {
        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = '';

        let table = document.getElementById("productionTable");
        if (!table) {
            sidebar.innerHTML = `
          
                <table id="productionTable" class="productionTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Numéro de commande</th>
                            <th>Ville</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody"></tbody>
                </table>
            `;
            table = document.getElementById("productionTable");
        }

        const tbody = document.getElementById("productionTableBody");
        tbody.innerHTML = '';

        const codeClusters = localStorage.getItem('clusterForOnedays');
        const codeInsees = localStorage.getItem('codeinseeForOnedays');

        const data = await ProductionsForOnedays(date, codeClusters, codeInsees);

        if (data && data.length > 0) {
            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${new Date(item.date_vente_valid_b).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</td>
                    <td>${item.num_commande}</td>
                    <td>${item.ville}</td>
                    <td>${item.precision_etat}</td>
                `;

                row.addEventListener('click', () => showDetailsPopup(item)); // Ajout de l'événement click
                tbody.appendChild(row);
            });
        } else {
            tbody.innerHTML = '<tr><td colspan="4">No data found for the selected date.</td></tr>';
        }
    } catch (error) {
        console.error('Error fetching production data:', error);
        document.querySelector('.DetailsCAlenderdata').innerHTML = '<p>Failed to load data.</p>';
    }
}

function showDetailsPopup(item) {
    const popup = document.getElementById("detailsPopup");
    const popupContent = document.getElementById("popupContent");

    // Remplir le popup avec les données de l'élément sélectionné
    popupContent.innerHTML = `
          <span class="formListItem">
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Numéro de Commande:</span> ${item.num_commande ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Date de Commande:</span> ${item.date_cmd_a ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Date de Vente Validée:</span> ${item.date_vente_valid_b ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Ville:</span> ${item.ville ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Code INSEE:</span> ${item.code_insee ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Code Panier:</span> ${item.code_panier ?? 'N/A'}
    </span>
   
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Précision de l'État:</span> ${item.precision_etat ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Technologie:</span> ${item.tech ?? 'N/A'}
    </span>
  
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Statut Intervention:</span> ${item.statut_intervention ?? 'N/A'}
    </span>
   
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Vendeur ID:</span> ${item.seller_id ?? 'N/A'}
    </span>
  
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Date Raccordement:</span> ${item.date_racc ?? 'N/A'}
    </span>
    <span class="caret formSpan" style='display: flex; justify-content: space-between;'>
        <span style='color:#88969F'>Date Rem:</span> ${item.date_rem ?? 'N/A'}
    </span>
    
    </span>

    `;

    popup.style.display = "block"; // Afficher le popup
}

function closePopup() {
    document.getElementById("detailsPopup").style.display = "none";
}

async function ProductionsForOnedays(date, codeCluster, codeInsee,FilterType=null) {
  try {
    let url = `https://api.nomadcloud.fr/api/productions-details-for-one-day/${pointOfSaleId}`;

    // Ajout des paramètres uniquement s'ils existent
   let params = ''; // Initialize `params` at the beginning of the function.

    if (FilterType==='Allventes') {
        params = `yearMonth=${date}&optionSelect=${Type}&page=1`;
    } else if(FilterType==='ventesKO'){
        params = `yearMonth=${date}&optionSelect=${Type}&etatId=2&page=1`;
    }
    else {
        params = `date=${date}&optionSelect=${Type}&page=1`;
    }

    if (codeCluster) params += `&codeCluster=${codeCluster}`;
    if (codeInsee) params += `&codeInsee=${codeInsee}`;

    url += `?${params}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('HTTP error!', response.status, response.statusText);
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}

function sortTableBySatisfaction() {
    let sortOrder = sortTableBySatisfaction.sortOrder || 'desc'; // Use function property to hold the sort state
    
    const tbody = document.getElementById("productionTableBody");
    const rows = Array.from(tbody.querySelectorAll("tr"));
    const columnIndex = 4; // Assuming 'note satisfaction' is the fifth column

    // Modify the sorting logic to handle null values as always being the lowest priority
    rows.sort((a, b) => {
        const valA = a.cells[columnIndex].textContent;
        const valB = b.cells[columnIndex].textContent;
        const numA = parseFloat(valA);
        const numB = parseFloat(valB);

        // Check if either value is null, and handle accordingly
        if (valA === ' ' && valB !== ' ') return 1; // Always put nulls at the end when valA is null
        if (valB === ' ' && valA !== ' ') return -1; // Always put nulls at the end when valB is null
        if (valA === ' ' && valB === ' ') return 0; // If both are null, they are equal

        // Normal number comparison
        return sortOrder === 'asc' ? numA - numB : numB - numA;
    });

    // Toggle sort order for next click
    sortTableBySatisfaction.sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';

    // Update sort icon based on current sort order
    //document.querySelector('.orderBysat').className = sortOrder === 'asc' ? 'bi bi-chevron-down orderBysat' : 'bi bi-chevron-up orderBysat';

    // Append sorted rows back to the tbody
    rows.forEach(row => tbody.appendChild(row));
}
async function displaySearchResults() {
    const productionPanel = document.getElementById('displayproductionPanel');
    productionPanel.style.bottom = '0';
    var SearchContainerINPanel =document.querySelector('.SearchContainerINPanel');
    SearchContainerINPanel.style.display = 'block';
    var ClosingBigPanel =document.querySelector('.ClosingBigPanel');
    ClosingBigPanel.style.top="73px";
    
    const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = '';
}
sortTableBySatisfaction.sortOrder = 'desc';
async function DisplayAllProductionsForOnedays(FilterType) {
    const date = `${year}-${String(month).padStart(2, '0')}`;
    var SearchContainerINPanel =document.querySelector('.SearchContainerINPanel');
    SearchContainerINPanel.style.display = 'none';
    const productionPanel = document.getElementById('displayproductionPanel');
    productionPanel.style.bottom = '0';
    
    try {
        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = '';
        let table = document.getElementById("productionTable");
        if (!table) {
            sidebar.innerHTML = `
                <table id="productionTable" class="productionTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th style="width:50px;">Heure</th>
                            <th style="width:155px;">Numéro de commande</th>
                            <th style="width:170px;">Ville</th>
                            <th id="sortBySatisfaction" style="width:160px; cursor:pointer;">
                                <i style="color: var(--bootom-colortext); font-size: 13px; margin-right: 3px;" class="bi bi-chevron-down orderBysat"></i>
                                note satisfaction
                            </th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody"></tbody>
                </table>
            `;
            table = document.getElementById("productionTable");
            document.getElementById("sortBySatisfaction").addEventListener('click', sortTableBySatisfaction);
        }

        const tbody = document.getElementById("productionTableBody");
        tbody.innerHTML = '';

        const codeClusters = localStorage.getItem('clusterForOnedays');
        const codeInsees = localStorage.getItem('codeinseeForOnedays');
        const data = await ProductionsForOnedays(date, codeClusters, codeInsees, FilterType);

        if (data && data.length > 0) {
            data.forEach(item => {
                const dateCmd = new Date(item.date_cmd_a).toLocaleDateString();
                // Ensure the value is treated as a number for comparison
                const satisfaction = item.note_satisfaction !== null ? parseInt(item.note_satisfaction) : ' ';
                const noteClass = satisfaction === 0 ? 'satisfactiondown' : '';
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${dateCmd}</td>
                    <td>${new Date(item.date_vente_valid_b).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</td>
                    <td class="NumCmdVenteTable ${noteClass}">${item.num_commande}</td>
                    <td>${item.ville || ' '}</td>
                    <td>${satisfaction}</td>
                    <td>${item.precision_etat || ' '}</td>
                `;
                tbody.appendChild(row);
            });
            // Sort immediately after loading data
            sortTableBySatisfaction();
        } else {
            tbody.innerHTML = '<tr><td colspan="6">No data found for the selected date.</td></tr>';
        }
    } catch (error) {
        console.error('Error fetching production data:', error);
        document.querySelector('.DetailsCAlenderdata').innerHTML = '<p>Failed to load data.</p>';
    }

}

async function DisplayProductionsObjectifTable() {
    try {
        const productionPanel = document.getElementById('displayproductionPanel');
        if (!productionPanel) {
            console.error("Production panel element not found.");
            return;
        }
        productionPanel.style.bottom = '0';

        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = '';

        const date = `${year}-${String(month).padStart(2, '0')}`;

        const codeClusters = localStorage.getItem('clusterForOnedays');
        const codeInsees = localStorage.getItem('codeinseeForOnedays');

        let table = document.getElementById("productionTable");
        if (!table) {
            sidebar.innerHTML = `
                <table id="productionTable" class="productionTable">
                    <thead>
                        <tr>
                            <th class="" style="width: 6%;">Code Cluster</th>
                            <th class="" style="width: 9%;">libelle cluster</th>
                            <th style="width: 6%;">Objectif VV</th>
                            <th style="width: 6%;">Objectif VR</th>
                            <th style="width: 6%;">Objectif Mobiles</th>
                            <th style="width: 6%;">Objectif Item</th>
                            <th style=""></th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody"></tbody>
                </table>
            `;
            table = document.getElementById("productionTable");
        }

        await populateTableData(date, codeClusters, codeInsees);
    } catch (error) {
        console.error('Error fetching production data:', error);
        sidebar.innerHTML = '<p>Failed to load data.</p>';
    }
}

async function populateTableData(date, codeClusters, codeInsees) {
    const tbody = document.getElementById("productionTableBody");
    if (!tbody) {
        console.error("Table body not found.");
        return;
    }

    tbody.innerHTML = ''; // Clear previous contents
    const data = await fetchObjectionPRoduction(date, codeClusters, codeInsees);

    if (data && data.length > 0) {
        const fragment = document.createDocumentFragment();
        data.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="">${item.code_cluster}</td>
                <td class="">${item.libelle_cluster}</td>
                <td>${item.vv}</td>
                <td>${item.vr}</td>
                <td>${item.vMobiles}</td>
                <td>${item.vItem1}</td>
                <td></td>
            `;
            fragment.appendChild(row);
        });
        tbody.appendChild(fragment);
    } else {
        tbody.innerHTML = '<tr><td colspan="7">No data found for the selected date.</td></tr>';
    }
}



async function fetchObjectionPRoduction(date, codeCluster, codeInsee) {
    try {
    let url = `https://api.nomadcloud.fr/api/objectifs-clusters-details/${cpv}?yearMonth=${date}&page=1`;

    let params = '';
    if (codeCluster) params += `&codeCluster=${codeCluster}`;
    if (codeInsee) params += `&codeInsee=${codeInsee}`;

    url += `?${params}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('HTTP error!', response.status, response.statusText);
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}


async function FetchsatisfactionClientsDetails(date, codeCluster, codeInsee){

 try {
    let url = `https://api.nomadcloud.fr/api/satisfaction-clients-details/${pointOfSaleId}?yearMonth=${date}&page=2`;

    let params = '';
    if (codeCluster) params += `&codeCluster=${codeCluster}`;
    if (codeInsee) params += `&codeInsee=${codeInsee}`;

    url += `?${params}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('HTTP error!', response.status, response.statusText);
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}


async function DisplaysatisfactionClientsDetails() {
    var date = `${year}-${String(month).padStart(2, '0')}`;
    let sortOrder = 'desc'; // Default sort order for date
    var SearchContainerINPanel =document.querySelector('.SearchContainerINPanel');
    SearchContainerINPanel.style.display = 'none';
    const productionPanel = document.getElementById('displayproductionPanel');
    productionPanel.style.bottom = '0';

    try {
        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = '';
        let table = document.getElementById("productionTable");
        if (!table) {
            sidebar.innerHTML = `
                <table id="productionTable" class="productionTable">
                    <thead>
                        <tr>
                            <th style="width: 150px;">CONTRAT</th>
                            <th id="dateHeader" style="width: 90px;cursor:pointer;"><i style="color: var(--bootom-colortext);font-size: 13px;margin-right: 3px;" class="bi bi-chevron-down orderByDate"></i><span>DATE</span></th>
                            <th style="width: 20px;">NOTE</th>
                            <th style="width: 160px;">CONSEILLER</th>
                            <th style="width: 50px;">DOC</th>
                            <th>libelle Cluster</th>
                            <th>VERBATIM</th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody"></tbody>
                </table>
            `;
            table = document.getElementById("productionTable");
        }

        const tbody = document.getElementById("productionTableBody");
        tbody.innerHTML = '';

        const codeClusters = localStorage.getItem('clusterForOnedays');
        const codeInsees = localStorage.getItem('codeinseeForOnedays');
        const data = await FetchsatisfactionClientsDetails(date, codeClusters, codeInsees);
        document.getElementById('dateHeader').addEventListener('click', function () {
            sortOrder = sortOrder === 'desc' ? 'asc' : 'desc';
            document.querySelector('.orderByDate').className = sortOrder === 'desc' ? 'bbi bi-chevron-down orderByDate' : 'bi bi-chevron-up orderByDate';
            updateTable(data, sortOrder);
        });

        updateTable(data, sortOrder);
    } catch (error) {
        console.error('Error fetching production data:', error);
        document.querySelector('.DetailsCAlenderdata').innerHTML = '<p>Failed to load data.</p>';
    }
}

function updateTable(data, sortOrder) {
    const tbody = document.getElementById("productionTableBody");
    tbody.innerHTML = '';

    if (data && data.length > 0) {
        // Sorting the data
        data.sort((a, b) => {
            const dateA = new Date(a.dateContrat.date);
            const dateB = new Date(b.dateContrat.date);
            return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
        });

        data.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${checkValue(item.orderNumber)}</td>
                <td>${new Date(item.dateContrat.date).toISOString().split('T')[0]}</td>
                <td>${checkValue(item.noteSatisfaction)}</td>
                <td>${checkValue(item.rencontreConseiller)}</td>
                <td>${checkValue(item.docSynthetique)}</td>
                <td>${checkValue(item.libelleCluster)}</td>
                <td>${checkValue(item.verbatim)}</td>
            `;
            tbody.appendChild(row);
        });
    } else {
        tbody.innerHTML = '<tr><td colspan="6">No data found for the selected date.</td></tr>';
    }
}
function checkValue(value) {
    return value === "(non défini)" ? "" : value;
}



</script>