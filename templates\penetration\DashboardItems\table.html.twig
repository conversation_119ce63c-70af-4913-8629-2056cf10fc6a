<style>:root
{
	--card-width: 200px;
	--card-height: 135px;
	--card-gap-horizontal: 12.2px;
	--card-gap-vertical: 8px;
	--background-color-select: #ecf4ef;
	--background-color-cards: #eef6f1;
	--text-selects-colors: #335d67;
	--text-inside-car-colors: #2c455c;
	--filled-progress-bar-color: #bfc7cc;
	--background-color-card-none: #e3e5e9;
	--buttonvalide-background-color: #e7f5e5;
	--buttonvalide-text-color: green;
	--buttonvalide-border-type: none;
	--btn-border-color: transparent; /* Default no border in light mode */
		--pagination-text-color: #333;
	--pagination-border-color: #ddd;
	--pagination-hover-bg-color: #ddd;
	--pagination-active-bg-color: #47c2ae;
	--pagination-active-text-color: #fff;	
}

[data-theme="dark"] {
	--card-width: 200px;
	--card-height: 135px;
	--card-gap-horizontal: 12.2px;

	--background-color-select: #1e1f21;
	--background-color-cards: #1e1f21;
	--text-selects-colors: #7d7e81;
	--text-inside-car-colors: #888888;
	--filled-progress-bar-color: #3f3e40;
	--background-color-card-none: #272a31;
	--buttonvalide-background-color: transparent;
	--buttonvalide-text-color: #6a6a6e;
	--buttonvalide-border-type: 1px;
	--btn-border-color: #888888; /* Border color same as text color in dark mode */
		--pagination-text-color: #ccc;
	--pagination-border-color: #444;
	--pagination-hover-bg-color: #555;
	--pagination-active-bg-color: #47c2ae;
	--pagination-active-text-color: #000;
}

#rsuro .container {
	display: flex;
	flex-wrap: wrap;

	gap: var(--card-gap-vertical) var(--card-gap-horizontal);

}

#rsuro .container > div {
	flex: 0 0 auto; /* Prevent flex items from growing or shrinking */
	margin: 0; /* Reset margin if needed */
}

#rsuro .card-custom {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-cards);
	
	text-align: center;
	flex-direction: column;
}

#rsuro .card-custom-none {
	width: var(--card-width);
	height: var(--card-height);
	border-radius: 8px;
	background-color: var(--background-color-card-none);

	text-align: center;
	flex-direction: column;
}

#red {
	border-left: 5px solid #ff4c4c; /* Left border in red */
}
#green {
	border-left: 5px solid #21b39f; /* Left border in red */
}
#orange {
	border-left: 5px solid #ea9765; /* Left border in red */
}

#yellow {
	border-left: 5px solid #f4af00; /* Left border in red */
}

#gray {
	border-left: 5px solid var(--filled-progress-bar-color); /* Left border in red */
}

#rsuro .card-header {
	font-size: 1.2rem;
	font-weight: 600;
	background-color: transparent;
	border: none;
	
}

#headertextolor {
	color: var(--text-inside-car-colors);
}

#nonetext {
	color: var(--filled-progress-bar-color);
}

#rsuro .col-auto {
	margin-left: -1.4%;
}
#rsuro .card-number {
	margin-top: -15%;
	font-size: 2.4rem; /* Reduced text size by 10% */
	font-weight: bold;
	margin-left: 29.5%;
	color: var(--text-inside-car-colors);
	background-color: transparent;
}

#rsuro .card-number-none {
	margin-top: -15%;
	font-size: 2.4rem; /* Reduced text size by 10% */
	font-weight: bold;
	margin-left: 44%;
	color: var(--filled-progress-bar-color);
	background-color: transparent;
}


#rsuro .card-subtext {
	font-size: 0.8rem; /* Slightly smaller for "KO 852" */
	font-weight: 500;
	color: var(--text-inside-car-colors);
	background-color: transparent;
	text-align: left; /* Align text to the left */
}

#rsuro .card-footer {
	font-size: 0.68rem; /* Reduced text size by 10% */
	color: var(--text-inside-car-colors);
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: transparent;
	margin-top: -5px;
	border: none;
}

#rsuro .card-footer-none {
	font-size: 0.68rem; /* Reduced text size by 10% */
	color: var(--filled-progress-bar-color);
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
	
}

#rsuro .progress-bar-custom {
	height: 4px;
	background-color: #515254;
	border-radius: 2px;
	width: 100%;
}

#rsuro .progress-bar-fill {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 48%;
}

#rsuro .progress-bar-none {
	height: 100%;
	background-color: var(--filled-progress-bar-color);
	border-radius: 2px;
	width: 0;
}
#rsuro .navbar {
	background-color: transparent;

}

#rsuro .navbar .form-select {
	height: 90%; /* Reduced by 10% */
	margin-right: 9px; /* Reduced by 10% */
	font-size: 90%; /* Reduced text size by 10% */
}

#rsuro .navbar .form-select.date-select {
	background-color: var(--background-color-select);
	border: none;
	width: 126px; /* Reduced by 10% */
	color: var(--text-selects-colors);
	font-size: 90%; /* Reduced text size by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .navbar .form-select.week-select {
	width: 153px; /* Reduced by 10% */
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%; /* Reduced text size by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .navbar .form-select.manager-select {
	width: 160px; /* Reduced by 10% */
	background-color: var(--background-color-select);
	border: none;
	color: var(--text-selects-colors);
	font-size: 90%; /* Reduced text size by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .navbar .btn {
	width: 90px; /* Reduced by 10% */
	height: 81%; /* Reduced by 10% */
}

#rsuro .btnicon {
	color: var(--text-selects-colors);
	background-color: var(--background-color-select);
	border: none;
	height: 90%; /* Reduced by 10% */
	margin-right: 9px; /* Reduced by 10% */
	border-radius: 9px; /* Reduced border-radius by 10% */
}


#rsuro .btn {
	color: var(--buttonvalide-text-color);
	background-color: var(--buttonvalide-background-color);
	border: var(--buttonvalide-border-type) solid var(--btn-border-color); /* Apply the border color */
	border-radius: 9px; /* Reduced border-radius by 10% */
}

#rsuro .select-wrapper {
	display: flex;
	align-items: center;
	background-color: var(--background-color-cards);
	border-radius: 9px;
	padding: 0 2px;
	margin-right: 9px; /* Reduced by 10% */
}

#rsuro .select-wrapper .bi {
	color: var(--text-selects-colors);
	margin-left: 1.5%;
}
#rsuro .select-wrapper .form-select {
	border: none;
	margin: 0;
	background: none; /* Remove individual backgrounds */
	box-shadow: none;
	border-radius: 0;
	width: auto;
}

#rsuro .start-date {
	border-top-left-radius: 9px; /* Round only the left corner */
	border-bottom-left-radius: 9px;
}

#rsuro .end-date {
	border-top-right-radius: 9px;
	/ border-bottom-right-radius: 9px;
}

#rsuro .cardscont {
	margin-left: -6.5%;
}



.pagination-container {
	display: flex;
	align-items: center;
	font-family: Arial, sans-serif;
	color: #666;
	font-size: 14px;
	margin-top: 20px;
	justify-content: center;
}

.pagination-container .pagination-label {
	margin-right: 10px;
}

.pagination {
	display: flex;
	gap: 4px;
}

.pagination a {
	color: #666;
	padding: 4px 10px;
	text-decoration: none;
	border: 1px solid #ddd;
	border-radius: 4px;
	transition: background-color 0.3s, color 0.3s;
}

.pagination a.disabled {
	color: #ccc;
	border-color: #f0f0f0;
	cursor: not-allowed;
}

.pagination a:hover:not(.disabled) {
	background-color: #f0f0f0;
}

.pagination .active {
	background-color: #47c2ae;
	color: white;
	border: 1px solid #47c2ae;
}

.pagination-info {
	margin-left: 10px;
	color: #666;
}

</style>



<div id="rsuro">
<nav class="navbar">
	<div class="container">
		<div class="d-flex align-items-center">
			<button class="btnicon btn-primary ms-auto">
				<i class="bi bi-geo-alt-fill" style="font-size: 1.3rem;"></i>
			</button>
			<div class="select-wrapper">
				<i class="bi bi-calendar-week" style="font-size: 1.2rem;"></i>
				<select class="form-select date-select start-date">
					<option selected>01/10/2024</option>
				</select>
				<span class="date-separator">-</span>
				<select class="form-select date-select end-date">
					<option selected>31/10/2024</option>
				</select>
			</div>

			<select class="form-select week-select me-2">
				<option selected>Toutes semaines</option>
			</select>
			<select class="form-select manager-select me-2">
				<option selected>Tous gestionnaires</option>
			</select>
			<button class="btn btn-primary ms-auto">Valider</button>
		</div>
	</div>
</nav>
<div class="container cardscont">
	<div class="row custom-row justify-content-center">
		<div class="col-auto">
			<div class="card-custom" id="red">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
			
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>190</span>
						<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="green">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
				
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="green">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
				
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="green">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
		
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="green">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
			
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>
	</div>


	<div class="row custom-row justify-content-center">
		<div class="col-auto">
			<div class="card-custom" id="green">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
			
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="yellow">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
			
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="yellow">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
				
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="yellow">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
		
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="yellow">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
				
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>
	</div>


	<div class="row custom-row justify-content-center">
		<div class="col-auto">
			<div class="card-custom" id="orange">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
				
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="orange">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
			
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom" id="orange">
				<div class="card-header" id="headertextolor">Arcachon</div>
				<div class="card-footer">
					<div class="card-number">413</div>
				
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer">
					<span>4.84%</span>
					<span>3</span>
				</div>
			</div>
		</div>
			<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Arcachon</div>
				<div class="card-footer">
					<div class="card-number-none">413</div>
				
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-fill"></div>
				</div>
				<div class="card-footer-none">
					<span>4.84%</span>
					<span>3</span>
					<span>3</span>
				</div>
			</div>
		</div>

	
		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>
	</div>


	<div class="row custom-row justify-content-center">
		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>
		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>
	</div>


	<div class="row custom-row justify-content-center">
		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>

		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>
		<div class="col-auto">
			<div class="card-custom-none" id="gray">
				<div class="card-header" id="nonetext">Blaye</div>
				<div class="card-footer">
					<div class="card-number-none">0</div>
				</div>
				<div class="progress-bar-custom">
					<div class="progress-bar-none"></div>
				</div>
				<div class="card-footer-none">
					<span>0%</span>
				</div>
			</div>
		</div>
	</div>


	
	




</div>
<div class="pagination-container">
	<span class="pagination-label">Pages:</span>
	<div class="pagination">
		<a href="#" class="disabled">First</a>
		<a href="#" class="disabled">Prev</a>
		<a href="#" class="active">1</a>
		<a href="#">2</a>
		<a href="#">3</a>
		<a href="#">4</a>
		<a href="#">5</a>
		<a href="#">Next</a>
		<a href="#">Last</a>
	</div>
	<span class="pagination-info">1 of 5</span>
</div>

</div><script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
