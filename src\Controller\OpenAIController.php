<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class OpenAIController extends AbstractController
{
    #[Route('/wiki', name: 'app_open_ai')]
    public function index(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {

        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }



        try {
            // Fetch user data
            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
           

    
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('open_ai/index.html.twig', [
            'controller_name' => 'OpenAIController',
            'user' => $userData,
     
   
        ]);
    }
}
