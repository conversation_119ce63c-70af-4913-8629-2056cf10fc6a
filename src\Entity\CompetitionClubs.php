<?php

namespace App\Entity;

use App\Repository\CompetitionClubsRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CompetitionClubsRepository::class)]
class CompetitionClubs
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $competitionId = null;

    #[ORM\Column(nullable: true)]
    private ?array $clubs = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCompetitionId(): ?string
    {
        return $this->competitionId;
    }

    public function setCompetitionId(?string $competitionId): static
    {
        $this->competitionId = $competitionId;

        return $this;
    }

    public function getClubs(): ?array
    {
        return $this->clubs;
    }

    public function setClubs(?array $clubs): static
    {
        $this->clubs = $clubs;

        return $this;
    }
}
