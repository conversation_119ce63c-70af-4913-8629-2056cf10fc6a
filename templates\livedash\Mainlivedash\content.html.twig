<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Analytics Dashboard</title>
		<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
		<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
		<style>:root
		{
			/* Couleurs par défaut (mode clair) */
			--bg-color: #fff;
			--text-color: #000;
			--header-bg: #fff;
			--header-text: #333;
			--card-bg: #fff;
			--card-text: #444;
			--accent-color: #539AF8;
			--border-color: #ddd;
			--scrollbar-thumb: #bbb;
			--scrollbar-track: #eee;
			--chiffre-color: #000;
			--header-span-color: #ddd;
			--pourcent-color: #555;
		}

		[data-theme="dark"] {
			/* Couleurs pour le mode sombre */
			--bg-color: #111;
			--text-color: #fff;
			--header-bg: #222;
			--header-text: #b0b0b0;
			--card-bg: #111;
			--card-text: #b0b0b0;
			--accent-color: #539AF8;
			--border-color: #444;
			--scrollbar-thumb: #2B2D31;
			--scrollbar-track: #555;
			--chiffre-color: #fff;
			--header-span-color: rgba(255, 255, 255, 0.6);
			--pourcent-color: #b0b0b0;
		}

		/* Application des couleurs dans vos styles */
		body {
			margin: 0;
			font-family: 'Roboto', sans-serif;
			background-color: var(--bg-color);
			color: var(--text-color);
		}

		.dashboard {
			display: flex;
			flex-direction: column;
			gap: 10px;
			margin-top: 50px;
			overflow-y: auto; /* Enable scrolling when content overflows */
			max-height: 95vh; /* Limit the height, adjust as needed */
			scrollbar-width: thin;
			scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
			font-family: 'Roboto', sans-serif;
		}

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			color: var(--header-text);
		}

		.header h1 {
			font-size: 1.5rem;
			color: var(--text-color);
		}

		.header button {
			padding: 5px 15px;
			border: 2px solid var(--border-color);
			color: var(--accent-color);
			background-color: var(--header-bg);
			border-radius: 5px;
			cursor: pointer;
			font-size: 0.8em;
			z-index: 100;
		}

		.map-section {
			background-color: var(--card-bg);
			padding: 10px -20px !important;
		}

		#map {
			width: 100%;
			height: 530px;
		}

		.stats-section {
			display: flex;
			flex-wrap: wrap; /* This allows the cards to wrap to the next line when necessary */
			gap: 14px;
			padding-left: 32px;
			justify-content: flex-start; /* Align cards to the left */
		}

		.stats-card {
			background-color: var(--card-bg);
			padding: 20px;
			border-radius: 8px;
			color: var(--card-text);
			width: 306px; /* Ensure a fixed width */
			border: 1px solid var(--border-color);
			height: 402px;
			overflow: hidden;
			margin-bottom: 20px; /* Adds space between rows */
		}

		.stats-cards {
			position: absolute;
			top: -263px;
			z-index: 2000; /* Make sure it is above the map */
			background-color: var(--card-bg);
			padding: 15px;
			border-radius: 8px;
			width: 626px;
			height: 262px;
			margin-right: 500px;
		}


		.stats-cards h2,
		.stats-card h2 {
			font-size: 1.2rem;
			color: var(--card-text);

		}

		.chart-container {
			margin-top: 15px;
		}

		.table-section {
			margin-top: 20px;
		}

		.table-section table {
			width: 100%;
			border-collapse: collapse;
		}

		.table-section td {
			padding: 8px;
			border-bottom: 1px solid var(--border-color);
		}

		.table-section td:first-child {
			font-weight: bold;
		}

		.rows {
			display: flex;
			justify-content: space-between; /* Ou center, space-around, etc. selon vos besoins */
			align-items: center; /* Pour centrer verticalement les éléments */
		}

		.p {
			font-size: 10px;
		}

		.source-item {
			display: flex;
			justify-content: space-between;
			padding: 3px 0;
			border-bottom: 1px solid var(--border-color);
		}

		.progress-bar-custom {
			width: 270px;
			height: 3px;
			background-color: var(--border-color);
		}

		.progress-bar-fill {
			height: 100%;
			background-color: var(--accent-color);
		}


		.u {
			width: 50%;
			margin: 20px auto;
			border: 1px solid #000;

		}

		.containers {
			position: relative; /* This makes the container the reference point for absolute positioning */
			padding-left: 32px;
		}
		.chiffre {
			flex: 1;
			color: var(--chiffre-color);
		}
		.header .span {
			color: var(--header-span-color)
		}
		.pourcent {
			color: var(--pourcent-color);
		}
		.textp {
			color: var(--chiffre-color);
			font-size: 24px;
		}

	</style>
</head>
<body>
	<div class="dashboard">
		<main>
			<div style="font-size: 14px; padding-left: 20px;">
				<strong>
					Firebase demo project</strong>
				<i class="bi bi-caret-down-fill"></i>
			</div>
			<br>
			<div class="header">
				<div style="font-size: 20px; padding-left: 20px;">
					<i style="color: #ffa726;" class="bi bi-reception-4"></i>
					Analytics
					<span style="; font-size: 16px;">
						| Realtime</span>
				</div>
				<button>Afficher plus de données dans Google Analytics
					<i class="bi bi-box-arrow-up-right"></i>
				</button>
			</div>
			<br>
			<!-- Map Section -->
			<section class="map-section">

				<div id="map"></div>
				<div style="position: inherit;">
					<div class="containers">

						<div class="stats-cards">
							<div style="color: #b0b0b0; font-family: Arial, sans-serif;">
								<div style="display: flex; justify-content: space-between; font-size: 12px; text-transform: uppercase;">
									<div style="max-width: 310px; text-decoration: underline; font-size: 14px;">
										Utilisateurs actifs au cours des 30 dernières minutes
									</div>
									<div style="max-width: 310px; text-decoration: underline; font-size: 14px;">
										Utilisateurs actifs au cours des 30 dernières minutes
									</div>
								</div>
								<div style="display: flex; font-size: 30px; line-height: 1;">
									<div class="chiffre">360
										<div style=" color: #b0b0b0;  font-size: 12px; text-transform: uppercase; padding-right: 20px;">
											Utilisateurs actifs par minute
										</div>
									</div>


									<div class="chiffre">54</div>

								</div>

							</div>


							<div style=" ">
								<div style="width: 580px; margin-top: 18px" class="chart-container">
									<canvas style=" width: 580px; height: 130px;" id="users30MinutesChart"></canvas>
								</div>

							</div>
						</div>

					</div>
				</div>
			</section>
		</main>

		<div style=" margin-bottom:0rem;">
			<section style=" color: #b0b0b0;" class="stats-section">

				<div class="stats-card">
					<h2>Utilisateurs actifs par</h2>
					<h2>premiere source  Utilisateurs
						<i class="bi bi-caret-down-fill"></i>
					</h2>
					<h2 style="	font-weight: bold; ">#1 goole</h2>
					<div style="display: flex; align-items: center; justify-content:space-between;">
						<div>

							<br>
							<div style=" margin-left: 20px; margin-top: -30px;">
								<span class="textp">
									103</span>
								<br>
								<span class="pourcent">64,38%</span>
							</div>
						</div>
						<div style="width: 180px; height: 85px;    " class="chart-container">
							<canvas style="width: 180px; height: 85px;    " id="users30MinutesCharts"></canvas>
						</div>
					</div>

					<div class="source-list">
						<div class="source-item pourcent">

							<u>premiere source</u>
							<u>Utilisateurs</u>
						</div>
						<div class="source-item">
							<span>google</span>
							<span>103</span>
						</div>
						<div class="progress-bar-custom">
							<div class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>(direct)</span>
							<span>49</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 50%;" class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>google-play</span>
							<span>8</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 20%;" class="progress-bar-fill"></div>
						</div>
					</div>

					<!-- Footer Section -->
					<footer style="margin-top: 15px; text-align:end; ">
						<div>1 - 6 sur 13
							<i style="color: #444;" class="bi bi-chevron-left"></i>
							<i class="bi bi-chevron-right"></i>
						</div>
					</footer>
				</div>


				<div class="stats-card">
					<h2>Utilisateurs actifs
						<i class="bi bi-caret-down-fill"></i>
						par Audience</h2>

					<h2 style="	font-weight: bold; ">#1 All Users</h2>
					<div style="display: flex; justify-content:space-between;">
						<div>

							<div style=" margin-left: 20px; margin-top: 10px ;">
								<span class="textp">
									315</span>

								<span class="pourcent">
									64,38%</span>
							</div>
						</div>
						<div style="width: 180px; height: 80px;    " class="chart-container">
							<canvas style="width: 180px; height: 80px;     " id="users30MinutesChartes"></canvas>
						</div>
					</div>


					<div class="source-list">
						<div class="source-item">
							<u>Audience</u>
							<u>Utilisateurs</u>


						</div>
						<div class="source-item pourcent">
							<span>ALL USERS</span>
							<span>315</span>


						</div>
						<div class="progress-bar-custom">
							<div class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>7 day unnotified users</span>
							<span>313</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 50%;" class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>likely 7 day purchas</span>
							<span>136</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 20%;" class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>likely 7 day purchas</span>
							<span>136</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 20%;" class="progress-bar-fill"></div>
						</div>


						<!-- Footer Section -->
						<footer style="margin-top: 5px; text-align:end; ">
							<div>1 - 6 sur 13
								<i style="color: #444;" class="bi bi-chevron-left"></i>
								<i class="bi bi-chevron-right"></i>
							</div>
						</footer>
					</div>
				</div>
				<div class="stats-card">
					<h2>vues  par</h2>
					<h2>titre de la page et le nom</h2>
					<h2 style="	font-weight: bold; ">#1 All flood-lt!</h2>
					<div style="display: flex; align-items: center; justify-content:space-between;">
						<div>


							<div style="margin-left: 20px; ">
								<span class="textp">8</span>
								<br>
								<span class="pourcent">
									100%</span>
							</div>
						</div>
						<div style="width: 180px; height: 80px;     " class="chart-container">
							<canvas style="width: 180px; height: 80px;     " id="users30MinutesChartees"></canvas>
						</div>
					</div>


					<div class="source-list">
						<div class="source-item pourcent">
							<u>titre de la page</u>
							<u>vues</u>


						</div>
						<div class="source-item">
							<span>flood-lt</span>
							<span>8</span>


						</div>
						<div class="progress-bar-custom">
							<div class="progress-bar-fill"></div>
						</div>

					</div>
					<footer style="margin-top: 15px; text-align:end; ">
						<div>1 - 6 sur 13
							<i style="color: #444;" class="bi bi-chevron-left"></i>
							<i class="bi bi-chevron-right"></i>
						</div>
					</footer>
				</div>


				<div class="stats-card">
					<h2>Utilisateurs actifs par</h2>
					<h2>premiere source  Utilisateurs
						<i class="bi bi-caret-down-fill"></i>
					</h2>
					<h2 style="	font-weight: bold; ">#1 goole</h2>
					<div style="display: flex; align-items: center; justify-content:space-between;">
						<div>

							<br>
							<div style=" margin-left: 20px; margin-top: -30px;">
								<span class="textp">
									103</span>
								<br>
								<span class="pourcent">64,38%</span>
							</div>
						</div>
						<div style="width: 180px; height: 85px;    " class="chart-container">
							<canvas style="width: 180px; height: 85px;    " id="users30MinutesCharts"></canvas>
						</div>
					</div>

					<div class="source-list">
						<div class="source-item pourcent">

							<u>premiere source</u>
							<u>Utilisateurs</u>
						</div>
						<div class="source-item">
							<span>google</span>
							<span>103</span>
						</div>
						<div class="progress-bar-custom">
							<div class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>(direct)</span>
							<span>49</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 50%;" class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>google-play</span>
							<span>8</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 20%;" class="progress-bar-fill"></div>
						</div>
					</div>

					<!-- Footer Section -->
					<footer style="margin-top: 15px; text-align:end; ">
						<div>1 - 6 sur 13
							<i style="color: #444;" class="bi bi-chevron-left"></i>
							<i class="bi bi-chevron-right"></i>
						</div>
					</footer>
				</div>


				<div class="stats-card">
					<h2>Utilisateurs actifs
						<i class="bi bi-caret-down-fill"></i>
						par Audience</h2>

					<h2 style="	font-weight: bold; ">#1 All Users</h2>
					<div style="display: flex; justify-content:space-between;">
						<div>

							<div style=" margin-left: 20px; margin-top: 10px ;">
								<span class="textp">
									315</span>

								<span class="pourcent">
									64,38%</span>
							</div>
						</div>
						<div style="width: 180px; height: 80px;    " class="chart-container">
							<canvas style="width: 180px; height: 80px;     " id="users30MinutesChartes"></canvas>
						</div>
					</div>


					<div class="source-list">
						<div class="source-item">
							<u>Audience</u>
							<u>Utilisateurs</u>


						</div>
						<div class="source-item pourcent">
							<span>ALL USERS</span>
							<span>315</span>


						</div>
						<div class="progress-bar-custom">
							<div class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>7 day unnotified users</span>
							<span>313</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 50%;" class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>likely 7 day purchas</span>
							<span>136</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 20%;" class="progress-bar-fill"></div>
						</div>
						<div class="source-item">
							<span>likely 7 day purchas</span>
							<span>136</span>
						</div>
						<div class="progress-bar-custom">
							<div style="width: 20%;" class="progress-bar-fill"></div>
						</div>


						<!-- Footer Section -->
						<footer style="margin-top: 5px; text-align:end; ">
							<div>1 - 6 sur 13
								<i style="color: #444;" class="bi bi-chevron-left"></i>
								<i class="bi bi-chevron-right"></i>
							</div>
						</footer>
					</div>
				</div>
				<div class="stats-card">
					<h2>vues  par</h2>
					<h2>titre de la page et le nom</h2>
					<h2 style="	font-weight: bold; ">#1 All flood-lt!</h2>
					<div style="display: flex; align-items: center; justify-content:space-between;">
						<div>


							<div style="margin-left: 20px; ">
								<span class="textp">8</span>
								<br>
								<span class="pourcent">
									100%</span>
							</div>
						</div>
						<div style="width: 180px; height: 80px;     " class="chart-container">
							<canvas style="width: 180px; height: 80px;     " id="users30MinutesChartees"></canvas>
						</div>
					</div>


					<div class="source-list">
						<div class="source-item pourcent">
							<u>titre de la page</u>
							<u>vues</u>


						</div>
						<div class="source-item">
							<span>flood-lt</span>
							<span>8</span>


						</div>
						<div class="progress-bar-custom">
							<div class="progress-bar-fill"></div>
						</div>

					</div>
					<footer style="margin-top: 15px; text-align:end; ">
						<div>1 - 6 sur 13
							<i style="color: #444;" class="bi bi-chevron-left"></i>
							<i class="bi bi-chevron-right"></i>
						</div>
					</footer>
				</div>

			</section>

		</div>
	</div>

	<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>


	<script>

		// Initialize Map
const map = L.map('map').setView([
20, 0
], 2);

// Define inverted and dark tile layers
const tileLayerInverted = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {attribution: '&copy; OpenStreetMap contributors, &copy; CartoDB'});

const tileLayerDark = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {attribution: '&copy; OpenStreetMap contributors, &copy; CartoDB'});

// Add the default inverted layer
tileLayerInverted.addTo(map);

// Marker data
const markers = [
{
coords: [
48.8566, 2.3522
],
label: "Paris"
}, {
coords: [
40.7128, -74.006
],
label: "New York"
}, {
coords: [
35.6895, 139.6917
],
label: "Tokyo"
}
];

// Add markers to the map
markers.forEach(marker => {
L.marker(marker.coords).addTo(map).bindPopup(marker.label);
});

// Toggle dark mode
function toggleDarkMode() {
const isChecked = document.getElementById('chk').checked;
if (isChecked) {
map.removeLayer(tileLayerInverted);
tileLayerDark.addTo(map);
document.body.classList.add('dark-mode');
} else {
map.removeLayer(tileLayerDark);
tileLayerInverted.addTo(map);
document.body.classList.remove('dark-mode');
}
}

// Event listener for the checkbox
document.getElementById('chk').addEventListener('change', toggleDarkMode);

// Users in the Last 30 Minutes Chart
const users30MinutesCtx = document.getElementById('users30MinutesChart').getContext('2d');
new Chart(users30MinutesCtx, {
type: 'bar',
data: {
labels: Array.from(
{
length: 30
},
(_, i) => `-${
30 - i
} `
), // Création des labels
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 30
},
() => Math.floor(Math.random() * 100)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 0
}
]
},
options: {
responsive: true, // Chart responsive
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Cacher les ticks sur l'axe Y
},
grid: {
display: false // Cacher les lignes de la grille Y
}
},
x: {
ticks: {
display: true, // Assurez-vous que les ticks sont affichés
font: {

size: 14, // Taille de police lisible
style: 'normal', // Style normal (pas en italique)
weight: '400', // Poids de police normal
lineHeight: 1.5 // Hauteur de ligne
},


// Utiliser maxRotation et minRotation pour garantir des labels horizontaux
maxRotation: 0, // Pas de rotation maximum
minRotation: 0 // Pas de rotation minimum
},
grid: {
display: false // Cacher les lignes de la grille X
}
}
},
plugins: {
legend: {
display: false // Cacher la légende
}
}
}
});
	</script>
	<script>
		// Users in the Last 30 Minutes Chart
const users30MinutesCtxz = document.getElementById('users30MinutesCharts').getContext('2d');
new Chart(users30MinutesCtxz, {
type: 'bar',
data: {
labels: Array.from(
{
length: 20
},
(_, i) => ` `
),
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 20
},
() => Math.floor(Math.random() * 100)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 1
}
]
},
options: {
responsive: false,
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Set to true to display y-axis ticks
},
grid: {
display: false // Hide grid lines
}
},
x: {
ticks: {
display: true // Set to true to display x-axis ticks
},
grid: {
display: false // Hide grid lines
}
}
},
plugins: {
legend: {
display: false // Hide legend
},
// Ensure smilePlugin is properly defined if used
smilePlugin: {
display: false
}
}
}
});
const users30MinutesCtxzs = document.getElementById('users30MinutesChartes').getContext('2d');
new Chart(users30MinutesCtxzs, {
type: 'bar',
data: {
labels: Array.from(
{
length: 20
},
(_, i) => ` `
),
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 20
},
() => Math.floor(Math.random() * 100)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 1
}
]
},
options: {
responsive: false,
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Set to true to display y-axis ticks
},
grid: {
display: false // Hide grid lines
}
},
x: {
ticks: {
display: true // Set to true to display x-axis ticks
},
grid: {
display: false // Hide grid lines
}
}
},
plugins: {
legend: {
display: false // Hide legend
},
// Ensure smilePlugin is properly defined if used
smilePlugin: {
display: false
}
}
}
});
const users30MinutesCtxzes = document.getElementById('users30MinutesChartees').getContext('2d');
new Chart(users30MinutesCtxzes, {
type: 'bar',
data: {
labels: Array.from(
{
length: 5
},
(_, i) => ` `
),
datasets: [
{
label: 'Utilisateurs actifs',
data: Array.from(
{
length: 5
},
() => Math.floor(Math.random() * 50)
),
backgroundColor: '#539AF8',
borderColor: 'rgba(66, 133, 244, 1)',
borderWidth: 1
}
]
},
options: {
responsive: false,
scales: {
y: {
beginAtZero: false,
ticks: {
display: false // Set to true to display y-axis ticks
},
grid: {
display: false // Hide grid lines
}
},
x: {
ticks: {
display: true // Set to true to display x-axis ticks
},
grid: {
display: false // Hide grid lines
}
}
},
plugins: {
legend: {
display: false // Hide legend
},
// Ensure smilePlugin is properly defined if used
smilePlugin: {
display: false
}
}
}
});
	</script>
</body></html>
