
<style>
.statDash,.ChiffreDash {
    font-family: Consolas, monospace !important;
	color:var(--tree-view-color, #000) !important;
}
.ChiffreDash{
	height: 90px;
	    display: flex;
}

.Chiffre<PERSON>hart{
		height: 70px;
    width: 110px;
    border: 1px solid var(--border-fieldSpan);
    /* color: var(--chart-spans); */
    background: var(--Mainchart-background);
    margin: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    margin-right: 0;
    margin-left: 2px;
	cursor:pointer;

}

.ChiffreChartMeto{
	    height: 70px;
    width: 200px;
    border: 1px solid #3e4e54;
    /* color: var(--chart-spans); */
    background: var(--Mainchart-background);
    margin: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    justify-content: space-between;

}
.statDashcard-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px; /* Espacement entre les cartes */
}

.statDashcards {
    flex: 1 1 calc(50% - 10px); /* 50% de la largeur avec un petit espace entre les cartes */
    box-sizing: border-box;
}

.cpuUtilizationChart{
	position: static;
    margin-top: 10px;
    margin-bottom: 8px;
    box-sizing: border-box;
    height: 48%;
    justify-self: center;
    width: 165px;
}
.chartDays{
   width: 250px;
    height: 110px;
    border: 1px solid var(--border-fieldSpan);
    background: var(--Mainchart-background);
    padding: 5px;
    border-radius: 6px;
    }
    .annotationChart{
        margin-left: 93px;
        position: relative;
        top: -22px;
    }

.CardWithAction:hover{
	background-color: var(--active-field-bg);
    border: 1px solid var(--coloredTextPrimary);
}


    #bottomPanelko {
		position: fixed;
		bottom: -335px;
		left: 315px;
		width: 100%;
		height: 335px;
		background: var(--GraphePanel);
		box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.2);
		transition: bottom 0.3s ease-in-out;
		display: flex;
		z-index: 10;
		gap: 20px;
		padding: 18px;
		padding-left: 18px;
	    background: var(--Mainchart-background);
    }

    #bottomPanelko.active {
        bottom: 0; /* Affiché */
    }

     #closeBtn {
        position: absolute;
        top: 10px;
        left: 10px;
        background: transparent;
        color: white;
        border: none;
        padding: 5px 10px;
        cursor: pointer;
        font-size: 16px;
        border-radius: 3px;
    }

    #closeBtn:hover {
        background: darkred;
    }

.progress-containerchart {
    max-width: 100%;
    display: flex;
    flex-direction: column;
    height: 250px; 
    position: relative;
}
.titlefloating-container{
    display: flex;
    flex-direction: column;
    height: 15%; 
    position: relative;
}


.custom_floating-titledetailsmotifs {
    position: absolute;
    font-size: 1.2em; 
    color: var(--color-bartext-light);
    margin: 0 0 10px 0; 
    pointer-events: none;
    z-index: 1000;
}

.progress-bar-wrapper {
    flex: 1;
    height: 14px; 
    background-color: transparent; 
    border-radius: 8px; 
    margin-right: 5px; 
}

.progress-label {
    font-size: 10px; 
    font-weight: bold;
    white-space: nowrap;
    text-align: right; 
    width: 100%; 
}

#chart_div {
    width: 90%; 
    height: 100%; 
    display: flex; 
    margin-left: 0; 
}

.google-visualization-tooltip {
    text-align: right; 
}

text {
    text-anchor: end; 
    font-size: 10px; 
}

.container-wrapper2 {
    margin-top: 30%; 
}

.floating-title {
    padding: 3px 15px;
    border-radius: 5px;
    font-size: 15px;
}
    .detailsmotif-section {
        width: 230px; 
        background: linear-gradient(to bottom,  #abdef2,  #e7f5fe); 
        border-radius: 10px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        padding: 0; 
    }
</style>

<div class="ChiffreDash ventes" style="display:none;">
	<div class="ToggleFullScreenChartPanel" onclick="FullScreenPanelBottom()">
		<i class="bi bi-arrows-fullscreen"></i>
	</div>
	<div class="ChiffreChart CardWithAction" style="cursor:pointer;" onclick="DisplayAllProductionsForOnedays('Allventes')">
		<span style="font-size: 14px;">Ventes</span>
		<span class="totalventes" style="font-size: 20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart CardWithAction" onclick="DisplayProductionsObjectifTable()">
		<span style="font-size:14px;">objectif</span>
		<span class="card-number ttobjectif"style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart " onclick="DisplayProductionsObjectifTable()">
		<span style="font-size:14px;">R/O A DATE</span>
		<span class="card-number VenteObjectif"style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart" style="     padding: 0;width:220px;">
		{% include 'geomap/production/heartBeat.html.twig' %}
	</div>
	<div class="ChiffreChart CardWithAction" onclick="DisplaysatisfactionClientsDetails()">
		<span style="font-size:14px;">T.P</span>
		<span class="card-number TauxPenetration"style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart CardWithAction" onclick="DisplaysatisfactionClientsDetails()">
		<span style="font-size:14px;">Note Sat.</span>
		<span class="card-number MoyGeneralVente"style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart">
		<span style="font-size:14px;">Nb vendeur</span>
		<span class="card-number Nbvendeur"style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart">
		<span style="font-size:14px;">etp</span>
		<span class="card-number etp"style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart CardWithAction" onclick="DisplayAllProductionsForOnedays('ventesKO');togglePanelko()">
		<span style="font-size:14px;">ventes ko</span>
		<span class="card-number ttventesko" style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart">
		<span style="font-size:14px;">projection</span>
		<span class="card-number projection"style="justify-self: center;font-size:20px;color: var(--coloredTextPrimary);"></span>
	</div>
	<div class="ChiffreChart" >
		<span style="font-size: 14px;">villeSup 100jrs</span>
		<span class="nbrVilleSupCentJrs" style="font-size: 20px;color: var(--coloredTextPrimary);"></span>
	</div>
</div>
<div class="statDash">
	<div class=""style="gap: 5px;display: flex;flex-direction: column;" >
		<div class="PanelChartUpDown salesdashboard2-row salesdashboard2-row2"> 
			<div class="custom-progress-container">
				<div class="custom-progress-row">
					<div class="type-annulation-text">ventes</div>
					<!-- Xap RDV Section -->
					<div style="display: flex; align-items: center;justify-content: space-around;">
					<span style="font-size: 18px;color: var(--chart-spans2); display: none" id="totalVenteCluter" class="totalVenteCluter"></span>
					<select id="categorySelect" style="display: none" >
					</select>
					</div>
					<div class="chart-contentVente" style="height: 90px;">
						<div style="display:flex;width: 100%;height: 80%;">
							<div id="audienceChartStat" class="audienceChart" style="width: 100%; height: 100%;"></div>
						</div>
					</div>
				</div>
			</div>
	
		</div>
		<div class="PanelChartUpDown   salesdashboard2-row salesdashboard2-row3">
			<div class="containermotifechec">
				<div class="motifs-echec-title">Par parcours</div> 
				<!-- Progress Bar for Pb Vente/Client -->
			<div class="list-container">
				<div class="statistic">
					<div class="statistic-content">
						<div class="stat-title">CONQUETE</div>
						<div class="stat-number" id="totalsommeCONQUETE"></div>
					</div>
					<div class="progress-container">
						<div class="progress-bar" id="progress-bar-CONQUETE" style="width: 100%;"></div>
					</div>
				</div>
				<div class="statistic">
					<div class="statistic-content">
						<div class="stat-title">MIGRATION</div>
						<div class="stat-number" id="totalsommeMIGRATION"></div>
					</div>
					<div class="progress-container">
						<div class="progress-bar" id="progress-bar-MIGRATION" style="width: 80%;"></div>
					</div>
				</div>
					<div class="statistic">
					<div class="statistic-content">
						<div class="stat-title">MOBILES</div>
						<div class="stat-number" id="totalsommeMOBILES"></div>
					</div>
					<div class="progress-container">
						<div class="progress-bar" id="progress-bar-MOBILES" style="width: 80%;"></div>
					</div>
				</div>
			</div>
			</div>
		</div>
	</div>
<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 2px;">
    <div class="PanelChartUpDown    statDashcard" style=" padding:0px">
        <div class="chart-container-time">
            <canvas id="graph_ventes_par_mois"></canvas>
        </div>
    </div>
	<div class="PanelChartUpDown   statDashcard" style="">
        <div class="chart-container-time">
            <canvas id="graph_ventes_par_heure"></canvas>
        </div>
    </div>
    <div class="PanelChartUpDown   statDashcard" style="">
        <div class="chart-container-time">
            <canvas id="graph_ventes_par_semaine"></canvas>
        </div>
    </div>
    <div class="PanelChartUpDown   statDashcard" style="">
        <div class="chart-container-time">
            <canvas id="graph_ventes_par_jour"></canvas>
        </div>
    </div>
    
</div>	
	<div class="ContainerChartCards">
		<div class="PanelChartUpDown   chartsCard">
			<div class="par-techno"> {% include 'geomap/dashbordItems/partechno.html.twig' %} </div>
		</div>
		<div class="PanelChartUpDown   chartsCard" >
			<div class="par-gamme"> {% include 'geomap/dashbordItems/pargamme.html.twig' %} </div>
		</div>
		<div class="PanelChartUpDown   chartsCard" >
			<div class="par-parcours"> {% include 'geomap/dashbordItems/parparcours.html.twig' %} </div>
		</div>
		<div class="PanelChartUpDown   chartsCard" >
			<div class="par-status-preco"> {% include 'geomap/dashbordItems/parstatuspreco.html.twig' %} </div>
		</div>
	</div>



<div class="blocMotifEchec "style="gap: 5px; display: flex; flex-direction: row;">
    <!-- TOP Clusters -->
    <div class="PanelChartUpDown   salesdashboard2-row3"style="; align-items: center;" >
        <div class="containermotifechec">
            <div id="top-clusters"style="color: var(--coloredTextPrimary);" class="list-container"></div>
        </div>
    </div>

    <!-- FLOP Clusters -->
    <div class="PanelChartUpDown   salesdashboard2-row3" style=" align-items: center;"  >
        <div class="containermotifechec">
            <div id="flop-clusters" style="color: #fe8b00;" class="list-container"></div>
        </div>
    </div>
</div>


<div class="ContainerCohorteCards" >
		<div class="PanelChartUpDown   salesdashboard2-row salesdashboard2-row2"> 
			<div class="custom-progress-container">
				<div class="custom-progress-row" style="gap: 0px;">
					<div class="type-annulation-text">cohorte R</div>
					<!-- Xap RDV Section -->
					<div style="display: flex; align-items: center;justify-content: space-around;">
					<span style="font-size: 18px;color: var(--chart-spans2); display: none" id="totalVenteCluter" class="totalVenteCluter"></span>
					<select id="categorySelect" style="display: none" >
					</select>
					</div>
					<div class="chart-content" style="height:90px;">
						<div style="display:flex;width: 100%;height: 100%;margin-top:0px;">
							<div class="audienceChart" style="width: 100%; height: 100%;"><canvas id="audienceChartStatcohorter"></canvas></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="PanelChartUpDown   salesdashboard2-row salesdashboard2-row2"> 
			<div class="custom-progress-container">
				<div class="custom-progress-row" style="gap: 0px;">
					<div class="type-annulation-text">cohorte V</div>
					<!-- Xap RDV Section -->
					<div style="display: flex; align-items: center;justify-content: space-around;">
					<span style="font-size: 18px;color: var(--chart-spans2); display: none" id="totalVenteCluter" class="totalVenteCluter"></span>
					<select id="categorySelect" style="display: none" >
					</select>
					</div>
					<div class="chart-content"style="height:90px;" >
						<div style="display:flex;width: 100%;height: 100%;margin-top:0px;">
							<div class="audienceChart" style="width: 100%; height: 100%;"> <canvas id="audienceChartStatcohortev"></canvas></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		</div>
      	<div class="PanelChartUpDown   chartDays">
		<canvas id="cpuUtilizationChart" class="cpuUtilizationChart"></canvas>
		<span class="annotationChart"></span>
		</div>
	</div>

</div>
<script >

    function togglePanelko() { 
        const panelko = document.getElementById("bottomPanelko");
        panelko.classList.toggle("active");
    }
</script>
