<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class ProfileController extends AbstractController
{
    #[Route('/profil', name: 'app_profile')]
    public function index(): Response
    {
        return $this->render('profile/index.html.twig', [
            'controller_name' => 'ProfileController',
        ]);
    }

    #[Route('/settings/profil', name: 'app_profile')]
    public function profile(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
    
        $jwt = $session->get('jwt');
    
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
    
        try {
            $response = $httpClient->request('GET', 'http://api.nomadcloud.fr/api/users/5', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }
    
    
        return $this->render('profile/index.html.twig', [
            'controller_name' => 'ProfileController',
            'user' => $userData,
            'profile' => true,
            'compte' => false,
            'Confidentialite' => false,
        ]);
    }
    #[Route('/settings/account', name: 'app_profile_compte')]
    public function profileCompte(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        // Get JWT token from the session
        $jwt = $session->get('jwt');
    
        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }
    
        try {
            // Fetch user data using GET request
            $response = $httpClient->request('GET', 'http://api.nomadcloud.fr/api/users/5', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
 
            // Check if the form is submitted to update data
            if ($request->isMethod('POST')) {
                // Get modified data from the form

                $modifiedData = [
                    'nom' => $request->request->get('nom', $userData['nom']),
                    'prenom' => $request->request->get('prenom', $userData['prenom']),
                    'email' => $request->request->get('email', $userData['email']),
                ];
                
    
                // Send PATCH request to update user data
                $patchResponse = $httpClient->request('PATCH', 'http://api.nomadcloud.fr/api/users/5', [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $jwt,
                        'Content-Type' => 'application/merge-patch+json',  // Correct content type
                        'accept' => 'application/ld+json',  // Accept header as per curl example
                    ],
                    'json' => $modifiedData,  // Send the data as JSON
                ]);
    
                // Check the response for success
                if ($patchResponse->getStatusCode() === 200) {
                    $this->addFlash('success', 'Account successfully updated!');
                } else {
                    $this->addFlash('error', 'Failed to update the account. Response: ' . $patchResponse->getStatusCode());
                }
    
                // Redirect back to the profile page
                return $this->redirectToRoute('app_profile_compte');
            }
    
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching or updating data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }
    
        // Render the profile page with the user data
        return $this->render('profile/index.html.twig', [
            'controller_name' => 'ProfileController',
            'user' => isset($userData) ? $userData : [],
            'profile' => false,
            'compte' => true,
            'Confidentialite' => false,
        ]);
    }
    
    
    
    #[Route('/profile/confidentialite', name: 'app_profile_Confidentialite')]
    public function profileConfidentialite(HttpClientInterface $httpClient, SessionInterface $session, Request $request): Response
    {
        // Get JWT token from the session
        $jwt = $session->get('jwt');

        if (!$jwt) {
            return $this->redirectToRoute('app_login');
        }

        try {

            $response = $httpClient->request('GET', 'https://api.nomadcloud.fr/api/users/parent/all?page=1', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $jwt,
                ]
            ]);
            $userData = json_decode($response->getContent(), true);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error fetching data: ' . $e->getMessage());
            error_log('Error: ' . $e->getMessage());
        }

        return $this->render('profile/index.html.twig', [
   
            'controller_name' => 'ProfileController',
            'user' => $userData,
            'profile' => false,
            'compte' => false,  // This variable must be set
            'Confidentialite' => true,
        ]);
    }

}
