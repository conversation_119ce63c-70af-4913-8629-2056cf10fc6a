
<link rel="stylesheet" href="{{ asset('styles/openAI.css') }}">

<div class="container">

    <div class="content" style="max-width: 980px;"> 
        <h1>Structured Outputs</h1>
        <p style="font-size: 18px;">Ensure responses follow JSON Schema for Structured Outputs.</p>

        <h2 id="examples">Try it out</h2>
        <p>Try it out in the
            <a href="https://platform.openai.com/playground">Playground</a>
            or generate a ready-to-use schema definition to experiment with structured outputs.</p>
        <a href="#" class="button">Generate</a>
        <br/><br/>

        <h2 id="introduction">Introduction</h2>
        <p>JSON is one of the most widely used formats in the world for applications to exchange data.</p>
        <p>Structured Outputs is a feature that ensures the model will always generate responses that adhere to your supplied JSON Schema, 
		so you don't need to worry about the model omitting a required key, or hallucinating an invalid enum value.
            
            Some benefits of Structed Outputs include:</p>

        <ul class="list">
            <li>
                <span>1</span>
                <div style="font-weight: 600;">Reliable type-safety:</div>
                No need to validate or retry incorrectly formatted responses.
            </li>
            <li>
                <span>2</span>
                <div style="font-weight: 600;">Explicit refusals:</div>
                Safety-based model refusals are now programmatically detectable.
            </li>
            <li>
                <span>3</span>
                <div style="font-weight: 600;">Simpler prompting:</div>
                No need for strongly worded prompts to achieve consistent formatting.
            </li>
        </ul>
        <p>In addition to supporting JSON Schema in the REST API, the OpenAI SDKs for Python and JavaScript
		 also make it easy to define object schemas using Pydantic and Zod respectively. Below, you can see how to extract information 
		 from unstructured text that conforms to a schema defined in code.</p>
        
        <div class="card">
            <div class="card-header">
                Getting a structured response
                <div class="menu-button">python ▾
                    <i class="bi bi-copy"></i>
                </div>
            </div>
            <div class="code-container">
                <pre>
<span class="keyword">from</span> pydantic <span class="keyword">import</span> BaseModel
<span class="keyword">from</span> openai <span class="keyword">import</span> OpenAI

client = OpenAI()

<span class="keyword">class</span> <span class="class-name">CalendarEvent</span>(BaseModel):
    <span class="attr-name">name</span>: <span class="type">str</span>
    <span class="attr-name">date</span>: <span class="type">str</span>
    <span class="attr-name">participants</span>: <span class="type">list[str]</span>
                </pre>
            </div>
        </div>
		</br>
       <h2 id="how-to-use"> How to use Structured Outputs with response_format</h2>
	   <p>You can use Structured Outputs with the new SDK helper to parse the model's output into your desired format, or you can specify the JSON schema directly.


		<h2>Refusals with Structured Outputs</h2>
		<p>When using Structured Outputs with user-generated input, OpenAI models may occasionally refuse to fulfill the request for safety reasons. Since a refusal does not necessarily follow the schema you have supplied in response_format, the API response will include a new field called refusal to indicate that the model refused to fulfill the request.

When the refusal property appears in your output object, you might present the refusal in your UI, or include conditional logic in code that consumes the response to handle the case of a refused request.

</p>
</p>
    </div>

    <div class="sidbars" >
        <div class="progress-bar-custom" style="margin-right: 20px;">
            <div class="progress-bar-fill"></div>
        </div>
        <ul  style="margin-right: 20px;">
            <li>
                <a href="#examples" class="menu-link">Introduction</a>
            </li>
            <li >
                <a href="#" class="menu-link">Examples</a>
            </li>
            <li >
                <a href="#how-to-use" class="menu-link.active ">How to use</a>
            </li>
            <li >
                <a href="#supported-schemas" class="menu-link">Supported schemas</a>
            </li>
            <li>
                <a href="#json-mode" class="menu-link">JSON mode</a>
            </li>
        </ul>
    </div>
</div>

<script>
// Smooth Scroll and Active State on Click
menuLinks.forEach(link => {
    link.addEventListener("click", function (e) {
        e.preventDefault();
        const targetId = this.getAttribute("href").substring(1);
        const targetElement = document.getElementById(targetId);

        // Scroll with an offset to account for the navbar height
        window.scrollTo({
            top: targetElement.offsetTop - navbarHeight,  // Offset by navbar height
            behavior: "smooth"
        });

        // Update the progress bar height
        const targetSection = document.getElementById(targetId);
        updateProgressBarForSection(targetSection);

        menuLinks.forEach(link => link.classList.remove("active"));
        this.classList.add("active");
    });
});

// Update Progress Bar Based on Section
function updateProgressBarForSection(section) {
    const sectionTop = section.offsetTop;
    const sectionHeight = section.offsetHeight;
    const scrollPosition = window.scrollY;

    const progressPercentage = ((scrollPosition - sectionTop) / sectionHeight) * 100;
    progressBar.style.height = Math.min(Math.max(progressPercentage, 0), 100) + "%";
}

// Progress Bar Update on Scroll
window.addEventListener('scroll', function () {
    let scrollTop = document.documentElement.scrollTop;
    let docHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    let scrollPercentage = (scrollTop / docHeight) * 100;
    updateProgressBar(scrollPercentage);
});
</script>

