{% extends 'base.html.twig' %}

{% block body %}
<style>
  
    .container-fluid, .main-content, .chat-area, .table-area {
        background-color: transparent; 
    }

    .container-fluid {
        display: flex;
        height: 100vh; 
        width: 100vw; 
        margin: 0;
        padding: 0;
    }

 .left-block {
    transition: width 0.5s ease-in-out; 
    width: 315px; 
    overflow: hidden; 
}




</style>
<body data-theme="light">
    <div class="container-fluid d-flex" style="margin: 0; padding: 0; min-height: 100vh;">
        {# <div class="sidebar">
            {% include 'dashMap/LeftSideBar/content.html.twig' %}
        </div> #}

        <div id="leftSidebar" class="left-block col-auto col-md-3 col-xl-2 px-sm-2 px-0">
            <div class="content">
                {% include 'dashMap/LeftSideBar2/content.html.twig' %}
            </div>
            {# <div class="bottombar footer">
                {% include 'dashboard/bottombar/content.html.twig' %}
            </div> #}
        </div>

        <div class="main-content">
            <div class="right-block col-24">
                <div class="topbar topbar-right-blcok">
                    {% include 'dashMap/Navbar/content.html.twig' %}
                </div>
                <div class="chat-areablock">
                    <div class="table-area" id="dynamic-content">
                        {% if showClusters %}
                            {% include 'clusters/clusterdash.html.twig' %}
                        {% elseif showVentes %}
                            {% include 'ventes/ventesdash.html.twig' %}
                        {% elseif showPrisesLivraison %}
                  
                        {% include 'LivraisonPrises/livraisonprises.html.twig' %}
                        {% elseif showClusterDetails %}
                       
                            {% include 'LivraisonPrises/details.html.twig' %}
                        {% elseif showClusterDetailsDataliste %}
                        
                            {% include 'LivraisonPrises/tabledetails.html.twig' %}
                        {% elseif showClusterDetailsRue %}
                       
                            {% include 'LivraisonPrises/livraisonrue.html.twig' %}
                        {% elseif showprisesArretcutest %}                      
                            {% include 'Arretcu/prisesArretcu.html.twig' %}
                        {% elseif prisesArretcuCluster %}                      
                            {% include 'Arretcu/prisesArretcuCluster.html.twig' %}
                             {% elseif showprisesArretcudetailleRue %}                      
                            {% include 'Arretcu/prisesArretcuClusterRue.html.twig' %}
                        {# {% elseif showVentesRsuro %}
                            {% include 'ventes/rsuro.html.twig' %} #}
                            {% elseif priseslivraisonmap %}
                            {% include 'map/priseslivraisonmap.html.twig' %}
                        {% else %}
                            {% include 'dashboard/MainBloc/content.html.twig' %}
                        {% endif %}
                    </div>

                    {# {% if showClusters %}
                        <div class="chat-area row">
                            <div id="right-sidebar-forms" class="right-sidebar-forms col-auto col-md-3 col-xl-2 px-sm-2 px-0 d-flex flex-column align-items-end" style="height: 100%; position: absolute; right: 0;"></div>
                        </div>
                    {% else %}
                        <div class="chat-area row"></div>
                    {% endif %} #}
                </div>
            </div>
        </div>
    </div>
</body>

<script>
    const checkbox = document.getElementById('chk');
    function applyTheme(theme) {
        if (theme === 'dark') {
            document.body.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');
        } else {
            document.body.setAttribute('data-theme', 'light');
            localStorage.setItem('theme', 'light');
        }
    }
    checkbox.addEventListener('change', () => {
        applyTheme(checkbox.checked ? 'dark' : 'light');
    });

    const currentTheme = localStorage.getItem('theme') || 'light';
    checkbox.checked = currentTheme === 'dark';

    applyTheme(currentTheme);

    document.getElementById('load-cluster-dashboard').addEventListener('click', function(e) {
        e.preventDefault(); 

        fetch('{{ path('clusters_dashboard') }}') 
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text(); 
            })
            .then(html => {
                document.getElementById('dynamic-content').innerHTML = html;
            })
            .catch(error => {
                console.error('There was a problem with the fetch operation:', error);
            });
    });

    document.getElementById('load-ventes-dashboard').addEventListener('click', function(e) {
        e.preventDefault(); 

        fetch('{{ path('ventes_dashboard') }}') 
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text(); 
            })
            .then(html => {
                document.getElementById('dynamic-content').innerHTML = html;
            })
            .catch(error => {
                console.error('There was a problem with the fetch operation:', error);
            });
    });
</script>

{% endblock %}
