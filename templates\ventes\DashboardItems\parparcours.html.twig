<link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

<style>
* {
    font-family: 'Ubuntu', sans-serif;
}

:root {
    --pargamme-color-background-light: #C0E7F7;
    --pargamme-color-background-dark: #e9f5fd;
    --4text-colors: #1d4d6b;
}

[data-theme="dark"] {
    --pargamme-color-background-light: #1e1e1e;
    --pargamme-color-background-dark: #2a2a2a;
        --4text-colors: #919093; 
}

.parparcours-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.parparcours-header {
    font-size: 0.8em;
    margin: 0;
}

.parparcours-chart-and-legend {
    display: flex;
    align-items: center;
    margin-top: 5%;
    gap: 20px;
}

.parparcours-chart-container {
    height: 112px;
    width: 112px;
}

.parparcours-legend {
    list-style: none;
    padding: 0;
    margin: 0;
}

.parparcours-legend li {
    display: flex;
    align-items: center;
    font-size: 0.8em;
     color: var( --4text-colors);
    margin-bottom: 5px;
}

.parparcours-legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 30%;
}
</style>
<div class="bonustechno-container">
    <h5 class="text-header4s">Par parcours</h5>
    <div class="bonustechno-chart-and-legend">
        <div class="bonustechno-chart-container">
            <canvas id="parparcoursPieChart"></canvas>
        </div>
        <ul class="bonustechno-legend">
            {% for item in productionsGroupData.Categorie %}
                {% set gradient %}
                    {% if item.categoryName == 'CONQUETE FIXE' %}
                        linear-gradient(to right, #49b9ee, #a8e2f5)
                    {% elseif item.categoryName == 'MIGRATION' %}
                        linear-gradient(to right, #027fc0, #4bb3e5)
                    {% elseif item.categoryName == 'MOBILES' %}
                        linear-gradient(to right, #145277, #3a7d9c)
                    {% else %}
                        linear-gradient(to right, #888, #ccc) {# Default gradient for others #}
                    {% endif %}
                {% endset %}
                <li><span class="bonustechno-legend-color" style="background: {{ gradient }};"></span>{{ item.categoryName }}</li>
            {% endfor %}
        </ul>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    var chartData = {{ productionsGroupData.Categorie | json_encode() | raw }}; // Pass data from PHP to JS

    var bonustechnoCtx = document.getElementById('parparcoursPieChart').getContext('2d');

    // Create gradients for the segments
    var bonustechnoGradientA = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientA.addColorStop(0, '#49b9ee');
    bonustechnoGradientA.addColorStop(1, '#a8e2f5');

    var bonustechnoGradientB = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientB.addColorStop(0, '#027fc0');
    bonustechnoGradientB.addColorStop(1, '#4bb3e5');

    var bonustechnoGradientC = bonustechnoCtx.createLinearGradient(0, 0, 0, 160);
    bonustechnoGradientC.addColorStop(0, '#145277');
    bonustechnoGradientC.addColorStop(1, '#3a7d9c');

    // Process chartData for the chart's labels and data
    var labels = [];
    var data = [];
    var backgroundColors = []; // Array to hold background colors for each segment

    chartData.forEach(function(item) {
        labels.push(item.categoryName); // Add category name as label
        data.push(item.totalVentes); // Add totalVentes as data
        
        // Assign gradient based on categoryName
        if (item.categoryName == 'CONQUETE FIXE') {
            backgroundColors.push(bonustechnoGradientA);
        } else if (item.categoryName == 'MIGRATION') {
            backgroundColors.push(bonustechnoGradientB); // MIGRATION gets the correct gradient
        } else if (item.categoryName == 'MOBILES') {
            backgroundColors.push(bonustechnoGradientC);
        } else {
            backgroundColors.push('#888'); // Default color for other categories
        }
    });

    // Create pie chart with the processed data
    var parparcoursPieChart = new Chart(bonustechnoCtx, {
        type: 'pie',
        data: {
            labels: labels, // Category names as labels
            datasets: [{
                data: data, // Sales data for each category
                backgroundColor: backgroundColors, // Assign the background colors array dynamically
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false // Hide the default legend (you've created a custom one)
                }
            }
        }
    });
</script>


