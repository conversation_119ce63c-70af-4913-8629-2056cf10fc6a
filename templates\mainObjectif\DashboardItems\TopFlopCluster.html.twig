<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">

<style>
  body {
    font-family: 'Roboto', sans-serif;
  }
  .top-flop-cluster {
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    padding: 15px;

    display: flex;
    width: 50%;
    position: relative;
  }

  .expand-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 0.1rem;
    color: #888;
    cursor: pointer;
  }

  .container {
    display: flex;
    align-items: center;
  }

  .chart-container {
    width: 50%;
    height: 230px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tables-container {
    width: 49%;
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .sales-table,
  .sales-table th,
  .sales-table td {
    border: none !important;
    background-color: transparent;
  }

  .sales-table th {
    color: #000;
    background-color: transparent;
    font-size: 0.8em;
    padding: 12px;
    font-size: 0.8em;
    padding: 1px 11px;
    text-align: left;
  }

  .sales-table td {
    color: #72ac9d;
    padding: 2px;
    font-size: 0.8em;
    text-align: left;
    padding: 1px 11px;
  }

  .sales-table thead {
    font-weight: bold;
    border-bottom: 2px solid transparent;
  }

  .sales-table tbody tr:nth-child(even) {
    background-color: transparent;
  }

  .sales-table .negative-sales {
    color: red;
  }

  .sales-table-bad {
    background-color: #ceece7;
    color: black;
    padding: 15px;
    border-radius: 15px;
    border-collapse: collapse;
    margin-top: 10px;
    text-align: left;
    border: none;
  }

  .sales-table-bad td {
    padding: 1px 11px;
    font-size: 0.8em;
    color: red;
    text-align: left;
    border: none;
  }

  .text-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #000;
    z-index: 10;
  }

  #global-text {
    font-size: 1.1em;
    color: #333;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    margin: 0;
    line-height: 1.2;
  }

  #total-sales {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
    margin: 0;
    line-height: 1.2;
  }

  #sales-info {
    font-size: 0.6em;
    color: #87a094;
    margin: 0;
        font-weight: bold;

    line-height:1.2;
  }

  #sales-bad {
    font-size: 0.6em;
    color: red;
    margin: 0;
        font-weight: bold;

    line-height: 1.2;
  }

  .sales-table th,
  .sales-table td {
    text-align: left;
  }
</style>

<h3>Top Flop Cluster</h3>
      <i class="expand-icon" data-feather="maximize"></i>
      <div class="container">
        <div class="chart-container">
          <canvas id="globalSalesChart"></canvas>
          <div class="text-overlay">
            <div id="global-text">Global</div>
            <div id="total-sales">21890</div>
            <div id="sales-info">Sales: 190 (0.86%)</div>
            <div id="sales-bad">(0.86%)</div>
          </div>
        </div>
        <div class="tables-container">
          <table class="table sales-table">
            <thead>
              <tr>
                <th>Ville</th>
                <th>Prises</th>
                <th>Ventes</th>
                <th>PCT 3%</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Lille</td>
                <td>13</td>
                <td>13</td>
                <td>32%</td>
              </tr>
              <tr>
                <td>Lille</td>
                <td>13</td>
                <td>13</td>
                <td>32%</td>
              </tr>
              <tr>
                <td>Lille</td>
                <td>13</td>
                <td>13</td>
                <td>32%</td>
              </tr>
              <tr>
                <td>Amiens</td>
                <td>12</td>
                <td>12</td>
                <td>9.79%</td>
              </tr>
            </tbody>
          </table>
          <table class="table sales-table-bad">
            <tbody>
              <tr class="negative-sales">
                <td>Nancy</td>
                <td>860</td>
                <td>1</td>
                <td>0.87%</td>
              </tr>
              <tr class="negative-sales">
                <td>Nancy</td>
                <td>860</td>
                <td>1</td>
                <td>0.87%</td>
              </tr>
              <tr class="negative-sales">
                <td>Nancy</td>
                <td>860</td>
                <td>1</td>
                <td>0.87%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/feather-icons"></script>
<script>
  feather.replace();
  var ctx = document.getElementById('globalSalesChart').getContext('2d');
  var globalSalesChart = new Chart(ctx, {
    type: 'doughnut',
 data: {
  datasets: [{
    data: [80, 20],
    backgroundColor: ['#47c2ae', '#d1c9cd'],
  
    borderWidth: 0,
  }]
},

    options: {
      cutoutPercentage: 70,
      responsive: true,
      maintainAspectRatio: false,
      tooltips: {
        enabled: false
      },
      hover: {
        mode: null
      }
    }
  });
</script>