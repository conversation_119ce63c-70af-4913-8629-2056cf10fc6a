
<style>
.statDash,.ChiffreDash {
    font-family: Consolas, monospace !important;
	color:var(--tree-view-color, #000) !important;
        /*overflow: hidden;*/
}
.ChiffreDash{
	height: 90px;
	    display: flex;
}

.ChiffreChart{
	height: 70px;
    width: 110px;
    border: 1px solid var(--Mainchart-border);
    /* color: var(--chart-spans); */
    background: var(--Mainchart-background);
    margin: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    margin-right: 0;
    margin-left: 5px;

}

.ChiffreChartMeto{
	height: 72x;
    width: 200px;
    border: 1px solid #3e4e54;
    background: var(--Mainchart-background);
    margin: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.year-2025 {
    display: flex;
    align-items: center; 
    gap: 10px;
}
.year-2026 {
    display: flex;
    align-items: center; 
    gap: 10px; 
}
.year-2027 {
    display: flex;
    align-items: center; 
    gap: 10px; 
}
.year-2028 {
    display: flex;
    align-items: center; 
    gap: 10px; 
}
.chart-container-cuivre{
    padding: 10px;
    background: var(--Mainchart-background);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--Mainchart-border);
    color: var(--chart-spans);
    margin-right: 0;
    height: 48%;
    width:307px;
}


/* Styles généraux pour les barres de défilement */
.scrollbar-custom {
    overflow-y: none;
    scrollbar-width: thin; /* Pour Firefox */
    scrollbar-color: var(--Tree-Froms-scrolbar-bg-font) var(--Tree-Froms-scrolbar-bg);
    scroll-behavior: smooth;
     overflow-x: none;
}

/* Style pour Webkit (Chrome, Edge, Safari) */
.scrollbar-custom::-webkit-scrollbar {
    width: 9px;
    background-color: var(--Tree-Froms-scrolbar-bg);
}

.scrollbar-custom::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px var(--Tree-Froms-scrolbar-bg) !important;
    border-radius: 10px;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
    background-color: var(--Tree-Froms-scrolbar-bg-font);
    border-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
    background: #555; /* Couleur du curseur au survol */
}

/* Application des styles aux sections spécifiques */
.year-section {
    height: 70px;
}

.listeDest {
    max-height: 200px; /* Ajout pour éviter un débordement */
}

.listeDest{
    overflow-y: auto;
    scrollbar-width: thin;
}


.title {
            font-size: 16px;
                margin-top: -7px;
                margin-bottom: 5px;
        }
        .months {
            display: flex;
            justify-content: center;
            
            color: #64748b;
            font-size: 11px;
        }

        .months span {
            margin: 0 5px;
        }

            .count {
            width: 30px;
            text-align: right;
            font-size: 11px;
        }
        .dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color:var(--coloredTextPrimary);
            cursor: pointer;
        }
            .year {
            width: 40px;
            font-size: 14px;
            color: #94a3b8;
        }

        .dots {
            display: flex;
            gap: 6px;
            flex-grow: 1;
        }
        .dot.gray {
            background-color: #3e4e54;
           /*background:var(--coloredTextPrimary);*/
            opacity: 0.5;
        }
    .chartDays{
    width: 250px;
    height: 113px;
    border: 1px solid var(--Mainchart-border);
    background: var(--Mainchart-background);
    padding: 5px;
    }
    .annotationChart{
        margin-left: 93px;
        position: relative;
        top: -22px;
    }

.villeByInsee {
    position: relative;
    width: 360px;
    height: 235px;
    border: 1px solid var(--Mainchart-border);
    background: var(--Mainchart-background);
}
.villeByInsee::before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(180deg, var(--Mainchart-background-rgb) 0, var(--Mainchart-background-rgb-80) 20%, rgb(0 0 0 / 0%) 50%);
    z-index: 1;
}
.villeByInsee::after {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(0deg, var(--Mainchart-background-rgb) 0, var(--Mainchart-background-rgb-80) 20%, rgb(0 0 0 / 0%) 50%);
}
.villeByInsee:hover .cityImage {
    filter: unset;
    cursor: pointer;
}
.blockCityImage::before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    /* background: #2d8ceb; */
}

.cityImage {
    width: 100%;
    filter: grayscale(100%);
    opacity: 0.6;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}
.cityWeather {
    position: absolute;
    z-index: 9;
    background: none;
    /* border: 1px solid red; */
    width: 100%;
    padding: 0.5em;
}
.cityHabitant {
    position: absolute;
    bottom: 0;
    color: var(--tree-view-color, #000) !important;
    left: 0;
    z-index: 1;
    width: 100%;
    padding: 0.5em;
    text-align: right;
}

.cpuUtilizationChart{
    position:static;
    margin-top: 10px;
    margin-bottom: 8px;
    box-sizing: border-box;
    height: 48%;
    justify-self: center;
    width: 165px;
}

        .time-text {
            font-size: 18px;
            font-family: 'Arial', sans-serif;
            fill: #1e90ff;
        }
   

   input#InputNumber{
    width: 100%;
    background: inherit;
    box-shadow: none;
    border: 1px solid #636363;
    border-radius: 6px;
    color: #fff;
}
input#InputNumber:focus-visible {
    border: 1px solid #636363 !important;
    box-shadow: none;
    outline: #636363;
}
  .stat-number {
    text-align: right;
    min-width: 30%; /* Pour s'assurer qu'il reste à droite */
    margin-right: 5px;
}



/* Add this CSS to style the span when it's in edit mode */
.edit-mode {
    width: 50px;
    outline: none;
    background: inherit;
    color: inherit;
    box-shadow: none;
    border: none;
}

.JachereDiv{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
}

</style>



<div class="ChiffreDash prises" >
   
	{# <div class="ChiffreChartMeto">
		<div id="weather"></div>
	</div> #}
    <div class="ChiffreChart CardWithAction" >
		<span style="font-size:14px;">T.P</span>
		<span class="card-number TauxPenetration" class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart" >
		<span style="font-size: 14px;">FTTB</span>
		<span class="nb_fyr_fttb" class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart" >
		<span style="font-size: 14px;">ADSL</span>
		<span class="nb_fyr_adsl" class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart" >
		<span style="font-size: 14px;">Mobile</span>
		<span class="nb_fyr_mob_mono" class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart" >
		<span style="font-size: 14px;">Thd+mobile</span>
		<span class="nb_fyr_mob_multi_thd" class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart" >
		<span style="font-size: 14px;">ADSL+mobile</span>
		<span class="nb_fyr_mob_multi_adsl" class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart" >
		<span style="font-size: 14px;">THD</span>
		<span class="nb_fyr_thd" class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart" >
		<span style="font-size: 14px;">villeSup 100jrs</span>
		<span class="nbrVilleSupCentJrs"class="NumberSpanCards"></span>
	</div>
    <div class="ChiffreChart"> 
        <span style="font-size: 14px;">Cuivre</span>
        <span id="currentTime"class="NumberSpanCards"></span>
    </div>
    <div class="ChiffreChart"> 
        <span style="font-size: 14px;">Jachere</span>
        <div class="JachereDiv">
        <span id="jachere" class="NumberSpanCards"  onclick="editSpanJachere(this)"></span>
        <span style="font-size: 17px;">J</span>
        </div>
    </div>
    <div class="ChiffreChart"> 
        <span style="font-size: 14px;">Deja Client</span>
        <span id="dejaClient"class="NumberSpanCards"></span>
    </div>
</div>

<div class="statDash">
    {# <div class="villeByInsee">
        <img id="cityImage"src="" class="cityImage" alt="" />
        <span id="cityHabitant" class="cityHabitant"></span>
    </div> #}
    <div class="villeByInsee">
        <div id="weather" class="cityWeather" >
            <span style="font-size: 14px">Aléria</span>
            <span style="display: flex;justify-content: space-between; font-size: 20px;align-items: center;"><img src="/image/Weather/2.svg" alt="Peu nuageux" style="width:40px" class="weather-icon"> <span>12.1°C</span></span>
        </div>
        <div class="blockCityImage" style="position: relative; height: 100%;width: 100%;">
            <img id="cityImage" src="" class="cityImage" alt="" style="position: relative; display: block;">        
        </div>
        <div id="cityHabitant" class="cityHabitant">2239 Habitants</div>
    </div>
<div class="blocMotifEchec" style="gap: 5px;  flex-direction: row; align-items: flex-start;">


    <div class="statDashcard" style="">
        <div class="chart-container-cuivre">
            <div class="title">Arrêt du cuivre</div>

        <div class="year-section scrollbar-custom">
            <!-- Dynamic Year Sections will be added here -->
        </div>

            <div class="months">
                <span>J</span><span>F</span><span>M</span><span>A</span><span>M</span>
                <span>J</span><span>J</span><span>A</span><span>S</span><span>O</span>
                <span>N</span><span>D</span>
            </div>
        </div>
    </div>
   <div class="statDashcard" style="height: 235px;">
        <div class="chart-container-cuivre ">
            <div class="title">Distribué aujourd'hui</div>

       <div id="top-clustersc"style="color: var(--coloredTextPrimary); background:none;"  class="list-container listeDest scrollbar-custom"></div>
           </div>
    </div>
      </div>
    <div class="chartDays">
        <canvas id="cpuUtilizationChart" class="cpuUtilizationChart"></canvas>
        <span class="annotationChart"></span>
    </div>

    {# <div class="ChiffreChart"style="    width: 130px;height: 111px;margin: 2px;" >
        <div class="ClusterForprospection"style="display:none;">
            <span style="font-size: 14px;">Cluster</span>
		    <span class="ClusterCodeAppend"style="font-size: 14px;"></span>
        </div>
        <input type="number" id="InputNumber" name="tentacles" min="1" />
        <button style="width: 100%;background: #636363;color: #dcdcdc; border-radius: 7px;"onclick="fetchprospection()">update</button>
	</div> #}

</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@1.2.0"></script>


<script>
function editSpanJachere(element) {
    let currentValue = element.innerText.trim();
    let input = document.createElement("input");
    input.type = "text";
    input.value = currentValue;
    input.style.width = "50px";
    input.classList.add("edit-mode");

    input.onkeydown = function(event) {
        if (event.key === "Enter") {
            event.preventDefault();
            let newValue = input.value.trim();
            if (newValue !== "") {
                element.innerText = newValue;
                element.textContent = newValue;
                element.innerHTML = newValue;
                element.style.display = 'none';
                element.style.display = 'block';

                fetchprospection(newValue);
                input.remove();
                element.onclick = function() { editSpanJachere(element); }; 
            }
        }
    };

    input.onblur = function() {
        element.innerText = currentValue;
        input.remove();
        element.classList.remove("edit-mode");
        element.onclick = function() { editSpanJachere(element); }; 
    };

    element.innerHTML = "";
    element.appendChild(input);
    input.focus();
}


document.addEventListener("DOMContentLoaded", function () {
        async function fetchdataArretcuivre() {
            try {
                if (!cpv) {
                    throw new Error("Le paramètre 'cpv' est manquant.");
                }
                if (!jwtToken) {
                    throw new Error("Le token JWT est manquant.");
                }
    
                let url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu/${cpv}?page=1`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${jwtToken}`,
                        'Content-Type': 'application/json'
                    }
                });
    
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
    
                const data = await response.json();
        
    
                updateYearSections(data);
            } catch (error) {
                console.error('Fetch error:', error);
            }
        }
    
    
        function updateYearSections(data) {
            const yearSection = document.querySelector(".year-section");
            yearSection.innerHTML = ''; // Effacer le contenu existant
    
            // Fonction pour générer une couleur unique pour chaque année
            function getYearColor(year) {
                const colors = [ "#ff9800", "#ff5722", "#8bc34a","#a855f7", "#67e8f9", "#2563eb"];
                return colors[year % colors.length]; // Cycle à travers les couleurs
            }
    
            // Itérer dynamiquement sur les années disponibles dans `data`
            Object.entries(data).forEach(([year, yearData]) => {
                const yearContainer = document.createElement('div');
                yearContainer.classList.add(`year-${year}`);
    
                const yearDiv = document.createElement('div');
                yearDiv.classList.add('year');
                yearDiv.innerText = year;
    
                const dotsDiv = document.createElement('div');
                dotsDiv.classList.add('dots');
    
                const yearColor = getYearColor(parseInt(year));
    
                let totalPrises = 0;
                const totalMonths = 12;
    
                for (let i = 1; i <= totalMonths; i++) {
                    const dot = document.createElement('div');
                    dot.classList.add('dot');
                    dot.setAttribute("data-month", i); // Assurer que le mois est stocké
    
                    if (yearData.hasOwnProperty(i)) {
                        totalPrises += yearData[i].totalPrises || 0;
                        //dot.style.backgroundColor = yearColor;
                        dot.style.backgroundColor = "var(--coloredTextPrimary)";
                    } else {
                        dot.classList.add('gray');
                    }
    
                    dotsDiv.appendChild(dot);
                }
    
                const countDiv = document.createElement('div');
                countDiv.classList.add('count');
                countDiv.innerText = totalPrises;
    
                yearContainer.appendChild(yearDiv);
                yearContainer.appendChild(dotsDiv);
                yearContainer.appendChild(countDiv);
    
                yearSection.appendChild(yearContainer);
            });
    
            let selectedYear = null; // Store selected year
    
            document.querySelectorAll(".dot").forEach(dot => {
                dot.addEventListener("click", function () {
                    const yearContainer = this.closest("[class^='year-']");
                    if (!yearContainer) return;
    
                    const year = yearContainer.classList[0].split("-")[1];
                    const month = this.getAttribute("data-month");
    
                    let storedYear = localStorage.getItem("selectedYear");
                    let storedMonth = localStorage.getItem("selectedMonth");
    
                    // Si aucun mois n'est sélectionné, définir un mois par défaut (janvier par exemple)
                    if (!storedMonth) {
                        storedMonth = "1";  // Janvier
                        localStorage.setItem("selectedMonth", storedMonth);
                    }
    
                    // Vérification de la sélection actuelle pour basculer entre sélection/désélection
                    if (storedYear === year && storedMonth === month) {
                        localStorage.removeItem("selectedYear");
                        localStorage.removeItem("selectedMonth");
                    } else {
                        localStorage.setItem("selectedYear", year);
                        localStorage.setItem("selectedMonth", month);
                    }
                    displayataArretcuivre(year,month);
                    // Changer la couleur de la "dot" sélectionnée
                    const originalColor = getYearColor(parseInt(year));
                    if (dot.classList.length===1){
                        this.style.backgroundColor = this.style.backgroundColor === "var(--coloredTextPrimary)" ? originalColor : "var(--coloredTextPrimary)";
    
                    }else{
                        this.style.backgroundColor = this.style.backgroundColor === "rgb(62, 78, 84)" ? originalColor : "#3e4e54";
                    }
    
                    // Mettre à jour les données affichées sans recharger la page
                   // updateFilteredData();
                });
            });
        }
    
        
        fetchdataArretcuivre();
        
    
    
    async function productionsTopFlop() {
        try {
            // Obtenir la date du jour et la formater en "DD-MM-YYYY"
            const today = new Date();
            const day = String(today.getDate()).padStart(2, '0'); 
            const month = String(today.getMonth() + 1).padStart(2, '0'); // Les mois commencent à 0
            const year = today.getFullYear();
            const formattedDate = `${day}-${month}-${year}`; // "DD-MM-YYYY"

            let url = `https://api.nomadcloud.fr/api/interventions-places-distribution/${pointOfSaleId}?dateDebut=${formattedDate}&dateFin=${formattedDate}&page=1`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${jwtToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            console.log("Données reçues:", data);

            displayClusters(data);
        } catch (error) {
            console.error('Erreur lors de la récupération des données:', error);
        }
    }
    function truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
    }


    function displayClusters(clusters) {
        const topContainer = document.getElementById("top-clustersc");

        if (!topContainer) {
            console.error("L'élément #top-clustersc est introuvable.");
            return;
        }

        // Vider le conteneur avant d'ajouter de nouvelles données
        topContainer.innerHTML = "";

        if (!Array.isArray(clusters) || clusters.length === 0) {
            topContainer.innerHTML = "<p>Aucune donnée disponible.</p>";
            return;
        }

        clusters.forEach(cluster => {
            const clusterDiv = document.createElement("div");
            clusterDiv.classList.add("statistic");

            let clusterContent = `
                <div class="statistic-content">
                    <div class="stat-title">${truncateText( cluster.user_nom_prenom,20)}</div>
                    <div class="stat-number">${cluster.total_prises}</div>
                </div>
            `;

            clusterDiv.innerHTML = clusterContent;

            // Ajouter un événement pour ouvrir le panel
            clusterDiv.addEventListener("click", async function () {
                try {
                    const SearchContainerINPanel = document.querySelector('.SearchContainerINPanel');
                    if (SearchContainerINPanel) SearchContainerINPanel.style.display = 'none';

                    const productionPanel = document.getElementById('displayproductionPanel');
                    if (productionPanel) productionPanel.style.bottom = '0';

                    const sidebar = document.querySelector('.DetailsCAlenderdata');
                    if (!sidebar) {
                        console.error("Sidebar element not found.");
                        return;
                    }
                    sidebar.innerHTML = '';

                    let table = document.getElementById("productionTable");
                    if (!table) {
                        sidebar.innerHTML = `
                            <table id="productionTable" class="productionTable">
                                <thead>
                                    <tr>
                                        <th>Cluster</th>
                                        <th>Ville</th>
                                        <th>Total Prises</th>
                                    </tr>
                                </thead>
                                <tbody id="productionTableBody"></tbody>
                            </table>
                        `;
                        table = document.getElementById("productionTable");
                    }

                    const tbody = document.getElementById("productionTableBody");
                    tbody.innerHTML = '';

                    // Iterating over the clusters and their villes
                    cluster.clusters.forEach(clusterItem => {
                        clusterItem.villes.forEach(ville => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${clusterItem.codeCluster}</td>
                                <td>${ville.ville}</td>
                                <td>${ville.total_prises}</td>
                            `;
                            tbody.appendChild(row);
                        });
                    });
                } catch (error) {
                    console.error('Erreur lors de la gestion de la production:', error);
                }
            });

            topContainer.appendChild(clusterDiv);
        });
    }
    function showDetailsPopup(item) {
        console.log(item);
    }
    productionsTopFlop();
});
{# async function displayataArretcuivre(year, month) {
    try {
  
        const codeClustercuivre = localStorage.getItem("clusterCode");
        const codeInseeCuivre = localStorage.getItem("CodeInsee");

        let url = "";
        let data;

        if (codeInseeCuivre) {
            // cas codeInsee présent => API streets
            url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu-streets/${cpv}`;
                if (codeClustercuivre) url += `/${codeClustercuivre}`;
            url += `/${codeInseeCuivre}`;
            if (year && month) url += `?annee=${year}&mois=${month}`;
        
        } else {
            // cas cluster ou général
            url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu/${cpv}`;
            if (year && month) url += `?annee=${year}&mois=${month}`;
            if (codeClustercuivre) url += `&codeCluster=${codeClustercuivre}`;
        }

        console.log("Fetch URL:", url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

        data = await response.json();

        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) return console.error("Sidebar element not found.");
        sidebar.innerHTML = ''; // reset sidebar

        let tableId = "productionTableCuV2";
        let tableHeaders = '';
        let tableData = [];

        if (codeInseeCuivre) {
            tableHeaders = `
                <th>Code iris</th>
                <th>Libelle voie</th>
                <th>Total Prises</th>
                <th></th>`;
            tableData = Array.isArray(data.voies) ? data : [data];
        } else if (codeClustercuivre) {
            tableHeaders = `
                <th>Code Ville</th>
                <th>Libelle Ville</th>
                <th>Total Prises</th>
                <th></th>`;
            tableData = data.villes;
        } else {
            tableHeaders = `
                <th>Code Cluster</th>
                <th>Libelle Cluster</th>
                <th>Total Prises</th>
                <th></th>`;
            tableData = data.clusters;
        }

        sidebar.innerHTML = `
            <table id="${tableId}" class="productionTable">
                <thead><tr>${tableHeaders}</tr></thead>
                <tbody id="CuV2TableBody"></tbody>
            </table>`;

        await populateTableDataCuV2(tableData, codeClustercuivre,codeInseeCuivre);
    } catch (error) {
        console.error('Fetch error:', error);
    }
} #}

async function populateTableDataCuV2(data, codeClustercuivre, codeInseeCuivre) {
    console.log('Loading production', data);
    const tbody = document.getElementById("CuV2TableBody");
    if (!tbody) {
        console.error("Table body not found.");
        return;
    }
    tbody.innerHTML = ''; // Clear previous content

    if (data && data.length > 0) {
        const fragment = document.createDocumentFragment();

        data.forEach(item => {
            const row = document.createElement('tr');

            if (codeInseeCuivre) {
                // Data for street-level
                row.innerHTML = `
                    <td>${item.code_iris}</td>
                    <td>${item.nom_voie}</td>
                    <td>${item.total_prises}</td>
                    <td></td>
                `;
            } else if (codeClustercuivre) {
                // Data for city-level
                row.innerHTML = `
                    <td>${item.cod_insee}</td>
                    <td>${item.ville}</td>
                    <td>${item.totalPrises}</td>
                    <td></td>
                `;
            } else {
                // General cluster-level data
                row.innerHTML = `
                    <td>${item.code_cluster}</td>
                    <td>${item.libelle_cluster}</td>
                    <td>${item.totalPrises}</td>
                    <td></td>
                `;
            }

            fragment.appendChild(row);
        });

        tbody.appendChild(fragment);
    } else {
        tbody.innerHTML = '<tr><td colspan="4">No data found for the selected date.</td></tr>';
    }
}

async function displayataArretcuivre(year, month) {
    try {
        const codeClustercuivre = localStorage.getItem("clusterCode");
        const codeInseeCuivre = localStorage.getItem("CodeInsee");


        let url = "";

        if (codeInseeCuivre) {
            url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu-streets/${cpv}`;
            if (codeClustercuivre) url += `/${codeClustercuivre}`;
            url += `/${codeInseeCuivre}`;
            if (year && month) url += `?annee=${year}&mois=${month}`;
        } else {
            url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu/${cpv}`;
            if (year && month) url += `?annee=${year}&mois=${month}`;
            if (codeClustercuivre) url += `&codeCluster=${codeClustercuivre}`;
        }

        console.log("Fetch URL:", url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

              if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        var displayPanelSearching =document.getElementById('displayPanelSearching');
        productionPanel.style.bottom = '-7px'
        
        const data = await response.json();

        const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) return console.error("Sidebar element not found.");
        sidebar.innerHTML = ''; // reset sidebar

        let tableId = "productionTableCuV2";
        let tableHeaders = '';
        let tableData = [];

        if (codeInseeCuivre && data.voies) {
            tableHeaders = `
                <th style="width:5%" >Code iris</th>
                <th style="width:20%">Libelle voie</th>
                <th>Total Prises</th>
                <th></th>`;
            tableData = data.voies;
        } else if (codeClustercuivre && data.villes) {
            tableHeaders = `
                <th style="width:5%" >Code Ville</th>
                <th>Libelle Ville</th>
                <th>Total Prises</th>
                <th></th>`;
            tableData = data.villes;
        } else if (data.clusters) {
            tableHeaders = `
                <th style="width:5%" >Code Cluster</th>
                <th>Libelle Cluster</th>
                <th>Total Prises</th>
                <th></th>`;
            tableData = data.clusters;
        } else {
            sidebar.innerHTML = `<p>No data found for this selection.</p>`;
            return;
        }

        sidebar.innerHTML = `
            <table id="${tableId}" class="productionTable">
                <thead><tr>${tableHeaders}</tr></thead>
                <tbody id="CuV2TableBody"></tbody>
            </table>`;

        await populateTableDataCuV2(tableData, codeClustercuivre, codeInseeCuivre);
    } catch (error) {
        console.error('Fetch error:', error);
    }
}

{# async function populateTableDataCuV2(data,codeClustercuivre) {
    console.log('Loading production', data);
    const tbody = document.getElementById("CuV2TableBody");
    if (!tbody) {
        console.error("Table body not found.");
        return;
    }
    tbody.innerHTML = ''; // Clear previous content

    if (data && data.length > 0) {
        const fragment = document.createDocumentFragment();
     data.forEach(item => {
    const row = document.createElement('tr');
    
    // Teste si c'est un cluster ou une ville
    if (codeClustercuivre) {
      row.innerHTML = `
            <td>${item.cod_insee}</td>
            <td>${item.ville}</td>
            <td>${item.totalPrises}</td>
        
        `;
    }
    
     else  {
  
         row.innerHTML = `
            <td>${item.code_cluster}</td>
            <td>${item.libelle_cluster}</td>
            <td>${item.totalPrises}</td>
       
        `;
    }

    fragment.appendChild(row);
});

        tbody.appendChild(fragment);
    } else {
        tbody.innerHTML = '<tr><td colspan="4">No data found for the selected date.</td></tr>';
    }
} #}
{# async function displayataArretcuivre(year, month) {
    try {

        let url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu-v2/${cpv}`;
        let codeClustercuivre = localStorage.getItem("clusterCode");
        let CodeInseercuivre = localStorage.getItem("codInsee");
        
        console.log("displayataArretcuivre",codeClustercuivre);
         console.log("displayataArretcuivre",CodeInseercuivre);
        if (year && month) {
            url += `?annee=${year}&mois=${month}`;
        }
        if (codeClustercuivre) {
            url += `&codeCluster=${codeClustercuivre}`;
        }
        console.log(url);
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        var displayPanelSearching =document.getElementById('displayPanelSearching');
        productionPanel.style.bottom = '-7px';
         const sidebar = document.querySelector('.DetailsCAlenderdata');
        if (!sidebar) {
            console.error("Sidebar element not found.");
            return;
        }
        sidebar.innerHTML = ''; // Clear previous content
        var data = await response.json();
         console.log("updateYearSectionsv2",data);
        let tableId = "productionTableCuV2";
        let table = document.getElementById(tableId);
        
        if (!table) {
            let tableHeaders = '';
            if (codeClustercuivre) {
                 
                     data=Array.isArray(data.ville) ? data : [data];
                tableHeaders = `
                    <th style="width: 7%;">Code Clusteur</th>
                    <th style="width: 7%;">Libelle Clusteur</th>
                    <th style="width: 7%;">Total Prises</th>
                    <th></th>
                `;
    
            } else {
                 data=Array.isArray(data.clusters) ? data : [data];
               
                tableHeaders += `
                    <th style="width: 7%;">Code Ville</th>
                    <th style="width: 7%;">Libelle Ville</th>
                    <th style="width: 7%;">Total Prises</th>
                    <th></th>
                `;
            }
            sidebar.innerHTML = `
                <table id="${tableId}" class="productionTable">
                    <thead>
                        <tr>${tableHeaders}</tr>
                    </thead>
                    <tbody id="CuV2TableBody"></tbody>
                </table>
            `;
            table = document.getElementById(tableId);
        }

        await populateTableDataCuV2(data,codeClustercuivre);
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
async function populateTableDataCuV2(data, codeClustercuivre) {
    console.log('Loading production', data);
    const tbody = document.getElementById("CuV2TableBody");
    if (!tbody) {
        console.error("Table body not found.");
        alert("An error occurred while loading the table. Please try again.");
        return;
    }
    tbody.innerHTML = ''; // Clear previous content

    if (!Array.isArray(data) || data.length === 0) {
        console.error("No data available to populate the table.");
        return;
    }

    const fragment = document.createDocumentFragment();
    data.forEach(item => {
        const row = document.createElement('tr');

        // Test if it's a cluster or a city
        if (item.code_cluster && codeClustercuivre) {
            row.innerHTML = `
                <td>${item.code_cluster}</td>
                <td>${item.libelle_cluster}</td>
                <td>${item.totalPrises}</td>
                <td>${item.arret_cu || 'N/A'}</td>
            `;
        } else if (item.cod_insee) {
            row.innerHTML = `
                <td>${item.code_cluster}</td>
                <td>${item.libelle_cluster}</td>
                <td>${item.totalPrises}</td>
                <td>${item.arret_cu || 'N/A'}</td>
            `;
        }

        fragment.appendChild(row);
    });

    tbody.appendChild(fragment); // Append all rows at once
} #}
function calculateDaysUntil(targetDate) {

    const currentDate = new Date();

    const target = new Date(targetDate);

    const differenceInMillis = target - currentDate;
    const differenceInDays = Math.floor(differenceInMillis / (1000 * 60 * 60 * 24));

    return differenceInDays;
}

function updateTimeTextWithDays(targetDate) {
    const timeTextElement = document.getElementById('currentTime');
    if (!targetDate) {
        timeTextElement.textContent = "--";
        return;
    }

    const daysRemaining = calculateDaysUntil(targetDate);
    if (timeTextElement) {
        timeTextElement.textContent = `-${daysRemaining} j`;
    }
}
async function transformData(rawData) {
    const transformedData = {};
    
    // Ensure we always work with an array
    const clusters = Array.isArray(rawData) ? rawData : [rawData];

    clusters.forEach(cluster => {
        const [year, month] = cluster.arret_cu.split('-');

        if (!transformedData[year]) {
            transformedData[year] = {};
        }

        if (!transformedData[year][month]) {
            transformedData[year][month] = {
                totalPrises: 0,
                clusters: []
            };
        }

        transformedData[year][month].totalPrises += cluster.totalPrises;
        transformedData[year][month].clusters.push(cluster);
    });

    updateYearSectionsv2(transformedData);
    console.log("transformedData", transformedData);
    return transformedData;
}


async function fetchdataArretcuivrev2(codeClustercuivre, codeinseecuivre) {
    try {
        const params = new URLSearchParams();

        if (codeClustercuivre) {
            params.append('codeCluster', codeClustercuivre);
        }
        if (codeinseecuivre) {
            params.append('codeInsee', codeinseecuivre);
            params.append('page', '1');
        }

        const url = `https://api.nomadcloud.fr/api/interventions-places-arret-cu-v2/${cpv}?${params.toString()}`;
        console.log(url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${jwtToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Fetched data", data);

        const transformed = transformData(data); // Not necessarily async
        console.log( "Fetched data transformed",transformed);
        if (data && Array.isArray(data) && data.length > 0) {
            const arretCu = data[0].arret_cu;
            updateTimeTextWithDays(arretCu || null);
        } else {
            console.warn('No data found or invalid response format');
            updateTimeTextWithDays(null);
        }
    } catch (error) {
        console.error('Fetch error:', error);
        updateTimeTextWithDays(null);
    }
}


async function cuivreV2(codeClustercuivre, codeinseecuivre) {
    fetchdataArretcuivreForMenuSelection();
    await fetchdataArretcuivrev2(codeClustercuivre, codeinseecuivre);  
    
}

function updateYearSectionsv2(data) {
    const yearSection = document.querySelector(".year-section");
    yearSection.innerHTML = ""; // Effacer le contenu existant

    function getYearColor(year) {
        const colors = ["#ff9800", "#ff5722", "#8bc34a", "#a855f7", "#67e8f9", "#2563eb"];
        return colors[parseInt(year, 10) % colors.length]; 
    }

    // Itérer sur les années disponibles dans `data`
    Object.entries(data).forEach(([year, yearData]) => {
        console.log(`🔍 Vérification des données pour ${year}:`, yearData);

        const yearContainer = document.createElement("div");
        yearContainer.classList.add(`year-${year}`);

        const yearDiv = document.createElement("div");
        yearDiv.classList.add("year");
        yearDiv.innerText = year;

        const dotsDiv = document.createElement("div");
        dotsDiv.classList.add("dots");

        const yearColor = getYearColor(parseInt(year, 10));

        let totalPrises = 0;
        const totalMonths = 12;

        // S'assurer que les clés des mois sont bien des nombres entiers
        const fixedYearData = {};
        Object.keys(yearData).forEach((key) => {
            fixedYearData[parseInt(key, 10)] = yearData[key];
        });

        // Générer les dots pour chaque mois
        for (let i = 1; i <= totalMonths; i++) {
            const dot = document.createElement("div");
            dot.classList.add("dot");
            dot.setAttribute("data-month", i); // Assurer que le mois est stocké

            if (fixedYearData[i]) {
                totalPrises += fixedYearData[i].totalPrises || 0;
                dot.style.backgroundColor = "var(--coloredTextPrimary)";
            } else {
                dot.classList.add("gray");
            }

            dotsDiv.appendChild(dot);
        }

        const countDiv = document.createElement("div");
        countDiv.classList.add("count");
        countDiv.innerText = totalPrises;

        yearContainer.appendChild(yearDiv);
        yearContainer.appendChild(dotsDiv);
        yearContainer.appendChild(countDiv);

        yearSection.appendChild(yearContainer);
    });

    // Gestion de la sélection d'un mois
    document.querySelectorAll(".dot").forEach((dot) => {
        dot.addEventListener("click", function () {
            const yearContainer = this.closest("[class^='year-']");
            if (!yearContainer) return;

            const year = yearContainer.classList[0].split("-")[1];
            const month = this.getAttribute("data-month");

            let storedYear = localStorage.getItem("selectedYear");
            let storedMonth = localStorage.getItem("selectedMonth");

   

            // Appeler la fonction pour afficher les données
            displayataArretcuivre(year, month);

            // Changer la couleur de la "dot" sélectionnée
            const originalColor = getYearColor(parseInt(year, 10));
            if (dot.classList.length === 1) {
                this.style.backgroundColor = this.style.backgroundColor === "var(--coloredTextPrimary)" ? originalColor : "var(--coloredTextPrimary)";
            } else {
                this.style.backgroundColor = this.style.backgroundColor === "rgb(62, 78, 84)" ? originalColor : "#3e4e54";
            }
        });
    });
}
</script>
