<style>
:root {
    --color-bartext-light: #1a2327; 
}

[data-theme="dark"] {
    --color-bartext-light: #ffff; 
}

.progress-container {
    max-width: 100%;
    display: flex;
    flex-direction: column;
    height: 250px; 
    position: relative;
}
.titlefloating-container{
    display: flex;
    flex-direction: column;
    height: 15%; 
    position: relative;
}


.custom_floating-titledetailsmotifs {
    position: absolute;
    font-size: 1.2em; 
    color: var(--color-bartext-light);
    margin: 0 0 10px 0; 
    pointer-events: none;
    z-index: 1000;
}

.progress-bar-wrapper {
    flex: 1;
    height: 14px; 
    background-color: transparent; 
    border-radius: 8px; 
    margin-right: 5px; 
}

.progress-label {
    font-size: 10px; 
    font-weight: bold;
    white-space: nowrap;
    text-align: right; 
    width: 100%; 
}

#chart_div {
    width: 90%; 
    height: 100%; 
    display: flex; 
    margin-left: 0; 
}

.google-visualization-tooltip {
    text-align: right; 
}

text {
    text-anchor: end; 
    font-size: 10px; 
}

.container-wrapper2 {
    margin-top: 30%; 
}

.floating-title {
    position: absolute;
    top: 10px; 
    left: 10px; 
    padding: 5px 10px; 
    border-radius: 5px; 
    font-size: 15px; 
    font-weight: bold; 
    color: var( --color-bartext-light);
    z-index: 9999;
}
</style>
 <div class="titlefloating-container">
    <div class="floating-title">Détails motifs échecs</div>
</div>
<div class="col">
    <div class="progress-container" id="progressContainer">
        <div id="chart_div"></div>
    </div>
</div>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript">
 google.charts.load('current', {'packages':['corechart']});
google.charts.setOnLoadCallback(drawChart);

function drawChart() {
    var dataArray = [
        ['Element', 'Density', { role: 'style' }],
        {% for item in productionsMotif.details %}
   {% if item.totalVentes > 0 and item.libelle_motif_instance %}
                [
                    '{{ item.libelle_motif_instance }}',
                    {{ item.totalVentes }},
                    '{{ item.totalVentes > 20 ? "linear-gradient(90deg, #47abe9, #59caf2)" : "#8a8889" }}'
                ],
            {% endif %}
        {% endfor %}
    ];

    // Log des données pour vérifier leur contenu
    console.log("Données à passer dans le graphique:", dataArray);

    var data = google.visualization.arrayToDataTable(dataArray);

    var options = {
        title: '',
        width: '90%', 
        height: '100%', 
        legend: { position: 'none' },
        bars: 'horizontal',
        bar: { groupWidth: 'calc(30% - 5px)' },
        hAxis: {
            title: '',
            minValue: 0,
            gridlines: { count: 0 },
            textStyle: { color: 'transparent' }
        },
        vAxis: {
            title: '',
            textPosition: 'out',
            textStyle: {
                color: '#000',
                fontSize: 10,
                marginRight: '20px'
            },
            textAlign: 'right'
        },
        backgroundColor: 'transparent',
        chartArea: {
            left: '65%',
            top: 13,
            width: '90%',
            height: '100%'
        },
        tooltip: { isHtml: false, trigger: 'focus' } 
    };

    var chart = new google.visualization.BarChart(document.getElementById('chart_div'));
    chart.draw(data, options);
}

setTimeout(function() {
    const textElements = document.querySelectorAll('#chart_div text');
    textElements.forEach(text => text.setAttribute('fill', '#8a8889'));
}, 500);


</script>


